# Description
API service for rongxin.loan.mgr.app. This project intends to serve various client management apps for different tenants. If tenant-specific API service is required, will need to adjust the project structure and domain accordingly.

# Domain settings
Use HTTPS for prod/stage/test environments, and HTTP for dev environment.
- prod: mgrapp.api.loan.cacfintech.com
- stage: s-mgrapp.api.loan.cacfintech.com
- test: t-mgrapp.api.loan.cacfintech.com
- dev: dev.mgrapp.api.loan.cacfintech.com

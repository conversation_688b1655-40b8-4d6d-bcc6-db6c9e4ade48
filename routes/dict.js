/**
 * Dict Router
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:dashboard.api:routes:land');

const data = require('../data/dict');
const fundSvc = require('../services/fund');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE
const { STATUS_MAP } = require('../services/loanApplicationInsureOrder/const');
const { APPLICATION_STATUS } = require('../utils/loanApplicationConst');
const {
  argiType: argi_products,
  villageInfo_argiType
} = require('../utils/const/argiProductsConst');

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
   * @api {get} /api/v1.0/dict/survey/user/grade 获取调查问卷用户评级
   * @apiVersion 1.0.0
   * @apiName getSurveyUserGrade
   * @apiGroup Dict
   * @apiPermission authenticated 
   *
   * @apiDescription 获取调查问卷用户评级
   * 
   * @apiParamExample {url} Request-Params-Example:
   * ?
   *
   * @apiExample Example usage:
   * curl -i http://localhost/api/v1.0/dict/survey/user/grade
   *
   * @apiSuccess {String} name 级别展示名称
   * @apiSuccess {Number} value 级别值
   * 
   * @apiSuccessExample {json} Response (example):
   * 
   * HTTP/1.1 200 OK
   * [
   *   {
   *      "name": "非目标用户",
   *      "value": 0
   *   },
   *   ...
   * ]
   * 
   * @apiError(Error 40x) ErrorQuery error params
   * 
   * @apiError(Error 50x) InternalServerError 服务器内部错误
   * 
   */
  async getSurveyUserGrade(req, res) {
    let method = 'getSurveyUserGrade';
    debug(method, '[Enter]');
    try {
      let result = await data.SURVEY_USERGRADE;
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  /**
   * @api {get} /api/v1.0/dict/survey/relationship 获取问卷调研与户主关系
   * @apiVersion 1.0.0
   * @apiName getRelationship
   * @apiGroup Dict
   * @apiPermission authenticated
   *
   * @apiDescription 获取问卷调研与户主关系
   *
   * @apiParamExample {url} Request-Params-Example:
   * ?
   *
   * @apiExample Example usage:
   * curl -i http://localhost/api/v1.0/dict/survey/relationship
   *
   * @apiSuccess {String} name 关系名称
   * @apiSuccess {Number} value 取值
   *
   * @apiSuccessExample {json} Response (example):
   *
   * HTTP/1.1 200 OK
   * [
   *   {
   *      "name": "未知",
   *      "value": 0
   *   },
   *   ...
   * ]
   *
   * @apiError(Error 40x) ErrorQuery error params
   *
   * @apiError(Error 50x) InternalServerError 服务器内部错误
   *
   */
  async getRelationship(req, res) {
    let method = 'getRelationship';
    debug(method, '[Enter]');
    try {
      let result = await data.RELATIONSHIP;
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getFundList(req, res) {
    let method = 'getFundList';
    debug(method, '[Enter]');
    try {
      let result = await fundSvc.getFunds({});
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getLoanAppInsureStatus(req, res) {
    let method = 'getLoanAppInsureStatus';
    debug(method, '[Enter]');
    try {
      let result = [];
      for (let [key, value] of STATUS_MAP) {
        result.push({ status: key, desc: value });
      }
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  //农产品枚举
  async getProductsDict(req, res) {
    let method = 'getProductsDict';
    debug(method, '[Enter]');
    try {
      if (!req.query) {
        throw {
          errorCode: 'VILLAGE_322',
          httpCode: 406,
          reason: 'Invalid Param'
        };
      }
      let type = req.query.type;

      let result = argi_products;
      if (type) {
        result = argi_products[type];
      }
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getBaseProducts(req, res) {
    let method = 'getBaseProducts';
    debug(method, '[Enter]');
    try {
      if (!req.query) {
        throw {
          errorCode: 'DICT_180',
          httpCode: 406,
          reason: 'Invalid Param'
        };
      }
      let type = req.query.type;

      let result = villageInfo_argiType;
      if (type) {
        result = villageInfo_argiType[type];
      }
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  async getCzdStatus(req, res) {
    let method = 'getCzdStatus';
    debug(method, '[Enter]');
    try {
      let result = [{
          code: APPLICATION_STATUS.WAIT_PHONE_CHECK,
          name: "待电核"
        },
        {
          code: APPLICATION_STATUS.REJECTED_PHONE_CHECK,
          name: "电核拒绝"
        },
        {
          code: APPLICATION_STATUS.PRE_CENSOR,
          name: "待初审"
        },
        {
          code: APPLICATION_STATUS.REJECTED_CENSOR,
          name: "初审拒绝"
        },
        {
          code: APPLICATION_STATUS.APPLY_CREDIT,
          name: "待授信"
        },
        {
          code: APPLICATION_STATUS.REJECTED_CREDIT_ACCESS,
          name: "授信拒绝"
        },
        {
          code: APPLICATION_STATUS.WAIT_BANK_SIGN,
          name: "待签约"
        },
        {
          code: APPLICATION_STATUS.REJECTED_BANK_SIGN,
          name: "拒绝签约"
        },
        {
          code: APPLICATION_STATUS.WAIT_LOAN,
          name: "待放款"
        },
        {
          code: APPLICATION_STATUS.REJECTED_LOAN,
          name: "拒绝放款"
        },
        {
          code: APPLICATION_STATUS.LOANED,
          name: "已放款"
        }
      ]
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this;

    self._policyRouter.get('/api/v1.0/dict/survey/user/grade', self.getSurveyUserGrade.bind(self), {
      name: 'dict.getSurveyUserGrade'
    });
    self._policyRouter.get('/api/v1.0/dict/survey/relationship', self.getRelationship.bind(self), {
      name: 'dict.getRelationship'
    });
    self._policyRouter.get('/api/v1.0/dict/fund', self.getFundList.bind(self), {
      name: 'dict.getFundList',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/dict/loan-app-insure-status', self.getLoanAppInsureStatus.bind(self), {
      name: 'dict.getLoanAppInsureStatus',
      role: PERMISSION_ROLE.EVERYONE
    });

    // 村庄作物代码枚举
    self._policyRouter.get('/api/v1.0/dict/village/products', self.getProductsDict.bind(self), {
      name: 'dict.getProductsDict',
      role: PERMISSION_ROLE.EVERYONE
    });
    // 村信息种植，养殖枚举
    self._policyRouter.get('/api/v1.0/dict/village/baseinfo/product', self.getBaseProducts.bind(self), {
      name: 'dict.getBaseProducts',
      role: PERMISSION_ROLE.EVERYONE
    });

    // 车主贷状态枚举
    self._policyRouter.get('/api/v1.0/dict/czd/status', self.getCzdStatus.bind(self), {
      name: 'dict.getCzdStatus',
      role: PERMISSION_ROLE.EVERYONE
    });

    return self;
  }
}

module.exports = Router;
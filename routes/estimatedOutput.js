/*
 * @Description: 地块预估产量
 * @Author: zhu xue song
 * @Date: 2021-07-27 15:26:56
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-07-29 21:48:04
 * @FilePath: \rongxin.loan.user.app.api\routes\estimatedOutput.js
 */
'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:estimatedOutput');
const estimatedOutputSvc = require('../services/estimatedOutput');
const Parameter = require('parameter');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class estimatedOutput {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  
  /**
    * @api {put} /api/v1.0/estimated/output/audit 编辑测产信息
    * @apiVersion 1.0.0
    * @apiName auditEstimatedOutput
    * @apiGroup estimatedOutput
    * @apiPermission authenticated
    * @apiParam {string} id get方式传参：{ type: 'string', required: true, allowEmpty: false, trim: true }
    * @apiParamExample {json} Request-Example:
      {
        status: { type: 'enum', required: true, values: ['reject', 'pass'] }, // 审核状态
      }
    *
    * @apiSuccessExample Success-Response:
      HTTP/1.1 200 OK
      {
        "create": 1,
        "status": "SUCCESS"
    }
    * 
    */
  async auditEstimatedOutput(req, res) {
    let method = 'auditEstimatedOutput';
    debug(method, '[Enter]');
    try {
      const user = req.user;
      const params = Object.assign({}, req.query, req.body);

      if (!user || !user.userid) {
        throw {
          errorCode: 'E_EO_AUDIT_104',
          httpCode: 401,
          reason: '用户未登录',
        };
      }
        const parameter = new Parameter({ validateRoot: true });
        const rule = {
          id: { type: 'string', required: true, allowEmpty: false, trim: true },
          status: { type: 'enum', required: true, values: ['reject', 'pass'] }, // 审核状态
        };
        const errors = parameter.validate(rule, params);
        if (errors) {
          throw {
            errorCode: 'E_EO_AUDIT_105',
            httpCode: 406,
            reason: 'invalid param',
            detail: JSON.stringify(errors),
          };
        }

      let condition = {status: params.status};
      let opts = {
        id: params.id,
        userid: user.userid,
      };
      let result = await estimatedOutputSvc.auditEstimatedOutput(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
     * @api {get} /api/v1.0/estimated/output/list 获得测产列表
     * @apiVersion 1.0.0
     * @apiName estimatedOutputList
     * @apiGroup estimatedOutput
     * @apiParamExample {json} Request-Example:
         {
            gov_sn: { type: 'string', require: false, allowEmpty: true, trim: true },
            createYear: { type: 'string', require: false, format: /^\d{4}$/ },
            status: { type: 'enum', require: false, values: ['wait', 'reject', 'pass', 'under_review']},
            createTimeStart: { type: 'string', require: false, format: reg },
            createTimeEnd: { type: 'string', require: false, format: reg },
            roleId:  { type: 'string', required: true, allowEmpty: false, trim: true },
            limit:  { type: 'string', required: false },
            skip:  { type: 'string', required: false },
        }
      *
      * @apiSuccessExample Success-Response:
        HTTP/1.1 200 OK
        {
          "result": [{
          "_id": "6102ca00b0a0d190b0ee3304",
          "name": "朱雪松",
          "land": "朱雪松的自定义名字地块444",
          "areOutput": 400,
          "landOutput": 1000,
          "createUser": "李红",
          "createdTime": "2021-07-29 23:32:16",
          "checkStage": 3,
          "status": "reject",
          "statusStr": "未通过"
        }],
        "total": 1
      }
    */
  async estimatedOutputList(req, res) {
    let method = 'estimatedOutputList';
    debug(method, '[Enter]');
    try {
      const user = req.user;

      if (!user || !user.userid) {
        throw {
          errorCode: 'E_EO_AUDIT_104',
          httpCode: 401,
          reason: '用户未登录',
        };
      }
      let params = req.query;
      const parameter = new Parameter({ validateRoot: true });
      var reg = /^((\d{2}(([02468][048])|([13579][26]))[\-\/\s]?((((0?[13578])|(1[02]))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\-\/\s]?((0?[1-9])|([1-2][0-9])))))|(\d{2}(([02468][1235679])|([13579][01345789]))[\-\/\s]?((((0?[13578])|(1[02]))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\-\/\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\-\/\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\s((([0-1][0-9])|(2?[0-3]))\:([0-5]?[0-9])((\s)|(\:([0-5]?[0-9])))))?$/;
      const rule = {
        gov_sn: { type: 'string', require: false, allowEmpty: true, trim: true },
        createYear: { type: 'string', required: false, allowEmpty: true, format: /^\d{4}$/ },
        status: { type: 'enum', required: false, values: ['', 'wait', 'reject', 'pass', 'under_review']},
        createTimeStart: { type: 'string', required: false, allowEmpty: true, format: reg },
        createTimeEnd: { type: 'string', required: false, allowEmpty: true, format: reg },
        roleId:  { type: 'string', required: true, allowEmpty: false, trim: true },
        limit:  { type: 'string', required: false },
        skip:  { type: 'string', required: false },
      };
      const errors = parameter.validate(rule, params);
      if (errors) {
        throw {
          errorCode: 'E_EO_List_106',
          httpCode: 406,
          reason: 'invalid param',
          detail: JSON.stringify(errors),
        };
      }

      let condition = {
        archived: false,
        limit: +params.limit || 10,
        skip: params.skip || 0,
        gov_sn: params.gov_sn,
        createYear: params.createYear,
        status: params.status,
        createTimeStart: params.createTimeStart,
        createTimeEnd: params.createTimeEnd,
        $sort: { createdTime: params.$sort || -1 },
      };
      
      const opts = {
        roleId: params.roleId,
        userid: user.userid,
      };
      let result = await estimatedOutputSvc.listEstimatedOutput(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  
  /**
   * @api {get} /api/v1.0/estimated/output/get 获得单个测产
    * @apiVersion 1.0.0
    * @apiName getEstimatedOutput
    * @apiGroup estimatedOutput
    * @apiPermission authenticated
    * @apiParamExample {json} Request-Example:
      {
        _id:  { type: 'string', required: true, allowEmpty: false, trim: true }
      }
    *
    * @apiSuccessExample Success-Response:
      HTTP/1.1 200 OK
      {
          "_id": "6101458b79496512ec757fb0",
          "name": "朱雪松",
          "idCard": "130629199510190816",
          "gov_sn": "652823502501",
          "is_staff": 1,
          "landType": 2,
          "land": "朱雪松的自定义名字地块333",
          "contractArea": 10,
          "seedArea": 10,
          "realGetArea": 10,
          "plantType": "棉花",
          "areGetCount": 1000,
          "averagebellCount": 20,
          "bellWeight": 100,
          "areOutput": 600,
          "landOutput": null,
          "createUser": "朱雪松",
          "createUserId": "610112ec609a6664940093e1",
          "createYear": "2021",
          "checkStage": 2,
          "status": "wait",
          "createdTime": "2021-07-28 19:54:51",
          "lastModTime": "2021-07-28T11:54:51.767Z",
          "archived": false,
          "__v": 0
      }
    */
  async getEstimatedOutput(req, res) {
    let method = 'getestimatedOutput';
    debug(method, '[Enter]');
    try {
      const user = req.user;

      if (!user || !user.userid) {
        throw {
          errorCode: 'E_EO_AUDIT_104',
          httpCode: 401,
          reason: '用户未登录',
        };
      }
      let params = req.query;
      const parameter = new Parameter({ validateRoot: true });
      const rule = {
        _id:  { type: 'string', required: true, allowEmpty: false, trim: true },
      };
      const errors = parameter.validate(rule, params);
      if (errors) {
        throw {
          errorCode: 'E_EO_get_107',
          httpCode: 406,
          reason: 'invalid param',
          detail: JSON.stringify(errors),
        };
      }

      let condition = {
        _id: params._id,
        archived: false,
      };
      const opts = {};
      let result = await estimatedOutputSvc.getEstimatedOutput(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
    * @api {get} /api/v1.0/estimated/gov/list 获得连队列表
    * @apiVersion 1.0.0
    * @apiName getEstimatedGovList
    * @apiGroup estimatedOutput
    * @apiPermission authenticated
    *
    * @apiSuccessExample Success-Response:
      HTTP/1.1 200 OK
      [{
        "gov_sn": "652823502400",
        "typeCode": "123",
        "name": "团部"
    }, {
        "gov_sn": "652823502501",
        "typeCode": "220",
        "name": "一连"
    }, {
        "gov_sn": "652823502502",
        "typeCode": "123",
        "name": "二连"
    }, {
        "gov_sn": "652823502503",
        "typeCode": "220",
        "name": "三连"
    }, {
        "gov_sn": "652823502504",
        "typeCode": "220",
        "name": "五连"
    }, {
        "gov_sn": "652823502505",
        "typeCode": "220",
        "name": "六连"
    }, {
        "gov_sn": "652823502506",
        "typeCode": "220",
        "name": "八连"
    }, {
        "gov_sn": "652823502507",
        "typeCode": "220",
        "name": "九连"
    }, {
        "gov_sn": "652823502508",
        "typeCode": "220",
        "name": "十连"
    }, {
        "gov_sn": "652823502509",
        "typeCode": "220",
        "name": "十一连"
    }, {
        "gov_sn": "652823502510",
        "typeCode": "220",
        "name": "农一队"
    }, {
        "gov_sn": "652823502511",
        "typeCode": "220",
        "name": "农三队"
    }, {
        "gov_sn": "652823502512",
        "typeCode": "220",
        "name": "十二连"
    }, {
        "gov_sn": "652823502513",
        "typeCode": "220",
        "name": "林一连"
    }, {
        "gov_sn": "652823502516",
        "typeCode": "220",
        "name": "二社区"
    }, {
        "gov_sn": "652823502517",
        "typeCode": "220",
        "name": "一十六连"
    }, {
        "gov_sn": "652823502518",
        "typeCode": "220",
        "name": "四连"
    }, {
        "gov_sn": "652823502519",
        "typeCode": "220",
        "name": "十五连"
    }, {
        "gov_sn": "652823502520",
        "typeCode": "220",
        "name": "七连"
    }, {
        "gov_sn": "652823502521",
        "typeCode": "220",
        "name": "一十八连"
    }, {
        "gov_sn": "652823502522",
        "typeCode": "220",
        "name": "一十九连"
    }, {
        "gov_sn": "652823502523",
        "typeCode": "220",
        "name": "二十连"
    }, {
        "gov_sn": "652823502524",
        "typeCode": "220",
        "name": "一十七连"
    }, {
        "gov_sn": "652823502525",
        "typeCode": "220",
        "name": "蛭石矿生活区"
    }]
    */
  async getGovsList(req, res) {
    let method = 'getEstimatedGovList';
    debug(method, '[Enter]');
    try {
      const user = req.user;

      if (!user || !user.userid) {
        throw {
          errorCode: 'E_EO_UPDATE_104',
          httpCode: 401,
          reason: '用户未登录',
        };
      }
      let params = req.query;
      const parameter = new Parameter({ validateRoot: true });
      const rule = {
        roleId:  { type: 'string', required: true, allowEmpty: false, trim: true },
      };
      const errors = parameter.validate(rule, params);
      if (errors) {
        throw {
          errorCode: 'E_EO_gov_108',
          httpCode: 406,
          reason: 'invalid param',
          detail: JSON.stringify(errors),
        };
      }
      let condition = {
        roleId: params.roleId,
      };
      const opts = {
        userid: user.userid,
      };
      let result = await estimatedOutputSvc.getEstimatedGovList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  init() {
    let self = this;
    self._policyRouter.put('/api/v1.0/estimated/output/audit', self.auditEstimatedOutput.bind(self), {
      name: 'estimatedOutput.auditEstimatedOutput',
      role: PERMISSION_ROLE.EVERYONE,
    });
    self._policyRouter.get('/api/v1.0/estimated/output/list', self.estimatedOutputList.bind(self), {
      name: 'estimatedOutput.estimatedOutputList',
      role: PERMISSION_ROLE.EVERYONE,
    });
    self._policyRouter.get('/api/v1.0/estimated/output/get', self.getEstimatedOutput.bind(self), {
     name: 'estimatedOutput.estimatedOutputList',
     role: PERMISSION_ROLE.EVERYONE,
    });
    self._policyRouter.get('/api/v1.0/estimated/gov/list', self.getGovsList.bind(self), {
      name: 'estimatedOutput.estimatedGovList',
      role: PERMISSION_ROLE.EVERYONE,
    });
    return self;
  }
}
module.exports = estimatedOutput;

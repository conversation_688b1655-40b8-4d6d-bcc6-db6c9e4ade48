/*
 * @Author: ysd
 * @Last Modified by: ysd
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:system:rog');
const systemOrgService = require('../services/system_org');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const { timeout } = require('q');

class OutLand {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }


  async list(req, res) {
    const method = 'list';
    debug(method, '[Enter]');
    try {
      let tId = req.Client && req.Client.tId;
      if (!tId) {
        throw {
          errorCode: 'E_LIST_108',
          httpCode: 406,
          reason: 'clientId not find'
        }
      }
      let condition = { tId: tId, archived: false, limit: "unlimited" };
      let codeAddCondition = [];
      if (req.query.code) {
        codeAddCondition.push({ code: `/^${req.query.code}.*/` });
      }
      if (req.query.level) {
        let level = parseInt(req.query.level)
        codeAddCondition.push({ code: `/^(\\d{3}){${level}}$/` })
      }
      condition['$and'] = codeAddCondition;
      const opts = {uId:req.user && req.user.userid || '',roleId: req.query.roleId || ''};
      const result = await systemOrgService.list(condition,opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async create(req, res) {
    const method = 'create';
    debug(method, '[Enter]');
    try {
      let tId = req.Client && req.Client.tId;
      if (!tId) {
        throw {
          errorCode: 'E_CREATE_108',
          httpCode: 406,
          reason: 'clientId not find'
        }
      }
      if (!req.body.name) {
        throw {
          errorCode: 'E_CREATE_108',
          httpCode: 406,
          reason: 'miss name'
        }
      }
      let condition = { tId: tId, name: req.body.name };
      const result = await systemOrgService.create(condition);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async archive(req, res) {
    const method = 'archive';
    debug(method, '[Enter]');
    try {
      let tId = req.Client && req.Client.tId;
      if (!tId) {
        throw {
          errorCode: 'E_CREATE_108',
          httpCode: 406,
          reason: 'clientId not find'
        }
      }
      if (!req.body.id) {
        throw {
          errorCode: 'E_CREATE_108',
          httpCode: 406,
          reason: 'miss id'
        }
      }
      let condition = { id: req.body.id };
      const result = await systemOrgService.archive(condition);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  init() {

    this._policyRouter.get('/api/v1.0/system/org/list', this.list.bind(this), {
      name: 'systemOrg.list',
      // role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.post('/api/v1.0/system/org/create', this.create.bind(this), {
      name: 'systemOrg.list',
      // role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.post('/api/v1.0/system/org/archive', this.archive.bind(this), {
      name: 'systemOrg.list',
      // role: PERMISSION_ROLE.EVERYONE
    })


    return this
  }

}

module.exports = OutLand;
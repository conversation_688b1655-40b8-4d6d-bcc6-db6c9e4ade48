/** 
 * newsRouter 
 * <AUTHOR>
 * 2019-10-18
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:news');
const moment = require('moment');
const landSuperVisory = require('../services/land_supervisory');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async createOrEdit(req, res) {
    const method = 'createOrEdit';
    debug(method, '[Enter]');
    try {
      const result = await landSuperVisory.createOrEdit(req.body, {uId: req.user.userid});

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getLandSupervisoryList(req, res) {
    const method = 'getLandSupervisoryList'
    debug(method, '[Enter]')
    try {

      const query = Object.entries(req.query).filter(([k,v])=>v)
          .reduce((res,v)=>(res[v[0]]= v[1],res),{}),
          opts = {tId:req.Client.tId,uId:req.user && req.user.userid,roleId:req.query.roleId};
      delete query.roleId;
      const condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        // tId:req.Client.tId,
        archived: false,
        $sort: query.$sort && JSON.parse(query.$sort) || { createdTime: -1 }
      };
      opts.getLandData = query.getLandData && true || false;
      delete query.getLandData;
      delete query.$sort;
      Object.assign(condition, query);
      if (condition.areaCode) {
        condition.areaCode = `/^${condition.areaCode}/`;
      }
      if (condition.startTime) {
        let startTime = moment(condition.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (condition.endTime) {
        let endTime = moment(condition.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      condition.areaCode && (condition.areaCode = `/^${condition.areaCode}/`);
      condition.landAreaCode && (condition.landAreaCode = `/^${condition.landAreaCode}/`);
      condition.orderAreaCode && (condition.orderAreaCode = `/^${condition.orderAreaCode}/`);
      delete condition.startTime;
      delete condition.endTime;
      delete condition.name;
      query.username = query.username || query.name ;// 前端写错了参数，要求后端兼容
      query.username && ( condition.username = { '$regex': `.*${query.username}.*`, '$options': 'si' } );
      query.requestName && ( condition.requestName = { '$regex': `.*${query.requestName}.*`, '$options': 'si' } );
      query.requestType && ( condition.requestType = query.requestType );
      query.operatorName && ( condition.operatorName = { '$regex': `.*${query.operatorName}.*`, '$options': 'si' } );
      query.hadClosed && ( condition.hadClosed = parseInt(query.hadClosed) || 0 );
      opts.roleId = req.query.roleId || req.headers['x-role-id'];

      // debug('debug233 ',query.name,JSON.stringify(condition))
      const result = await landSuperVisory.getLandSupervisoryList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getLandSupervisoryDetail(req, res) {
    const method = 'getLandSupervisoryDetail'
    debug(method, '[Enter]')
    try {

      const condition = {
        id: req.query.id
      };

      const result = await landSuperVisory.getLandSupervisoryDetail(condition, {});
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  async getLandListByApplicationHandler(req, res) {
    const method = 'getLandListByApplicationHandler'
    debug(method, '[Enter]')
    try {

      const query = Object.entries(req.query).filter(([k,v])=>v)
          .reduce((res,v)=>(res[v[0]]= v[1],res),{}),
          opts = {tId:req.Client.tId,uId:req.user && req.user.userid,roleId:req.query.roleId};
      delete query.roleId;
      if ( !query.status ) {
        throw {
          errorCode: 'E_SUPERVISORY_001',
          httpCode: 406,
          reason: 'status cant all be empty'
        }
      }

      const condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        tId:req.Client.tId,
        archived: false,
        $sort: query.$sort && JSON.parse(query.$sort) || { createdTime: -1 }
      };
      Object.assign(condition, query);
      if (condition.startTime) {
        const startTime = moment(condition.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (condition.endTime) {
        const endTime = moment(condition.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      condition.area && (condition.area = `/^${condition.area}/`);
      delete condition.startTime;
      delete condition.endTime;

      const result = await landSuperVisory.getLandListByApplicationHandler(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async statistic(req, res) {
    let method = 'statistic'
    debug(method, '[Enter]')
    try {

      let condition = req.query;
      condition.name && ( condition.username = condition.name );
      delete condition.name;
      let opts = {roleId:req.headers && req.headers['x-role-id']};
      let result = await landSuperVisory.statistic(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {

    this._policyRouter.post('/api/v1.0/land/supervisory', this.createOrEdit.bind(this), {
      name: 'land_supervisory.createOrEdit',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/land/supervisory', this.getLandSupervisoryList.bind(this), {
      name: 'land_supervisory.getLandSupervisoryList',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/land/supervisory/detail', this.getLandSupervisoryDetail.bind(this), {
      name: 'land_supervisory.getLandSupervisoryDetail',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/land/list/by/application', this.getLandListByApplicationHandler.bind(this), {
      name: 'land_supervisory.getLandListByApplicationHandler',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/land/supervisory/record/statistic', this.statistic.bind(this), {
      name: 'land_supervisory.statistic',
      role: PERMISSION_ROLE.EVERYONE
    });


    return this;
  }
}

module.exports = Router;
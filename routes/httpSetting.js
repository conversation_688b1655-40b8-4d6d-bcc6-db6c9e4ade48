/**
 * httpSetting router
 * <AUTHOR>
 */

'use strict'

const bodyParser = require('body-parser')
const compression = require('compression')
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class HttpSetting {
  constructor(policyRouter) {
    this._policyRouter = policyRouter
  }

  init() {
    let self = this

    self._policyRouter.use('/', bodyParser.json(), {
      name: 'httpSetting.bodyParser.json',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.use('/', bodyParser.urlencoded({
      extended: false
    }), {
      name: 'httpSetting.bodyParser.urlencoded',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.use('/', compression({
      filter: function(req, res) {
        if (req.headers['x-no-compression']) {
          // don't compress responses with this request header
          return false;
        }
        // fallback to standard filter function
        return compression.filter(req, res);
      }
    }), {
      name: 'httpSetting.compression',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = HttpSetting
'use strict'

const logFactory = require('../utils/logFactory')
const logUtil = require('../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:capital3')
const capital3Svc = require('../services/capital3')
const capitai3Request = require('../services/capital3/capitai3Request')
class Ai {
  constructor(policyRouter) {
    this._policyRouter = policyRouter
  }
  // --------------------  四川省合作社  --------------------
  /** 成员产品交易表 */
  async getMemberProductTradeForm(req, res) {
    let method = 'getMemberProductTradeForm'
    debug(method, '[Enter]')
    try {
      const { jjzzCode, searchValue, skip, limit } = req.query || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_001',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!jjzzCode) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_101',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        jjzzCode,
        searchValue,
        skip,
        limit,
      }
      const result = await capital3Svc.getMemberProductTradeForm(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 成员账户统计表 */
  async getMemberAccoutStatisticsForm(req, res) {
    let method = 'getMemberAccoutStatisticsForm'
    debug(method, '[Enter]')
    try {
      const { year, jjzzCode, searchValue, skip, limit } = req.query || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_002',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!jjzzCode) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_102',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        year,
        jjzzCode,
        searchValue,
        skip,
        limit,
      }
      const result = await capital3Svc.getMemberAccoutStatisticsForm(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 收支明细表 */
  async getMoneyDetailForm(req, res) {
    let method = 'getMoneyDetailForm'
    debug(method, '[Enter]')
    try {
      const { issue, jjzzCode } = req.query || {}
      const {} = req.query || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_003',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!jjzzCode) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_103',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        issue,
        jjzzCode,
      }
      const result = await capital3Svc.getMoneyDetailForm(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 查询token的用户拥有的经济组织列表 */
  async getTokenUserEconomicOrganizationList(req, res) {
    let method = 'getTokenUserEconomicOrganizationList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_004',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
      }
      const result = await capital3Svc.getTokenUserEconomicOrganizationList(
        body,
        {}
      )
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      console.log(`199 - ${500 || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 资产负债表 */
  async getAssertsLiabilitiesForm(req, res) {
    let method = 'getAssertsLiabilitiesForm'
    debug(method, '[Enter]')
    try {
      const { issue, jjzzCode } = req.query || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_005',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!jjzzCode) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_105',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        issue,
        jjzzCode,
      }
      const result = await capital3Svc.getAssertsLiabilitiesForm(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // --------------------  通河｜依兰 银农直连  --------------------
  /** 查询token拥有的账号 */
  async getAccountsListWithToken(req, res) {
    let method = 'getAccountsListWithToken'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_006',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
      }
      const result = await capital3Svc.getAccountsListWithToken(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 审批列表 */
  async getAuditList(req, res) {
    let method = 'getAuditList'
    debug(method, '[Enter]')
    try {
      const { status, startDate, endDate, skip, limit, account } =
        req.body || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_007',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!account) {
        throw {
          errorCode: 'E_CAPITAL3_param_106',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        status,
        startDate,
        endDate,
        skip,
        limit,
        account,
      }
      const result = await capital3Svc.getAuditList(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 审批详情 */
  async getAuditDetail(req, res) {
    let method = 'getAuditDetail'
    debug(method, '[Enter]')
    try {
      const { refundId, account } = req.body || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_008',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!account || !refundId) {
        throw {
          errorCode: 'E_CAPITAL3_param_107',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        refundId,
        account,
      }
      const result = await capital3Svc.getAuditDetail(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 查询审批的附件信息 */
  async getAssertAttachments(req, res) {
    let method = 'getAssertAttachments'
    debug(method, '[Enter]')
    try {
      const { streamCode, account } = req.body || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_009',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!account || !streamCode) {
        throw {
          errorCode: 'E_CAPITAL3_param_108',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        streamCode,
        account,
      }
      const result = await capital3Svc.getAssertAttachments(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 获取审批流程信息 */
  async getAuditFlowing(req, res) {
    let method = 'getAuditFlowing'
    debug(method, '[Enter]')
    try {
      const { streamCode, account } = req.body || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_010',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!account || !streamCode) {
        throw {
          errorCode: 'E_CAPITAL3_param_109',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        streamCode,
        account,
      }
      const result = await capital3Svc.getAuditFlowing(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 获取文件流 */
  async getFilestream(req, res) {
    let method = 'getFilestream'
    debug(method, '[Enter]')
    try {
      const { attachId } = req.body || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_011',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!attachId) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_105',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        attachId,
        res,
      }
      const result = await capital3Svc.getFilestream(body, {})
      // debug(method, '[Exit](success)', result)
      // res.set('Content-type', 'application/octet-stream')
      // result.pipe(res)
      res.status(200).send();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 拒绝审批 */
  async modifyAuditReject(req, res) {
    let method = 'modifyAuditReject'
    debug(method, '[Enter]')
    try {
      const { streamCode, opinion, account } = req.body || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_012',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!account || !streamCode || !opinion) {
        throw {
          errorCode: 'E_CAPITAL3_param_110',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        streamCode,
        opinion,
        account,
      }
      const result = await capital3Svc.modifyAuditReject(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 通过审批 */
  async modifyAuditPass(req, res) {
    let method = 'modifyAuditPass'
    debug(method, '[Enter]')
    try {
      const { streamCode, opinion, account } = req.body || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_013',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!account || !streamCode || !opinion) {
        throw {
          errorCode: 'E_CAPITAL3_param_111',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        streamCode,
        opinion,
        account,
      }
      const result = await capital3Svc.modifyAuditPass(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 成员列表 */
  async getMemberList(req, res) {
    let method = 'getMemberList'
    debug(method, '[Enter]')
    try {
      const { jjzzCode, searchValue, skip, limit } = req.query || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_013',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!jjzzCode) {
        throw {
          errorCode: 'E_CAPITAL3_param_111',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        jjzzCode,
        searchValue,
        skip,
        limit
      }
      const result = await capital3Svc.getMemberList(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /** 成员账户列表 */
  async getMemberAccountList(req, res) {
    let method = 'getMemberAccountList'
    debug(method, '[Enter]')
    try {
      const { jjzzCode, year, memberCode } = req.query || {}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CAPITAL3_ROUTER_013',
          httpCode: 401,
          reason: '用户未登录',
        }
      }
      if (!jjzzCode || !year || !memberCode) {
        throw {
          errorCode: 'E_CAPITAL3_param_111',
          httpCode: 406,
          reason: 'Invalid Param',
        }
      }
      const body = {
        token: req.user.token,
        client: req.user.client,
        jjzzCode,
        year,
        memberCode,
      }
      const result = await capital3Svc.getMemberAccountList(body, {})
      debug(method, '[Exit](success)', result)
      res.status(200).send(result)
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    // --------------------  四川省合作社  --------------------
    this._policyRouter.get(
      '/api/v1.0/capital3/get/product/trade',
      this.getMemberProductTradeForm.bind(this),
      {
        name: 'capital3.getMemberProductTradeForm',
      }
    )

    this._policyRouter.get(
      '/api/v1.0/capital3/get/account/statistics',
      this.getMemberAccoutStatisticsForm.bind(this),
      {
        name: 'capital3.getMemberAccoutStatisticsForm',
      }
    )

    this._policyRouter.get(
      '/api/v1.0/capital3/get/money/detail',
      this.getMoneyDetailForm.bind(this),
      {
        name: 'capital3.getMoneyDetailForm',
      }
    )

    this._policyRouter.get(
      '/api/v1.0/capital3/list/user/economic/organization',
      this.getTokenUserEconomicOrganizationList.bind(this),
      {
        name: 'capital3.getTokenUserEconomicOrganizationList',
      }
    )

    this._policyRouter.get(
      '/api/v1.0/capital3/get/asserts/liabilities',
      this.getAssertsLiabilitiesForm.bind(this),
      {
        name: 'capital3.getAssertsLiabilitiesForm',
      }
    )

    // --------------------  通河｜依兰 银农直连  --------------------
    this._policyRouter.post(
      '/api/v1.0/capital3/get/token/accounts',
      this.getAccountsListWithToken.bind(this),
      {
        name: 'capital3.getAccountsListWithToken',
      }
    )

    this._policyRouter.post(
      '/api/v1.0/capital3/get/audit/list',
      this.getAuditList.bind(this),
      {
        name: 'capital3.getAuditList',
      }
    )

    this._policyRouter.post(
      '/api/v1.0/capital3/get/audit/detail',
      this.getAuditDetail.bind(this),
      {
        name: 'capital3.getAuditDetail',
      }
    )

    this._policyRouter.post(
      '/api/v1.0/capital3/get/assert/attachments',
      this.getAssertAttachments.bind(this),
      {
        name: 'capital3.getAssertAttachments',
      }
    )

    this._policyRouter.post(
      '/api/v1.0/capital3/get/audit/flowing',
      this.getAuditFlowing.bind(this),
      {
        name: 'capital3.getAuditFlowing',
      }
    )

    this._policyRouter.post(
      '/api/v1.0/capital3/get/file/stream',
      this.getFilestream.bind(this),
      {
        name: 'capital3.getFilestream',
      }
    )

    this._policyRouter.post(
      '/api/v1.0/capital3/get/audit/reject',
      this.modifyAuditReject.bind(this),
      {
        name: 'capital3.modifyAuditReject',
      }
    )

    this._policyRouter.post(
      '/api/v1.0/capital3/get/audit/pass',
      this.modifyAuditPass.bind(this),
      {
        name: 'capital3.modifyAuditPass',
      }
    )

    this._policyRouter.get(
      '/api/v1.0/capital3/get/member/list',
      this.getMemberList.bind(this),
      {
        name: 'capital3.getMemberList',
      }
    )

    this._policyRouter.get(
      '/api/v1.0/capital3/get/member/account/list',
      this.getMemberAccountList.bind(this),
      {
        name: 'capital3.getMemberAccountList',
      }
    )

    return this
  }
}

module.exports = Ai

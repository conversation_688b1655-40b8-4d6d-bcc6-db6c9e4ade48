'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:routes:commission');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const commissionSvc = require('../services/commission')
const moment = require('moment');

class Commission {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  // 业务佣金详情（已完成，差部分信息）
  async detailCommissionEmployee(req, res) {
    let method = 'detailCommissionEmployee'
    debug(method, '[Enter]')
    try {
      // 默认给一个
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_Detail_COMMISSION_106',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      const condition = {userid: req.user.userid}

      let opts = {};
      let result = await commissionSvc.detailCommissionEmployee(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 用于测试，提现用的
  async createCommissionWithdraw(req, res) {
    let method = 'createCommissionWithdraw'
    debug(method, '[Enter]')
    try {
      const body = req.body || {};
      // 默认给一个
      // req.user = { userid: '632702c809063e5256b813b6' } // 王起
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CREATE_COMMISSION_WITHDRAW_106',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      
      const { withdrawPrice } = body
      // if(!employeeId) {
      //   throw {
      //     errorCode: 'E_CREATE_COMMISSION_WITHDRAW_107',
      //     httpCode: 406,
      //     reason: 'invalid params'
      //   }
      // }

      const condition = { employeeId: req.user.userid }
      
      if(withdrawPrice) condition.withdrawPrice = withdrawPrice

      let opts = {};
      let result = await commissionSvc.createCommissionWithdraw(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 业务佣金列表
  async listCommissionEmployee(req, res) {
    let method = 'listCommissionEmployee'
    debug(method, '[Enter]')
    try {
      const {commissionType, skip = 0, limit = 10} = req.query || {}

      // 默认给一个
      // req.user = { userid: '632702c809063e5256b813b6' } // 王起
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_Detail_COMMISSION_106',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      const nowDate = moment().add(1, 'days').toDate();
      const lastYearDate = moment().subtract(1, 'years').toDate();
      const condition = {
        employeeId: req.user.userid,
        createdTime: { $gte: lastYearDate, $lte: nowDate },
        $sort: { createdTime: -1 },
        archived: false
      }
      if(commissionType) condition.commissionType = commissionType
      if(skip) condition.skip = skip
      if(limit) condition.limit = limit

      let opts = {};
      let result = await commissionSvc.listCommissionEmployee(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this;

    this._policyRouter.get('/api/v1.0/commission/employee/detail', this.detailCommissionEmployee.bind(this), {
      name: 'commission.detailCommissionEmployee',
      // role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.post('/api/v1.0/commission/withdraw/create', this.createCommissionWithdraw.bind(this), {
      name: 'commission.createCommissionWithdraw',
      // role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.get('/api/v1.0/commission/employee/list', this.listCommissionEmployee.bind(this), {
      name: 'commission.listCommissionEmployee',
      // role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}
module.exports = Commission;
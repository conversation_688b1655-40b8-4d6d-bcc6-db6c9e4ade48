'use strict'

/**
 * This is a server mode restricter, if the api is restricted to
 * run under specific server mode, this validator will ensure the behavior
 *
 * <AUTHOR>
 */

const PolicyHandler = require('nongfu.merchant.policyrouter').PolicyHandler;
const debug                 = require('debug')('rongxin:loan:policies:servermode');
const env                   = require('../../services/env');

class ServerModeRestricter extends PolicyHandler {
  constructor() {
    super();
  }

  preInvoke(context, cont) {
    if (! context.bindings || ! context.bindings.security ||
        ! context.bindings.security.serverMode) {
      return cont();
    }

    debug(this.getName() + ' Middleware "' + context.bindings.name + '" is enabled server mode restriction "' + context.bindings.security.serverMode + '"');
    if ('devOnly' === context.bindings.security.serverMode && 'development' !== env.getServerMode()) {

      let err = {
        errorCode: 'E_SECENVRESTR_0001',  
        reason: 'Not allowed server mode',
        httpStatus: 401
      };
      context.res.status(401).json(err);
      return cont(err);
    }
    
		if ('benchmarkOnly' === context.bindings.security.serverMode && 'benchmark' !== env.getServerMode()) {

      let err = {
        errorCode: 'E_SECENVRESTR_0001',  
        reason: 'Not allowed server mode',
        httpStatus: 401
      };
      context.res.status(401).json(err);
      return cont(err);
    }

    cont();
  }

  postInvoke(context, cont) {
    cont();
  }

  getName() {
    return 'ServerModeRestricter';
  }
}

module.exports = exports = ServerModeRestricter;

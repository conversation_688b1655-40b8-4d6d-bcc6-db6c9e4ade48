/**
* validate url pattern
* <AUTHOR>
*/

'use strict'

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:policies:urlPatternValidator')
const PolicyHandler = require('nongfu.merchant.policyrouter').PolicyHandler;

const URL_STANDARD_PATTERN = '/api/'

class Handler extends PolicyHandler {
  constructor() {
    super()
  }

  getName() {
    return 'urlPatternValidator'
  }

  begin(context, conti) {
    let self = this
    let method = `${self.getName()}.begin`
    debug(method, '[Enter]')

    try {
      if (!context || !context.req)
        throw {
          errorCode: 'EURLP031',
          httpCode: 500,
          reason: 'invalid req instance'
        }

      let orignUrl = context.req.originalUrl
      if (!orignUrl || orignUrl.indexOf(URL_STANDARD_PATTERN) !== 0)
        throw {
          errorCode: 'EURLP041',
          httpCode: 400,
          reason: `invalid req url: ${orignUrl || ''}`
        }

      debug(method, '[Exit](success)')
      return conti()
    } catch (error) {
      debug.error(method, '[Exit](error)', error)
      context.res.status(error.httpCode || 500).send(error)
    }
  }

  end(context, conti) {
    return conti()
  }
}

module.exports = Handler

"use strict";

/**
 * This is a adapter class with intention to adopt the legacy visitor login 
 * the new policy router framework. The adapter will translate/delegate the
 * request to the legacy facility to keep consist behavior after move to 
 * the new policy router framework.
 *
 * <AUTHOR>
 *
 */

const Q = require('q');

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const logger = logFactory(logUtil())('rongxin:loan.mgr.app.api:polices:humanclick');
const PolicyHandler = require('nongfu.merchant.policyrouter').PolicyHandler;
const dataStore = require('../../persistence/dataStore');

const DEFAULT_KEY = 'DEFAULT_KEY';
const REDIS_PREFIX = 'humanclickvalidator:';

class HumanClickHandler extends PolicyHandler {
  constructor() {
    super();
  }

  getSession(req, sidStrategy) {

    let userId = null;

    if (sidStrategy && sidStrategy.sidFromBodyItem) {
      userId = req.body && req.body[sidStrategy.sidFromBodyItem];
    }

    if (sidStrategy && sidStrategy.sidFromQuery) {
      userId = req.query && req.query[sidStrategy.sidFromQuery];
      if (!userId) {
        userId = DEFAULT_KEY;
      }
    }

    if (sidStrategy && sidStrategy.sidFromUserItem) {
      userId = req.user && req.user[sidStrategy.sidFromUserItem];
    }

    if (sidStrategy && sidStrategy.prefix)
      userId = sidStrategy.prefix + ':' + userId;

    userId = REDIS_PREFIX + userId;
    logger.debug('humanclick validation redis id: ', userId);
    return dataStore.getUserSession({
      userId: userId
    }).then(function (session) {
      logger.debug('Use session per user indeed');
      if (!session || session === '') {
        session = {};
      }

      return session;
    });
  }

  saveSession(req, session, forceSave, sidStrategy) {
    if (!session) {
      session = {};
    }

    let userId = null;
    if (sidStrategy && sidStrategy.sidFromBodyItem) {
      userId = req.body && req.body[sidStrategy.sidFromBodyItem];
    }

    if (sidStrategy && sidStrategy.sidFromQuery) {
      userId = req.query && req.query[sidStrategy.sidFromQuery];
      if (!userId) {
        userId = DEFAULT_KEY;
      }
    }

    if (sidStrategy && sidStrategy.sidFromUserItem) {
      userId = req.user && req.user[sidStrategy.sidFromUserItem];
    }

    if (sidStrategy && sidStrategy.prefix)
      userId = sidStrategy.prefix + ':' + userId;

    // login user, use user specific session of redis
    userId = REDIS_PREFIX + userId;
    logger.debug('humanclick validation, save session to userid: ', session, userId);
    return dataStore.setUserSession(session, {
      userId: userId
    });
  }

  /**
   * HumanClickValidateHook.preinvoke, 
   * 
   */
  preInvoke(context, cont) {
    let method = 'preInvoke';

    let self = this;
    let req = context.req;

    if (!context.bindings.humanClick || !context.bindings.humanClick.enabled) {
      logger.debug(method, '[Exit]');
      return cont();
    }

    let session = null;
    let sidStrategy = context.bindings.humanClick.sidStrategy;
    let timestamp = new Date();
    timestamp = timestamp.getTime();
    logger.debug(method, '[req.body] -> ' + JSON.stringify(req.body), ' [req.query] ->' + JSON.stringify(req.query));
    self.getSession(req, sidStrategy).then(function (_session) {
      session = _session;

      let scopeKey = context.bindings.name;

      if (context.bindings.humanClick.scope && context.bindings.humanClick.scope.perQuery) {
        scopeKey = scopeKey + '-' + context.req.query[context.bindings.humanClick.scope.perQuery];
      }

      if (context.bindings.humanClick.scope && context.bindings.humanClick.scope.perParam) {
        scopeKey = scopeKey + '-' + context.req.params[context.bindings.humanClick.scope.perParam];
      }

      if (context.bindings.humanClick.scope && context.bindings.humanClick.scope.perBodyItem) {
        if (context.req.body && context.req.body[context.bindings.humanClick.scope.perBodyItem]) {
          scopeKey = scopeKey + '-' + context.req.body[context.bindings.humanClick.scope.perBodyItem];
        }
      }

      if (context.bindings.humanClick.scope && context.bindings.humanClick.scope.perUserItem) {
        if (context.req && req.user && context.req.user[context.bindings.humanClick.scope.perUserItem]) {
          scopeKey = scopeKey + '-' + context.req.user[context.bindings.humanClick.scope.perUserItem];
        }
      }

      if (!session[scopeKey]) {
        session[scopeKey] = {};
      }

      logger.debug('lastInvocationtimestamp: ', session[scopeKey]);
      let lastInvocationTimestamp = session[scopeKey].lastInvocationTimestamp;
      if (!lastInvocationTimestamp) {
        session[scopeKey].lastInvocationTimestamp = timestamp;
        return;
      }

      let interval = timestamp - lastInvocationTimestamp;
      logger.debug('Interval: ', context.bindings.humanClick.interval);
      session[scopeKey].lastInvocationTimestamp = timestamp;
      if (interval < context.bindings.humanClick.interval) {
        logger.error('Invalid two invocation interval: ', interval, session.user);
        let error = {
          errorCode: 429,
          reason: '您点的太频繁啦，请休息片刻再试。'
        };
        throw error;
      }
    }).then(function () {
      return self.saveSession(context.req, session, false, sidStrategy);
    }).then(function () {
      return cont();
    }).fail(function (error) {
      logger.error('Error: ', error);
      let errorCode = error.errorCode;
      if (!errorCode) {
        errorCode = 500;
      }
      if (errorCode === 429)
        context.res.set('Warning', `199 - E_HUMANCLICK_160 ${new Date()}`);
      context.res.status(errorCode).json(error);
      session[scopeKey].lastInvocationTimestamp = timestamp;
      context.fault = error;
      return self.saveSession(context.req, session, false, sidStrategy).finally(function () {
        return cont();
      });
    });
  }

  postInvoke(context, cont) {
    let method = 'postInvoke';

    let self = this;
    let req = context.req;

    try {
      logger.debug('Response statuscode: ', context.res.statusCode);
      if (!context.bindings.humanClick || !context.bindings.humanClick.enabled) {
        logger.debug(method, '[Exit]');
        return cont();
      }
      let session = null;
      let sidStrategy = context.bindings.humanClick.sidStrategy;
      self.getSession(req, sidStrategy).then(function (_session) {
        session = _session;
        if (context.bindings.humanClick &&
          context.bindings.humanClick.resetIfFailed &&
          context.res &&
          context.res.statusCode &&
          context.res.statusCode > 299 &&
          context.res.statusCode !== 429) {
          logger.debug('Previous stored last time invocation timestamp: ', session[context.bindings.name]);
          delete session[context.bindings.name];
          logger.debug('Reset last time invocation timestamp: ', session[context.bindings.name]);
          return self.saveSession(context.req, session, true, sidStrategy);
        }
      }).then(function () {
        cont();
      }).fail(function (error) {
        logger.error('Error occurs due to: ', error);
        cont();
      });
    } catch (error) {
      logger.error('Error occurs due to: ', error);
      cont(error);
    }
  }

  getName() {
    return 'HumanClickHandler';
  }
}

module.exports = exports = HumanClickHandler;
/**
 * authorization policy handler
 * <AUTHOR>
 */

const authorSvc = require('../../services/auth')
const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:policies:auth')
const PolicyHandler = require('nongfu.merchant.policyrouter').PolicyHandler;
const getUserFromReqCtx = require('../../utils/getAuthFromReq').getUserFromReqCtx

class Handler extends PolicyHandler {
  constructor() {
    super()
  }

  getName() {
    return 'Auth'
  }

  preInvoke(context, conti) {
    let self = this
    let method = `${self.getName()}.preInvoke`
    debug(method, '[Enter]')

    try {
      if (getUserFromReqCtx(context.req)) {
        debug(method, '[Exit](success)')
        return conti()
      }

      authorSvc.auth(context.req, context.res).then(() => {
        debug(method, '[Exit](success)')
        return conti()
      }).catch(error => {
        debug.error(method, '[Exit](failed)', error)
        context.res.status(error.httpCode || 500).send(error)
        return conti(error)
      })
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      context.res.status(error.httpCode || 500).send(error)
      return conti(error)
    }
  }

  postInvoke(context, conti) {
    return conti()
  }
}

module.exports = Handler

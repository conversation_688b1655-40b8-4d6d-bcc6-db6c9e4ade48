/*
 * @Author: wcy  
 * @Date: 2019-02-14 14:39:42 
 * @Last Modified by: wcy
 * @Last Modified time: 2019-02-22 10:42:58
 * @Desc 为用户进行role info初始化
 */


const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:policies:userRoleAuth');
const PolicyHandler = require('nongfu.merchant.policyrouter').PolicyHandler;
const dataUtil = require('../../services/dataSvc/dataUtil');
const employeeData = dataUtil.employees;
const PERMISSION_ROLE = require('../../services/permission').PERMISSION_ROLE;

class Handler extends PolicyHandler {
  constructor() {
    super();
  }

  getName() {
    return 'Permission';
  }

  async preInvoke(context, conti) {
    let self = this;
    let method = `${self.getName()}.preInvoke`;
    debug(method, '[Enter]');

    try {

      let assert_role = context.bindings.role || PERMISSION_ROLE.AUTHENTICATED_USER;

      if (assert_role === PERMISSION_ROLE.EVERYONE) {
        debug(method, '[Exit](success)');
        return conti();
      }

      let req = context.req;

      let _profile = req.user;

      if (!_profile || !_profile.userid) {
        throw {
          errorCode: 'EUSERROLEAUTHPRE047',
          httpCode: 401,
          reason: `Invalid Employee Info`
        }
      }

      let condition = {
        id: _profile.userid
      };

      let employeeInfo = await employeeData.getByUrl('/v1.0/employee/group/list', condition);

      if (!employeeInfo || !employeeInfo._id) {
        throw {
          errorCode: 'EUSERROLEAUTHPRE058',
          httpCode: 401,
          reason: `Invalid Employee Info`
        }
      }
      _profile.roles = employeeInfo.groups || [];
      delete employeeInfo.groups;

      context.req.user = { ..._profile, ...employeeInfo };
      debug(method, '[Exit](success)', req.user);
      return conti();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      context.res.status(error.httpCode || 500).send(error)
      return conti(error)
    }
  }

  postInvoke(context, conti) {
    return conti()
  }
}

module.exports = Handler
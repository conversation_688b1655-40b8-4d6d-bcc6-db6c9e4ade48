/**
 * permission verify handler
 * <AUTHOR>
 */

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:policies:permission')
const PolicyHandler = require('nongfu.merchant.policyrouter').PolicyHandler;
const permissionSvc = require('../../services/permission').Permission

class Handler extends PolicyHandler {
  constructor() {
    super()
  }

  getName() {
    return 'Permission'
  }

  preInvoke(context, conti) {
    let self = this
    let method = `${self.getName()}.preInvoke`
    debug(method, '[Enter]')

    try {
      permissionSvc.assert(context.req.user, context.bindings.role).then(() => {
        debug(method, '[Exit](success)')
        return conti()
      }).catch(error => {
        debug.error(method, '[Exit](failed)', error)
        if (error.httpCode && error.httpCode !== 500)
          context.res.set('Warning', `199 - ${error.errorCode || 'EJWTAUTHPRE094'} ${new Date()}`)
        context.res.status(error.httpCode || 500).send(error)
        return conti(error)
      })
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      if (error.httpCode && error.httpCode !== 500)
        context.res.set('Warning', `199 - ${error.errorCode || 'EJWTAUTHPRE094'} ${new Date()}`)
      context.res.status(error.httpCode || 500).send(error)
      return conti(error)
    }
  }

  postInvoke(context, conti) {
    return conti()
  }
}

module.exports = Handler

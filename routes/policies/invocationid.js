/**
* validate invocation id from req header
* <AUTHOR>
*/

'use strict'

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:policies:invocationId');
const PolicyHandler = require('nongfu.merchant.policyrouter').PolicyHandler;

const INNOVATION_HEADER = 'rongxin-passport-iid';

class Handler extends PolicyHandler {
  constructor() {
    super();
  }

  getName() {
    return 'invocationId';
  }

  begin(context, conti) {
    let self = this;
    let method = `${self.getName()}.begin`;
    debug(method, '[Enter]');

    try {
      if (!context || !context.req)
        throw {
          errorCode: 'EINVOCATION033',
          httpCode: 500,
          reason: 'invalid req instance'
        };

      let invocationId = context.req.headers[INNOVATION_HEADER];
      if (!invocationId || !invocationId.length) {
        debug.warning(method, '[Warn] Invalid invoacationid');
        // temporarily allow req without invocation id
        // throw {
        //   errorCode: 'EINVOCATION042',
        //   httpCode: 400,
        //   reason: 'invalid invocationid'
        // }
      }

      debug(method, '[Exit](success)');
      return conti();
    } catch (error) {
      debug.error(method, '[Exit](error)', error);
      context.res.status(error.httpCode || 500).send(error);
    }
  }

  preInvoke(context, conti) {
    return conti();
  }

  postInvoke(context, conti) {
    return conti();
  }
}

module.exports = Handler

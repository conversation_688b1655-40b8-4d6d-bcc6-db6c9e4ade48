"use strict";

/**
 * The handler intents to setup the default cache configuration 
 *
 * <AUTHOR>
 *
 */

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const PolicyHandler = require('nongfu.merchant.policyrouter').PolicyHandler;
const logger = logFactory(logUtil())('rongxin:loan:mgr:app:api:policies:cachesetting');

class CacheSettingHandler extends PolicyHandler {
  constructor() {
    super();
  }

  /**
   * @Override
   *
   */
  begin(context, done) {
    try {
      let res = context.res;
      res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    } catch (err) {
      logger.error('Error occurs during cache setting handler enter, due to: ', err);
    } finally {
      done();
    }
  }

  /**
   * @Override
   *
   */
  end(context, done) {
    done();
  }

  /**
   * @Override
   *
   */
  preInvoke(context, cont) {
    cont();
  }

  /**
   * @Override
   *
   */
  postInvoke(context, cont) {
    cont();
  }

  /**
   * @Override
   *
   */
  getName() {
    return 'CacheSettingHandler';
  }

}

module.exports = CacheSettingHandler;
/**
 * bind client info to req
 * <AUTHOR>
 */

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:policies:client')
const PolicyHandler = require('nongfu.merchant.policyrouter').PolicyHandler;
const clientCache = require('../../persistence/client');

const APP_CLIENT_HEADER = 'rongxin-app';

class Handler extends PolicyHandler {
  constructor() {
    super()
  }

  getName() {
    return 'ClientBinder'
  }

  async preInvoke(context, conti) {
    let self = this
    let method = `${self.getName()}.preInvoke`
    debug(method, '[Enter]')

    try {
      if (context.req.Client) {
        debug(method, '[Exit](continue)');
        return conti();
      }

      let cId = "";
      if (context.req.headers[APP_CLIENT_HEADER]) {
        cId = context.req.headers[APP_CLIENT_HEADER];
      } else if (context.req.user && context.req.user.client) {
        cId = context.req.user.client;
      }

      if (!cId) {
        context.req.Client = {};

        debug(method, '[Exit](continue)');
        return conti();
      }

      let client = await clientCache.getClient(cId);
      context.req.Client = client || {};

      debug(method, '[Exit](success)');
      return conti();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      context.res.status(error.httpCode || 500).send(error);
      return conti(error)
    }
  }

  postInvoke(context, conti) {
    return conti()
  }
}

module.exports = Handler
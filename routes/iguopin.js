/**
 * iguopin Router
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:iguopin');
const iguopinSvc = require('../services/iguopin');
const moment = require('moment');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
   * @api {get} /api/v1.0/iguopin/invitation/main 岗位列表
   * @apiVersion 1.0.0
   * @apiName getInvitationMain
   * @apiGroup iguopin
   * @apiPermission authenticated
   *
   */
  async getInvitationMain(req, res) {
    let method = 'getInvitationMain'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_JT_ACC_033',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let query = req.query;
      let condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        status: '1',
        archived: false,
        $sort: { createdTime: -1 }
      };
      // if (query.startTime) {
      //   let startTime = moment(query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
      //   condition.createdTime = condition.createdTime || {};
      //   condition.createdTime.$gte = startTime;
      // }
      // if (query.endTime) {
      //   let endTime = moment(query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
      //   condition.createdTime = condition.createdTime || {};
      //   condition.createdTime.$lte = endTime;
      // }

      let opts = {};
      let result = await iguopinSvc.getInvitationMain(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /**
   * @api {get} /api/v1.0/iguopin/position/info 岗位详情
   * @apiVersion 1.0.0
   * @apiName getPositionInfo
   * @apiGroup iguopin
   * @apiPermission authenticated
   *
   */
  async getPositionInfo(req, res) {
    let method = 'getPositionInfo'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_JT_ACC_033',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query.id) {
        throw {
          errorCode: 'E_IGUOPIN_041',
          httpCode: 406,
          reason: '必要信息缺失'
        }
      }

      let condition = {
        id: req.query.id
      };

      let opts = {};

      let result = await iguopinSvc.getPositionInfo(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /**
   * @api {post} /api/v1.0/iguopin/enroll 报名
   * @apiVersion 1.0.0
   * @apiName saveEnroll
   * @apiGroup iguopin
   * @apiPermission authenticated
   *
   */
  async saveEnroll(req, res) {
    let method = 'saveEnroll'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_JT_ACC_033',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let body = req.body || {};
      if (!body.id || !body.offlineInvitationId || !body.name) {
        throw {
          errorCode: 'E_IGUOPIN_129',
          httpCode: 406,
          reason: '必传参数为空'
        }
      }

      let condition = {
        id: body.id,
        offlineInvitationId: body.offlineInvitationId,
        name: body.name,
        uId: req.user.userid,
        profile_photo: body.profile_photo,
        idCard: body.idCard,
        birthday: body.birthday,
        sex: body.sex,
        mobile: body.mobile,
        address: body.address,
        areaCode: body.areaCode,
        education: body.education,
        nation: body.nation,
        marriage: body.marriage
      };

      let opts = {};

      let result = await iguopinSvc.saveEnroll(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /**
   * @api {get} /api/v1.0/iguopin/enroll/options 选项
   * @apiVersion 1.0.0
   * @apiName getEnrollOptions
   * @apiGroup iguopin
   * @apiPermission authenticated
   *
   */
  async getEnrollOptions(req, res) {
    let method = 'getEnrollOptions'
    debug(method, '[Enter]')
    try {
      let result = {
        "credentials": {
          "1": "建筑类证书",
          "2": "物业管理类证书",
          "3": "电工证",
          "4": "电焊工证",
          "5": "消防证",
          "6": "厨师证",
          "7": "面点师",
          "8": "养老护理员",
          "9": "营养师",
          "10": "执业护士",
          "11": "高处作业证",
          "12": "制冷与空调作业证",
          "13": "煤矿安全作业证",
          "14": "金属非金属矿山安全作业证",
          "15": "石油天然气安全作业证",
          "16": "冶金生产安全作业证",
          "17": "危险化学品安全作业证",
          "18": "电梯操作证",
          "19": "起重机械操作证",
          "20": "汽车维修",
          "21": "车工",
          "22": "铣工",
          "23": "磨工",
          "24": "镗工",
          "25": "机床操作工",
          "26": "数控车工",
          "27": "数控铣工",
          "28": "铸造工",
          "29": "锻造工",
          "30": "房地产经纪人",
          "31": "会计类证书"
        },
        "education": {
          "10": "初中",
          "20": "高中",
          "30": "高职",
          "40": "中专",
          "50": "大专",
          "60": "本科",
          "70": "本科以上",
          "80": "不限"
        },
        "nation": {
          "1": "汉族",
          "2": "壮族",
          "3": "满族",
          "4": "回族",
          "5": "苗族",
          "6": "维吾尔族",
          "7": "土家族",
          "8": "彝族",
          "9": "蒙古族",
          "10": "藏族",
          "11": "布依族",
          "12": "侗族",
          "13": "瑶族",
          "14": "朝鲜族",
          "15": "白族",
          "16": "哈尼族",
          "17": "哈萨克族",
          "18": "黎族",
          "19": "傣族",
          "20": "畲族",
          "21": "傈僳族",
          "22": "仡佬族",
          "23": "东乡族",
          "24": "高山族",
          "25": "拉祜族",
          "26": "水族",
          "27": "佤族",
          "28": "纳西族",
          "29": "羌族",
          "30": "土族",
          "31": "仫佬族",
          "32": "锡伯族",
          "33": "柯尔克孜族",
          "34": "达斡尔族",
          "35": "景颇族",
          "36": "毛南族",
          "37": "撒拉族",
          "38": "塔吉克族",
          "39": "阿昌族",
          "40": "普米族",
          "41": "鄂温克族",
          "42": "怒族",
          "43": "京族",
          "44": "基诺族",
          "45": "德昂族",
          "46": "保安族",
          "47": "俄罗斯族",
          "48": "裕固族",
          "49": "乌兹别克族",
          "50": "门巴族",
          "51": "鄂伦春族",
          "52": "独龙族",
          "53": "塔塔尔族",
          "54": "赫哲族",
          "55": "珞巴族",
          "56": "布朗族",
          "57": "其他"
        },
        "marriage": {
          "1": "未婚",
          "2": "已婚",
          "3": "离异",
          "4": "丧偶"
        },
        "language": {
          "1": "英语",
          "2": "法语",
          "3": "日语",
          "4": "韩语",
          "5": "德语",
          "6": "俄语",
          "7": "西班牙语",
          "8": "意大利语",
          "9": "葡萄牙语",
          "10": "阿拉伯语",
          "11": "普通话",
          "12": "粤语",
          "13": "其他"
        }
      }
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }

  }

  async share(req, res) {
    let method = 'share'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_JT_ACC_033',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        userId: req.user.userid
      };

      let opts = {};

      let result = await iguopinSvc.share(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async statistic(req, res) {
    let method = 'statistic'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_JT_ACC_033',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query.groupV2Id) {
        throw {
          errorCode: 'E_IGUOPIN_129',
          httpCode: 406,
          reason: '必传参数为空'
        }
      }
      let condition = {
        userId: req.user.userid,
        groupV2Id: req.query.groupV2Id,
      };

      let opts = {};

      let result = await iguopinSvc.statistic(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }


  async enrollList(req, res) {
    let method = 'enrollList'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_JT_ACC_033',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      let query = req.query;
      let condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        archived: false,
        $sort: { createdTime: -1 }
      };
      if (query.startTime) {
        let startTime = moment(query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (query.endTime) {
        let endTime = moment(query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      if (query.status) {
        condition.status = query.status;
      }
      if (query.id) {
        condition._id = query.id;
      }

      let opts = {};

      let result = await iguopinSvc.enrollListHandler(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async marketing(req, res) {
    let method = 'marketing'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CTRC_079',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      if (!req.query || !req.query.username || !req.query.qrcodeUrl) {
        throw {
          httpCode: 406,
          errorCode: 'E_SHARE_040',
          reason: 'invalid params'
        };
      }

      let condition = req.query;
      let opts = {};

      let result = await iguopinSvc.marketing(condition, opts);
      debug(method, '[Exit]', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 入驻成员列表
  async leagueList(req, res) {
    let method = 'leagueList'
    debug(method, '[Enter]')
    try {

      let condition = {
        skip: req.query.skip,
        limit: req.query.limit,
        archived: false,
        $sort: { createdTime: -1 }
      };
      if (req.query.startTime && req.query.endTime) {
        condition.createdTime = {
          $gte: moment(req.query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ'),
          $lte: moment(req.query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ')
        };
      }

      if (req.query.approveStartTime && req.query.approveEndTime) {
        condition.approveTime = {
          $gte: moment(req.query.approveStartTime).utc().format('YYYY-MM-DD HH:mm:ssZ'),
          $lte: moment(req.query.approveEndTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ')
        };
      }
      if (req.query.status) {
        condition.status = req.query.status;
      }

      let opts = {};

      let result = await iguopinSvc.leagueList(condition, opts);
      debug(method, '[Exit]', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  // 成员审批列表
  async leagueApprove(req, res) {
    let method = 'leagueList'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.id || !req.body.status) {
        throw {
          httpCode: 406,
          errorCode: 'E_SHARE_040',
          reason: 'invalid params'
        }
      }
      let condition = {
        status: req.body.status
      };


      let opts = {
        id: req.body.id
      };

      let result = await iguopinSvc.leagueApprove(condition, opts);
      debug(method, '[Exit]', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this;

    self._policyRouter.get('/api/v1.0/iguopin/invitation/main', self.getInvitationMain.bind(self), {
      name: 'inguopin.getInvitationMain',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/iguopin/position/info', self.getPositionInfo.bind(self), {
      name: 'inguopin.getPositionInfo',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/iguopin/enroll/options', self.getEnrollOptions.bind(self), {
      name: 'inguopin.getEnrollOptions',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.post('/api/v1.0/iguopin/enroll', self.saveEnroll.bind(self), {
      name: 'inguopin.saveEnroll',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.post('/api/v1.0/iguopin/share', self.share.bind(self), {
      name: 'inguopin.share',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/iguopin/statistic', self.statistic.bind(self), {
      name: 'inguopin.statistic',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/iguopin/enrollList', self.enrollList.bind(self), {
      name: 'inguopin.enrollList',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/share/marketing', self.marketing, {
      name: 'share.marketing',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/iguopin/leagueList', self.leagueList.bind(self), {
      name: 'inguopin.leagueList',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.post('/api/v1.0/iguopin/league/approve', self.leagueApprove.bind(self), {
      name: 'inguopin.leagueApprove',
      role: PERMISSION_ROLE.EVERYONE
    });

    return self;
  }
}

module.exports = Router;
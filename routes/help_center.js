/**
 * <AUTHOR>
 * 2019-05-05
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:help_center');
const helpCenterSvc = require('../services/help_center');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class HelpCenter {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }


  async getHelpCenterList(req, res) {
    let method = 'getHelpCenterList'
    debug(method, '[Enter]')
    try {
      if (!req.Client) {
        throw {
          errorCode: 'E_getHelpCenterList_102',
          httpCode: 406,
          reason: 'Client is miss'
        }
      }
      let condition = {
        cId: req.Client && req.Client._id,
        tId: req.Client && req.Client.tId,
        archived: false
      };
      let opts = {};

      let result = await helpCenterSvc.getHelpCenterList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  async getHelpCenterDetail(req, res) {
    let method = 'getHelpCenterDetail';
    debug(method, '[Enter]');
    try {

      if (!req.query.id) {
        throw {
          errorCode: 'E_HelpCenter_102',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }

      let condition = {
        id: req.query.id
      };
      let opts = {};

      let result = await helpCenterSvc.getHelpCenterDetail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/help/center/detail', self.getHelpCenterDetail.bind(self), {
      name: 'HelpCenter.getHelpCenterDetail',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/help/center/list', self.getHelpCenterList.bind(self), {
      name: 'HelpCenter.getHelpCenterList',
      role: PERMISSION_ROLE.EVERYONE
    });

    return self
  }
}

module.exports = HelpCenter
/**
 * <AUTHOR>
 * 2019-05-05
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:client');
const clientSvc = require('../services/client');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class Carrousel {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
   * @api {get} /api/v1.0/client/version 获取APP最新版本信息
   * @apiVersion 1.0.0
   * @apiName GetClientVersion
   * @apiGroup Client
   * @apiPermission EVERYONE 
   *
   * @apiDescription 获取APP最新版本信息
   * 
   * @apiParam {String} clientId app的client _id
   * 
   * @apiParamExample {url} Request-Params-Example:
   * ?clientId=xxxxxxxxxx
   *
   * @apiExample Example usage:
   * curl -i http://localhost/api/v1.0/client/version
   *
   * @apiSuccess {String} client client _id
   * @apiSuccess {String} version 版本号
   * @apiSuccess {String} versionCode 版本数
   * @apiSuccess {Object} file 文件信息
   * @apiSuccess {String} file.path 文件相对路径
   * @apiSuccess {String} file.md5 文件MD5值
   * @apiSuccess {Number} file.size 文件大小
   * @apiSuccess {Boolean} forceUpdate 是否强制更新
   * @apiSuccess {String} [forceUpdateRange] 强制更新信息
   * @apiSuccess {Number} forceUpdateRange.lower 强制更新下界
   * @apiSuccess {Number} forceUpdateRange.upper 强制更新上界
   * @apiSuccess {Boolean} update 是否发布更新
   * @apiSuccess {String} updateRange 更新信息
   * @apiSuccess {Number} updateRange.lower 更新下界
   * @apiSuccess {Number} updateRange.upper 更新上界
   * 
   * @apiSuccessExample {json} Response (example):
   * 
   * HTTP/1.1 200 OK
   * {
   *    "_id": "5cceed1727951501492ac4c6",
   *    "client": "5c08bc0a75728a3bf785ac89",
   *    "version": "2.0.1",
   *    "versionCode": 11,
   *    "file": {
   *      "path": "/data/xxx_v2.1.0.apk",
   *      "md5": "xxxxxx",
   *      "size": 200000
   *    },
   *    "description": "xxxxxxxxxxxx",
   *    "forceUpdate": true,
   *    "forceUpdateRange": {
   *      "lower": 1,
   *      "upper": 11
   *    },
   *    "update": true,
   *    "updateRange": {
   *      "lower": 1,
   *      "upper": 11
   *    },
   *    "archived": false,
   *    "createdTime": "2019-05-05T14:03:40.969Z",
   *    "lastModTime": "2019-05-05T14:03:40.969Z"
   *  }
   *  
   * @apiError ErrorQuery error params
   * 
   * @apiError(Error 50x) InternalServerError   服务器内部错误
   * 
   */
  async getClientVersion(req, res) {
    let method = 'getClientVersion'
    debug(method, '[Enter]')
    try {
      if (!req.query.clientId) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_065',
          reason: 'invalid param'
        };
      }

      let condition = {
        clientId: req.query.clientId,
        platform: req.query.platform || "android"
      };
      let opts = {};

      let result = await clientSvc.getClientVersion(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/client/version', self.getClientVersion.bind(self), {
      name: 'client.getClientVersion',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = Carrousel
/**
 * @description: area routes 
 * @author: hexu 
 */

'use strict'

const logFactory = require('../utils/logFactory')
const logUtil = require('../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:mgr.api:routes:assistanter')
const assistanterSvc = require('../services/assistanter')
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE
const moment = require('moment');
const { async } = require('q')

class Assistanter {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  //协理员列表
  async getAssistanterList(req, res) {
    let method = 'getAssistanterList'
    debug(method, '[Enter]')
    try {
      let query = req.query
      if (!query || !query.limit || !query.skip) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_LIST_029',
          reason: 'invalid param'
        }
      }

      let condition = {
        limit: query.limit || 10,
        skip: query.skip || 0,
        archived: false,
        $sort: {
          lastModTime: -1
        }
      }
      if (query.mobile) {
        condition.mobile = query.mobile
      }
      if (query.username) {
        condition.username = `/${query.username}/`
      }

      if (query.areaList) {
        condition.areaList = `/^${query.areaList}/`
      }

      if (query.status) {
        condition.flowStatus = query.status
      }

      let opts = {
        uId: req.user.userid,
        tId: req.Client.tId,
        userInfo: req.user
      };
      if (req.headers && req.headers['x-dash-role']) {
        opts.role = req.headers['x-dash-role'];
      }
      let result = await assistanterSvc.getAssistanterList(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  //协理员列表
  async createAssistanters(req, res) {
    let method = 'createAssistanters'
    debug(method, '[Enter]')
    try {
      let body = req.body
      if (!body || !body.mobile || !body.username || !body.IDCard || !body.domicileAddress || !body.presentAddress || !body.position || !body.politicalStatus || !body.areaList) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_LIST_029',
          reason: 'invalid param'
        }
      }

      let condition = req.body;
      condition.tId = req.Client.tId;

      let opts = {
        uId: req.user.userid,
        tId: req.Client.tId,
      };

      let result = await assistanterSvc.createAssistanters(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 协理员签约入驻合同
  async signRegisterContract(req, res) {
    let method = 'signRegisterContract'
    debug(method, '[Enter]')
    try {
      let body = req.body
      if (!body || !body.id || !body.signStart || !body.signEnd) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_LIST_029',
          reason: 'invalid param'
        }
      }

      let condition = {
        id: body.id,
        signStart: body.signStart,
        signEnd: body.signEnd
      }


      let opts = {
        uId: req.user.userid,
        tId: req.Client.tId
      };

      let result = await assistanterSvc.signRegisterContract(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 协理员增加身份证照片和银行卡号
  async updateAssistanter(req, res) {
    let method = 'updateAssistanter'
    debug(method, '[Enter]')
    try {
      let body = req.body
      if (!body || !body.id) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_LIST_029',
          reason: 'invalid param'
        }
      }

      let condition = {
        id: body.id,
        update: body
      }
      condition.update.lastModTime = new Date()
      delete condition.update.id

      let opts = {};

      let result = await assistanterSvc.updateAssistanter(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 详情
  async getAssistanterDetail(req, res) {
    let method = 'getAssistanterDetail'
    debug(method, '[Enter]')
    try {
      let query = req.query
      if (!query || !query.id) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_LIST_029',
          reason: 'invalid param'
        }
      }

      let condition = {
        id: query.id
      }



      let opts = {};

      let result = await assistanterSvc.getAssistanterDetail(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 待审批列表
  async getAssistanterApproveList(req, res) {
    let method = 'getAssistanterApproveList'
    debug(method, '[Enter]')
    try {
      let query = req.query
      if (!query || !query.limit || !query.skip) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_LIST_029',
          reason: 'invalid param'
        }
      }

      let condition = {
        limit: query.limit || 10,
        skip: query.skip || 0,
        archived: false,
        flowStatus: {
          $in: ["sign", "relieve"]
        },
        status: { $in: ["county_approve", "filiale_approve"] },
        $sort: {
          lastModTime: -1
        }
      }


      if (query.areaList) {
        condition.areaList = `/^${query.areaList}/`
      }

      if (query.flowStatus) {
        condition.flowStatus = query.flowStatus
      }


      let opts = {
        uId: req.user.userid,
        tId: req.Client.tId,
        userInfo: req.user
      };
      if (req.headers && req.headers['x-dash-role']) {
        opts.role = req.headers['x-dash-role'];
      }
      let result = await assistanterSvc.getAssistanterApproveList(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  // 县域审批
  async countyApprove(req, res) {
    let method = 'countyApprove'
    debug(method, '[Enter]')
    try {
      let body = req.body
      if (!body || !body.id || !body.status) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_LIST_029',
          reason: 'invalid param'
        }
      }

      let condition = {
        id: body.id,
        status: body.status,
        reason: body.reason,
        comments: body.comments
      }

      let opts = {
        uId: req.user.userid,
        tId: req.Client.tId,
      };
      if (req.headers && req.headers['x-dash-role']) {
        opts.role = req.headers['x-dash-role'];
      }

      let result = await assistanterSvc.countyApprove(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 分公司审批
  async filialeApprove(req, res) {
    let method = 'filialeApprove'
    debug(method, '[Enter]')
    try {
      let body = req.body
      if (!body || !body.id) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_LIST_029',
          reason: 'invalid param'
        }
      }

      let condition = {
        id: body.id,
        status: body.status,
        reason: body.reason,
        comments: body.comments
      }

      let opts = {
        uId: req.user.userid,
        tId: req.Client.tId,
      };
      if (req.headers && req.headers['x-dash-role']) {
        opts.role = req.headers['x-dash-role'];
      }

      let result = await assistanterSvc.filialeApprove(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 风控报告


  // 解约流程
  async relieveAssistanters(req, res) {
    let method = 'relieveAssistanters'
    debug(method, '[Enter]')
    try {
      let body = req.body
      if (!body || !body.id || !body.reason) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_LIST_029',
          reason: 'invalid param'
        }
      }

      let condition = {
        id: body.id,
        reason: body.reason,
        comments: body.comments || ""
      }


      let opts = {
        uId: req.user.userid,
        tId: req.Client.tId
      };

      let result = await assistanterSvc.relieveAssistanters(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 获取当前用户头像信息
  async getUserPhoto(req, res) {
    let method = 'getUserPhoto'
    debug(method, '[Enter]')
    try {

      let condition = {}

      let opts = {
        uId: req.user.userid,
        tId: req.Client.tId
      };

      let result = await assistanterSvc.getUserPhoto(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 上传当前用户头像
  async setUserPhoto(req, res) {
    let method = 'setUserPhoto'
    debug(method, '[Enter]')
    try {

      if (!req.body || !req.body.photo) {
        throw {
          httpCode: 406,
          errorCode: 'E_USER_PHOTO_432',
          reason: 'invalid param'
        }
      }

      let condition = {
        photo: req.body.photo
      }

      let opts = {
        uId: req.user.userid,
        tId: req.Client.tId
      };

      let result = await assistanterSvc.setUserPhoto(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 填写银行卡号和身份证号信息
  async setUserBankAccount(req, res) {
    let method = 'setUserBankAccount'
    debug(method, '[Enter]')
    try {

      if (!req.body || !req.body.bankCard || !req.body.bankName || !req.body.bankDeposit) {
        throw {
          httpCode: 406,
          errorCode: 'E_USER_PHOTO_432',
          reason: 'invalid param'
        }
      }

      let condition = {
        bankCard: req.body.bankCard,
        bankName: req.body.bankName,
        bankDeposit: req.body.bankDeposit,
        frontIDCardImage: req.body.frontIDCardImage,
        backIDCardImage: req.body.backIDCardImage,
      }

      let opts = {
        uId: req.user.userid,
        tId: req.Client.tId,
        userInfo: req.user
      };

      let result = await assistanterSvc.setUserBankAccount(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this

    // 
    self._policyRouter.get('/api/v1.0/assistanter/list', self.getAssistanterList.bind(self), {
      name: 'assistanter.getAssistanterList',
    })

    // 
    self._policyRouter.post('/api/v1.0/assistanters', self.createAssistanters.bind(self), {
      name: 'assistanter.createAssistanters',
    })

    // 
    self._policyRouter.put('/api/v1.0/assistanters', self.updateAssistanter.bind(self), {
      name: 'assistanter.updateAssistanter',
    })

    // 
    self._policyRouter.get('/api/v1.0/assistanter', self.getAssistanterDetail.bind(self), {
      name: 'assistanter.getAssistanterDetail',
    })

    // 
    self._policyRouter.get('/api/v1.0/assistanter/approve/list', self.getAssistanterApproveList.bind(self), {
      name: 'assistanter.getAssistanterApproveList',
    })

    self._policyRouter.put('/api/v1.0/assistanters/sign/register/contract', self.signRegisterContract.bind(self), {
      name: 'assistanter.signRegisterContract',
    })
    // 
    self._policyRouter.put('/api/v1.0/assistanters/county/approve', self.countyApprove.bind(self), {
      name: 'assistanter.countyApprove',
    })

    // 
    self._policyRouter.put('/api/v1.0/assistanters/filiale/approve', self.filialeApprove.bind(self), {
      name: 'assistanter.rejectAssistanters',
    })

    self._policyRouter.put('/api/v1.0/assistanters/relieve', self.relieveAssistanters.bind(self), {
      name: 'assistanter.relieveAssistanters',
    })

    self._policyRouter.get('/api/v1.0/user/photo', self.getUserPhoto.bind(self), {
      name: 'assistanter.getUserPhoto',
    })

    self._policyRouter.put('/api/v1.0/user/photo', self.setUserPhoto.bind(self), {
      name: 'assistanter.setUserPhoto',
    })

    self._policyRouter.put('/api/v1.0/user/bank/account', self.setUserBankAccount.bind(self), {
      name: 'assistanter.setUserBankAccount',
    })

    return self
  }
}

module.exports = Assistanter
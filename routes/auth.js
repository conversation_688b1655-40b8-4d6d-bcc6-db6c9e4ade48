'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:assistanceSubscribe');

class Auth {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
   * @api {get} /api/v1.0/auth 获取用户信息
   * @apiVersion 1.0.0
   * @apiName getUserInfo
   * @apiGroup Auth
   * @apiPermission authenticated
   *
   * @apiDescription 获取用户信息
   */
  async getUserInfo(req, res) {
    const method = 'getUserInfo';
    debug(method, '[Enter]');
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_AUTH_028',
          httpCode: 401,
          reason: '用户未登录',
        };
      }

      const condition = {
        id: req.user.userid,
      };
      const opts = {};

      const result = {
        client: req.Client,
        user: req.user,
      };
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    const self = this;

    self._policyRouter.get('/api/v1.0/auth', self.getUserInfo.bind(self), {
      name: 'auth.getUserInfo',
    });

    return self;
  }
}

module.exports = Auth;

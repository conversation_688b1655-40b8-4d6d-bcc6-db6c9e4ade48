'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:assistanceSubscribe');
const assistanceSubscribeService = require('../services/assistanceSubscribe');

class AssistanceSubscribe {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
   * @api {get} /api/v1.0/assistance-subscribe/list 获取远程协助客服关注用户列表
   * @apiVersion 1.0.0
   * @apiName getAssistanceSubscribeList
   * @apiGroup AssistanceSubscribe
   * @apiPermission authenticated
   *
   * @apiDescription 获取远程协助客服关注用户列表
   *
   * @apiParam {Number} skip
   * @apiParam {Number} limit
   *
   * @apiSuccess {Number} total 总条数
   * @apiSuccess {Object[]} result 结果数组
   * @apiSuccess {String} result._id 记录_id
   */
  async getAssistanceSubscribeList(req, res) {
    const method = 'getAssistanceSubscribeList';
    debug(method, '[Enter]');
    try {
      const condition = req.query || {};
      condition.skip = req.query.skip || 0;
      condition.limit = req.query.limit || 10;
      condition.agentId = req.user.userid;
      condition.archived = false;
      condition.$sort = { createdTime: -1 };

      const opts = {};

      const result = await assistanceSubscribeService.getAssistanceSubscribeList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {get} /api/v1.0/assistance-subscribe/detail 获取远程协助客服关注用户详情
   * @apiVersion 1.0.0
   * @apiName getAssistanceSubscribeDetail
   * @apiGroup Queue
   * @apiPermission authenticated
   *
   * @apiDescription 获取远程协助客服关注用户详情
   *
   * @apiParam {String} id 远程协助客服关注用户_id
   *
   * @apiSuccess {String} _id 记录_id
   */
  async getAssistanceSubscribeDetail(req, res) {
    const method = 'getAssistanceSubscribeDetail';
    debug(method, '[Enter]');
    try {
      const condition = {
        id: req.query.id,
      };
      const opts = {
        userid: req.user.userid,
      };

      const result = await assistanceSubscribeService.getAssistanceSubscribeDetail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {delete} /api/v1.0/assistance-subscribe 删除远程协助客服关注用户
   * @apiVersion 1.0.0
   * @apiName removeAssistanceSubscribe
   * @apiGroup AssistanceSubscribe
   * @apiPermission authenticated
   *
   * @apiDescription 删除远程协助客服关注用户
   *
   * @apiParam {String} id 记录_id
   */
  async removeAssistanceSubscribe(req, res) {
    const method = 'removeAssistanceSubscribe';
    debug(method, '[Enter]');
    try {
      const condition = {
        id: req.query.id,
        archived: true,
      };

      const opts = {
        userid: req.user.userid,
      };

      const result = await assistanceSubscribeService.removeAssistanceSubscribe(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    const self = this;

    self._policyRouter.get('/api/v1.0/assistance-subscribe/list', self.getAssistanceSubscribeList.bind(self), {
      name: 'queue.getAssistanceSubscribeList',
    });
    self._policyRouter.get('/api/v1.0/assistance-subscribe/detail', self.getAssistanceSubscribeDetail.bind(self), {
      name: 'queue.getAssistanceSubscribeDetail',
    });
    self._policyRouter.delete('/api/v1.0/assistance-subscribe', self.removeAssistanceSubscribe.bind(self), {
      name: 'queue.removeAssistanceSubscribe',
    });

    return self;
  }
}

module.exports = AssistanceSubscribe;

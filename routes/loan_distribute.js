/**
 * <AUTHOR>
 * 2020-01-06
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:loan_distribute');
const loanDistributeSvc = require('../services/loan_distribute');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const moment = require('moment');

class LoanDistribute {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  // 待办已办列表
  async getDistributeList(req, res) {
    let method = 'getDistributeList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_30',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query.gId || !req.query.roleId) {
        throw {
          errorCode: 'E_DISTRIBUTE_34',
          httpCode: 406,
          reason: 'gId or roleId not null'
        }
      }
      
      let type = ~~req.query.type == 2 ? true : false;
      let condition = {
        skip: req.query.skip || 0,
        limit: req.query.limit || 10,
        assigned: req.user.userid,
        handleStatus: type,
        roleId: req.query.roleId,
        archived: false,
        $sort: req.query.$sort || { createdTime: -1 }
      };
      if(!type){
        condition.gId = req.query.gId;
      }
      
      if (req.query.startTime) {
        condition.startTime = moment(req.query.startTime).utc().format();
      }
      if (req.query.endTime) {
        condition.endTime = moment(req.query.endTime).utc().add(1, 'd').format();
      }
      if (req.query.pIdsStr) {
        let pIds = req.query.pIdsStr.split(',');
        condition.pId = {
          "$in":pIds
        };
      }
      if (req.query.area) {
        condition.area = "/^"+req.query.area+"/";
      }
      
      let opts = {};

      let result = await loanDistributeSvc.getDistributeList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 待办派发
  async createDistributeProcess(req, res) {
    let method = 'createDistributeProcess'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_63',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.body || !req.body.gId || !req.body.assigned || !req.body.aId || !req.body.assignStatus || !req.body.roleId) {
        throw {
          errorCode: 'E_DISTRIBUTE_70',
          httpCode: 406,
          reason: 'invalid params'
        }
      }
      
      let condition = {
        distribute: req.user.userid,
        assigned: req.body.assigned,
        gId: req.body.gId,
        roleId: req.body.roleId,
        aId: req.body.aId,
        assignStatus: req.body.assignStatus,
        handleStatus: false
      };
      let opts = {};

      let result = await loanDistributeSvc.createDistributeProcess(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 待办已办数量
  async getStatisticsDistribute(req, res) {
    let method = 'getStatisticsDistribute'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_63',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query.gId && !req.query.roleId) {
        throw {
          errorCode: 'E_DISTRIBUTE_134',
          httpCode: 406,
          reason: 'gId or roleId not null'
        }
      }
      
      let condition = {
        userid: req.user.userid,
        gId: req.query.gId,
        roleId: req.query.roleId
      };
      let opts = {};

      let result = await loanDistributeSvc.getStatisticsDistribute(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  
  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/distribute/list', self.getDistributeList.bind(self), {
      name: 'distribute.getDistributeList',
      role: PERMISSION_ROLE.EVERYONE
    })
    self._policyRouter.post('/api/v1.0/distribute/create', self.createDistributeProcess.bind(self), {
      name: 'distribute.createDistributeProcess',
      role: PERMISSION_ROLE.EVERYONE
    })
    self._policyRouter.get('/api/v1.0/distribute/count', self.getStatisticsDistribute.bind(self), {
      name: 'distribute.getStatisticsDistribute',
      role: PERMISSION_ROLE.EVERYONE
    })
    
    return self
  }
}

module.exports = LoanDistribute
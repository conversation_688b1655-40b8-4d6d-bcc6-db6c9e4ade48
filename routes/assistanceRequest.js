'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:assistanceRequest');
const assistanceRequestService = require('../services/assistanceRequest');

class AssistanceRequest {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
   * @api {get} /api/v1.0/assistance-request/list 获取服务订单列表
   * @apiVersion 1.0.0
   * @apiName getAssistanceRequestList
   * @apiGroup AssistanceRequest
   * @apiPermission authenticated
   *
   * @apiDescription 获取服务订单列表
   *
   * @apiParam {Number} skip
   * @apiParam {Number} limit
   *
   * @apiSuccess {Number} total 总条数
   * @apiSuccess {Object[]} result 结果数组
   * @apiSuccess {String} result._id 记录_id
   */
  async getAssistanceRequestList(req, res) {
    const method = 'getAssistanceRequestList';
    debug(method, '[Enter]');
    try {
      const condition = req.query || {};
      condition.skip = req.query.skip || 0;
      condition.limit = req.query.limit || 10;
      condition.agentId = req.user.userid;
      condition.archived = false;
      condition.$sort = { createdTime: -1 };

      const opts = {};

      const result = await assistanceRequestService.getAssistanceRequestList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {get} /api/v1.0/assistance-request/detail 获取服务订单详情
   * @apiVersion 1.0.0
   * @apiName getAssistanceRequestDetail
   * @apiGroup Queue
   * @apiPermission authenticated
   *
   * @apiDescription 获取服务订单详情
   *
   * @apiParam {String} id 服务订单_id
   *
   * @apiSuccess {String} _id 记录_id
   */
  async getAssistanceRequestDetail(req, res) {
    const method = 'getAssistanceRequestDetail';
    debug(method, '[Enter]');
    try {
      if (!req.query.id && !req.query.msgId) {
        throw {
          errorCode: 'E_ASSISTANCE_REQUEST_101',
          httpCode: 406,
          reason: 'id 和 msgId 不能同时为空',
        }
      }
      const condition = {
        id: req.query.id,
        msgId: req.query.msgId,
      };
      const opts = {
        userid: req.user.userid,
        tId: req.Client && req.Client.tId,
      };

      const result = await assistanceRequestService.getAssistanceRequestDetail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async acceptAssistanceRequest(req, res) {
    const method = 'acceptAssistanceRequest';
    debug(method, '[Enter]');
    try {
      const condition = {
        id: req.query.id,
        body: req.body,
      };
      const opts = {
        userid: req.user.userid,
      };

      const result = await assistanceRequestService.acceptAssistanceRequest(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async closeAssistanceRequest(req, res) {
    const method = 'closeAssistanceRequest';
    debug(method, '[Enter]');
    try {
      const condition = {
        id: req.query.id,
        body: req.body,
      };
      const opts = {
        userid: req.user.userid,
      };

      const result = await assistanceRequestService.closeAssistanceRequest(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {put} /api/v1.0/assistance-request 修改服务订单
   * @apiVersion 1.0.0
   * @apiName updateAssistanceRequest
   * @apiGroup AssistanceRequest
   * @apiPermission authenticated
   *
   * @apiDescription 修改服务订单
   *
   * @apiParam {String} id 记录_id
   */
  async updateAssistanceRequest(req, res) {
    const method = 'updateAssistanceRequest';
    debug(method, '[Enter]');
    try {
      const condition = {
        id: req.query.id,
        body: req.body,
      };
      const opts = {
        userid: req.user.userid,
      };

      const result = await assistanceRequestService.updateAssistanceRequest(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {delete} /api/v1.0/assistance-request 删除服务订单
   * @apiVersion 1.0.0
   * @apiName removeAssistanceRequest
   * @apiGroup AssistanceRequest
   * @apiPermission authenticated
   *
   * @apiDescription 删除服务订单
   *
   * @apiParam {String} id 记录_id
   */
  async removeAssistanceRequest(req, res) {
    const method = 'removeAssistanceRequest';
    debug(method, '[Enter]');
    try {
      const condition = {
        id: req.query.id,
        archived: true,
      };

      const opts = {
        userid: req.user.userid,
      };

      const result = await assistanceRequestService.removeAssistanceRequest(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    const self = this;

    self._policyRouter.get('/api/v1.0/assistance-request/list', self.getAssistanceRequestList.bind(self), {
      name: 'queue.getAssistanceRequestList',
    });
    self._policyRouter.get('/api/v1.0/assistance-request/detail', self.getAssistanceRequestDetail.bind(self), {
      name: 'queue.getAssistanceRequestDetail',
    });
    self._policyRouter.post('/api/v1.0/assistance-request/accept', self.acceptAssistanceRequest.bind(self), {
      name: 'queue.acceptAssistanceRequest',
    });
    self._policyRouter.post('/api/v1.0/assistance-request/close', self.closeAssistanceRequest.bind(self), {
      name: 'queue.closeAssistanceRequest',
    });
    self._policyRouter.put('/api/v1.0/assistance-request', self.updateAssistanceRequest.bind(self), {
      name: 'queue.updateAssistanceRequestDetail',
    });
    self._policyRouter.delete('/api/v1.0/assistance-request', self.removeAssistanceRequest.bind(self), {
      name: 'queue.removeAssistanceRequest',
    });

    return self;
  }
}

module.exports = AssistanceRequest;

/**
 * <AUTHOR>
 * 2019-05-05 
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:employee');
const employeeSvc = require('../services/employee');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class Carrousel {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getNextEmployee(req, res) {
    let method = 'getNextEmployee'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_63',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query || !req.query.currentRole) {
        throw {
          httpCode: 406,
          errorCode: 'E_EMPLOYEE_R_065',
          reason: 'invalid param'
        };
      }

      let condition = {
        currentRole: req.query.currentRole
      };
      let opts = {
        userInfo: req.user
      };

      let result = await employeeSvc.getNextEmployee(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  // 获取协理员列表
  async getAssistantList(req, res) {
    let method = 'getAssistantList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_63',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        limit: req.query.limit || 10,
        skip: req.query.skip || 0
      };
      let opts = {
        uId: req.user.userid
      };

      let result = await employeeSvc.getAssistantList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  //获取协理员详情
  async getAssistantDetail(req, res) {
    let method = 'getAssistantDetail'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_63',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query || !req.query.id) {
        throw {
          httpCode: 406,
          errorCode: 'E_EMPLOYEE_R_065',
          reason: 'invalid param'
        };
      }

      let condition = {
        id: req.query.id
      };
      let opts = {
        //userInfo: req.user
      };

      let result = await employeeSvc.getAssistantDetail(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
    
  }


  // 获取吉林测绘地块列表
  async getAssetLandByAreaCodeV2(req, res) {
    let method = 'getAssetLandByAreaCodeV2'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_R_62',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        skip: req.query.skip || 0,
        limit: req.query.limit || 20,
        areaCode: req.query.areaCode,
        CBFMC: req.query.CBFMC,
        uId: req.user.userid,
        tId: req.Client && req.Client.tId
      }

      let opts = {};
      let result = await employeeSvc.getAssetLandByAreaCodeV2(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 获得吉林测绘地块详情
  async getAssetLandDetail(req, res) {
    let method = 'getAssetLandDetail'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CON_085',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query || !req.query.landId) {
        throw {
          errorCode: 'E_LAND_R_175',
          httpCode: 406,
          reason: 'invalid param'
        };
      }
      let condition = {landId: req.query.landId}


      let opts = {};
      let result = await employeeSvc.getAssetLandDetail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 获得吉林测绘地块地图模式
  async getAssetLandMap(req, res) {
    let method = 'getAssetLandMap'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CON_085',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        areaCode: req.query.areaCode,
        CBFMC: req.query.CBFMC,
        skip: req.query.skip || 0,
        limit: 'unlimited',
        uId: req.user.userid
      }

      let opts = {};
      let result = await employeeSvc.getAssetLandMap(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 获取新网银行推广二维码
  async getXWBankQRCode(req, res) {
    let method = 'getXWBankQRCode'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CON_085',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        url: req.query.url,
      }

      let opts = {
        uId: req.user.userid
      };
      let result = await employeeSvc.getXWBankQRCode(condition, opts);
      debug(method, '[Exit](success)');
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/next/employee', self.getNextEmployee.bind(self), {
      name: 'client.getNextEmployee'
    })
    self._policyRouter.get('/api/v1.0/employee/assistant/list', self.getAssistantList.bind(self), {
      name: 'client.getAssistantList',
      role: PERMISSION_ROLE.EVERYONE
    })
    self._policyRouter.get('/api/v1.0/assistant/detail', self.getAssistantDetail.bind(self), {
      name: 'client.getAssistantDetail',
      // role: PERMISSION_ROLE.EVERYONE
    })
    self._policyRouter.get('/api/v2.0/employee/land/asset', self.getAssetLandByAreaCodeV2.bind(self), {
      name: 'user.getLandByIdCardV2',
      // role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.get('/api/v1.0/employee/land/asset/get/detail', self.getAssetLandDetail.bind(self), {
      name: 'user.getAssetLandDetail',
      // role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.get('/api/v1.0/employee/land/asset/get/map', self.getAssetLandMap.bind(self), {
      name: 'user.getAssetLandMap',
      // role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.get('/api/v1.0/xwbank/qrcode', self.getXWBankQRCode.bind(self), {
      name: 'user.getXWBankQRCode',
      // role: PERMISSION_ROLE.EVERYONE
    });
    return self
  }
}

module.exports = Carrousel
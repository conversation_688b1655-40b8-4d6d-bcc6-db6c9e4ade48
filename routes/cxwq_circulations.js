/** 
 * newsRouter 
 * <AUTHOR>
 * 2019-10-18
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:land_origin_fund_receive');
const cxwqCirculationsService = require('../services/cxwq_circulations');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const {assert,formatParas} = require('../utils/general')
const { cloneDeep } = require('lodash');

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async listLands({},req, res) {
    const method = 'listLands';
    debug(method, '[Enter]')
    try {

      // process.env.NODE_ENV !=='local-dev' && delete req.query.uId;
      // process.env.NODE_ENV !=='local-dev' && delete req.query.tId;
      // req.user && (req.query.uId = req.user.userid);
      // req.user && (req.query.tId = req.Client && req.Client.tId);
      // assert(req.query.uId,'E_LAND_ORIGIN_FUND_RECEIVE_003','uId is required')
      // assert(req.query.tId,'E_LAND_ORIGIN_FUND_RECEIVE_004','tId is required')
      // assert(req.query.uId,'E_LAND_ORIGIN_FUND_RECEIVE_000','您必须登录之后才能操作')
      // assert(req.query.tId,'E_LAND_ORIGIN_FUND_RECEIVE_000','您必须登录之后才能操作')
      const config = [
        'aId','_id','idCard'
      ];
      const opts = {
        tId:req.query.tId,uId:req.query.uId,
      } ;
      const condition = {
        ...formatParas(config,req.query),
        archived: false,
      };
      const result = await cxwqCirculationsService.listLands(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  async updateCirculationAndLand(req, res) {
    const method = 'updateCirculationAndLand'
    debug(method, '[Enter]',req.body)
    try {

      const condition = cloneDeep( req.body );
      const opts = {
        tId: req.body.tId || ( req.Client && req.Client.tId ),
        uId:req.user && req.user.userid,
        saveWithLand:true,
      } ;
      const result = await cxwqCirculationsService.updateCirculationAndLand(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async listCirculations(req, res) {
    const method = 'landOriginFundReceiveConfirm'
    debug(method, '[Enter]',req.body)
    try {
      const query = req.query;
      const config = [
        'aId','idCard','status','sourceType','requestType','mobile',
        'principalUniqueId','principalName',
        'idCard','outlandId','_id',
        { from: 'name', rule: 'contain' },
        { from: 'requestName', rule: 'contain' },
        { from: 'areaCode', rule: 'startWith', to: "requestAreaCode" },
        { from: 'circulationType', rule: 'in' },
        { from: 'circulationUserType', rule: 'in' },
        { from: 'createdTimeStart', to: 'createdTime', rule: 'gte', fs: 'toMinDay' },
        { from: 'createdTimeEnd', to: 'createdTime', rule: 'lte', fs: 'toMaxDay' },
        { from: 'lastModTimeStart', to: 'lastModTime', rule: 'gte', fs: 'toMinDay' },
        { from: 'lastModTimeEnd', to: 'lastModTime', rule: 'lte', fs: 'toMaxDay' },
        { from: 'isRevoke', rule: 'boolean' },
        { from: 'skip', dv: 0 },
        { from: 'limit', dv: '10' },
        { from: '$sort', dv: { lastModTime: -1 }, fs: 'json' },
      ];
      const opts = {
        tId:query.tId,//uId:req.query.uId,
      } ;
      opts.uId = req.user && req.user.userid;
      opts.roleId = query.roleId || req.headers['x-role-id'];
      const condition = {
        ...formatParas(config,query),
        archived: false,isRevoked:false,
      };
      condition.isRevoked = query.isRevoked === 'true' ;
      condition.hasAgent = false;
      req.query.bindOutLand && ( condition.outlandId = { [ parseInt(req.query.bindOutLand) && '$ne' || '$eq' ]:null } );
      const result = await cxwqCirculationsService.listCirculations(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async bindCirculationsAndOutLand(req, res) {
    const method = 'bindCirculationsAndOutLand'
    try {
      const condition = cloneDeep( req.body ),opts = {}
      const result = await cxwqCirculationsService.bindCirculationsAndOutLand( condition , opts );
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `48 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {

    this._policyRouter.get('/api/v2.0/loan/cxwq_circulations/lands',
        (...paras)=>this.listLands({},...paras), {
          name: 'circulation.listLands',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.post('/api/v2.0/loan/cxwq_circulations/update',
        (...paras)=>this.updateCirculationAndLand(...paras), {
          name: 'circulation.updateCirculationAndLand',
          // role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get(
      '/api/v2.0/loan/cxwq_circulations/list',
        (...paras)=>this.listCirculations(...paras),
        {
          name: 'circulation.listCirculations',
          role: PERMISSION_ROLE.EVERYONE
        }
    );

    this._policyRouter.put('/api/v1.0/cxwq/circulation/bind/circulations/outland', this.bindCirculationsAndOutLand.bind(this), {
          name: 'circulation.bindCirculationsAndOutLand',
    });
    



    // this._policyRouter.get('/api/v1.0/land/origin/fund/receive/excel',
    //     (...paras)=>this.getLandOriginFundReceiveList({exportExcel:true,fundReceiveStatusIn:'unused,new'},...paras), {
    //       name: 'fund.getLandOriginFundReceive.list.excel',
    //       role: PERMISSION_ROLE.EVERYONE
    //     });

    // this._policyRouter.post('/api/v1.0/land/origin/fund/receive/import',
    //     (...paras)=>this.importLandOriginFundReceive(...paras), {
    //       name: 'fund.editLandOriginFundReceive',
    //       role: PERMISSION_ROLE.EVERYONE
    //     });

    // this._policyRouter.put('/api/v1.0/land/origin/fund/receive/edit',
    //     (...paras)=>this.editLandOriginFundReceive(...paras), {
    //       name: 'fund.editLandOriginFundReceive',
    //       role: PERMISSION_ROLE.EVERYONE
    //     });

    // this._policyRouter.put('/api/v1.0/land/origin/fund/receive/confirm', this.landOriginFundReceiveConfirm.bind(this), {
    //   name: 'fund.confirmLandOriginFundReceive',
    //   role: PERMISSION_ROLE.EVERYONE
    // })

    // this._policyRouter.put('/api/v1.0/land/origin/fund/receive/unused/one', this.landOriginFundReceiveUnusedOne.bind(this), {
    //   name: 'fund.unusedOneLandOriginFundReceive',
    //   role: PERMISSION_ROLE.EVERYONE
    // })

    return this;
  }
}

module.exports = Router;
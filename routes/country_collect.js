/**
 * User Router
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:country_collect');
const countryCoullectSvc = require('../services/country_collect');

const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const moment = require('moment');

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async list(req, res) {
    let method = 'save'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      let query = req.query;
      let condition = query;
      condition["$sort"] = { createdTime: -1 };
      if (query.startTime) {
        let startTime = moment(query.startTime).utc().toISOString();
        condition.startTime = startTime;
        delete condition.startTime;
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (query.endTime) {
        let endTime = moment(query.endTime).utc().add(1, 'd').toISOString();
        condition.endTime = endTime;
        delete condition.endTime;
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      if (query.name) {
        condition["content.basic.cooperativeName"] = {
          '$regex': query.name,
          '$options': 'si'
        };
        delete condition.name;
      }
      if (query['content.basic.areacode']) {
        condition['content.basic.areacode'] = {
          '$regex': condition['content.basic.areacode'],
          '$options': 'si'
        };
      }

      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };
      if (query.roleId) {
        opts.roleId = query.roleId;
        delete condition.roleId;
      }

      let result = await countryCoullectSvc.list(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async save(req, res) {
    let method = 'save'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if (!req.body.content || !req.body.type) {
        throw {
          errorCode: 'E_DRAFT_034',
          httpCode: 401,
          reason: '缺少采集id/content'
        }
      }

      let condition = req.body;

      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };

      let result = await countryCoullectSvc.save(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async detail(req, res) {
    let method = 'detail'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if (!req.query.id) {
        throw {
          errorCode: 'E_070',
          httpCode: 401,
          reason: '缺少采集id'
        }
      }
      let condition = { id: req.query.id };
      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };

      let result = await countryCoullectSvc.detail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  enums(req, res) {
    const _enmus = {
      "temperateZone": {
        1: "第一积温带",
        "2": "一下二上积温带",
        3: "第二积温带",
        4: "二上三下积温带",
        5: "第三积温带",
        6: "三上四下积温带",
        7: "第四积温带",
        8: "四上五下积温带",
        9: "第五积温带",
        10: "五上六下积温带",
        11: "第六积温带"
      },
      cooperativeNature: { 1: "农民合作社", 2: "村集体合作社" },
      warehousingNature: {
        1: "自有",
        2: "租赁"
      },
      subsidyType: {
        1: "耕地地力保护补贴",
        2: "农民专业合作社补贴",
        3: "新型职业农民培育补贴",
        4: "适度规模经营补贴",
        5: "农机购置补贴",
        6: "农业保险保费补贴",
        7: "玉米、大豆和稻谷生产者补贴",
        8: "其他",
      },
      isSelected: { true: "是", false: "否" },
      hasSpecialLine: { true: "有", false: "无" },
      trafficCondition: { 1: "好", 2: "中", 3: "差" },
      storageVarieties: { 1: "玉米", 2: "水稻", 3: "大豆" }
    }
    res.status(200).send(_enmus);
  }

  async approve(req, res) {
    let method = 'approve'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if (!req.body.id) {
        throw {
          errorCode: 'E_070',
          httpCode: 401,
          reason: '缺少采集id'
        }
      }
      let condition = req.body;
      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };

      let result = await countryCoullectSvc.approve(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this;

    self._policyRouter.post('/api/v1.0/country/collect/save', self.save.bind(self), {
      name: 'countrycollect.save',
      role: PERMISSION_ROLE.EVERYONE,
      humanClick: {
        enabled: true,
        interval: 5000,
        scope: {
          perQuery: 'type'
        },
        sidStrategy: {
          sidFromBodyItem: 'type'
        }
      },
    });
    self._policyRouter.get('/api/v1.0/country/collect/detail', self.detail.bind(self), {
      name: 'countrycollect.detail',
      role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.get('/api/v1.0/country/collect/list', self.list.bind(self), {
      name: 'countrycollect.list',
      role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.get('/api/v1.0/country/collect/enums', self.enums.bind(self), {
      name: 'countrycollect.enums',
      role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.post('/api/v1.0/country/collect/approve', self.approve.bind(self), {
      name: 'countrycollect.approve',
      role: PERMISSION_ROLE.EVERYONE
    });
    return self;
  }
}

module.exports = Router;
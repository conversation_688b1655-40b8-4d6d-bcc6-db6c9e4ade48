/**
 * User Router
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:collection_mgr');
const collectionMgrSvc = require('../services/collection_mgr');

const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
  * @api {get} /api/v1.0/collection/mgr/list 采集单管理列表
  * @apiVersion 1.0.0
  * @apiName collectionMgrList
  * @apiGroup Loan
  * @apiPermission authenticated 
  */
  async collectionMgrList(req, res) {
    let method = 'collectionMgrList'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if(!req.query.areaCode){
        throw {
          errorCode: 'E_COLL_LIST_040',
          httpCode: 406,
          reason: 'Invalid word'
        }
      }
      let condition = {
        areaCode:req.query.areaCode,
        limit: req.query.limit || 10,
        skip: req.query.skip || 0
      };
      
      let opts = {
        // userid: req.user.userid
      };

      let result = await collectionMgrSvc.collectionMgrList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  
    /**
    * @api {get} /api/v1.0/collection/mgr/village/list 采集单管理列表
    * @apiVersion 1.0.0
    * @apiName collectionMgrVillageList
    * @apiGroup Loan
    * @apiPermission authenticated 
    */
  async collectionMgrVillageList(req, res) {
    let method = 'collectionMgrVillageList'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if(!req.query.areaCode || !req.query.type){
        throw {
          errorCode: 'E_COLL_LIST_040',
          httpCode: 406,
          reason: 'Invalid word'
        }
      }
      let condition = {
        areaCodeStr:req.query.areaCode,
        type:req.query.type,
        skip: req.query.skip || 0,
        limit: req.query.limit || 10
      };
      
      let opts = {
        // userid: req.user.userid
      };

      let result = await collectionMgrSvc.collectionMgrVillageList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  
    /**
    * @api {get} /api/v1.0/collection/mgr/employee/list 采集单管理列表
    * @apiVersion 1.0.0
    * @apiName collectionMgrEmployeeList
    * @apiGroup Loan
    * @apiPermission authenticated 
    */
  async collectionMgrEmployeeList(req, res) {
    let method = 'collectionMgrVillageList'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if(!req.query.areaCode){
        throw {
          errorCode: 'E_COLL_LIST_040',
          httpCode: 406,
          reason: 'Invalid word'
        }
      }
      let condition = {
        areaCode:req.query.areaCode
      };
      
      let opts = {
        // userid: req.user.userid
      };

      let result = await collectionMgrSvc.collectionMgrEmployeeList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  
    /**
    * @api {get} /api/v1.0/collection/loan/list 采集单管理列表
    * @apiVersion 1.0.0
    * @apiName collectionLoanList
    * @apiGroup Loan
    * @apiPermission authenticated 
    */
  async collectionLoanList(req, res) {
    let method = 'collectionLoanList'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if(!req.query.areaCode){
        throw {
          errorCode: 'E_COLL_LIST_040',
          httpCode: 406,
          reason: 'Invalid word'
        }
      }
      let condition = {
        areaCode:req.query.areaCode,
        skip: req.query.skip || 0,
        limit: req.query.limit || 10
      };
      
      let opts = {
        // userid: req.user.userid
      };

      let result = await collectionMgrSvc.collectionLoanList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  
  /**
  * @api {get} /api/v1.0/collection/loan/village/list 采集单管理列表
  * @apiVersion 1.0.0
  * @apiName collectionLoanVillageList
  * @apiGroup Loan
  * @apiPermission authenticated 
  */
async collectionLoanVillageList(req, res) {
  let method = 'collectionLoanVillageList'
  debug(method, '[Enter]')
  try {
    // if (!req.user || !req.user.userid) {
    //   throw {
    //     errorCode: 'E_ENTERPRISE_034',
    //     httpCode: 401,
    //     reason: '用户未登录'
    //   }
    // }
    if(!req.query.areaCode || !req.query.type){
      throw {
        errorCode: 'E_COLL_LIST_040',
        httpCode: 406,
        reason: 'Invalid word'
      }
    }
    let condition = {
      areaCode:req.query.areaCode,
      tId:"5e55ec2e7f2aa8d78805f156",
      type:req.query.type,
      skip: req.query.skip || 0,
      limit: req.query.limit || 10
    };
    
    let opts = {
      // userid: req.user.userid
    };

    let result = await collectionMgrSvc.collectionLoanVillageList(condition, opts)
    debug(method, '[Exit](success)', result);
    res.status(200).send(result);
  } catch (error) {
    debug.error(method, '[Exit](failed)', error)
    res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
    res.status(error.httpCode || 500).send(error.message || error)
  }
}

  /**
  * @api {get} /api/v1.0/collection/loan/employee/list 采集单管理列表
  * @apiVersion 1.0.0
  * @apiName collectionLoanEmployeeList
  * @apiGroup Loan
  * @apiPermission authenticated 
  */
async collectionLoanEmployeeList(req, res) {
  let method = 'collectionLoanEmployeeList'
  debug(method, '[Enter]')
  try {
    // if (!req.user || !req.user.userid) {
    //   throw {
    //     errorCode: 'E_ENTERPRISE_034',
    //     httpCode: 401,
    //     reason: '用户未登录'
    //   }
    // }
    if(!req.query.areaCode){
      throw {
        errorCode: 'E_COLL_LIST_040',
        httpCode: 406,
        reason: 'Invalid word'
      }
    }
    let condition = {
      areaCode:req.query.areaCode
    };
    
    let opts = {
      // userid: req.user.userid
    };

    let result = await collectionMgrSvc.collectionLoanEmployeeList(condition, opts)
    debug(method, '[Exit](success)', result);
    res.status(200).send(result);
  } catch (error) {
    debug.error(method, '[Exit](failed)', error)
    res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
    res.status(error.httpCode || 500).send(error.message || error)
  }
}

  /**
  * @api {get} /api/v1.0/collection/loan/ranking/list 龙虎榜采集信息排行
  * @apiVersion 1.0.0
  * @apiName collectionLoanRankingList
  * @apiGroup Loan
  * @apiPermission authenticated 
  */
  async  collectionLoanRankingList(req, res) {
    let method = 'collectionLoanRankingList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_ENTERPRISE_034',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if(!req.query.areaCode){
        throw {
          errorCode: 'E_COLL_LIST_040',
          httpCode: 406,
          reason: 'Invalid word'
        }
      }
      let condition = {
        tId:'5e803414d213d0cd86069033',
        "startTime":req.query.startTime,
        "endTime":req.query.endTime,
        "areaCode":req.query.areaCode
      };
      
      let opts = {
        // userid: req.user.userid
      };

      let result = await collectionMgrSvc.collectionLoanRankingList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this;

    self._policyRouter.get('/api/v1.0/collection/mgr/list', self.collectionMgrList.bind(self), {
      name: 'collection.collectionMgrList',
      role: PERMISSION_ROLE.EVERYONE
    });
    
    self._policyRouter.get('/api/v1.0/collection/mgr/village/list', self.collectionMgrVillageList.bind(self), {
      name: 'collection.collectionMgrVillageList',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/collection/mgr/employee/list', self.collectionMgrEmployeeList.bind(self), {
      name: 'collection.collectionMgrEmployeeList',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/collection/loan/list', self.collectionLoanList.bind(self), {
      name: 'collection.collectionLoanList',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/collection/loan/village/list', self.collectionLoanVillageList.bind(self), {
      name: 'collection.collectionLoanVillageList',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/collection/loan/employee/list', self.collectionLoanEmployeeList.bind(self), {
      name: 'collection.collectionLoanEmployeeList',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/collection/loan/ranking/list', self.collectionLoanRankingList.bind(self), {
      name: 'collection.collectionLoanRankingList',
      role: PERMISSION_ROLE.EVERYONE
    });


    return self;
  }
}

module.exports = Router;
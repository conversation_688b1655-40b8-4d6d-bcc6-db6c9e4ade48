'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:app.api:routes:infoPersonal');
const infoPersonalService = require('../services/info_personal');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class InfoPersonal {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }
  
  /**
   * @api {post} /api/v1.0/info/personal/create 采集信息
   * @apiVersion 1.0.0
   * 
   */
  async createInfoPersonal(req, res) {
    let method = 'createInfoPersonal'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.module) {
        throw {
          errorCode: 'E_INFO_PERSONAL_25',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }
      if (req.body.module !== 'basic' && !req.body.mId) {
        throw {
          errorCode: 'E_INFO_PERSONAL_32',
          httpCode: 406,
          reason: 'mId null'
        }
      }
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_186',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = req.body;
      condition.operator = req.user.userid
      let opts = {};
      let result = await infoPersonalService.createInfoPersonal(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  
  /**
   * @api {post} /api/v1.0/info/personal/saveMainStatus 提交
   * @apiVersion 1.0.0
   * 
   */
  async saveMainStatus(req, res) {
    let method = 'saveMainStatus'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_186',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if(!req.body || !req.body.mId || !req.body.locate){
        throw {
          errorCode: 'E_MAINSTATUS_69',
          httpCode: 406,
          reason: 'mId or locate null'
        }
      }
      let condition = {
        mId : req.body.mId,
        locate : req.body.locate,
        source: req.body.source,
        operator : req.user.userid
      }
      let opts = {};
      let result = await infoPersonalService.saveMainStatus(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /**
   * @api {get} /api/v1.0/info/personal/list 采集信息列表
   * @apiVersion 1.0.0
   * 
   */
  async getMainList(req, res) {
    let method = 'getMainList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_186',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let condition = {
        skip: req.query.skip || 0,
        limit: req.query.limit || 10,
        archived: false
      };
      
      if (req.query.startTime) {
        let startTime = moment(req.query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.startTime = startTime;
      }
      if (req.query.endTime) {
        let endTime = moment(req.query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.endTime = endTime;
      }
      
      if (req.query.name) {
        condition.name =  req.query.name;
      }
      if (req.query.mainStatus) {
        condition.mainStatus = req.query.mainStatus;
      }
      condition.operator = req.user.userid;
      // condition.$sort = req.query.$sort || { createdTime: -1 };

      let opts = {
        // tId: req.Client && req.Client.tId,
        // user: req.user
      };

      let result = await infoPersonalService.getMainList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  /**
   * @api {get} /api/v1.0/info/personal/mainInfo 采集单状态页详情
   * @apiVersion 1.0.0
   * 
   */
  async getMainInfo(req, res) {
    let method = 'getMainInfo'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_109',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if(!req.query.id){
        throw {
          errorCode: 'E_LOAN_APP_116',
          httpCode: 406,
          reason: 'id null'
        }
      }
      
      let condition = req.query;

      let opts = {
      };

      let result = await infoPersonalService.mainInfo(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  /**
   * @api {get} /api/v1.0/info/personal/moduleInfo 采集单8项详情
   * @apiVersion 1.0.0
   * 
   */
  async getModuleInfo(req, res) {
    let method = 'getModuleInfo'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_109',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if(!req.query.id || !req.query.module){
        throw {
          errorCode: 'E_LOAN_APP_154',
          httpCode: 406,
          reason: 'parameter null'
        }
      }
      
      let condition = req.query;

      let opts = {
      };

      let result = await infoPersonalService.getModuleInfo(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  /**
   * @api {get} /api/v1.0/info/personal/getOption 获取选择项内容
   * @apiVersion 1.0.0
   * 
   */
  async getOption(req, res) {
    let method = 'getOption'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_109',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if(!req.query.module || !req.query.field ){
        throw {
          errorCode: 'E_LOAN_APP_194',
          httpCode: 406,
          reason: 'parameter null'
        }
      }
      
      let condition = req.query;

      let result = await infoPersonalService.getOption(condition)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }


  init() {
    let self = this;

    self._policyRouter.post('/api/v1.0/info/personal/create', self.createInfoPersonal.bind(self), {
      name: 'infoPersonal.createInfoPersonal',
      // role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.post('/api/v1.0/info/personal/saveMainStatus', self.saveMainStatus.bind(self), {
      name: 'infoPersonal.saveMainStatus',
      // role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/info/personal/list', self.getMainList.bind(self), {
      name: 'infoPersonal.getMainList',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/info/personal/mainInfo', self.getMainInfo.bind(self), {
      name: 'infoPersonal.mainInfo',
      // role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/info/personal/moduleInfo', self.getModuleInfo.bind(self), {
      name: 'infoPersonal.getModuleInfo',
      //role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/info/personal/getOption', self.getOption.bind(self), {
      name: 'infoPersonal.getOption',
      // role: PERMISSION_ROLE.EVERYONE
    })


    return self
  }
}

module.exports = InfoPersonal
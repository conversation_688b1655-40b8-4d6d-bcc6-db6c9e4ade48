/** 
 * newsRouter 
 * <AUTHOR>
 * 2019-10-18
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:loan_application_customer_visit');
const moment = require('moment');
const loanVillageInfoService = require('../services/loanVillageInfo');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const {assert,formatParas} = require('../utils/general');
const {cloneDeep} = require('lodash');
const areaService = require('../services/area')

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getVillageInfos({getOne,hadVisitTimes}, req, res) {
    const method = 'getVillageInfos';
    debug(method, '[Enter]')
    try {
      const opts = {getOne};
      assert(!getOne || req.query._id,'E_request_village_info_001','para _id is required')
      opts.uId = req.user && req.user.userid;
      opts.roleId = req.headers['x-role-id'];
      process.env.NODE_ENV ==='local-dev' && req.query.uId && ( opts.uId = req.query.uId);
      // assert(opts.uId,'E_EDIT_DISASTER_CLAIMS_002','PERMISSION ERROR');

      const config = [
        '_id',
        {from:'name',to:'name',rule:'contain'},
        // {from:'customerName',to:'basicInfo.customerName',rule:'contain'},
        {from:'areaCode',to:'areaCode',rule:'startWith'},
        {from:'skip',dv:0},
        {from:'limit',dv:'10'},
        {from:'$sort',dv:{ lastModTime: -1 },fs:'json'},
      ];

      const condition = {
        ...formatParas(config,req.query),
        archived: false,
      };

      req.query.sortByCreatedTime && ( condition.$sort.createdTime = parseInt( req.query.sortByCreatedTime ) );
      req.query.sortByLastModTime && ( condition.$sort.lastModTime = parseInt( req.query.sortByLastModTime ) );
      // opts.uId && ( condition.operator = opts.uId )
      const result = await loanVillageInfoService.list(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  async getAssistants({}, req, res) {
    const method = 'getAssistants';
    debug(method, '[Enter]')
    try {
      const result = await loanVillageInfoService.listAssistant(req.query, {});
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async loanVillageMyAreaInfo(req, res) {
    const method = 'loanVillageMyAreaInfo';
    debug(method, '[Enter]')
    try {
      const opts = {
        uId: req.user && req.user.userid ,
        roleId : req.headers['x-role-id'] ,
        tId:req.query.tId,
      }
      // opts.uId = '62396fb943a15040854830db';
      // opts.roleId = '5eb8fee1c6ecfe44d4ecaed1';
      debug(`${method}Opts`,opts);
      const result = await loanVillageInfoService.loanVillageMyAreaInfo({}, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  async createOrEditVillageInfo({edit}, req, res) {
    const method = 'createOrEditVillageInfo';
    debug(method, '[Enter]')
    try {
      const condition = cloneDeep(req.body),opts = {};
      opts.uId = req.user && req.user.userid;
      opts.roleId = req.headers['x-role-id'];
      process.env.NODE_ENV ==='local-dev' && req.body.uId && ( opts.uId = req.body.uId);
      edit && assert(condition._id,'E_EDIT_VILLAGE_INFO_001','_id is required');
      // assert(opts.uId,'E_EDIT_DISASTER_CLAIMS_002','PERMISSION ERROR');
      const result = await loanVillageInfoService.createOrEdit(condition,opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async loanVillageStatistics(req, res) {
    const method = 'loanVillageStatistics';
    debug(method, '[Enter]')
    try {
      const condition = cloneDeep(req.query),opts = {};
      opts.uId = opts.assistant = req.user && req.user.userid;//留出扩展，assistant将来可以用于传参，万一需求变了呢
      opts.roleId = condition.roleId || req.headers['x-role-id'];
      // opts.uId = req.user && req.user.userid;
      process.env.NODE_ENV ==='local-dev' && req.body.uId && ( opts.uId = req.body.uId);
      // assert(opts.uId,'E_EDIT_DISASTER_CLAIMS_002','PERMISSION ERROR');
      const result = await loanVillageInfoService.loanVillageStatistics(condition,opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {

    this._policyRouter.get('/api/v1.0/loan/village/info/list',
      (...paras)=>this.getVillageInfos({},...paras), {
      name: 'loan.village.info.list',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/loan/village/info/assistant',
        (...paras)=>this.getAssistants({hadVisitTimes:true},...paras), {
          name: 'loan.village.info.assistant.list',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.get('/api/v1.0/loan/village/info/my/area/info',
        (...paras)=>this.loanVillageMyAreaInfo(...paras), {
          name: 'loan.village.info.assistant.list',
          role: PERMISSION_ROLE.EVERYONE
        });


    this._policyRouter.get('/api/v1.0/loan/village/info/detail',
        (...paras)=>this.getVillageInfos({getOne:true},...paras), {
          name: 'loan.village.info.detail',
          role: PERMISSION_ROLE.EVERYONE
        });

    // this._policyRouter.get('/api/v1.0/loan/village/info/enum',
    //     (...paras)=>this.enumCustomerVisits(...paras), {
    //       name: 'loan.village.info.detail',
    //       role: PERMISSION_ROLE.EVERYONE
    //     })


    this._policyRouter.post('/api/v1.0/loan/village/info/add',
        (...paras)=>this.createOrEditVillageInfo({},...paras), {
          name: 'loan.village.info.add',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.put('/api/v1.0/loan/village/info/edit',
        (...paras)=>this.createOrEditVillageInfo({edit:true},...paras), {
          name: 'loan.village.info.edit',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.get('/api/v1.0/loan/village/statistics',
        (...paras)=>this.loanVillageStatistics(...paras), {
          name: 'loan.village.statistics',
          role: PERMISSION_ROLE.EVERYONE
        });


    // public api
    // this._policyRouter.get('/api/v1.0/area/list', this.getAreaList.bind(this), {
    //   name: 'area.getAreaList',
    //   role: PERMISSION_ROLE.EVERYONE
    // })
    //
    // this._policyRouter.post('/api/v1.0/area/info', this.getAreaInfo.bind(this), {
    //   name: 'area.getAreaList',
    //   role: PERMISSION_ROLE.EVERYONE
    // })

    return this;
  }
}

module.exports = Router;
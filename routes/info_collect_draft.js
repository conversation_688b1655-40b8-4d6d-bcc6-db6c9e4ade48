/**
 * User Router
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:info_collect_draft');
const infoCollectDraftSvc = require('../services/info_collect_draft');

const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;


class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async save(req, res) {
    let method = 'save'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if (!req.body.id || !req.body.content || !req.body.type) {
        throw {
          errorCode: 'E_DRAFT_034',
          httpCode: 401,
          reason: '缺少采集id/content'
        }
      }

      let condition = req.body;

      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };

      let result = await infoCollectDraftSvc.save(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getDraft(req, res) {
    let method = 'getDraft'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      let condition = { archived: false };

      if (req.query.aId) {
        condition.aId = req.query.aId;
      }
      if (req.query._id) {
        condition._id = req.query._id;
      }


      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };

      let result = await infoCollectDraftSvc.getDraft(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async approve(req, res) {
    let method = 'approve'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if (!req.body.id || !req.body.type || !req.body.source) {
        throw {
          errorCode: 'E_DRAFT_034',
          httpCode: 401,
          reason: '缺少采集id/type/source'
        }
      }

      let condition = req.body;

      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };

      let result = await infoCollectDraftSvc.approve(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }


  init() {
    let self = this;

    self._policyRouter.post('/api/v1.0/info/collect/draft/save', self.save.bind(self), {
      name: 'infoCollectDraft.save',
      // role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.get('/api/v1.0/info/collect/draft/getDraft', self.getDraft.bind(self), {
      name: 'infoCollectDraft.getDraft',
      role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.post('/api/v1.0/info/collect/draft/approve', self.approve.bind(self), {
      name: 'infoCollectDraft.approve',
      role: PERMISSION_ROLE.EVERYONE
    });

    return self;
  }
}

module.exports = Router;
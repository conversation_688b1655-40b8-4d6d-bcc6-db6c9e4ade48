'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:routes:assistanceAgent');
const assistanceAgentService = require('../services/assistanceAgent');

class AssistanceAgent {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }
  
  async getAssistanceAgentDetail(req, res) {
    const method = 'getAssistanceAgentDetail';
    debug(method, '[Enter]');
    try {
      const condition = {};
      if (req.query.id) {
        condition._id = req.query.id;
      } else {
        condition.agentId = req.user.userid;
        condition.tId = req.Client.tId;
      }
      const opts = { userid: req.user.userid };
      const result = await assistanceAgentService.getAssistanceAgentDetail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {put} /api/v1.0/assistance-agent 修改坐席状态
   * @apiVersion 1.0.0
   * @apiName updateAssistanceAgent
   * @apiGroup AssistanceAgent
   * @apiPermission authenticated
   *
   * @apiDescription 修改坐席状态
   */
  async updateAssistanceAgent(req, res) {
    const method = 'updateAssistanceAgent';
    debug(method, '[Enter]');
    try {
      const condition = req.body || {};
      condition.userId = req.user.userid;

      const opts = {
        userid: req.user.userid,
        tId: req.Client.tId,
      };

      const result = await assistanceAgentService.updateAssistanceAgent(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    const self = this;

    self._policyRouter.get('/api/v1.0/assistance-agent/detail', self.getAssistanceAgentDetail.bind(self), {
      name: 'assistanceAgent.getAssistanceAgentDetail',
    });

    self._policyRouter.put('/api/v1.0/assistance-agent', self.updateAssistanceAgent.bind(self), {
      name: 'assistanceAgent.updateAssistanceAgent',
    });

    return self;
  }
}

module.exports = AssistanceAgent;

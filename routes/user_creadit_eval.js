/**
 * <AUTHOR>
 * 2021-05-31
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:user_creadit_eval');
const userCreaditEvalSvc = require('../services/user_creadit_eval');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const moment = require('moment');

class UserCreaditEval {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  // 首页Num
  async getIndexNumber(req, res) {
    let method = 'getIndexNumber'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_USEREVAL_30',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let query = req.query;
      let condition = {
      };
      let opts = {
        userInfo: req.user,
        roleId : query.roleId
      };

      let result = await userCreaditEvalSvc.getIndexNumber(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 待办已办列表
  async getList(req, res) {
    let method = 'getList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_USEREVAL_053',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let query = req.query;
      let condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        archived: false,
        $sort: {
          createdTime: -1
        }
      }
      if(query.sort == '1'){
        condition['$sort'] =  {
          createdTime: 1
        }
      }
      if(query.sort == '2'){
        condition['$sort'] =  {
          lastModTime: -1
        }
      }
      if(query.sort == '3'){
        condition['$sort'] =  {
          lastModTime: 1
        }
      }
      if (query.areaCode) {
        condition.areaCode = `/^${query.areaCode}/`
      }
      
      if (query.type == '1') { //待办
        condition.creditStatus = {$nin:[1,2,3]}
      }else if (query.type == '2') { //已办
        condition.creditStatus = {$in:[1,2,3]}
      }
      if (query.startTime && query.type == '1') {
        let startTime = moment(query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (query.endTime && query.type == '1') {
        let endTime = moment(query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      if (query.startTime && query.type == '2') {
        let startTime = moment(query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.lastModTime = condition.lastModTime || {};
        condition.lastModTime.$gte = startTime;
      }
      if (query.endTime && query.type == '2') {
        let endTime = moment(query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.lastModTime = condition.lastModTime || {};
        condition.lastModTime.$lte = endTime;
      }
      if (query.type == '2' && query.creaditType) { //待办
        condition.creditStatus = ~~query.creaditType;
      }
      
      let opts = {
        userInfo: req.user,
        roleId : query.roleId
      };

      let result = await userCreaditEvalSvc.getList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 待办已办详情
  async getInfo(req, res) {
    let method = 'getInfo'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_USEREVAL_053',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query.id) {
        throw {
          errorCode: 'E_USEREVAL_120',
          httpCode: 406,
          reason: 'id err'
        }
      }
      let id = req.query.id;
      
      let opts = {
        userInfo: req.user
      };

      let result = await userCreaditEvalSvc.getInfo(id, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  
  // 评定保存
  async evalSave(req, res) {
    let method = 'evalSave'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_USEREVAL_113',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if(!req.body || !req.body.id || !req.body.maritalStatus || !req.body.isPoverty || !req.body.isBadHabit || !req.body.isSeriousDisease || !req.body.isPrivateLending || !req.body.isOtherLocality){
        throw {
          errorCode: 'E_USEREVAL_120',
          httpCode: 406,
          reason: 'id cannot be empty'
        }
      }
      let {
        id,
        maritalStatus,
        isPoverty,
        isBadHabit,
        isSeriousDisease,
        isPrivateLending,
        isOtherLocality,
        status = 'finished'
      } = req.body;
      let condition = {
        operator:req.user.userid,
        id,
        maritalStatus: ~~maritalStatus,
        isPoverty: ~~isPoverty,
        isBadHabit: ~~isBadHabit,
        isSeriousDisease: ~~isSeriousDisease,
        isPrivateLending: ~~isPrivateLending,
        isOtherLocality: ~~isOtherLocality,
        status,
      };
      let opts = {
        action : 'add'
      };

      let result = await userCreaditEvalSvc.evalUpdate(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  
  // 评定编辑
  async evalUpdate(req, res) {
    let method = 'evalUpdate'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_USEREVAL_113',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if(!req.body || !req.body.id || !req.body.maritalStatus || !req.body.isPoverty || !req.body.isBadHabit || !req.body.isSeriousDisease || !req.body.isPrivateLending || !req.body.isOtherLocality){
        throw {
          errorCode: 'E_USEREVAL_120',
          httpCode: 406,
          reason: 'id cannot be empty'
        }
      }
      let {
        id,
        maritalStatus,
        isPoverty,
        isBadHabit,
        isSeriousDisease,
        isPrivateLending,
        isOtherLocality,
        status = 'finished'
      } = req.body;
      let condition = {
        operator:req.user.userid,
        id,
        maritalStatus: ~~maritalStatus,
        isPoverty: ~~isPoverty,
        isBadHabit: ~~isBadHabit,
        isSeriousDisease: ~~isSeriousDisease,
        isPrivateLending: ~~isPrivateLending,
        isOtherLocality: ~~isOtherLocality,
        status,
      };
      let opts = {
        action : 'update'
      };

      let result = await userCreaditEvalSvc.evalUpdate(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  
  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/creadit/eval/index', self.getIndexNumber.bind(self), {
      name: 'UserCreaditEval.getIndexNumber',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/creadit/eval/list', self.getList.bind(self), {
      name: 'UserCreaditEval.getList',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/creadit/eval/info', self.getInfo.bind(self), {
      name: 'UserCreaditEval.getList',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.put('/api/v1.0/creadit/eval/save', self.evalSave.bind(self), {
      name: 'UserCreaditEval.evalSave',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.put('/api/v1.0/creadit/eval/update', self.evalUpdate.bind(self), {
      name: 'UserCreaditEval.evalUpdate',
      role: PERMISSION_ROLE.EVERYONE
    })
    
    return self
  }
}

module.exports = UserCreaditEval
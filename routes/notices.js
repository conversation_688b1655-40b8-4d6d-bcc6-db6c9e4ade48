/**
 * @description: area routes 
 * @author: hexu 
 */

'use strict'

const logFactory = require('../utils/logFactory')
const logUtil = require('../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:dashboard.api:routes:area')
const noticesSvc = require('../services/notices')
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE
const moment = require('moment');

class Notices {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  // 协理员公告列表
  async getNoticesList(req, res) {
    let method = 'getNoticesList'
    debug(method, '[Enter]')
    try {
      let query = req.query;
      let condition = {
        limit: query.limit || 10,
        skip: query.skip || 0,
        status: "release_succee",
        archived: false,
        $sort: {
          level: -1,
          weight: -1,
          createdTime: -1,
        }
      }
      let opts = {
        userInfo: req.user,
        tId: req.Client.tId,
        roleId: req.headers['x-role-id'],
      };
      let result = await noticesSvc.getNoticesList(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 详情
  async getNoticesDetail(req, res) {
    let method = 'getNoticesDetail'
    debug(method, '[Enter]')
    try {
      let query = req.query;
      if (!query || !query.id) {
        throw {
          httpCode: 406,
          errorCode: 'E_NOTICES_DETAIL_063',
          reason: 'invalid param'
        }
      }
      let condition = {
        id: req.query.id
      }
      let opts = {

      };
      let result = await noticesSvc.getNoticesDetail(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this

    // public api 
    self._policyRouter.get('/api/v1.0/notices/list', self.getNoticesList.bind(self), {
      name: 'Notices.getNoticesList',
      // role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/notices/detail', self.getNoticesDetail.bind(self), {
      name: 'Notices.getNoticesDetail',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = Notices
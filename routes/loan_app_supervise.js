/**
 * newsRouter
 * <AUTHOR>
 * 2019-10-18
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:land_origin_fund_receive');
const loanAppSuperviseService = require('../services/loanAppSupervise');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const { assert, formatParas } = require('../utils/general')
const { cloneDeep } = require('lodash');

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async appSuperviseAdd(opts, req, res) {
    const method = 'editLandOriginFundReceive'
    debug(method, '[Enter]', req.body)
    try {
      const opts = {};
      process.env.NODE_ENV === 'local-dev' && req.body.uId && (opts.uId = req.body.uId);
      req.user && (opts.uId = req.user.userid);
      assert(opts.uId, 'E_APP_SUPERVISE_ADD_REQUEST_001', 'no permission')

      const config = [
        'id', 'superviseInfo',
      ];
      const condition = cloneDeep(formatParas(config, req.body));

      const result = await loanAppSuperviseService.loanAppSuperviseAdd(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async grainSaleAdd(opts, req, res) {
    const method = 'editLandOriginFundReceive'
    debug(method, '[Enter]', req.body)
    try {
      const opts = {};
      process.env.NODE_ENV === 'local-dev' && req.body.uId && (opts.uId = req.body.uId);
      req.user && (opts.uId = req.user.userid);
      assert(opts.uId, 'E_APP_SUPERVISE_ADD_REQUEST_001', 'no permission')

      const config = [
        'sale',
      ];
      const condition = cloneDeep(formatParas(config, req.body));

      const result = await loanAppSuperviseService.loanAppGrainSaleAdd(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async grainSaleList({ getOne }, req, res) {
    const method = 'grainSaleList';
    debug(method, '[Enter]')
    try {

      assert(!getOne || req.query._id, 'E_APP_FUND_RECEIVE_001', 'para _id is required')
      process.env.NODE_ENV !== 'local-dev' && delete req.query.uId;
      process.env.NODE_ENV !== 'local-dev' && delete req.query.tId;
      req.user && (req.query.uId = req.user.userid);
      // req.user && (req.query.tId = req.Client && req.Client.tId);
      // assert(req.query.uId, 'E_GRAIN_SALE_LIST_001', 'uId is required')
      const config = [
        'aId', '_id',
        'requestMobile', 'appSn',
        { from: 'requestName', rule: 'contain' },
        { from: 'requestMobile', rule: 'eq' },
        // {from:'requestCompanyName',rule:'eq'},//contain
        { from: 'requestAreaCode', rule: 'startWith' },
        // {from:'sn',rule:'eq'},
        // {from:'appSn',rule:'eq'},//startWith
        { from: 'cropType', rule: 'eq' },
        { from: 'saleType', rule: 'eq' },
        { from: 'settlementType', rule: 'eq' },
        { from: 'status', rule: 'eq' },
        { from: 'saleType', rule: 'eq' },
        { from: 'saleCount', rule: 'eq' },
        { from: 'createdTimeStart', to: 'createdTime', rule: 'gte', fs: 'toMinDay' },
        { from: 'createdTimeEnd', to: 'createdTime', rule: 'lte', fs: 'toMaxDay' },
        { from: 'saleTimeStart', to: 'saleTime', rule: 'gte', fs: 'toMinDay' },
        { from: 'saleTimeEnd', to: 'saleTime', rule: 'lte', fs: 'toMaxDay' },
        { from: 'skip', dv: 0 },
        { from: 'limit', dv: '10' },
        { from: '$sort', dv: { createdTime: -1 }, fs: 'json' },
      ];
      const opts = {
        getOne,
        uId: req.query.uId,
      };
      opts.uId = req.user && req.user.userid;
      opts.roleId = req.query.roleId || req.headers['x-role-id'];
      opts.tId = req.query.tId || req.Client && req.Client.tId ;

      const condition = {
        ...formatParas(config, req.query),
        archived: false,
      };
      const result = await loanAppSuperviseService.loanAppGrainList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} `);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {

    this._policyRouter.post('/api/v1.0/loan/application/supervise',
      (...paras) => this.appSuperviseAdd({}, ...paras), {
        name: 'fund.app.supervise.add',
        role: PERMISSION_ROLE.EVERYONE
      });

    this._policyRouter.post('/api/v1.0/loan/application/grain/sale/add',
      (...paras) => this.grainSaleAdd({}, ...paras), {
        name: 'fund.app.supervise.add',
        role: PERMISSION_ROLE.EVERYONE
      });

    this._policyRouter.get('/api/v1.0/loan/application/grain/sale/list',
      (...paras) => this.grainSaleList({}, ...paras), {
        name: 'fund.app.supervise.add',
        role: PERMISSION_ROLE.EVERYONE
      });

    this._policyRouter.get('/api/v1.0/loan/application/grain/sale/detail',
      (...paras) => this.grainSaleList({ getOne: true }, ...paras), {
        name: 'fund.app.supervise.add',
        role: PERMISSION_ROLE.EVERYONE
      });


    return this;
  }
}

module.exports = Router;
/*
 * @Author: qays
 * @Date: 2023-02-01 11:39:09
 * @LastEditTime: 2023-04-19 10:28:54
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.mgr.app.api\routes\user.js
 */
'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:routes:villager');
const userService = require('../services/user');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class LoanBill {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getUserDetail(req, res) {
    let method = 'getUserDetail';
    debug(method, '[Enter]');
    try {
      let condition = { uId: req.query.uId };

      let result = await userService.getUserDetail(condition, {});
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // todo: 等待删除
  async getTmpSysUserDetail(req, res) {
    let method = 'getTmpSysUserDetail';
    debug(method, '[Enter]');
    try {
      let condition = { uId: req.query.uId };

      let result = await userService.getTmpSysUserDetail(condition, {});
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getSm4Info(req, res) {
    let method = 'getSm4Info';
    debug(method, '[Enter]');
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_USER_1706',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {};

      let opts = {
        uId: req.user.userid
      };

      let result = await userService.getSm4Info(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this;

    self._policyRouter.get('/api/v1.0/user/detail', self.getUserDetail.bind(self), {
      name: 'user.getUserDetail',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/tmp/sys/user/detail', self.getTmpSysUserDetail.bind(self), {
      name: 'user.getTmpSysUserDetail',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/users/sm4/info', self.getSm4Info.bind(self), {
      name: 'user.getSm4Info',
    })

    return self
  }
}

module.exports = LoanBill
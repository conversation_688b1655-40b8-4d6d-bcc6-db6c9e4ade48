'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:cgbcai');
const cgbcAiSvc = require('../services/cgbcAi');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class Ai {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }


  async getCheckList(req, res) {
    let method = 'getCheckList';
    debug(method, '[Enter]');
    try {
      const { areaCode } = req.query || {};
      if(!areaCode) {
        throw {
          errorCode: 'E_CGBC_111',
          httpCode: 406,
          reason: 'Invalid Param'
        }
      }
      const result = await cgbcAiSvc.getCheckList({areaCode}, {});
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getUpdatePower(req, res) {
    let method = 'getUpdatePower';
    debug(method, '[Enter]');
    try {
      const { id } = req.query || {};
      if(!id) {
        throw {
          errorCode: 'E_CGBC_235',
          httpCode: 406,
          reason: 'Invalid Param'
        }
      }
      const result = await cgbcAiSvc.getUpdatePower({id}, {});
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {

    this._policyRouter.get('/api/v1.0/nongjin/ai/get/checkList', this.getCheckList.bind(this), {
      name: 'nongjin.ai.getCheckList',
    });

    this._policyRouter.get('/api/v1.0/nongjin/ai/get/update/power', this.getUpdatePower.bind(this), {
      name: 'nongjin.ai.getUpdatePower',
    });

    return this;
  }
}

module.exports = Ai;
/**
 * <AUTHOR>
 * 2019-05-05
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:client');
const organizationSvc = require('../services/organization');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class Organization {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  // 获取组织架构
  async getOrganization(req, res) {
    let method = 'getOrganization'
    debug(method, '[Enter]')
    try {
      if (!req.query || !req.query.code) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_065',
          reason: 'invalid param'
        };
      }
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_NEWS_CREATE_106',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let condition = {
        code: req.query.code
      };
      let opts = {
        uId: req.user.userid
      };

      let result = await organizationSvc.getOrganization(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/organization', self.getOrganization.bind(self), {
      name: 'organization.getOrganization',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = Organization
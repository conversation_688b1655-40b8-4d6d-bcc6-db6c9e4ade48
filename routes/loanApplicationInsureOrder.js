'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:loanApplicationInsureOrder');
const loanApplicationInsureOrderService = require('../services/loanApplicationInsureOrder');

class LoanApplicationInsureOrder {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
   * @api {get} /api/v1.0/loan-application-insure-order/list 获取保单列表
   * @apiVersion 1.0.0
   * @apiName getLoanApplicationInsureOrderList
   * @apiGroup LoanApplicationInsureOrder
   * @apiPermission authenticated
   *
   * @apiDescription 获取列表
   *
   * @apiParam {Number} skip
   * @apiParam {Number} limit
   *
   * @apiSuccess {Number} total 总条数
   * @apiSuccess {Object[]} result 结果数组
   * @apiSuccess {String} result._id 记录_id
   */
  async getLoanApplicationInsureOrderList(req, res) {
    const method = 'getLoanApplicationInsureOrderList';
    debug(method, '[Enter]');
    try {
      const condition = {};
      condition.tId = req.Client.tId;
      condition.operator = req.user.userid;
      condition.skip = req.query.skip || 0;
      condition.limit = req.query.limit || 10;
      condition.archived = false;
      if (req.query.$sort == 1) {
        condition.$sort = { payDate: 1, createdTime: 1 };
      } else {
        condition.$sort = { payDate: -1, createdTime: -1 };
      }

      if (req.query.status) {
        condition.status = req.query.status;
      }

      const opts = {
        userid: req.user.userid,
      };

      const result = await loanApplicationInsureOrderService.getLoanApplicationInsureOrderList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {get} /api/v1.0/loan-application-insure-order/detail 获取指定的保单详情
   * @apiVersion 1.0.0
   * @apiName getLoanApplicationInsureOrderDetail
   * @apiGroup Queue
   * @apiPermission authenticated
   *
   * @apiDescription 获取详情
   *
   * @apiParam {String} id _id
   *
   * @apiSuccess {String} _id 记录_id
   */
  async getLoanApplicationInsureOrderDetail(req, res) {
    const method = 'getLoanApplicationInsureOrderDetail';
    debug(method, '[Enter]');
    try {
      const condition = {
        id: req.query.id,
      };
      const opts = {
        userid: req.user.userid,
      };

      const result = await loanApplicationInsureOrderService.getLoanApplicationInsureOrderDetail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    const self = this;

    self._policyRouter.get('/api/v1.0/loan-application-insure-order/list', self.getLoanApplicationInsureOrderList.bind(self), {
      name: 'loanApplicationInsureOrder.getLoanApplicationInsureOrderList',
    });
    self._policyRouter.get('/api/v1.0/loan-application-insure-order/detail', self.getLoanApplicationInsureOrderDetail.bind(self), {
      name: 'loanApplicationInsureOrder.getLoanApplicationInsureOrderDetail',
    });

    return self;
  }
}

module.exports = LoanApplicationInsureOrder;

/**
 * <AUTHOR>
 * @description 提供postgis查询服务
 */

'use strict';
const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:gis');
const moment = require('moment');
const gisService = require('../services/gis');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const GROUP_ROLE = require('../services/permission').GROUP_ROLE
const env = require('../services/env');

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }


  /**
   * @api {post} /api/v1.0/gis/coordinate/byland 按土地编码批量查询坐标信息
   * @apiVersion 1.0.0
   * @apiName getCoordinateByLand
   * @apiGroup gis
   * @apiPermission authenticated
   */
  async getCoordinateByLand(req, res) {
    let method = 'getCoordinateByLand'
    debug(method, '[Enter]')
    try {

      if (!req.body.landCode || !req.body.dbname || !Array.isArray(req.body.landCode)) {
        throw {
          errorCode: 'E_COORDINATE_041',
          httpCode: 406,
          reason: 'invalid params'
        }
      }

      let condition = {
        landCode: req.body.landCode,
        dbname: req.body.dbname
      }


      let opts = {};

      let result = await gisService.getCoordinateByLand(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }


  /**
   * @api {post} /api/v1.0/gis/areacode/parse 按areacode编码查询地址信息
   * @apiVersion 1.0.0
   * @apiName parseAreacode
   * @apiGroup gis
   * @apiPermission authenticated
   */
  async parseAreacode(req, res) {
    let method = 'parseAreacode'
    debug(method, '[Enter]')
    try {
      console.log('parseAreacodequery', req.query)
      if (!req.query.areacode) {
        throw {
          errorCode: 'E_COORDINATE_041',
          httpCode: 406,
          reason: 'invalid params'
        }
      }

      let condition = {
        areaCode: req.query.areacode,
      }


      let opts = {};

      let result = await gisService.parseAreaCode(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }


  init() {
    let self = this;

    self._policyRouter.post('/api/v1.0/gis/coordinate/byland', self.getCoordinateByLand.bind(self), {
      name: 'gis.getCoordinateByLand',
      // role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/gis/areacode/parse', self.parseAreacode.bind(self), {
      name: 'gis.parseAreacode',
      // role: PERMISSION_ROLE.EVERYONE
    })

    return self;
  }
}

module.exports = Router;
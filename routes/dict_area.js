/**
 * @description: area routes
 * @author: hexu 
 */

'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:routes:area');
const areaService = require('../services/dict_area');
const formatAreaCode = require('../persistence/formatAreaCode');

class Area {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }
  /**
   * @api {get} /api/v1.0/dict/area/list 获取区域列表
   * @apiVersion 1.0.0
   * @apiName GetAreaList
   * @apiGroup Area 
   * @apiPermission authenticated 
   *
   * @apiDescription 获取区域列表，version 1.0.0 兼容错误的areaCode。
   * 
   * @apiParam {String}    current   当前areaCode
   * @apiParam {String}    subLevel  向下查询级别
   * @apiParam {String}    upLevel   向上查询级别
   * @apiParam {String}    traversal 是否遍历查询，默认false
   * 
   * @apiParamExample {url} Request-Params-Example:
   * ?current=150125&subLevel=2&traversal=true
   *
   * @apiExample Example usage:
   * curl -i http://localhost/api/v1.0/dict/area/list
   *
   * @apiSuccess {Object[]}       result           区域列表
   * @apiSuccess {String}         result.id       区域编码
   * @apiSuccess {String}         result.name      区域名称
   * @apiSuccess {Object[]}       result.children  下级区域列表
   * 
   * @apiSuccessExample {json} Response (example):
   * 
   *     HTTP/1.1 200 OK
   *    {
   *       "result": [
   *           {
   *               "id": "150125403",
   *               "name": "武川金三角开发区",
   *               "children": [
   *                   {
   *                       "id": "150125403498",
   *                       "name": "武川县金山角开发区虚拟社区"
   *                   }
   *               ]
   *           },
   *           ...
   *       ]
   *    }

   * @apiError ErrorQuery error params
   * 
   * @apiErrorExample Response (example):
   *     HTTP/1.1 406 ErrorQuery
   *     {
   *       "errorCode": "EAQUERY061",
   *       "reason": "too large subLevel"
   *     }
   */
  getAreaList(req, res) {
    let method = 'getAreaList'
    debug(method, '[Enter]')
    try {
      let condition = {
        limit: req.query.limit || 10,
        skip: req.query.skip || 0,
        archived: false
      }
      let opts = {}

      if (req.query.current)
        condition.current = req.query.current

      if (req.query.subLevel)
        condition.subLevel = req.query.subLevel

      if (req.query.upLevel)
        condition.upLevel = req.query.upLevel

      condition.traversal = req.query.traversal || false

      areaService.getAreaList(condition, opts).then(data => {
        debug(method, '[Exit](success)', data)
        res.status(200).send(data)
      }).fail(error => {
        debug.error(method, '[Exit](failed)', error)
        res.set('Warning', `199 - ${error.errorCode || 'EAQUERY061'} ${new Date()}`)
        res.status(error.httpCode || 500).send(error.message || error)
      })
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || 'EAQUERY061'} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  /**
   * @api {get} /api/v1.0/dict/area/byCodes 采集单管理列表
   * @apiVersion 1.0.0
   * @apiName collectionMgrEmployeeList
   * @apiGroup Loan
   * @apiPermission authenticated 
   */
  async areaListByCodes(req, res) {
    let method = 'areaListByCodes'
    debug(method, '[Enter]')
    try {
      if (!req.query.areaCode) {
        throw {
          errorCode: 'E_DICT_CODE_120',
          httpCode: 406,
          reason: 'Invalid areaCode'
        }
      }
      let condition = {
        limit: req.query.limit || 10,
        skip: req.query.skip || 0,
        areaCode: req.query.areaCode
      };

      let opts = {};

      let result = await areaService.areaListByCodes(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async areaByCode(req, res) {
    let method = 'areaByCode'
    debug(method, '[Enter]')
    try {
      if (!req.query.areaCode) {
        throw {
          errorCode: 'E_DICT_CODE_166',
          httpCode: 406,
          reason: 'Invalid areaCode'
        }
      }

      const result = await formatAreaCode.getFormatAreaCode(req.query.areaCode)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this

    // public api 
    self._policyRouter.get('/api/v1.0/dict/area/list', self.getAreaList.bind(self), {
      name: 'area.getAreaList'
    })

    self._policyRouter.get('/api/v1.0/dict/area/byCodes', self.areaListByCodes.bind(self), {
      name: 'area.getAreaList'
    });

    self._policyRouter.get('/api/v1.0/dict/area/code', self.areaByCode.bind(self), {
      name: 'area.areaByCode'
    });

    return self
  }
}

module.exports = Area
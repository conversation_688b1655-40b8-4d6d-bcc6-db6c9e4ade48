/**
 * <AUTHOR>
 * 2020-01-06 
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:loan_assistant');
const loanAssistantSvc = require('../services/loan_assistant');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const moment = require('moment');
class LoanAssistant {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  // 获取进件列表
  async getAssistantLoanList(req, res) {
    let method = 'getAssistantLoanList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_30',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let query = req.query;

      let condition = {
        limit: query.limit || 10,
        skip: query.skip || 0,
        source: "spring_app",
        // tId: req.Client && req.Client.tId,
        archived: false,
        $sort: {
          createdTime: -1
        }
      };
      if (query.username) condition.username = `/${query.username}/`;
      if (query.pId) condition.pId = query.pId;
      if (query.area) {
        condition.area = `/^${query.area}/`;
      }
      let opts = {
        userInfo: req.user,
        type: query.type,
        start: query.start,
        end: query.end
      };

      let result = await loanAssistantSvc.createApplicationFactory.get(opts.type).call(this, condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  // 首页统计
  async getStatisticsLoan(req, res) {
    let method = 'getStatisticsLoan'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_63',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let condition = {
        // tId: req.Client && req.Client.tId,
        source: "spring_app",
        archived: false,
        $sort: {
          createdTime: -1
        }
      };
      let opts = {
        userInfo: req.user,
      };

      let result = await loanAssistantSvc.getStatisticsLoan(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 补充资料
  async supplementInformation(req, res) {
    let method = 'supplementInformation'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_63',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.body || !req.body.productType || !req.body.id) {
        throw {
          errorCode: 'E_supplementInformation_63',
          httpCode: 406,
          reason: 'productType is required'
        }
      }

      let condition = {
        productType: req.body.productType
      };
      let opts = {
        id: req.body.id,
        userid: req.user.userid
      };
      delete req.body.productType;
      delete req.body.id;
      condition.addons = req.body


      let result = await loanAssistantSvc.supplementInformation(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  async getLoanDetail(req, res) {
    let method = 'getLoanDetail'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_63',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query || !req.query.id) {
        throw {
          errorCode: 'E_supplementInformation_63',
          httpCode: 406,
          reason: 'id is required'
        }
      }
      let condition = {
        id: req.query.id
      };
      let opts = {};

      let result = await loanAssistantSvc.getLoanDetail(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/assistant/list', self.getAssistantLoanList.bind(self), {
      name: 'distribute.getAssistantLoanList',
    })
    self._policyRouter.get('/api/v1.0/assistant/statistics/count', self.getStatisticsLoan.bind(self), {
      name: 'distribute.getStatisticsLoan',
    })
    self._policyRouter.put('/api/v1.0/supplement/information', self.supplementInformation.bind(self), {
      name: 'distribute.supplementInformation',
    })
    self._policyRouter.get('/api/v1.0/loan/detail', self.getLoanDetail.bind(self), {
      name: 'distribute.getLoanDetail',
    })
    return self
  }
}

module.exports = LoanAssistant
/**
 * @summary loanApplication routes
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:routes:loanApplication');
const loanApplicationService = require('../services/loan_application_v2');

const moment = require('moment');
const Permission = require('../services/permission');
const isChinese = /^[\u4e00-\u9fa5]+/;
const LOAN_APP_STATUS = require('../utils/const/applicationConst').LOAN_APP_STATUS;
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

const REMAINING_TIME_SELECT = new Map([
  [0, '全部'],
  [1, '180-365天 （包含）'],
  [2, '90天-180天（包含）'],
  [3, '30-90天（包含）'],
  [4, '0-30天（包含）'],
  [-1, '已逾期'],
]);

class LoanApplication {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }



  // 认领订单
  async destineLoanApplication(req, res) {
    let method = 'destineLoanApplication'
    debug(method, '[Enter]')
    try {

      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_186',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      if (!req.body || !req.body.id) {
        throw {
          errorCode: 'E_LOAN_APP_1265',
          httpCode: 406,
          reason: 'invalid params'
        }
      }

      let condition = {
        id: req.body.id
      };

      let opts = {
        uId: req.user.userid
      };
      if (req.headers && req.headers['x-dash-role']) {
        opts.role = req.headers['x-dash-role'];
      }
      let result = await loanApplicationService.destineLoanApplication(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  // 取消认领
  async cancelDestineLoanApplication(req, res) {
    let method = 'cancelDestineLoanApplication'
    debug(method, '[Enter]')
    try {

      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_186',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      if (!req.body || !req.body.id) {
        throw {
          errorCode: 'E_LOAN_APP_1265',
          httpCode: 406,
          reason: 'invalid params'
        }
      }

      let condition = {
        id: req.body.id
      };

      let opts = {
        uId: req.user.userid
      };
      if (req.headers && req.headers['x-dash-role']) {
        opts.role = req.headers['x-dash-role'];
      }
      let result = await loanApplicationService.cancelDestineLoanApplication(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 初审审批
  async approvedLoanApplication(req, res) {
    let method = 'approvedLoanApplication'
    debug(method, '[Enter]')
    try {

      if (!req.body.id || !req.body.amount || !req.body.status) {
        throw {
          errorCode: 'E_LOAN_APP_309',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }
      let condition = {
        id: req.body.id,
        status: req.body.status,
        body: {
          amount: req.body.amount * 100,
        }
      };
      if (req.body.status === "loaned") {
        condition.loaned = req.body.loaned;
      }
      if (req.body.status === "credit_access" && (!req.body.bankCard || !req.body.bankMobile || !req.body.realname || !req.body.IDCard)) {
        throw {
          errorCode: 'E_LOAN_APP_133',
          httpCode: 406,
          reason: 'bankMobile bankCard empty'
        }
      }
      if (req.body.bankCard) {
        condition.bankCard = req.body.bankCard;
      }
      if (req.body.bankMobile) {
        condition.bankMobile = req.body.bankMobile;
      }
      if (req.body.comments) {
        condition.comments = req.body.comments;
      }
      let opts = {
        user: req.user,
        tId: req.Client && req.Client.tId,
      };

      let result = await loanApplicationService.approvedLoanApplication(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  // 审批拒绝
  async rejectLoanApplication(req, res) {
    let method = 'rejectLoanApplication'
    debug(method, '[Enter]')
    try {

      if (!req.body.id || !req.body.status) {
        throw {
          errorCode: 'E_LOAN_APP_521',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }
      let targetStatus = req.body.status;
      let condition = {
        id: req.body.id,
        comments: req.body.comments,
        body: {
          status: targetStatus
        }
      };
      let opts = {
        user: req.user,
        status: targetStatus,
        templateId: '5c133731fc9aedd8f1dc644f',
        trackingOpt: {
          src_t: "staff",
          source: req.user.userid,
          target_t: 1,
          target: req.body.id,
          action: targetStatus,
          comments: req.body.comments || ''
        }
      };

      let result = await loanApplicationService.rejectLoanApplication(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 根据不同角色获取订单列表
  async getLoanApplicationList(req, res) {
    let method = 'getLoanApplicationList'
    debug(method, '[Enter]')
    try {

      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_186',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let query = req.query;
      let condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        tId: req.Client && req.Client.tId,
        archived: false,
        $sort: {
          createdTime: -1
        }
      };
      if (query.sn) condition.sn = `/${query.sn}/`;
      if (query.outTradeNo) condition.outTradeNo = `/${query.outTradeNo}/`;
      if (query.username) condition.username = `/${query.username}/`;
      if (query.userMobile) condition.userMobile = `/${query.userMobile}/`;
      if (query.status) condition.status = query.status;
      if (query.loanTerm) condition.loanTerm = query.loanTerm * 1;
      if (query.pId) condition.pId = query.pId;
      if (query.startTime) {
        let startTime = moment(query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (query.endTime) {
        let endTime = moment(query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      if (query.lastStartTime) {
        let startTime = moment(query.lastStartTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.lastModTime = condition.lastModTime || {};
        condition.lastModTime.$gte = startTime;
      }

      if (query.lastEndTime) {
        let endTime = moment(query.lastEndTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.lastModTime = condition.lastModTime || {};
        condition.lastModTime.$lte = endTime;
      }
      if (query.areaCode) {
        condition.area = `/^${query.areaCode}/`
      }
      if (query.sort === "1") {
        condition.$sort = {
          lastModTime: 1
        }
      }

      if (query.sort === "2") {
        condition.$sort = {
          lastModTime: -1
        }
      }

      if (condition.status === "rejected") {
        condition.status = {
          $regex: '^rejected',
          $options: 'i'
        }
      }
      if (condition.status === "completing_info") {
        condition.status = {
          $in: ['biopsy_approved', 'decline_censor']
        };
      }
      if (condition.status === "wait_destine") {
        condition.status = {
          $in: ['pre_censor', 'pre_approve']
        };
      }
      //查询认领订单
      if (req.query.destined && req.query.destined == -1) {
        condition.destined = false;
      }


      let opts = {
        userInfo: req.user,
        tId: req.Client.tId,
      };

      if (query.operator) {
        opts.operator = query.operator;
      }
      if (req.headers && req.headers['x-dash-role']) {
        opts.role = req.headers['x-dash-role'];
      } else if (req.headers && req.headers['role']) {
        opts.role = req.headers['role'];
      }
      // opts.role = req.headers && req.headers['x-dash-role'] || req.headers['role'];

      let result = await loanApplicationService.getLoanApplicationList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  // 已认领订单列表
  async getLoanApplicationDestinedList(req, res) {
    let method = 'getLoanApplicationDestinedList'
    debug(method, '[Enter]')
    try {

      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_186',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let query = req.query;
      let condition = {
        tId: req.Client && req.Client.tId,
        destiner: req.user.userid,
        skip: query.skip || 0,
        limit: query.limit || 10,
        archived: false,
      };
      if (query.sn) condition.sn = query.sn;
      if (query.outTradeNo) condition.outTradeNo = query.outTradeNo;
      if (query.username) condition.username = query.username;
      if (query.userMobile) condition.userMobile = query.userMobile;
      if (query.status) condition.status = query.status;
      if (query.pId) condition.pId = query.pId;
      if (query.loanTerm) condition.loanTerm = query.loanTerm;

      if (query.startTime) {

        condition.startTime = query.startTime;
      }
      if (query.endTime) {

        condition.endTime = query.endTime;
      }

      if (condition.status === "rejected") {
        condition.status = {
          $regex: '^rejected',
          $options: 'i'
        }
      }

      let opts = {
        userInfo: req.user
      };
      if (req.headers && req.headers['x-dash-role']) {
        opts.role = req.headers['x-dash-role'];
      }
      let result = await loanApplicationService.getLoanApplicationDestinedList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 订单详情
  async getLoanApplicationDetail(req, res) {
    let method = 'getLoanApplicationDetail'
    debug(method, '[Enter]')
    try {

      if (!req.query.id) {
        throw {
          errorCode: 'E_LOAN_APP_064',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }

      let condition = {
        id: req.query.id
      };
      let opts = {};
      // opts.role = req.headers && req.headers['x-role-id'] ;

      let result = await loanApplicationService.getLoanApplicationDetail(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 订单操作记录
  async getLoanApplicationTracking(req, res) {
    let method = 'getLoanApplicationTracking'
    debug(method, '[Enter]')
    try {

      if (!req.query.id) {
        throw {
          errorCode: 'E_LOAN_APP_309',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }

      let condition = {
        target: req.query.id,
        $sort: req.query.$sort || {
          createdTime: -1
        },
        action: {
          $nin: ['cb_notify', 'cert_sms', 'certified', 'pre_censor_log', 'bhApi_result', 'bhApi_apply', 'bhApi_repo_halt', 'jhApi_apply']
        },
        limit: 'unlimited'
      };
      let opts = {};

      let result = await loanApplicationService.getLoanApplicationTracking(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 订单操作记录
  async getLoanApplicationGather(req, res) {
    let method = 'getLoanApplicationGather'
    debug(method, '[Enter]')
    try {

      if (!req.query.id) {
        throw {
          errorCode: 'E_LOAN_APP_064',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }

      let condition = {
        id: req.query.id
      };
      let opts = {};

      let result = await loanApplicationService.getLoanApplicationGather(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 获得资料
  async getCxwqSupplement(req, res) {
    let method = 'cxwqSupplement';
    debug(method, '[Enter]');
    try {
      if (!req.query || !req.query.aId) {
        throw {
          errorCode: 'E_VERIFY_063',
          httpCode: 406,
          reason: 'invalid param'
        };
      }
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CON_085',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        aId: req.query.aId
      };
      let opts = {
        userid: req.user.userid
      };
      let result = await loanApplicationService.getICxwqSupplement(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getRemainingTimeSelect(req, res) {
    let method = 'getRemainingTimeSelect';
    debug(method, '[Enter]');
    try {
      const result = Array.from(REMAINING_TIME_SELECT, ([type, name]) => ({ type, name }));
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 贷后订单
  async getLoanedApplicationList(req, res) {
    let method = 'getLoanedApplicationList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_186',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let query = req.query;
      let condition = {
        limit: query.limit || 10,
        skip: query.skip || 0,
        tId: req.Client && req.Client.tId,
        destiner: req.user.userid,
        archived: false,
        $sort: {
          status: -1,
          creditEndTime: query.$sort || 1
        }
      };
      if (query.sn) condition.sn = `/${query.sn}/`;
      if (query.outTradeNo) condition.outTradeNo = `/${query.outTradeNo}/`;
      if (query.username) condition.username = `/${query.username}/`;
      if (query.userMobile) condition.userMobile = `/${query.userMobile}/`;
      if (query.status) condition.status = query.status;
      if (query.loanTerm) condition.loanTerm = query.loanTerm;
      if (query.type) condition.type = query.type;
      if (query.pId) condition.pId = query.pId;
      if (query.fund) condition.fund = query.fund;
      if (query.startTime) {
        let startTime = moment(query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (query.endTime) {
        let endTime = moment(query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      if (query.area) {
        condition.area = `/^${query.area}/`;
      }
      if (!condition.status) {
        condition.status = {
          $in: ['loaned', 'finished', 'finished_loan']
        };
      }

      if (query.creditEndTime) {
        condition.status = {
          $in: ['loaned']
        };
        let startTime = moment(query.creditEndTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        let endTime = moment(query.creditEndTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.creditEndTime = {
          $gte: startTime,
          $lte: endTime,
        };
      }

      if (query.creditRemainingTime) {
        condition.status = {
          $in: ['loaned']
        };

        switch (query.creditRemainingTime) {
        case '-1':
          condition.creditEndTime = { $lte: moment().utc().add(-1, 'd').format('YYYY-MM-DD HH:mm:ssZ') };
          break;
        case '4':
          condition.creditEndTime = {
            $gt: moment().utc().add(-1, 'd').format('YYYY-MM-DD HH:mm:ssZ'),
            $lte: moment().utc().add(31, 'd').format('YYYY-MM-DD HH:mm:ssZ')
          };
          break;
        case '3':
          condition.creditEndTime = {
            $gt: moment().utc().add(31, 'd').format('YYYY-MM-DD HH:mm:ssZ'),
            $lte: moment().utc().add(91, 'd').format('YYYY-MM-DD HH:mm:ssZ')
          };
          break;
        case '2':
          condition.creditEndTime = {
            $gt: moment().utc().add(91, 'd').format('YYYY-MM-DD HH:mm:ssZ'),
            $lte: moment().utc().add(181, 'd').format('YYYY-MM-DD HH:mm:ssZ')
          };
          break;
        case '1':
          condition.creditEndTime = {
            $gt: moment().utc().add(181, 'd').format('YYYY-MM-DD HH:mm:ssZ'),
            $lte: moment().utc().add(366, 'd').format('YYYY-MM-DD HH:mm:ssZ')
          };
          break;
        }
      }

      let opts = {
        userInfo: req.user
      };
      // opts.role = req.headers && req.headers['x-role-id'];

      let result = await loanApplicationService.getLoanedApplicationList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 获取贷后订单详情
  async getLoanedApplicationDetail(req, res) {
    let method = 'getLoanedApplicationDetail'
    debug(method, '[Enter]')
    try {
      if (!req.query.id) {
        throw {
          errorCode: 'E_LOAN_APP_064',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }

      let condition = {
        id: req.query.id
      };
      let opts = {};
      let result = await loanApplicationService.getLoanedApplicationDetail(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 通辽订单列表
  async getMxtLoanAppList(req, res) {
    let method = 'getMxtLoanAppList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_186',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let query = req.query;
      let condition = {
        limit: query.limit || 10,
        skip: query.skip || 0,
        tId: req.Client && req.Client.tId,
        archived: false,
        destiner: { $exists: false },
        $sort: query.$sort || { createdTime: -1 }
      };
      if (req.query.userObj) {
        if (isChinese.test(req.query.userObj)) {
          condition.username = `/${req.query.userObj}/`;
        } else {
          condition.userMobile = `/${req.query.userObj}`;
        }
      }
      if (query.sn) condition.sn = `/${query.sn}/`;
      if (query.outTradeNo) condition.outTradeNo = `/${query.outTradeNo}/`;
      if (query.status) condition.status = query.status;
      if (query.loanTerm) condition.loanTerm = query.loanTerm;
      if (query.type) condition.type = query.type;
      if (query.pId) condition.pId = query.pId;
      if (query.fund) condition.fund = query.fund;
      if (query.startTime) {
        let startTime = moment(query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (query.endTime) {
        let endTime = moment(query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      if (query.area) {
        condition.area = `/^${query.area}/`;
      }
      if (!condition.status) {
        condition.status = LOAN_APP_STATUS.LOAN_APP_COLLECTION;
      }
      if (query.destined) {
        condition.destined = true;
        condition.destiner = req.user.userid;
      }

      let opts = {
        userInfo: req.user
      };

      let result = await loanApplicationService.getMxtLoanAppList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 县域审批
  async finalReviewApplication(req, res) {
    let method = 'finalReviewApplication'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.aId || !req.body.status) {
        throw {
          errorCode: 'E_APP_V2_R_27',
          httpCode: 406,
          reason: '参数错误'
        };
      }

      let condition = req.body;
      let opts = {
        user: req.user,
      };

      let result = await loanApplicationService.finalReviewApplication(condition, opts)

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // pre_comment补充文字备注后跳转待初审
  async commentApplication(req, res) {
    let method = 'commentApplication'
    debug(method, '[Enter]')
    try {
      req.user = {userid: '6720d25d4abf7469da3608cd'}
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_COMMENT_APP_785',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      if (!req.body || !req.body.aId || !req.body.comment) {
        throw {
          errorCode: 'E_COMMENT_APP_793',
          httpCode: 406,
          reason: '参数错误'
        };
      }

      let condition = req.body;
      let opts = {
        user: req.user,
      };

      let result = await loanApplicationService.commentApplication(condition, opts)

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)

      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this;


    self._policyRouter.post('/api/v2.0/loan/application/destined', self.destineLoanApplication.bind(self), {
      name: 'loanApplication.destineLoanApplication'
    });
    self._policyRouter.put('/api/v2.0/loan/application/destined/cancel', self.cancelDestineLoanApplication.bind(self), {
      name: 'loanApplication.cancelDestineLoanApplication'
    });


    self._policyRouter.patch('/api/v2.0/loan/application/approved', self.approvedLoanApplication.bind(self), {
      name: 'loanApplication.approvedLoanApplication'
    });

    self._policyRouter.patch('/api/v2.0/loan/application/reject', self.rejectLoanApplication.bind(self), {
      name: 'loanApplication.rejectLoanApplication'
    });

    self._policyRouter.get('/api/v2.0/loan/application/list', self.getLoanApplicationList.bind(self), {
      name: 'loanApplication.getLoanApplicationList'
    });

    self._policyRouter.get('/api/v2.0/loan/application/destined/list', self.getLoanApplicationDestinedList.bind(self), {
      name: 'loanApplication.getLoanApplicationDestinedList'
    });

    self._policyRouter.get('/api/v1.0/loan/application/statusFlow', self.getLoanApplicationTracking.bind(self), {
      name: 'loanApplication.getLoanApplicationTracking'
    })

    self._policyRouter.get('/api/v2.0/loan/application/detail', self.getLoanApplicationDetail.bind(self), {
      name: 'loanApplication.getLoanApplicationDetail',
    })

    self._policyRouter.get('/api/v2.0/loan/application/gather', self.getLoanApplicationGather.bind(self), {
      name: 'loanApplication.getLoanApplicationGather'
    })

    self._policyRouter.get('/api/v1.0/loan/get/application/supplement', self.getCxwqSupplement.bind(self), {
      name: 'loanApplication.getCxwqSupplement'
    });

    self._policyRouter.get('/api/v1.0/loaned/remaining-time/select', self.getRemainingTimeSelect.bind(self), {
      name: 'loanApplication.getRemainingTimeSelect'
    });

    self._policyRouter.get('/api/v1.0/loaned/application/list', self.getLoanedApplicationList.bind(self), {
      name: 'loanApplication.getLoanedApplicationList'
    });

    self._policyRouter.get('/api/v1.0/loaned/application/detail', self.getLoanedApplicationDetail.bind(self), {
      name: 'loanApplication.getLoanedApplicationDetail'
    });

    self._policyRouter.post('/api/v2.0/loan/application/comment', self.commentApplication.bind(self), {
      name: 'loanApplication.commentApplication',
      role: PERMISSION_ROLE.EVERYONE
    });

    /************蒙信管家*************/
    //蒙信通订单列表
    self._policyRouter.get('/api/v1.0/loan/application/mxt/list', self.getMxtLoanAppList.bind(self), {
      name: 'loanApplication.getMxtLoanAppList'
    });

    self._policyRouter.put('/api/v3.0/loan/application/mgr/final/review', self.finalReviewApplication.bind(self), {
      name: 'loanApplication.finalReviewApplication',
      role: PERMISSION_ROLE.EVERYONE
    });

    return self;
  }
}

module.exports = LoanApplication
/**
 * User Router
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:info_collect_history');
const infoCollectHistorySvc = require('../services/info_collect_history');
const infoCollectDraftSvc = require('../services/info_collect_draft');
const moment = require('moment');
const { cond } = require('lodash');

const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;


class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getSchemaEnums(req, res) {
    let method = 'save'
    debug(method, '[Enter]')
    try {
      if (!req.query.type) {
        throw {
          errorCode: 'E_Enum_034',
          httpCode: 401,
          reason: '缺少采集type'
        }
      }
      let condition = req.query;
      let result = await infoCollectDraftSvc.getSchemaEnums(condition);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async list(req, res) {
    let method = 'save'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      let query = req.query;
      let condition = query;
      condition["$sort"] = { createdTime: -1 };
      if (query.startTime) {
        let startTime = moment(query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.startTime = startTime;
        delete condition.startTime;
      }
      if (query.endTime) {
        let endTime = moment(query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.endTime = endTime;
        delete condition.endTime;
      }
      if (query.name) {
        condition['$or'] = [
          {
            legalPersonName:{
              '$regex': query.name,
              '$options': 'si'
            }
          },
          {
            name:{
              '$regex': query.name,
              '$options': 'si'
            }
          }
        ]
        delete query.name;
      }

      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };
      if (query.roleId) {
        opts.roleId = query.roleId;
        delete condition.roleId;
      }

      let result = await infoCollectHistorySvc.list(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async save(req, res) {
    let method = 'save'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if (!req.body.id || !req.body.content || !req.body.type) {
        throw {
          errorCode: 'E_DRAFT_034',
          httpCode: 401,
          reason: '缺少采集id/content'
        }
      }

      let condition = req.body;

      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };

      let result = await infoCollectDraftSvc.save(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async detail(req, res) {
    let method = 'detail'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if (!req.query.id) {
        throw {
          errorCode: 'E_DRAFT_034',
          httpCode: 401,
          reason: '缺少采集id'
        }
      }
      let condition = { id: req.query.id };
      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };

      let result = await infoCollectHistorySvc.detail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async detailv2(req, res) {
    let method = 'detailv2'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if (!req.query.id) {
        throw {
          errorCode: 'E_DRAFT_034',
          httpCode: 401,
          reason: '缺少采集id'
        }
      }
      let condition = { id: req.query.id };
      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };

      let result = await infoCollectHistorySvc.detailv2(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async approve(req, res) {
    let method = 'approve'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      if (!req.body.id || !req.body.type || !req.body.source) {
        throw {
          errorCode: 'E_DRAFT_034',
          httpCode: 401,
          reason: '缺少采集id/type/source'
        }
      }

      let condition = req.body;

      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };

      let result = await infoCollectDraftSvc.approve(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }


  init() {
    let self = this;
    self._policyRouter.get('/api/v1.0/info/collect/history/getSchemaEnums', self.getSchemaEnums.bind(self), {
      name: 'infoCollectHistory.getSchemaEnums',
      role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.get('/api/v1.0/info/collect/history/list', self.list.bind(self), {
      name: 'infoCollectHistory.list',
      role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.get('/api/v1.0/info/collect/history/detail', self.detail.bind(self), {
      name: 'infoCollectHistory.detail',
      role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.get('/api/v1.0/info/collect/history/detailv2', self.detailv2.bind(self), {
      name: 'infoCollectHistory.detailv2',
      role: PERMISSION_ROLE.EVERYONE
    });
    // self._policyRouter.post('/api/v1.0/info/collect/draft/approve', self.approve.bind(self), {
    //   name: 'infoCollectDraft.approve',
    //   // role: PERMISSION_ROLE.EVERYONE
    // });

    return self;
  }
}

module.exports = Router;
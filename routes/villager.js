'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:routes:villager');
const villagerService = require('../services/villager');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class LoanBill {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getVillagerList(req, res) {
    let method = 'getVillagerList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid || !req.headers.arealist) {
        throw {
          errorCode: 'E_VILLAGER_020',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let area;
      if (req.query.areaCode) {
        if (req.query.areaCode.includes(",")) {
          area = req.query.areaCode.split(",");
        } else {
          area = [req.query.areaCode];
        }
      } else {
        area = JSON.parse(req.headers.arealist);
      }
      let condition = {
        areaList: area,
        skip: req.query.skip || 0,
        limit: req.query.limit || 10,
        $sort: req.query.$sort || { createdTime: -1 },
      };
      if (req.query.username) {
        condition.username = req.query.username;
      }

      let opts = {};

      let result = await villagerService.getVillagerList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getVillagerDetail(req, res) {
    let method = 'getVillagerDetail'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_VILLAGER_047',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query.id) {
        throw {
          errorCode: 'E_VILLAGER_054',
          httpCode: 406,
          reason: 'invalid params'
        }
      }
      let condition = {
        id: req.query.id
      };

      let opts = {};

      let result = await villagerService.getVillagerDetail(condition.id, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getGroupList(req, res) {
    let method = 'getGroupList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid || !req.headers.arealist) {
        throw {
          errorCode: 'E_VILLAGER_074',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let areaList = req.query.areaCode || req.headers.arealist;
      let condition = {
        areaList: JSON.parse(areaList)
      };

      let opts = {};

      let result = await villagerService.getGroupList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getGroupDetail(req, res) {
    let method = 'getGroupDetail'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_VILLAGER_103',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query.id) {
        throw {
          errorCode: 'E_VILLAGER_110',
          httpCode: 406,
          reason: 'invalid params'
        }
      }
      let condition = {
        id: req.query.id
      };

      let opts = {};

      let result = await villagerService.getGroupDetail(condition.id, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async updateGroupMember(req, res) {
    let method = 'updateGroupMember'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_VILLAGER_130',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let body = req.body;
      if (!body || !body.groupId || !body.action || !body.member || !Array.isArray(body.member) ||
        !body.member.length) {
        throw {
          errorCode: 'E_VILLAGER_152',
          httpCode: 406,
          reason: 'invalid param'
        };
      }

      let condition = { member: body.member };
      condition.lastModTime = new Date();
      let opts = { id: body.groupId, action: body.action };

      let result = await villagerService.updateGroupMember(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this;

    self._policyRouter.get('/api/v1.0/villager/list', self.getVillagerList.bind(self), {
      name: 'villager.getVillagerCounts'
    })
    self._policyRouter.get('/api/v1.0/villager/detail', self.getVillagerDetail.bind(self), {
      name: 'villager.getVillagerDetail'
    })
    self._policyRouter.get('/api/v1.0/villager/group/list', self.getGroupList.bind(self), {
      name: 'villager.getGroupList'
    })
    self._policyRouter.get('/api/v1.0/villager/group/detail', self.getGroupDetail.bind(self), {
      name: 'villager.getGroupDetail'
    })
    self._policyRouter.put('/api/v1.0/villager/group/member', self.updateGroupMember.bind(self), {
      name: 'villager.updateGroupMember'
    })

    return self
  }
}

module.exports = LoanBill
/** 
 * newsRouter 
 * <AUTHOR>
 * 2019-10-18
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:loan_application_fund_receive');
const moment = require('moment');
const loanApplicationFundReceive = require('../services/loanApplicationFundReceive');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const {assert,formatParas} = require('../utils/general');
const { cloneDeep } = require('lodash');

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getFundReceive({exportExcel,getOne,page,actionIn,successfulIn},req, res) {
    const method = 'getFundReceive';
    debug(method, '[Enter]')
    try {

      assert(!getOne || req.query._id,'E_APP_FUND_RECEIVE_001','para _id is required')
      actionIn && ( req.query.actionIn = actionIn );
      successfulIn && ( req.query.successfulIn = successfulIn );
      const config = [
        'uId','aId','_id','requestType','action','status','successful',
        'requestMobile','requestCompanyName','sn','appSn','requestUniqueId',
        {from:'requestName',rule:'contain'},
        {from:'username',rule:'contain'},
        // {from:'requestMobile',rule:'eq'},
        // {from:'requestCompanyName',rule:'eq'},//contain
        // {from:'requestMobile',rule:'eq'},
        // {from:'requestCompanyName',rule:'eq'},//contain
        {from:'requestAreaCode',rule:'startWith'},
        // {from:'sn',rule:'eq'},
        // {from:'appSn',rule:'eq'},//startWith
        {from:'actionIn',to:'action',rule:'in'},
        {from:'actionNin',to:'action',rule:'nin'},
        {from:'statusIn',to:'status',rule:'in'},
        {from:'statusNin',to:'status',rule:'nin'},
        {from:'successfulIn',to:'successful',rule:'in'},
        {from:'successfulNin',to:'successful',rule:'nin'},
        {from:'createdTimeStart',to:'createdTime',rule:'gte',fs:'toMinDay'},
        {from:'createdTimeEnd',to:'createdTime',rule:'lte',fs:'toMaxDay'},
        {from:'hasPayToken',fs:'boolean'},
        {from:'skip',dv:0},
        {from:'limit',dv:10},
        {from:'$sort',dv:{ lastModTime: -1 },fs:'json'},
      ];
      const opts = {getOne,page,uId:req.user && req.user.userid} , condition = {
        ...formatParas(config,req.query),
        archived: false,
      };
      //     {
      //   uId:query.uId,
      //   skip: query.skip || 0,
      //   limit: query.limit || 'unlimited',
      //   archived: false,
      //   $sort: query.$sort || { createdTime: -1 }
      // };
      // query.aId && ( condition.aId = query.aId );
      // query._id && ( condition._id = query.id );
      opts.role = req.headers && req.headers['x-role-id'] ;

      const mName = exportExcel ? 'fundReceiveExport' : 'fundReceiveList';
      const result = await loanApplicationFundReceive[mName](condition, opts);
      if( exportExcel ){
        res.set("Content-Disposition", `attachment;fileName=${encodeURIComponent(result.filename)}.xls`);
        res.set('Content-Type', 'application/x-xls');
        debug(method, '[Exit](success)');
        res.status(200).send(result.buffer);
        return;
      }
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async executeLoanAppVerify({justSave},req, res) {
    const method = 'executeLoanAppVerify'
    debug(method, '[Enter]')
    try {
      const config = [
        'id','action','description','roleId','userid','extendInfo',
        {from:'overwriteVerifyInfo',dv: {}},
        {from:'addonVerifyInfo',dv: {}},
      ];

      const opts = {} , condition = cloneDeep( {
        ...formatParas(config,req.body),
        userId: req.user && req.user.userid,
      } );
      process.env.DEBUG_NO_NEED_LOGIN && req.body.userId && ( condition.userId = req.body.userId );
      assert(condition.userId,'E_APP_FUND_RECEIVE_EXECUTE_000','no permission');
      justSave && ( condition.action = 'justSave' );
      !justSave && condition.action === 'justSave' && ( delete condition.action);//这个接口不许只保存
      const result = await loanApplicationFundReceive.executeFundReceiveVerify(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async loanApplicationFundReceiveUserList({getOne},req, res) {
    const method = 'loanApplicationFundReceiveUserList'
    debug(method, '[Enter]')
    try {
      assert(!getOne || req.query._id,'E_APP_FUND_RECEIVE_001','para _id is required')
      const config = [
        'uId','requestType',
        'requestMobile','requestCompanyName','requestAreaCode',
        // 'aId','sn','appSn','_id',
        {from:'requestName',rule:'contain'},
        // {from:'requestMobile',rule:'eq'},
        // {from:'requestCompanyName',rule:'eq'},//contain
        // {from:'requestAreaCode',rule:'startWith'},
        // {from:'sn',rule:'eq'},
        // {from:'appSn',rule:'eq'},//startWith
        // {from:'actionIn',to:'action',rule:'in'},
        // {from:'actionNin',to:'action',rule:'nin'},
        // {from:'statusIn',to:'status',rule:'in'},
        // {from:'statusNin',to:'status',rule:'nin'},
        // {from:'successfulIn',to:'successful',rule:'in'},
        // {from:'successfulNin',to:'successful',rule:'nin'},
        // {from:'createdTimeStart',to:'createdTime',rule:'gte',fs:'toMinDay'},
        // {from:'createdTimeEnd',to:'createdTime',rule:'lte',fs:'toMaxDay'},
        // {from:'hasPayToken',fs:'boolean'},
        {from:'skip',dv:0},
        {from:'limit',dv:10},
        {from:'sort',dv:1},
      ];
      const opts = {getOne} , condition = {
        ...formatParas(config,req.query),
        archived: false,
      };
      const result = await loanApplicationFundReceive.loanApplicationFundReceiveUserList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async loanApplicationFundReceiveLandList({getOne},req,res){
    const method = 'loanApplicationFundReceiveLandList'
    debug(method, '[Enter]')
    try {
      assert(req.query.fundReceiveId,'E_FUND_RECEIVE_LAND_LIST_161','fundReceiveId is required')

      const config = [
        'fundReceiveId',
        {from:'fundReceiveStatusIn',to:'fundReceiveStatus',rule:'in'},
        {from:'skip',dv:0},
        {from:'limit',dv:10},
        {from:'$sort',dv:{ lastModTime: -1 },fs:'json'},
      ];
      const opts = {getOne} , condition = {
        ...formatParas(config,req.query),
        archived: false,
      };
      const result = await loanApplicationFundReceive.LoanApplicationFundReceiveLandList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getOutLandFundReceiveList({getOne,page,no},req, res) {
    const method = 'getOutLandFundReceiveList';
    debug(method, '[Enter]')
    try {

      assert(!getOne || req.query._id,'E_APP_FUND_RECEIVE_001','para _id is required')
      process.env.DEBUG_NO_NEED_LOGIN || assert(req.user,'E_OUT_LAND_FUND_RECEIVE_000','您必须登录之后才能操作')
      process.env.DEBUG_NO_NEED_LOGIN || assert(req.Client,'E_OUT_LAND_FUND_RECEIVE_000','您必须登录之后才能操作')
      req.user && (req.query.uId = req.user.userid);
      req.user && (req.query.tId = req.Client && req.Client.tId);
      assert(req.query.uId,'E_OUT_LAND_FUND_RECEIVE_003','uId is required')
      assert(req.query.tId,'E_OUT_LAND_FUND_RECEIVE_004','tId is required')
      const config = [
        'no','_id',
      ];
      const opts = {
        getOne,tId:req.query.tId,uId:req.query.uId,
        page, download:req.query.download !== 'false',
      } ;
      const condition = {
        ...formatParas(config,req.query),
        archived: false,
      };
      //     {
      //   uId:query.uId,
      //   skip: query.skip || 0,
      //   limit: query.limit || 'unlimited',
      //   archived: false,
      //   $sort: query.$sort || { createdTime: -1 }
      // };
      // query.aId && ( condition.aId = query.aId );
      // query._id && ( condition._id = query.id );
      const result = await loanApplicationFundReceive.outLandFundReceiveList(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  async fundReceiveStatusListHandler({getOne,page,no},req, res) {
    const method = 'fundReceiveStatusListHandler';
    debug(method, '[Enter]')
    try {
      const result = await loanApplicationFundReceive.fundReceiveStatusListHandler({}, {});

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {

    this._policyRouter.get('/api/v1.0/loan/app/fund/receive/list',
      (...paras)=>this.getFundReceive({successfulIn:'1,2'},...paras), {
      name: 'fund.getFundReceive.list',
      // role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/loan/app/fund/receive/list/page/only/disburse',
        (...paras)=>this.getFundReceive({page:true,actionIn:'land,outland,generalBuy,machinery,insurance',successfulIn:'1,2,3'},...paras), {
          name: 'fund.getFundReceive.list.only.disburse',
          // role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.get('/api/v1.0/loan/app/fund/receive/excel',
      (...paras)=>this.getFundReceive({exportExcel:true},...paras), {
      name: 'fund.getFundReceive.excel',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/loan/app/fund/receive/detail',
        (...paras)=>this.getFundReceive({getOne:true},...paras), {
          name: 'fund.getFundReceive.detail',
          // role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.post('/api/v1.0/loan/app/fund/receive/verify/execute',
        (...paras)=>this.executeLoanAppVerify({},...paras), {
          name: 'fund.getFundReceive.verify.execute',
          // role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.put('/api/v1.0/loan/app/fund/receive/info',
        (...paras)=>this.executeLoanAppVerify({justSave:true},...paras), {
          name: 'fund.getFundReceive.verify.info',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.get('/api/v1.0/loan/app/fund/receive/user/list',
        (...paras)=>this.loanApplicationFundReceiveUserList({getOne:false},...paras), {
          name: 'fund.getFundReceive.user.list',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.get('/api/v1.0/loan/app/fund/receive/user/list/detail',
        (...paras)=>this.loanApplicationFundReceiveUserList({getOne:true},...paras), {
          name: 'fund.getFundReceive.user.detail',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.get('/api/v1.0/fund/receive/land/origin',
        (...paras)=>this.loanApplicationFundReceiveLandList({},...paras), {
          name: 'landOrigin.landOriginList',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.get('/api/v1.0/out/land/fund/receive/list',
        (...paras)=>this.getOutLandFundReceiveList({page:true},...paras), {
          name: 'fund.getOutLandFundReceive.list.all',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.get('/api/v1.0/fund/receive/status/list',
        (...paras)=>this.fundReceiveStatusListHandler({page:true},...paras), {
          name: 'fund.getOutLandFundReceive.list.all',
          role: PERMISSION_ROLE.EVERYONE
        });


    return this;
  }
}

module.exports = Router;
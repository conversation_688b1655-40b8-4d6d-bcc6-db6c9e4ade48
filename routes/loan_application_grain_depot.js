/** 
 * newsRouter 
 * <AUTHOR>
 * 2019-10-18
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:loan_application_grain_depot');
const moment = require('moment');
const loanApplicationGrainDepotReceive = require('../services/loanApplicationGrainDepot');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const {assert,formatParas} = require('../utils/general');

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getGrainDepot({getOne},req, res) {
    const method = 'getGrainDepot';
    debug(method, '[Enter]')
    try {

      process.env.DEBUG_NO_NEED_LOGIN || assert(req.user,'E_GRAIN_DEPOT_LIST_000','no permission')
      assert(!getOne || req.query._id,'E_GRAIN_DEPOT_LIST_001','para _id is required')
      const config = [
        {from:'name',rule:'contain'},
        {from:'createdTimeStart',to:'createdTime',rule:'gte',fs:'toMinDay'},
        {from:'createdTimeEnd',to:'createdTime',rule:'lte',fs:'toMaxDay'},
        {from:'isRevoked',fs:'boolean'},
        {from:'skip',dv:0},
        {from:'limit',dv:10},
        {from:'$sort',dv:{ createdTime: -1 },fs:'json'},
      ];
      const opts = {getOne} , condition = {
        ...formatParas(config,req.query),
        archived: false,
      };
      const result = await loanApplicationGrainDepotReceive.grainDepotList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async createOrEditGrainDepot({editFlag,isRevoked},req, res) {
    const method = 'createOrEditGrainDepot'
    debug(method, '[Enter]')
    try {

      const opts = {uId:req.user && req.user.userid} , condition = req.body;
      process.env.DEBUG_NO_NEED_LOGIN || assert(req.user,'E_GRAIN_DEPOT_LIST_000','no permission')
      editFlag && assert(condition._id,'E_GRAIN_DEPOT_EDIT_001','_id is required')
      isRevoked && assert(typeof condition.isRevoked === 'boolean','E_GRAIN_DEPOT_EDIT_002','isRevoked is required and must be boolean')
      editFlag || assert(condition.name,'E_GRAIN_DEPOT_EDIT_003','name is required')
      editFlag || assert(condition.address,'E_GRAIN_DEPOT_EDIT_004','address is required')
      editFlag || ( condition.isRevoked = condition.isRevoked && true || false );

      const result = await loanApplicationGrainDepotReceive.createGrainDepot(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {

    this._policyRouter.get('/api/v1.0/loan/app/gran/depot/list',
      (...paras)=>this.getGrainDepot({},...paras), {
      name: 'grain.depot.list',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/loan/app/gran/depot/detail',
        (...paras)=>this.getGrainDepot({getOne:true},...paras), {
          name: 'grain.depot.detail',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.post('/api/v1.0/loan/app/gran/depot/add',
        (...paras)=>this.createOrEditGrainDepot({},...paras), {
          name: 'grain.depot.add',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.put('/api/v1.0/loan/app/gran/depot/edit',
        (...paras)=>this.createOrEditGrainDepot({editFlag:true},...paras), {
          name: 'grain.depot.edit',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.put('/api/v1.0/loan/app/gran/depot/revoked',
        (...paras)=>this.createOrEditGrainDepot({editFlag:true,isRevoked:true},...paras), {
          name: 'grain.depot.revoke',
          role: PERMISSION_ROLE.EVERYONE
        });

    return this;
  }
}

module.exports = Router;
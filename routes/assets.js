/**
 * <AUTHOR>
 * 2019-05-05
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:assets');
const assetSvc = require('../services/assets');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class Carrousel {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async sxLandsList(req, res) {
    let method = 'sxLandsList'
    debug(method, '[Enter]')
    try {
      let condition = req.query;
      let opts = {};

      condition.areaCode ? condition.areaCode = new RegExp('^'+condition.areaCode):'';
      
      parseInt(condition.flowType) === 0 && ( condition.flowType = JSON.stringify({$in:[null,0]}) );

      let result = await assetSvc.sxLands(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} `);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async sxLandsModify(req, res) {
    let method = 'sxLandsModify'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.id) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_043',
          reason: 'invalid param'
        };
      }
      if (!(req.body.flowType > -1)) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_050',
          reason: 'invalid param'
        };
      }
      let condition = req.body;
      let opts = {};

      let result = await assetSvc.sxLandsModify(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} `);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async sxLandsSelfModify(req, res) {
    let method = 'sxLandsSelfModify'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.id) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_043',
          reason: 'invalid param'
        };
      }
      if (!(req.body.flowType > -1)) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_081',
          reason: 'invalid param'
        };
      }
      let condition = req.body;
      let opts = {};

      let result = await assetSvc.sxLandsSelfModify(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} `);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/assets/sxlands/list', self.sxLandsList.bind(self), {
      name: 'assets.sxLandsList',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.put('/api/v1.0/assets/sxlands/modify', self.sxLandsModify.bind(self), {
      name: 'assets.sxLandsModify',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.put('/api/v1.0/assets/self/sxlands/modify', self.sxLandsSelfModify.bind(self), {
      name: 'assets.sxLandsSelfModify',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = Carrousel
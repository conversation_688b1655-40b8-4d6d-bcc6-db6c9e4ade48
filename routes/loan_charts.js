/**
 * <AUTHOR>
 * 2019-05-05
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:loan_charts');
const loanChartsSvc = require('../services/loan_charts');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class LoanCharts {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getLoanChartsList(req, res) {
    let method = 'getLoanChartsList'
    debug(method, '[Enter]')
    try {
      if (!req.query || !req.query.type) {
        throw {
          httpCode: 406,
          errorCode: 'E_EMPLOYEE_R_065',
          reason: 'invalid param'
        };
      }

      let condition = {
        limit: req.query.limit || "unlimited",
        skip: req.query.skip || 0,
        type: req.query.type,
        tId: req.Client && req.Client.tId,
      };
      if (req.query.startTime && req.query.endTime) {
        condition.startTime = req.query.startTime;
        condition.endTime = req.query.endTime;
      }
      if (req.query.department) {
        condition.department = req.query.department;
      }
      let opts = {};

      let result = await loanChartsSvc.getLoanChartsList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getAssistanterChartsList(req, res) {
    let method = 'getAssistanterChartsList'
    debug(method, '[Enter]')
    try {
      if (!req.query || !req.query.limit || !req.query.skip) {
        throw {
          httpCode: 406,
          errorCode: 'E_EMPLOYEE_R_065',
          reason: 'invalid param'
        };
      }

      let condition = {
        limit: req.query.limit || "unlimited",
        skip: req.query.skip || 0,
        tId: req.Client && req.Client.tId,
        type: req.query.type
      };
      if (req.query.startTime && req.query.endTime) {
        condition.startTime = req.query.startTime;
        condition.endTime = req.query.endTime;
      }
      if (req.query.code) {
        condition.code = req.query.code;
      }
      let opts = {};

      let result = await loanChartsSvc.getAssistanterChartsList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/loan/charts/list', self.getLoanChartsList.bind(self), {
      name: 'client.getLoanChartsList',
      //role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/assistanter/charts/list', self.getAssistanterChartsList.bind(self), {
      name: 'client.getAssistanterChartsList',
      //role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = LoanCharts
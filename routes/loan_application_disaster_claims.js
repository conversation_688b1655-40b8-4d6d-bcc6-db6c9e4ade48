/** 
 * newsRouter 
 * <AUTHOR>
 * 2019-10-18
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:loan_application_fund_receive');
const moment = require('moment');
const loanApplicationDisasterClaimsService = require('../services/loanApplicationDisasterClaims');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const {assert,formatParas} = require('../utils/general');
const {cloneDeep} = require('lodash');

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getDisasterClaims({getOne}, req, res) {
    const method = 'DisasterClaims';
    debug(method, '[Enter]')
    try {
      const opts = {getOne};
      assert(!getOne || req.query._id,'E_APP_FUND_RECEIVE_001','para _id is required')
      opts.uId = req.user && req.user.userid;
      process.env.NODE_ENV ==='local-dev' && req.query.uId && ( opts.uId = req.query.uId);
      assert(opts.uId,'E_EDIT_DISASTER_CLAIMS_002','PERMISSION ERROR');

      const config = [
        'aId','_id','requestType',
        'requestMobile','appSn',
        {from:'requestName',rule:'contain'},
        {from:'username',rule:'username'},
        {from:'requestAreaCode',rule:'startWith'},
        // {from:'sn',rule:'eq'},
        // {from:'appSn',rule:'eq'},//startWith

        {from:'createdTimeStart',to:'createdTime',rule:'gte',fs:'toMinDay'},
        {from:'createdTimeEnd',to:'createdTime',rule:'lte',fs:'toMaxDay'},
        {from:'skip',dv:0},
        {from:'limit',dv:'10'},
        {from:'$sort',dv:{ lastModTime: -1 },fs:'json'},
      ];
      const condition = {
        ...formatParas(config,req.query),
        archived: false,
      };
      opts.uId = req.user && req.user.userid;
      opts.roleId = req.query.roleId || req.headers['x-role-id'];
      opts.tId = req.query.tId || req.Client && req.Client.tId ;

      const result = await loanApplicationDisasterClaimsService.list(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  async createOrEditDisasterClaims({edit},req, res) {
    const method = 'createOrEditDisasterClaims';
    debug(method, '[Enter]')
    try {
      const condition = cloneDeep(req.body),opts = {};
      opts.uId = req.user && req.user.userid;
      process.env.NODE_ENV ==='local-dev' && req.body.uId && ( opts.uId = req.body.uId);
      edit && assert(condition._id,'E_EDIT_DISASTER_CLAIMS_001','_id is required');
      assert(condition.aId,'E_EDIT_DISASTER_CLAIMS_003','aId is required');
      assert(opts.uId,'E_EDIT_DISASTER_CLAIMS_002','PERMISSION ERROR');
      const result = await loanApplicationDisasterClaimsService.createOrEdit(condition,opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {

    this._policyRouter.get('/api/v1.0/loan/app/disaster/claims/list',
      (...paras)=>this.getDisasterClaims({},...paras), {
      name: 'app.disasterClaims.list',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/loan/app/disaster/claims/detail',
        (...paras)=>this.getDisasterClaims({getOne:true},...paras), {
          name: 'app.disasterClaims.detail',
          role: PERMISSION_ROLE.EVERYONE
        });


    this._policyRouter.post('/api/v1.0/loan/app/disaster/claims/add',
        (...paras)=>this.createOrEditDisasterClaims({},...paras), {
          name: 'app.disasterClaims.add',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.put('/api/v1.0/loan/app/disaster/claims/edit',
        (...paras)=>this.createOrEditDisasterClaims({edit:true},...paras), {
          name: 'app.disasterClaims.edit',
          role: PERMISSION_ROLE.EVERYONE
        });


    return this;
  }
}

module.exports = Router;
// 土地｜客户信息检测
'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.api:routes:info_check');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const infoCheckService = require('../services/info_check');

class InfoCheckRoutes {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  // 土地信息处理列表
  async getLandCheckList(req, res) {
    let method = 'getLandCheckList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_INFO_CHECK_678',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let { submitTime, status, startDate, endDate, areacode, skip, limit } = req.query || {};

      let condition = {
        submitTime: submitTime || 0,
        status,
        areacode,
        startDate,
        endDate,
        skip: skip || 0,
        limit: limit || 10,
        auth: req.user.token,
        uId: req.user.userid,
        roleId: req.headers['x-role-id'],
        tId: req.headers['tid']
      };
      let opts = {};

      let result = await infoCheckService.getLandCheckList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 获得信息处理详情
  async getLandCheckDetail(req, res) {
    let method = 'getLandCheckDetail'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_INFO_CHECK_678',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      const { id } = req.query || {};
      let condition = {
        id,
        auth: req.user.token
      };
      let opts = {};

      let result = await infoCheckService.getLandCheckDetail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 处理信息
  async reviewLandCheckInfo(req, res) {
    let method = 'reviewLandCheckInfo'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_INFO_CHECK_678',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      const headers = req.headers || {};
      const appUser = headers['app-user'] || '';

      let condition = {
        ...req.body,
        auth: req.user.token,
        uid: req.user.userid,
        appUser: appUser
      };
      let opts = {};

      let result = await infoCheckService.reviewLandCheckInfo(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  
  // 根据字典编码获取字典集合
  async getDictList(req, res) {
    let method = 'getDictList'
    debug(method, '[Enter]')
    try {
      const { dictId } = req.query || {};
      if(!dictId) {
        throw {
          errorCode: 'E_INFO_CHECK_622',
          httpCode: 406,
          reason: '参数错误'
        }
      }
      let condition = {
        dictId
      };
      let opts = {};

      let result = await infoCheckService.getDictList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 根据字典编码获取字典集合
  async getDictArrList(req, res) {
    let method = 'getDictArrList'
    debug(method, '[Enter]')
    try {
      const { dictList } = req.body || {};
      if(!dictList || !dictList.length) {
        throw {
          errorCode: 'E_INFO_CHECK_622',
          httpCode: 406,
          reason: '参数错误'
        }
      }
      let condition = {
        dictList
      };
      let opts = {};

      let result = await infoCheckService.getDictArrList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  // 根据字典编码获取字典集合
  async getPendingNum(req, res) {
    let method = 'getPendingNum'
    debug(method, '[Enter]')
    try {
      let condition = {
        uId: req.user.userid,
        roleId: req.headers['x-role-id'],
        tId: req.headers['tid']
      };
      let opts = {};

      let result = await infoCheckService.getPendingNum(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  init() {
    let self = this;

    // ---------- 以下是消息相关接口 ------------
    self._policyRouter.get('/api/v1.0/info/check/list', self.getLandCheckList.bind(self), {
      name: 'infoCheck.getLandCheckList',
    });
    
    self._policyRouter.get('/api/v1.0/info/check/detail', self.getLandCheckDetail.bind(self), {
      name: 'infoCheck.getLandCheckDetail'
    });

    self._policyRouter.post('/api/v1.0/info/check/review', self.reviewLandCheckInfo.bind(self), {
      name: 'infoCheck.reviewLandCheckInfo'
    });

    // ---------- 以下是字典相关接口 ------------
    self._policyRouter.get('/api/v1.0/info/check/dict/list', self.getDictList.bind(self), {
      name: 'infoCheck.getDictList'
    });

    self._policyRouter.post('/api/v1.0/info/check/dict/arr/list', self.getDictArrList.bind(self), {
      name: 'infoCheck.getDictArrList'
    });

    self._policyRouter.get('/api/v1.0/info/check/pending/num', self.getPendingNum.bind(self), {
      name: 'infoCheck.getPendingNum'
    });

    return self;
  }
}

module.exports = InfoCheckRoutes;
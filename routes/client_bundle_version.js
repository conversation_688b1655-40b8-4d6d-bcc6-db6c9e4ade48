/**
 * <AUTHOR>
 * 2019-07-22
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:client_bundle_version');
const clientBundleVersionSvc = require('../services/client_bundle_version');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class Carrousel {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  // 春耕管家Bundle升级
  async getClientBundleVersion(req, res) {
    let method = 'getClientBundleVersion'
    debug(method, '[Enter]')
    try {
      if (!req.query.clientId) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_065',
          reason: 'invalid param'
        };
      }

      let condition = {
        client: req.query.clientId
      };

      if (req.query.version) {
        condition.version = req.query.version;
      }
      if (req.query.channel) {
        condition.channel = req.query.channel;
      }

      let opts = {};

      let result = await clientBundleVersionSvc.getClientBundleVersion(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/client/bundle/version', self.getClientBundleVersion.bind(self), {
      name: 'client.getClientVersion',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = Carrousel
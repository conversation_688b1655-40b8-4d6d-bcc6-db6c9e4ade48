/*
 * @Author: ysd
 * @Last Modified by: ysd
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:cxwq_warnrule');
const cxwqInsSvc = require('../services/cxwq_insurance');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class CountryCollect {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  //获取创新物权投保凭证列表
  async getInsuranceList(req, res) {
    let method = 'getInsuranceList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CXWQ_INS_026',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let query = req.query;
      let roleId = req.headers['x-role-id'];
      let condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        archived: false
      };
      if (query.username) {
        condition.username = query.username;
      }
      if (query.mobile) {
        condition.userMobile = query.mobile;
      }
      if (query.status) {
        condition.status = query.status;
      }
      if (query.source) {
        condition.source = query.source;
      }
      if (query.areaCode) {
        condition.areaCode = query.areaCode;
      }
      if (query.startTime) {
        condition.startTime = query.startTime;
      }
      if (query.endTime) {
        condition.endTime = condition.endTime;
      }
      query.requestName && ( condition.requestName = query.requestName );
      query.requestType && ( condition.requestType = query.requestType );
      let opts = {
        roleId,
        employeeId: req.user.userid,
        tId: query.tId || ( req.Client && req.Client.tId ),
      }
      opts.uId = opts.employeeId;
      let result = await cxwqInsSvc.getInsuranceList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  //获取订单及投保凭证的详情
  async getInsDetail(req, res) {
    let method = 'getInsDetail'
    debug(method, '[Enter]')
    try {
      let query = req.query;
      if (!query.aId) {
        throw {
          errorCode: 'E_CXWQ_INS_069',
          httpCode: 406,
          reason: 'Invalid params'
        }
      }

      let result = await cxwqInsSvc.getInsDetail(query.aId);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this;

    //订单和投保凭证信息的列表
    self._policyRouter.get('/api/v1.0/cxwq/insurance/list', self.getInsuranceList.bind(self), {
      name: 'cxwqInsurance.getInsuranceList'
    });
    //订单及投保凭证详情
    self._policyRouter.get('/api/v1.0/cxwq/insurance/detail', self.getInsDetail.bind(self), {
      name: 'cxwqInsurance.getInsDetail'
    });
    return self;
  }
}

module.exports = CountryCollect;
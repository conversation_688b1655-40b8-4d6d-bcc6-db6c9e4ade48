/*
 * @Author: wcy 
 * @Date: 2018-11-23 15:24:29 
 * @Last Modified by: wcy 
 * @Last Modified time: 2018-12-10 11:41:04
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:outland');
const outLandService = require('../services/loanApplicationOutLand');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const moment = require('moment');

class OutLand {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async archivedOutLand(req, res) {
    const method = 'getNewsDetail';
    debug(method, '[Enter]');
    try {

      if (!req.body.id) {
        throw {
          errorCode: 'E_OUT_LAND_001',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }

      const opts = { uId: req.user.userid, }, condition = {
        id: req.body.id,
        archived: req.body.archived && true || false,
      };

      await outLandService.archivedOutLandHandler(condition, opts);
      const result = { success: true }
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async finishOutLand(req, res) {
    const method = 'finishOutLand';
    debug(method, '[Enter]');
    try {
      if (!req.body.aId) {
        throw {
          errorCode: 'E_OUT_LAND_058',
          httpCode: 406,
          reason: 'miss parameter aId'
        }
      }

      const condition = {
        aId: req.body.aId,
      };
      const opts = { uId: req.user.userid, };

      const result = await outLandService.finishOutLand(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async createOrEditOutLand(req, res) {
    const method = 'createOrEditOutLandHandler';
    debug(method, '[Enter]');
    try {

      const condition = req.body, opts = { uId: req.user.userid, tId: req.Client.tId };//{uId:'5cf6268f5e71c8729598d055',tId:''}
      // condition.tId = req.Client.tId;
      // condition.area = Number(condition.area) || 0;
      // condition.operator = req.user.userid;
      // if( typeof condition.area !== 'number' ){
      //   debug('createOrEditOutLandHandlerArea error',condition);
      //   throw {
      //     errorCode: 'E_OUT_LAND_010',
      //     httpCode: 406,
      //     reason: 'area cant be null '
      //   }
      // }
      // if (req.query.app == 'yyn' || req.query.app == 'cg' || req.query.app == 'cgdemo') {
      //   condition.app = req.query.app;
      // }

      const result = await outLandService.createOrEditOutLand(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getOutLandList(req, res) {
    const method = 'getOutLandListHandler';
    debug(method, '[Enter]');
    try {

      // if (!req.query.roleId && !req.query.groupV2) {
      //   throw {
      //     errorCode: 'E_OUT_LAND_007',
      //     httpCode: 406,
      //     reason: 'miss role id'
      //   }
      // }

      const opts = {
        uId: req.user && req.user.userid || '',
        tId: req.Client && req.Client.tId,
        roleId: req.query.roleId || '',
        groupV2: req.query.groupV2 || '',
        removeEmpty: req.query.removeEmpty && true || false,
      };
      const condition = {
        skip: req.query.skip || 0,
        limit: req.query.limit || 10,
        // aId:req.query.aId,
        archived: false,//req.query.archived === 'true',
        disabled: false,
        $sort: { createdTime: -1 },
      };
      req.query.name && (condition.name = `/${req.query.name}/`);
      req.query.flowType && (condition.flowType = req.query.flowType);
      typeof req.query.aId !== 'undefined' && (condition.aId = req.query.aId);
      typeof req.query.groupId !== 'undefined' && (condition.groupId = req.query.groupId);
      req.query.areaCode && (condition.areaCode = req.query.areaCode);
      req.query.landType && (condition.landType = parseInt(req.query.landType) || 0);
      req.query.noConfirmationCode && (condition.confirmationCode = { $exists: false });
      typeof req.query.tId !== 'undefined' && (condition.tId = req.query.tId);
      if (req.query.startTime && req.query.endTime) {
        condition.createdTime = JSON.stringify({
          $gte: moment(req.query.startTime).utc().toDate(),
          $lte: moment(req.query.endTime).utc().toDate()//.add(1, 'd').toDate()
        });
      }
      condition.$sort = req.query.$sort || { createdTime: -1 };
      Object.keys(req.query).filter(v => /^otherInfo\./.test(v)).forEach(k => condition[k] = req.query[k]);
      // if (req.Client && req.Client.tId) {
      //   condition.tId = req.Client.tId;
      // }

      const result = await outLandService.getOutLandList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getApplicationOutLandList(req, res) {
    const method = 'getApplicationOutLandList';
    debug(method, '[Enter]');
    try {

      // if (!req.query.aId) {
      //   throw {
      //     errorCode: 'E_OUT_LAND_002',
      //     httpCode: 406,
      //     reason: 'miss parameter'
      //   }
      // }

      const opts = { uId: req.user && req.user.userid, }, condition = {
        skip: req.query.skip || 0,
        limit: req.query.limit || 10,
        disabled: false,
        // aId:req.query.aId,
        archived: false,//req.query.archived === 'true',
        $sort: { createdTime: -1 },
      };
      typeof req.query.aId !== 'undefined' && (condition.aId = req.query.aId);
      typeof req.query.groupId !== 'undefined' && (condition.groupId = req.query.groupId);
      req.query.areaCode && (condition.areaCode = `/${req.query.areaCode}/`);
      typeof req.query.tId !== 'undefined' && (condition.tId = req.query.tId);
      if (req.query.startTime && req.query.endTime) {
        condition.createdTime = JSON.stringify({
          $gte: moment(req.query.startTime).utc().toDate(),
          $lte: moment(req.query.endTime).utc().toDate()//.add(1, 'd').toDate()
        });
      }
      req.query.withCirculation && (condition.circulationCount = { $gt: 0 });
      condition.$sort = req.query.$sort || { createdTime: -1 };
      Object.keys(req.query).filter(v => /^otherInfo\./.test(v)).forEach(k => condition[k] = req.query[k]);

      // if (req.Client && req.Client.tId) {
      //   condition.tId = req.Client.tId;
      // }

      const result = await outLandService.getApplicationOutLandList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getOutLandDetail(req, res) {
    const method = 'getOutLandDetail';
    debug(method, '[Enter]');
    try {

      if (!req.query.id) {
        throw {
          errorCode: 'E_OUT_LAND_003',
          httpCode: 406,
          reason: 'miss parameter id'
        }
      }

      const opts = { uId: req.user.userid, }, condition = { id: req.query.id, outlandId: req.query.outlandId };

      const result = await outLandService.getOutLandDetail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  /**
   * @api {get} /api/v1.0/ownership/list 所有权性质
   * @apiVersion 1.0.0
   * @apiName getOwnershipList
   * @apiGroup ins
   * @apiPermission authenticated
   *
   */
  async getOwnershipList(req, res) {
    const method = 'getOwnershipList'
    debug(method, '[Enter]')
    try {
      const result = await outLandService.getOwnershipList({}, {});
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {

    this._policyRouter.put('/api/v1.0/loan/application/out/land/archived', this.archivedOutLand.bind(this), {
      name: 'LoanApplicationOutLand.archivedOutLand',
      // role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.put('/api/v1.0/loan/application/out/land/finish', this.finishOutLand.bind(this), {
      name: 'LoanApplicationOutLand.finishOutLand',
    })

    this._policyRouter.post('/api/v1.0/loan/application/out/land', this.createOrEditOutLand.bind(this), {
      name: 'LoanApplicationOutLand.createOrEditOutLand',
      // role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.get('/api/v1.0/loan/application/out/land', this.getApplicationOutLandList.bind(this), {
      name: 'LoanApplicationOutLand.getApplicationOutLandList',
      role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.get('/api/v1.0/loan/out/land', this.getOutLandList.bind(this), {
      name: 'LoanApplicationOutLand.getOutLandList',
      role: PERMISSION_ROLE.EVERYONE
    })



    this._policyRouter.get('/api/v1.0/loan/application/out/land/detail', this.getOutLandDetail.bind(this), {
      name: 'LoanApplicationOutLand.getOutLandDetail',
      role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.get('/api/v1.0/ownership/list', this.getOwnershipList.bind(this), {
      name: 'ins.getLandProductList',
      role: PERMISSION_ROLE.EVERYONE
    });

    return this
  }

}

module.exports = OutLand;
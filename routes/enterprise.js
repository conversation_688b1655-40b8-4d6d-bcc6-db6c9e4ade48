/**
 * User Router
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:enterprise');
const enterpriseSvc = require('../services/enterprise');

const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const getUserFromReq = require('../utils/getUserFromReq').getUserFromReqCtx;

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
  * @api {get} /api/v1.0/enterprise/check 天眼查
  * @apiVersion 1.0.0
  * @apiName enterpriseCheck
  * @apiGroup Loan
  * @apiPermission authenticated 
  */
  async enterpriseCheck(req, res) {
    let method = 'enterpriseCheck'
    debug(method, '[Enter]')
    try {
      let userInfo = '';
      let tycNumMax = 20;
      if (!req.user || !req.user.userid) {
        // throw {
        //   errorCode: 'E_ENTERPRISE_034',
        //   httpCode: 401,
        //   reason: '用户未登录'
        // }
        tycNumMax = 100;
        userInfo = req.headers['x-forwarded-for'] || // 判断是否有反向代理 IP
          req.connection.remoteAddress || // 判断 connection 的远程 IP
          req.socket.remoteAddress || // 判断后端的 socket 的 IP
          req.connection.socket.remoteAddress;
      } else {
        userInfo = req.user.userid;
      }
      if(!req.query.word || req.query.word.length<0){
        throw {
          errorCode: 'E_ENTERPRISE_041',
          httpCode: 406,
          reason: 'Invalid word'
        }
      }
      let condition = {
        word:req.query.word
      };
      
      let opts = {
        userid: userInfo,
        tycNumMax,
        tId: req.Client && req.Client.tId
      };

      let result = await enterpriseSvc.tianYanCha(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this;

    self._policyRouter.get('/api/v1.0/enterprise/check', self.enterpriseCheck.bind(self), {
      name: 'enterprise.enterpriseCheck',
      role: PERMISSION_ROLE.EVERYONE
    });

    return self;
  }
}

module.exports = Router;
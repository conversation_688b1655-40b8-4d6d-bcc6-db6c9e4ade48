/**
 * @description: area routes 
 * @author: hexu 
 */

'use strict'

const logFactory = require('../utils/logFactory')
const logUtil = require('../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:dashboard.api:routes:area')
const statisSvc = require('../services/statis')
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE
const moment = require('moment');

class Statis {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  //统计分析
  async getLoanStatisSummary(req, res) {
    let method = 'getLoanStatisSummary'
    debug(method, '[Enter]')
    try {
      let condition = {}
      let opts = {};
      let result = await statisSvc.getLoanStatisSummary(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  init() {
    let self = this

    // public api 
    self._policyRouter.get('/api/v1.0/statis/summary', self.getLoanStatisSummary.bind(self), {
      name: 'statis.getLoanStatisSummary',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = Statis
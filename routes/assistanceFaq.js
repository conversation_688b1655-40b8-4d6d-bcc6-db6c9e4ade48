'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:assistanceFaq');
const assistanceFaqService = require('../services/assistanceFaq');
const { STATUS_MAP } = require('../services/assistanceFaq/const');
const moment = require('moment');

class AssistanceFaq {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getStatusList(req, res) {
    const method = 'getStatusList';
    debug(method, '[Enter]');
    try {
      const result = Array.from(STATUS_MAP, ([type, name]) => ({ type, name }));
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {get} /api/v1.0/assistance/faq/list 获取工单列表
   * @apiVersion 1.0.0
   * @apiName getAssistanceFaqList
   * @apiGroup AssistanceFaq
   * @apiPermission authenticated
   *
   * @apiDescription 获取工单列表
   *
   * @apiParam {Number} skip
   * @apiParam {Number} limit
   *
   * @apiSuccess {Number} total 总条数
   * @apiSuccess {Object[]} result 结果数组
   * @apiSuccess {String} result._id 记录_id
   */
  async getAssistanceFaqList(req, res) {
    const method = 'getAssistanceFaqList';
    debug(method, '[Enter]');
    try {
      const condition = {
        operator: req.user.userid,
        skip: req.query.skip || 0,
        limit: req.query.limit || 10,
        archived: false,
        $sort: { createdTime: -1 },
      };

      if (req.query.sn) condition.sn = req.query.sn;
      if (req.query.mobile) condition.mobile = req.query.mobile;
      if (req.query.userName) condition.userName = req.query.userName;
      if (req.query.status) condition.status = req.query.status;
      if (req.query.businessType != undefined) condition.businessType = req.query.businessType;
      if (req.query.category) condition.category = req.query.category;
      if (req.query.subCategory) condition.subCategory = req.query.subCategory;
      if (req.query.creatorName) condition.creatorName = `/${req.query.creatorName}/`;
      if (req.query.operatorName) condition.operatorName = `/${req.query.operatorName}/`;
      if (req.query.startTime) {
        const startTime = moment(req.query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (req.query.endTime) {
        const endTime = moment(req.query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }

      const opts = {};

      const result = await assistanceFaqService.getAssistanceFaqList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {get} /api/v1.0/assistance/faq/detail 获取工单详情
   * @apiVersion 1.0.0
   * @apiName getAssistanceFaqDetail
   * @apiGroup Queue
   * @apiPermission authenticated
   *
   * @apiDescription 获取工单详情
   *
   * @apiParam {String} id 工单_id
   *
   * @apiSuccess {String} _id 记录_id
   */
  async getAssistanceFaqDetail(req, res) {
    const method = 'getAssistanceFaqDetail';
    debug(method, '[Enter]');
    try {
      const condition = {
        id: req.query.id,
      };
      const opts = {
        userid: req.user.userid,
      };

      const result = await assistanceFaqService.getAssistanceFaqDetail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {put} /api/v1.0/assistance/faq 修改工单
   * @apiVersion 1.0.0
   * @apiName updateAssistanceFaq
   * @apiGroup AssistanceFaq
   * @apiPermission authenticated
   *
   * @apiDescription 修改工单
   *
   * @apiParam {String} id 记录_id
   */
  async updateAssistanceFaq(req, res) {
    const method = 'updateAssistanceFaq';
    debug(method, '[Enter]');
    try {
      const condition = {
        id: req.query.id,
        body: req.body,
      };
      const opts = {
        userid: req.user.userid,
      };

      const result = await assistanceFaqService.updateAssistanceFaq(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {delete} /api/v1.0/assistance-faq 删除工单
   * @apiVersion 1.0.0
   * @apiName removeAssistanceFaq
   * @apiGroup AssistanceFaq
   * @apiPermission authenticated
   *
   * @apiDescription 删除工单
   *
   * @apiParam {String} id 记录_id
   */
  async removeAssistanceFaq(req, res) {
    const method = 'removeAssistanceFaq';
    debug(method, '[Enter]');
    try {
      const condition = {
        id: req.query.id,
        archived: true,
      };

      const opts = {
        userid: req.user.userid,
      };

      const result = await assistanceFaqService.removeAssistanceFaq(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    const self = this;

    self._policyRouter.get('/api/v1.0/assistance/faq/status/list', self.getStatusList.bind(self), {
      name: 'assistanceFaq.getStatusList',
    });
    self._policyRouter.get('/api/v1.0/assistance/faq/list', self.getAssistanceFaqList.bind(self), {
      name: 'assistanceFaq.getAssistanceFaqList',
    });
    self._policyRouter.get('/api/v1.0/assistance/faq/detail', self.getAssistanceFaqDetail.bind(self), {
      name: 'assistanceFaq.getAssistanceFaqDetail',
    });
    self._policyRouter.put('/api/v1.0/assistance/faq', self.updateAssistanceFaq.bind(self), {
      name: 'assistanceFaq.updateAssistanceFaq',
    });
    self._policyRouter.delete('/api/v1.0/assistance/faq', self.removeAssistanceFaq.bind(self), {
      name: 'queue.removeAssistanceFaq',
    });

    return self;
  }
}

module.exports = AssistanceFaq;

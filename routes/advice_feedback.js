/**
 * <AUTHOR>
 * 2019-05-05
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:advice_feedback');
const adviceFeedbackSvc = require('../services/advice_feedback');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class AdviceFeedback {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }


  async createAdviceFeedback(req, res) {
    let method = 'createAdviceFeedback'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.content) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_065',
          reason: 'invalid param'
        };
      }
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_DISTRIBUTE_63',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let condition = {
        cId: req.Client && req.Client._id,
        eId: req.user && req.user.userid,
        content: req.body.content,
        images: req.body.images || {}
      };
      let opts = {};

      let result = await adviceFeedbackSvc.createAdviceFeedback(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this

    self._policyRouter.post('/api/v1.0/advice/feedback/add', self.createAdviceFeedback.bind(self), {
      name: 'client.createAdviceFeedback',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = AdviceFeedback
/**
 * <AUTHOR>
 * 2019-06-03
 */
'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:loanProduct');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const loanProductSvc = require('../services/loanProduct')
const moment = require('moment');
class LoanProduct {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }


  //产品列表
  async loanProductList(req, res) {
    let method = 'loanProductList';
    debug(method, '[Enter]');
    try {
      if (!req.Client || !req.query.tId) {
        throw {
          errorCode: 'E_LOAN_PRO_160',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }
      let condition = {
        tId: req.query.tId,
        archived: false,
        enable: true,
        limit: req.query.limit || 10,
        skip: req.query.skip || 0,
        $sort: req.query.$sort || {
          createdTime: 1
        }
      };

      if (req.query.consumerType) {
        condition.consumer_t = req.query.consumerType
      }

      let opts = {
        user: req.user
      };
      if (req.query.areaCode) {
        opts.areaCode = req.query.areaCode
      }

      let result = await loanProductSvc.loanProductList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  //产品列表
  async loanProductListV2(req, res) {
    let method = 'loanProductListV2';
    debug(method, '[Enter]');
    try {
      if (!req.Client || !req.query.tId) {
        throw {
          errorCode: 'E_LOAN_PRO_160',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }
      let condition = {
        tId: req.query.tId,
        archived: false,
        enable: true,
        /*name  : {
          $regex: '^(?!.*?建设)'
        },*/
        limit: req.query.limit || 10,
        skip: req.query.skip || 0,
        $sort: req.query.$sort || {
          createdTime: 1
        }
      };

      if (req.query.consumerType) {
        condition.consumer_t = req.query.consumerType
      }

      let opts = {
        user: req.user
      };
      if (req.query.areaCode) {
        opts.areaCode = req.query.areaCode
      }

      let result = await loanProductSvc.loanProductList(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  init() {
    let self = this;

    self._policyRouter.get('/api/v1.0/loanProduct/list', self.loanProductList.bind(self), {
      name: 'loanProduct.loanProductList',
      //role: PERMISSION_ROLE.EVERYONE
    });
    self._policyRouter.get('/api/v2.0/loanProduct/list', self.loanProductListV2.bind(self), {
      name: 'loanProduct.loanProductListV2',
      //role: PERMISSION_ROLE.EVERYONE
    });

    return self
  }
}
module.exports = LoanProduct;
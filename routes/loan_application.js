/**
 * <AUTHOR>
 * 2020-01-09
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:loanApplication');
const loanApplicationSvc = require('../services/loan_application');
const loanApplicationService = loanApplicationSvc;
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const moment = require('moment');
const LOAN_STATUS = require('../utils/const/applicationConst').LOAN_APP_STATUS;
const { assert, formatParas } = require('../utils/general')
const { cloneDeep } = require('lodash');
const { PRODUCT } = require('../utils/fundConst');
const { TENANT_LIST } = require('../utils/tenantConst');

class LoanApplication {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async putLoanAppInterview(req, res) {
    let method = 'putLoanAppInterview'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.id || req.body.result == undefined) {
        throw {
          errorCode: 'E_APPLOAN_34',
          httpCode: 406,
          reason: 'invalid param'
        }
      }

      let targetStatus;
      let parameters = {
        result: req.body.result,
        processTime: req.body.processTime,
        infoIsReal: req.body.infoIsReal
      };
      if (parameters.result) {
        parameters.isIncoming = req.body.isIncoming;
        parameters.incomingTime = req.body.incomingTime;
        targetStatus = LOAN_STATUS.LOAN_APP_WAIT_INCOME;
      } else {
        parameters.comments = req.body.rejectReason;
        targetStatus = LOAN_STATUS.LOAN_APP_REJECTED_INTERVIEW;
      }

      for (let key in parameters) {
        if (parameters[key] === undefined || parameters[key] === null || parameters[key] === "") {
          throw {
            errorCode: 'E_APPLOAN_55',
            httpCode: 406,
            reason: 'invalid param'
          }
        }
      }

      let condition = {
        id: req.body.id,
        body: {
          status: targetStatus
        }
      };
      let opts = {
        user: req.user,
        status: targetStatus,
        trackingOpt: {
          src_t: "staff",
          source: req.user.userid,
          target_t: 1,
          target: req.body.id,
          action: targetStatus,
          comments: parameters.comments || '',
          parameters: parameters
        }
      };

      let result = await loanApplicationSvc.putLoanApplication(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async putLoanAppFinal(req, res) {
    let method = 'putLoanAppFinal'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.id || req.body.result == undefined) {
        throw {
          errorCode: 'E_APPLOAN_109',
          httpCode: 406,
          reason: 'invalid param'
        }
      }

      let targetStatus;
      let parameters = {
        result: req.body.result,
        processTime: req.body.processTime
      };
      if (parameters.result) {
        parameters.loaned = req.body.loaned;
        targetStatus = LOAN_STATUS.LOAN_APP_WAITLOAN;
      } else {
        parameters.comments = req.body.rejectReason;
        targetStatus = LOAN_STATUS.LOAN_APP_REJECTED_FINAL;
      }

      for (let key in parameters) {
        if (parameters[key] === undefined || parameters[key] === null || parameters[key] === "") {
          throw {
            errorCode: 'E_APPLOAN_131',
            httpCode: 406,
            reason: 'invalid param'
          }
        }
      }

      let condition = {
        id: req.body.id,
        body: {
          status: targetStatus
        }
      };
      let opts = {
        user: req.user,
        status: targetStatus,
        trackingOpt: {
          src_t: "staff",
          source: req.user.userid,
          target_t: 1,
          target: req.body.id,
          action: targetStatus,
          comments: parameters.comments || '',
          parameters: parameters
        }
      };

      let result = await loanApplicationSvc.putLoanApplication(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async putLoaned(req, res) {
    let method = 'putLoaned'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.id || req.body.result == undefined) {
        throw {
          errorCode: 'E_APPLOAN_182',
          httpCode: 406,
          reason: 'invalid param'
        }
      }

      let targetStatus;
      let parameters = {
        result: req.body.result,
        processTime: req.body.processTime
      };
      if (parameters.result) {
        parameters.loanedAmount = req.body.loanedAmount;
        parameters.loanedType = req.body.loanedType;
        parameters.loanedTerm = req.body.loanedTerm;
        targetStatus = LOAN_STATUS.LOAN_APP_LOANED;
      } else {
        parameters.comments = req.body.rejectReason;
        targetStatus = LOAN_STATUS.LOAN_APP_REJECTED_LOAN;
      }

      for (let key in parameters) {
        if (parameters[key] === undefined || parameters[key] === null || parameters[key] === "") {
          throw {
            errorCode: 'E_APPLOAN_198',
            httpCode: 406,
            reason: 'invalid param'
          }
        }
      }

      let condition = {
        id: req.body.id,
        body: {
          status: targetStatus,
          loanTerm: parameters.loanedTerm,
          actualLoan: parameters.loanedAmount
        }
      };
      let opts = {
        user: req.user,
        status: targetStatus,
        trackingOpt: {
          src_t: "staff",
          source: req.user.userid,
          target_t: 1,
          target: req.body.id,
          action: targetStatus,
          comments: parameters.comments || '',
          parameters: parameters
        }
      };

      let result = await loanApplicationSvc.putLoanApplication(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getJXHomeNum(req, res) {
    let method = 'getJXHomeNum'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_APPLOAN_228',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let condition = {
        userid: req.user.userid,
        tId: req.Client && req.Client.tId
      };
      let opts = {};

      let result = await loanApplicationSvc.getJXHomeNum(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getClaimNum(req, res) {
    let method = 'getClaimNum'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_APPLOAN_228',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let role = ''
      if (req.headers && req.headers['x-dash-role']) {
        role = req.headers['x-dash-role'];
      } else {
        throw {
          errorCode: 'E_APPLOAN_436',
          httpCode: 401,
          reason: 'role null'
        }
      }
      let condition = {
        userid: req.user.userid,
        tId: req.Client && req.Client.tId
      };
      let opts = {
        role: role
      };

      let result = await loanApplicationSvc.getClaimNum(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getAlreadList(req, res) {
    let method = 'getAlreadList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_APPLOAN_256',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let condition = {
        distribute: req.user.userid,
        handleStatus: true,
        skip: req.query.skip,
        limit: req.query.limit,
        $sort: req.query.$sort || {
          aId: -1
        }
      }
      let opts = {
        tId: req.Client && req.Client.tId
      };

      let result = await loanApplicationSvc.getAlreadList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getDoList(req, res) {
    let method = 'getDoList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_APPLOAN_256',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let condition = {
        archived: false,
        status: "biopsy_approved",
        skip: req.query.skip,
        limit: req.query.limit,
        $sort: req.query.$sort || {
          createdTime: -1
        }
      }
      let opts = {
        userid: req.user.userid
      };

      let result = await loanApplicationSvc.getDoList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getCollectionMid(req, res) {
    let method = 'getCollectionMid'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_APPLOAN_326',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query.type) {
        throw {
          errorCode: 'E_APPLOAN_333',
          httpCode: 406,
          reason: 'type not null'
        }
      }

      let condition = {
        type: req.query.type,
        uniqueCode: req.query.uniqueCode
      }
      let opts = {
        userid: req.user.userid
      };

      let result = await loanApplicationSvc.getCollectionMid(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async smsUrging(req, res) {
    let method = 'smsUrging'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_APPLOAN_326',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query.loanId) {
        throw {
          errorCode: 'E_APPLOAN_370',
          httpCode: 406,
          reason: 'type not null'
        }
      }

      let condition = {
        loanId: req.query.loanId
      }
      let opts = {
        userid: req.user.userid
      };

      let result = await loanApplicationSvc.smsUrging(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async getJxDoAndAlreadList(req, res) {
    let method = 'getJxDoAndAlreadList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_APPLOAN_256',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let role = ''
      if (req.headers && req.headers['x-dash-role']) {
        role = req.headers['x-dash-role'];
      } else {
        throw {
          errorCode: 'E_APPLOAN_436',
          httpCode: 401,
          reason: 'role null'
        }
      }

      let condition = {
        skip: req.query.skip,
        limit: req.query.limit,
        // assignStatus:role,
        archived: false,
        $sort: req.query.$sort || {
          aId: -1
        }
      }
      condition.assigned = req.user.userid;
      if (req.query.type == 'assigned') {
        condition.handleStatus = false;
      } else {
        condition.handleStatus = true;
      }
      let opts = {
        tId: req.Client && req.Client.tId,
        role: role
      };

      let result = await loanApplicationSvc.getJxDoAndAlreadList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {get} /api/v1.0/loan/application/list 获取金融借贷申请列表
   * @apiVersion 1.0.0
   * @apiName getLoanApplicationList
   * @apiGroup LoanApplication
   * @apiPermission authenticated
   *
   * @apiDescription 获取金融借贷申请列表
   *
   * @apiParam {Number}    skip
   * @apiParam {Number}    limit
   *
   * @apiExample Example usage:
   * curl -i http://localhost/api/v1.0/loan/application/list
   *
   * @apiSuccess {Number}    total           总条数
   * @apiSuccess {Object[]}    result        结果数组
   * @apiSuccess {String}    result._id        申请记录的_id
   * @apiSuccess {String}    result.pId        产品金融_id
   * @apiSuccess {Number}    result.amount     申请额度，单位为元
   * @apiSuccess {String}    result.username    用户姓名
   * @apiSuccess {String}    result.userMobile    用户手机号
   * @apiSuccess {String}    result.formatCreatedTime    创建时间
   * @apiSuccess {String}    result.area    地区信息的areaCode
   * @apiSuccess {String}    result.status    申请状态说明. 初始状态为new, 代表申请已提交, 处于新申请状态; pre_censor(preliminary censoring), 当用户实名通过且人像对比通过, 处于待初审状态; review, 当待初审通过之后, 处于待复审状态; waitSign: 待网签; signed: 网签完成; waitLoan, 待放款状态(其前置状态待确定); loaned, 已放款状态; rejected_new_1, 实名失败, 订单结束; rejected_new_2, 申请和实名不是一个人, 订单结束; rejected_new_3, 人像比对未通过, 订单结束; rejected_censor, 初审被拒绝, 订单结束; unpaid 复审通过待支付
   * @apiSuccess {String}    result.type    申请单的类型，与产品相关。nhd：红本贷
   * @apiSuccess {Number}    result.actualLoan     实际放款金额
   * @apiSuccess {String}    result.biopsyConfidence    活体检测的置信度
   * @apiSuccess {String=rejected,approved}    result.biopsyStatus    活体检测的状态  rejected未通过 , approved通过
   * @apiSuccess {Object}    [result.totalFee]  支付金额
   *
   * @apiSuccessExample {json} Response (example):
   *
   *     HTTP/1.1 200 OK
   *     {
   *        total: 1,
   *        result: [{
              "_id": "5bfa0cdfae95156d5e6d8ddd",
              "pId": "5bf7c3ca9a5b9c21cac77149",
              "amount": 100,
              "username": "username",
              "userMobile": "userMobile",
              "area": "area",

              "status": "new",
              "uId": "5aa4017debcc2c8e0103e1d2",
              "type": "wnd",
              "createdTime": "2018-11-25T02:45:51.814Z",
              "lastModTime": "2018-11-25T02:45:51.814Z",
              "archived": false,
              "__v": 0
            }]
   *     }
   *
   * @apiError ErrorQuery error params
   *
   * @apiError MissingParameters The parameters must be given
   *
   * @apiErrorExample {json} Error-Response:
   *     HTTP/1.1 401 Not Acceptable
   *     {
   *       "errorCode": "E_LOAN_APP_186",
   *       "reason": "用户未登录"
   *     }
   *
   * @apiError(Error 50x) InternalServerError   服务器内部错误
   *
   */
  async getLoanApplicationList(req, res) {
    let method = 'getLoanApplicationList'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_LOAN_APP_186',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      const query = Object.entries(req.query).filter(([k, v]) => v)
        .reduce((res, v) => (res[v[0]] = v[1], res), {});
      const opts = {
        tId: query.tId || req.Client.tId,
        user: req.user,
        uId: req.user && req.user.userid,
        roleId: req.query.roleId,
        statusAfter: query.statusAfter,
        statusAfterType: query.statusAfterType,
        getAllArea: parseInt(query.getAllArea) || 0,
        outlandAbleStatus: query.outlandAbleStatus && query.outlandAbleStatus === 'true',
      };
      debug(`${method}Opts`, query.getAllArea, opts);
      query.superviseStatus && (query['verifyInfo.superviseExtendInfo.superviseStatus'] = query.superviseStatus);
      query.requestName && (query['addons.info.requestName'] = { '$regex': `${query.requestName}`, '$options': 'si' });
      query.requestType && (query['addons.info.type'] = query.requestType);
      delete query.roleId;
      delete query.statusAfter;
      delete query.statusAfterType;
      delete query.superviseStatus;
      delete query.getAllArea;
      delete query.requestName;
      delete query.requestType;
      delete query.outlandAbleStatus;

      let condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        tId: query.tId,
        archived: false
      };
      Object.assign(condition, query);

      // condition.uId = req.user.userid;
      query.area && (condition.area = { '$regex': `^${query.area}`, '$options': 'si' });
      query.areaCode && (condition.area = { '$regex': `^${query.areaCode}`, '$options': 'si' });

      delete condition.areaCode;
      condition.$sort = query.$sort && JSON.parse(query.$sort) || {
        createdTime: -1
      };
      if (condition.startTime) {
        let startTime = moment(condition.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (condition.endTime) {
        let endTime = moment(condition.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      // condition.area && (condition.area = `/^${condition.area}/`);
      delete condition.startTime;
      delete condition.endTime;

      query.status && (condition.status = { $in: query.status.split(',') });
      // if (query.status) condition.status = query.status;
      //判断委托监管的数量>0 粮食入库监管筛选需要
      if (condition.filterFoodConsignment == "1") {
        condition['verifyInfo.superviseExtendInfo.entrustCount'] = { $ne: 0 };
        condition['verifyInfo.superviseExtendInfo.superviseStatus'] = 'hadSubmit';
      }
      opts.onlyMyOrg = condition.onlyMyOrg;
      opts.toBeHandledByMe = req.query.toBeHandledByMe && true || false;
      delete condition.filterFoodConsignment;
      delete condition.onlyMyOrg;
      delete condition.toBeHandledByMe;
      opts.uId = req.user && req.user.userid;
      opts.roleId = req.query.roleId || req.headers['x-role-id'];
      // opts.role = req.headers && req.headers['x-role-id'] ;
      debug(`${method}roleid`, 'x-dash-role:', req.headers['x-dash-role'], 'x-role-id:', req.headers['x-role-id'], 'role:', req.headers['role'])

      let result = await loanApplicationSvc.getLoanApplicationList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getLoanApplicationDetail(req, res) {
    let method = 'getLoanApplicationDetail'
    debug(method, '[Enter]')
    try {

      if (!req.query.id) {
        throw {
          errorCode: 'E_LOAN_APP_064',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }

      let condition = {
        id: req.query.id,
        isRec:req.query.isRec
      };
      let opts = { uId: req.user && req.user.userid, };
      opts.roleId = req.headers && req.headers['x-role-id'];
      debug(`${method}roleid`, 'x-dash-role:', req.headers['x-dash-role'], 'x-role-id:', req.headers['x-role-id'], 'role:', req.headers['role'])

      let result = await loanApplicationSvc.getLoanApplicationDetail(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async executeLoanAppVerify({ justSave, finished, investigationVerify, addAssuranceInfo }, req, res) {
    const method = 'executeLoanAppVerify'
    debug(method, '[Enter]');
    process.env.NODE_ENV !== 'local-dev' && delete req.body.uId;
    try {
      const condition = cloneDeep({
        id: req.body.id,
        action: req.body.action,
        description: req.body.description,
        roleId: req.body.roleId || req.headers['x-role-id'],
        userId: req.user && req.user.userid || req.body.uId,
        verifyInfo: req.body.verifyInfo || {},
        supplements: req.body.supplements,
        addonVerifyInfo: req.body.addonVerifyInfo || {},
        enough: req.body.enough,
      });
      process.env.NODE_ENV === 'local-dev' && req.body.userId && (condition.userId = req.body.userId);
      assert(condition.userId, 'E_VERIFY_EXECUTE_R_000', 'no permission')
      justSave && (condition.action = 'justSave');
      !justSave && condition.action === 'justSave' && (delete condition.action); //这个接口不许只保存
      const opts = { finished, investigationVerify, addAssuranceInfo };
      opts.finished = opts.finished || req.body.finished;
      opts.investigationVerify = opts.investigationVerify || req.body.investigationVerify;
      opts.addAssuranceInfo = opts.addAssuranceInfo || req.body.addAssuranceInfo;
      const result = await loanApplicationService.executeLoanAppVerify(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async investigationVerify2Submit(req, res) {
    const method = 'executeLoanAppVerify'
    debug(method, '[Enter]');
    process.env.NODE_ENV !== 'local-dev' && delete req.body.uId;
    try {
      const condition = cloneDeep({
        id: req.body.id,
        action: req.body.action,
        description: req.body.description,
        roleId: req.body.roleId || req.headers['x-role-id'],
        userId: req.user && req.user.userid,
        verifyInfo: req.body.verifyInfo || {},
        supplements: req.body.supplements,
        addonVerifyInfo: req.body.addonVerifyInfo || {},
        enough: req.body.enough,
      });
      const opts = { saveSupplementsAtPreHandle: true };
      const result = await loanApplicationService.investigationVerify2Submit(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 已放款用户列表
  async getLoanApplicationUserList(req, res) {
    let method = 'getLoanApplicationList'
    debug(method, '[Enter]')
    try {

      const query = Object.entries(req.query).filter(([k, v]) => v)
        .reduce((res, v) => (res[v[0]] = v[1], res), {});
      const opts = {
        tId: query.tId || req.Client.tId,
        user: req.user,
        uId: req.user && req.user.userid,
        roleId: req.query.roleId,
      };
      delete query.roleId;
      delete query.statusAfter;
      delete query.statusAfterType;
      delete query.superviseStatus;

      let condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        tId: query.tId,
        archived: false
      };
      Object.assign(condition, query);

      // condition.uId = req.user.userid;
      query.area && (condition.area = { '$regex': `^${query.area}`, '$options': 'si' });
      query.areaCode && (condition.area = { '$regex': `^${query.areaCode}`, '$options': 'si' });

      delete condition.areaCode;
      condition.$sort = query.$sort && JSON.parse(query.$sort) || {
        createdTime: -1
      };
      if (condition.startTime) {
        let startTime = moment(condition.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
      }
      if (condition.endTime) {
        let endTime = moment(condition.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      // condition.area && (condition.area = `/^${condition.area}/`);
      delete condition.startTime;
      delete condition.endTime;


      opts.uId = req.user && req.user.userid;
      opts.roleId = req.query.roleId || req.headers['x-role-id'];

      let result = await loanApplicationSvc.getLoanApplicationUserList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getCrmLoanAppList(req, res) {
    let method = 'getCrmLoanAppList'
    debug(method, '[Enter]')
    try {
      /* if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_755',
          httpCode: 401,
          reason: '用户未登录'
        }
      } */
      if (!req.query || !req.query.idCard) {
        throw {
          errorCode: 'E_LOAN_APP_763',
          httpCode: 406,
          reason: 'Invalid params'
        }
      }
      let condition = {
        archived: false,
        idCard: req.query.idCard,
        $sort: req.query.$sort || { lastModTime: -1 }
      };
      if (req.query.status) {
        condition.status = req.query.status;
      }
      if (req.query.isAll == "true") {
        condition.isAll = true;
      }

      const opts = {
        //user: req.user
      };

      let result = await loanApplicationSvc.getCrmLoanAppList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getCzdLoanAppList(req, res) {
    let method = 'getCzdLoanAppList'
    debug(method, '[Enter]')
    try {
      const query = Object.entries(req.query).filter(([k, v]) => v)
        .reduce((res, v) => (res[v[0]] = v[1], res), {});
      const opts = {
        userInfo: req.user,
        uId: req.user && req.user.userid,
        role: req.query.role,
      };
      delete query.roleId;

      let condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        pId: PRODUCT.CG_CZD_ID,
        tId: TENANT_LIST.TENANT_ZN,
        archived: false
      };
      Object.assign(condition, query);

      query.area && (condition.area = { '$regex': `^${query.area}`, '$options': 'i' });
      query.username && (condition.username = `/${query.username}/`);
      query.userMobile && (condition.userMobile = `/${query.userMobile}/`);
      condition.$sort = query.$sort && JSON.parse(query.$sort) || {
        createdTime: -1
      };
      if (condition.startDate) {
        let startDate = moment(condition.startDate).utc().format();
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startDate;
      }
      if (condition.endDate) {
        let endDate = moment(condition.endDate).utc().add(1, 'd').format();
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endDate;
      }

      delete condition.startDate;
      delete condition.endDate;

      opts.role = req.query.role || req.headers['role'];

      let result = await loanApplicationSvc.getCzdLoanAppList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async postCzdCallTracking(req, res) {
    let method = 'postCzdCallTracking'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.aId) {
        throw {
          errorCode: 'E_LOAN_APP_907',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      let trackingOpt = {
        src_t: "staff",
        source: req.user.userid,
        target_t: 1,
        target: req.body.aId,
        action: "call_phone",
        comments: ""
      };

      let opts = {};

      let result = await loanApplicationSvc.postCzdCallTracking(trackingOpt, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 业务重组列表
  async getLoanRestructuring(req, res) {
    let method = 'getLoanRestructuring'
    debug(method, '[Enter]')
    try {

      // if (!req.query || !req.query.idCard) {
      //   throw {
      //     errorCode: 'E_LOAN_APP_763',
      //     httpCode: 406,
      //     reason: 'Invalid params'
      //   }
      // }
      let condition = {
        limit: req.query.limit || 10,
        skip: req.query.skip || 0,
      };
      if (req.query.username) {
        condition.username = req.query.username;
      }
      if (req.query.userMobile) {
        condition.userMobile = req.query.userMobile;
      }
      //nhd , nzd , ncd
      let mapType = new Map([['1', 'nhd'], ['2', 'nzd'], ['3', 'ncd']]);
      if (req.query.type) {
        condition.type = mapType.get(req.query.type);
      }
      if (req.query.areaCode) {
        condition.areaCode = req.query.areaCode;
      }
      if(req.query.loanRestructuring){
        condition.loanRestructuring = req.query.loanRestructuring == '1' ? 1:0;
      }

      let opts = {};

      opts.uId = req.user && req.user.userid;
      opts.roleId = req.query.roleId || req.headers['x-role-id'];

      let result = await loanApplicationSvc.getLoanRestructuring(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 申请重组
  async restructuringApply(req, res) {
    let method = 'restructuringApply'
    debug(method, '[Enter]')
    try {
      //lowerAmount amount
      let params = ['IDCard','name','mobile','landCode','relationship','originalRelationship','aId','oaImages'];

      let condition = {
        status: 2,
        trackings:[
          {
            action: 'restructuring_apply',
            target_t: 1,
            target: req.user.userid,
            comments: '提交申请成功',
            createdTime: new Date()
          }
        ],
        operator: req.user.userid
      };
      params.forEach(item => {
        if (!req.body || !req.body[item]) {
          throw {
            errorCode: 'E_LOAN_APP_963',
            httpCode: 406,
            reason: 'Invalid params'
          }
        }
        condition[item] = req.body[item];
      })
      condition.lowerAmount = req.body.lowerAmount * 1 || 0; //压降金额
      condition.isOwner = req.body.isOwner ? true : false; //是否是户主
      
      let result = await loanApplicationSvc.restructuringApply(condition, {})
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 取消重组申请
  async restructuringOff(req, res) {
    let method = 'restructuringOff'
    debug(method, '[Enter]')
    try {

      if (!req.query || !req.query.id) {
        throw {
          errorCode: 'E_LOAN_APP_1037',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      let condition = {
        id: req.query.id
      };
      let opts = {
        tId: req.Client && req.Client.tId
      };
      opts.uId = req.user && req.user.userid;
      
      let result = await loanApplicationSvc.restructuringOff(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 重组详情
  async restructuringDetail(req, res) {
    let method = 'restructuringDetail'
    debug(method, '[Enter]')
    try {

      if (!req.query || !req.query.id) {
        throw {
          errorCode: 'E_LOAN_APP_1037',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      let condition = {
        id: req.query.id
      };
      let opts = {
        tId: req.Client && req.Client.tId
      };
      
      let result = await loanApplicationSvc.restructuringDetail(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this

    self._policyRouter.put('/api/v1.0/application/interview', self.putLoanAppInterview.bind(self), {
      name: 'loanApplication.putLoanAppInterview',
      humanClick: {
        enabled: true,
        interval: 5000,
        scope: {
          perQuery: 'result'
        },
        sidStrategy: {
          sidFromBodyItem: 'result'
        }
      },
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.put('/api/v1.0/application/final', self.putLoanAppFinal.bind(self), {
      name: 'loanApplication.putLoanAppFinal',
      humanClick: {
        enabled: true,
        interval: 5000,
        scope: {
          perQuery: 'result'
        },
        sidStrategy: {
          sidFromBodyItem: 'result'
        }
      },
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.put('/api/v1.0/application/loaned', self.putLoaned.bind(self), {
      name: 'loanApplication.putLoaned',
      humanClick: {
        enabled: true,
        interval: 5000,
        scope: {
          perQuery: 'loanedAmount'
        },
        sidStrategy: {
          sidFromBodyItem: 'loanedAmount'
        }
      },
      role: PERMISSION_ROLE.EVERYONE
    })


    self._policyRouter.get('/api/v1.0/application/home/<USER>', self.getJXHomeNum.bind(self), {
      name: 'loanApplication.getJXHomeNum',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/application/alread/list', self.getAlreadList.bind(self), {
      name: 'loanApplication.getAlreadList',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/application/do/list', self.getDoList.bind(self), {
      name: 'loanApplication.getDoList',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/application/get/mid', self.getCollectionMid.bind(self), {
      name: 'loanApplication.getCollectionMid',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/application/invite/signing', self.smsUrging.bind(self), {
      name: 'loanApplication.smsUrging',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/application/claim/num', self.getClaimNum.bind(self), {
      name: 'loanApplication.getClaimNum',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/application/jx/do/list', self.getJxDoAndAlreadList.bind(self), {
      name: 'loanApplication.getClaimNum',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/loan/application/list', self.getLoanApplicationList.bind(self), {
      name: 'loanApplication.getLoanApplicationList',
      // role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/loan/application/detail', self.getLoanApplicationDetail.bind(self), {
      name: 'loanApplication.getLoanApplicationDetail'
    })

    this._policyRouter.post('/api/v1.0/loan/application/verify/execute', (...paras) => this.executeLoanAppVerify({ justSave: false }, ...paras), {
      name: 'loanApplication.executeLoanAppVerify',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.put('/api/v1.0/loan/application/finish', (...paras) => this.executeLoanAppVerify({ finished: true }, ...paras), {
      name: 'loanApplication.executeLoanAppVerifyFinish',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.post('/api/v1.0/loan/application/verify/update', (...paras) => this.executeLoanAppVerify({ justSave: true }, ...paras), {
      name: 'loanApplication.executeLoanAppUpdate',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.put('/api/v1.0/loan/application/verify/investigation/verify2/submit', (...paras) => this.investigationVerify2Submit(...paras), {
      name: 'loanApplication.investigationVerify2Submit',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.put('/api/v1.0/loan/application/verify/investigation/verify2/execute', (...paras) => this.executeLoanAppVerify({ investigationVerify: true }, ...paras), {
      name: 'loanApplication.investigationVerify2Execute',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.put('/api/v1.0/loan/application/verify/assurance/info/add', (...paras) => this.executeLoanAppVerify({ addAssuranceInfo: true }, ...paras), {
      name: 'loanApplication.addAssuranceInfo',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/loan/application/user/list', this.getLoanApplicationUserList.bind(self), {
      name: 'loanApplication.getLoanApplicationList',
      role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.get('/api/v1.0/loan/application/crm/list', this.getCrmLoanAppList.bind(self), {
      name: 'loanApplication.getCrmLoanAppList',
      role: PERMISSION_ROLE.EVERYONE
    })
    //车主贷订单列表
    this._policyRouter.get('/api/v1.0/loan/application/czd/list', this.getCzdLoanAppList.bind(self), {
      name: 'loanApplication.getCzdLoanAppList',
      //role: PERMISSION_ROLE.EVERYONE
    })

    //车主贷拨打记录
    this._policyRouter.post('/api/v1.0/loan/czd/call/tracking', this.postCzdCallTracking.bind(self), {
      name: 'loanApplication.postCzdCallTracking'
    })

    //申请业务重组
    this._policyRouter.get('/api/v1.0/loan/restructuring/list', this.getLoanRestructuring.bind(self), {
      name: 'loanApplication.getLoanRestructuring'
    })
    this._policyRouter.post('/api/v1.0/loan/restructuring/apply', this.restructuringApply.bind(self), {
      name: 'loanApplication.restructuringApply'
    })
    this._policyRouter.put('/api/v1.0/loan/restructuring/off', this.restructuringOff.bind(self), {
      name: 'loanApplication.restructuringOff'
    })
    //重组详情
    this._policyRouter.get('/api/v1.0/loan/restructuring/detail', this.restructuringDetail.bind(self), {
      name: 'loanApplication.restructuringDetail'
    })

    return self
  }
}

module.exports = LoanApplication
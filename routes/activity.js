'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:app.api:routes:infoPersonal');
const activityService = require('../services/activity');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class ActivityPersonal {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }
  
  /**
   * @api {get} /api/v1.0/activity/user/list 活动用户
   * @apiVersion 1.0.0
   * 
   */
  async activityUserList(req, res) {
    let method = 'activityUserList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_109',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      // if(!req.query.id || !req.query.module){
      //   throw {
      //     errorCode: 'E_LOAN_APP_154',
      //     httpCode: 406,
      //     reason: 'parameter null'
      //   }
      // }
      
      let condition = {
        "name":req.query.name,
        "mobile":req.query.mobile,
        "areaCode":req.query.areaCode,
        "activityStartTime":req.query.activityStartTime,
        "activityEndTime":req.query.activityEndTime,
        "startTime":req.query.startTime,
        "endTime":req.query.endTime,
        "fastLoan":req.query.fastLoan,
        "survey":req.query.survey,
        "landRecord":req.query.landRecord,
        "gId":req.query.gId,
        skip: req.query.skip || 0,
        limit: req.query.limit || 10,
        $sort: req.query.$sort
      };

      let opts = {
        userid:req.user.userid
      };

      let result = await activityService.activityUserList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }
  
  /**
   * @api {get} /api/v1.0/activity/user/loan/record 用户家庭成员管理订单
   * @apiVersion 1.0.0
   * 
   */
  async userLoanRecord(req, res) {
    let method = 'userLoanRecord'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_LOAN_APP_109',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      // if(!req.query.id || !req.query.module){
      //   throw {
      //     errorCode: 'E_LOAN_APP_154',
      //     httpCode: 406,
      //     reason: 'parameter null'
      //   }
      // }
      
      let condition = {
        "IDCard":req.query.IDCard,
        skip: req.query.skip || 0,
        limit: req.query.limit || 10,
        $sort: req.query.$sort
      };

      let opts = {
      };

      let result = await activityService.userLoanRecord(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this;

    self._policyRouter.get('/api/v1.0/activity/user/list', self.activityUserList.bind(self), {
      name: 'activityPersonal.activityUserList',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/activity/user/loan/record', self.userLoanRecord.bind(self), {
      name: 'activityPersonal.userLoanRecord',
      role: PERMISSION_ROLE.EVERYONE
    })


    return self
  }
}

module.exports = ActivityPersonal
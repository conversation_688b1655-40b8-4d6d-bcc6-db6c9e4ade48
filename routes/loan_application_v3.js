/**
 * @summary loanApplication routes
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:routes:loanApplication');
const loanApplicationService = require('../services/loan_application_v3');

const moment = require('moment');
const Permission = require('../services/permission');

const LOAN_APP_STATUS = require('../utils/const/applicationConst').LOAN_APP_STATUS;
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;


class LoanApplication {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getLoanApplicationList(req, res) {
    let method = 'getLoanApplicationList'
    debug(method, '[Enter]')
    try {

      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_LOAN_APP_186',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let query = req.query;
      let condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        tId: req.Client && req.Client.tId,
        archived: false,
        $sort: {
          createdTime: -1
        }
      };
      if (query.sn) condition.sn = `/${query.sn}/`;
      if (query.outTradeNo) condition.outTradeNo = `/${query.outTradeNo}/`;
      if (query.username) condition.username = `/${query.username}/`;
      if (query.userMobile) condition.userMobile = `/${query.userMobile}/`;
      if (query.loanTerm) condition.loanTerm = query.loanTerm;
      if (query.type) condition.type = query.type;
      if (query.pId) condition.pId = query.pId;

      if (query.isHandover) condition.isHandover = query.isHandover === '1'

      if (query.idCard) condition.idCard = query.idCard;
      if (query.areaCodeSpecial) condition.areaCodeSpecial = query.areaCodeSpecial;
      if (query.destiner) condition.destiner = query.destiner;
      if (query.channel) condition.channel = query.channel;
      if (query.storename) condition['addons.enterpriseInfo.name'] = `/${query.storename}/`;

      if (query.fund) {
        if (query.fund.includes(",")) {
          let arr = query.fund.split(",");
          condition.fund = { $in: arr }
        } else {
          condition.fund = query.fund
        }
      }
      if (query.status) {
        if (query.status.includes(",")) {
          let arr = query.status.split(",");
          condition.status = { $in: arr };
        } else {
          condition.status = query.status;
        }
      }

      if (query.skipRiskCheck === 'true') {
        condition.skipRiskCheck = true;
      }
      if (query.startTime) {
        let startTime = moment(query.startTime).utc().format();
        condition.createdTime = {};
        condition.createdTime.$gte = startTime;
      }
      if (query.endTime) {
        let endTime = moment(query.endTime).utc().add(1, 'd').format();
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
      }
      if (query.lastStartTime) {
        let startTime = moment(query.lastStartTime).utc().format();
        condition.lastModTime = {};
        condition.lastModTime.$gte = startTime;
      }
      if (query.lastEndTime) {
        let endTime = moment(query.lastEndTime).utc().add(1, 'd').format();
        condition.lastModTime = condition.lastModTime || {};
        condition.lastModTime.$lte = endTime;
      }

      if (query.payStartTime) {
        let startTime = moment(query.payStartTime).utc().format();
        condition.paidTime = {};
        condition.paidTime.$gte = startTime;
      }
      if (query.payEndTime) {
        let endTime = moment(query.payEndTime).utc().add(1, 'd').format();
        condition.paidTime = condition.paidTime || {};
        condition.paidTime.$lte = endTime;
      }
      if (query.sort === "1") {
        condition.$sort = {
          lastModTime: 1
        }
      }

      if (query.sort === "2") {
        condition.$sort = {
          lastModTime: -1
        }
      }

      if (condition.status === "rejected") {
        condition.status = {
          $regex: '^rejected',
          $options: 'i'
        }
      }
      if (condition.status === "completing_info") {
        condition.status = {
          $in: ['biopsy_approved', 'decline_censor']
        };
      }
      if (condition.status === "wait_destine") {
        condition.status = {
          $in: ['pre_censor', 'pre_approve']
        };
      }
      // 大屏展示相关内容默认参数
      if (condition.status === "expo_default") {
        condition.status = {
          $in: [
            'waitLoan', // 待放款
            'loaned', // 已放款
            'finished', // 提前终止授信
            'finished_loan' // 账单已结清
          ]
        };
        condition.pId = '5bf7c3ca9a5b9c21cac77149';
      }
      if (query.contractorNo) condition['addons.contractorNo'] = query.contractorNo;

      //查询认领订单
      if (req.query.destined && req.query.destined == -1) {
        condition.destined = false;
      }

      if (req.query.loanCustomerType) {
        condition.loanCustomerType = req.query.loanCustomerType;
      }

      if (req.query.customerRoleType != undefined) {
        condition.customerRoleType = req.query.customerRoleType;
      }

      let opts = {
        userInfo: req.user,
        groupV2: req.query.groupV2
      };

      if (req.headers && req.headers['x-dash-role']) {
        opts.role = req.headers['x-dash-role'];
      }

      let result = await loanApplicationService.getLoanApplicationList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  // 订单详情
  async getLoanApplicationDetail(req, res) {
    let method = 'getLoanApplicationDetail'
    debug(method, '[Enter]')
    try {

      if (!req.query.id) {
        throw {
          errorCode: 'E_LOAN_APP_064',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }

      let condition = {
        id: req.query.id
      };
      let opts = {};
      // opts.role = req.headers && req.headers['x-role-id'] ;

      let result = await loanApplicationService.getLoanApplicationDetail(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this;
    self._policyRouter.get('/api/v3.0/loan/application/list', self.getLoanApplicationList.bind(self), {
      name: 'loanApplication.getLoanApplicationList',
      // role: Permission.PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v3.0/loan/application/detail', self.getLoanApplicationDetail.bind(self), {
      name: 'loanApplication.getLoanApplicationDetail',
      // role: Permission.PERMISSION_ROLE.EVERYONE
    })

    return self;
  }
}

module.exports = LoanApplication
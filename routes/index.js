/**
 * routes
 * <AUTHOR>
 */

'use strict'

const PolicyRouter = require('nongfu.merchant.policyrouter').PolicyRouter;
const express = require('express');

let router = express.Router();
let policyRouter = new PolicyRouter(router);
let HealthChecker = require('nongfu.merchant.policyrouter').HealthChecker;

//====================================
//   Policy Handlers Registraiton
//====================================
policyRouter.register(new HealthChecker('/api/v1.0/health/check'));

let SimpleLogger = require('./policies/logger')
policyRouter.register(new SimpleLogger())

let UrlPatternValidator = require('./policies/urlPattern')
policyRouter.register(new UrlPatternValidator())

let InvocationIDValidator = require('./policies/invocationid');
policyRouter.register(new InvocationIDValidator())

let Authenticator = require('./policies/auth');
policyRouter.register(new Authenticator());

let ClientBinder = require('./policies/client');
policyRouter.register(new ClientBinder());

let UserRoleAuth = require('./policies/userRoleAuth');
policyRouter.register(new UserRoleAuth());

let Permission = require('./policies/permission');
policyRouter.register(new Permission());

let HumanClickHandler = require('./policies/humanclick');
policyRouter.register(new HumanClickHandler());

let ServermodeHandler = require('./policies/servermode');
policyRouter.register(new ServermodeHandler());

let CachesettingHandler = require('./policies/cachesetting');
policyRouter.register(new CachesettingHandler());

const Auth = require('./auth');
new Auth(policyRouter).init();

//=========================
//   HttpSetting Router
//=========================
let HttpSetting = require('./httpSetting');
let httpSetting = new HttpSetting(policyRouter);
httpSetting.init();

//=========================
//   Survey Router
//=========================
let Survey = require('./survey');
let survey = new Survey(policyRouter);
survey.init();

//=========================
//   Survey Router
//=========================
let Dict = require('./dict');
let dict = new Dict(policyRouter);
dict.init();

//=========================
//   Survey Router
//=========================
let Area = require('./dict_area');
let area = new Area(policyRouter);
area.init();

//============================
//  client Router
//============================
let Client = require('./client');
let client = new Client(policyRouter);
client.init();

//============================
//  msgqueue Router
//============================
let Msgqueue = require('./msgqueue');
let msgqueue = new Msgqueue(policyRouter);
msgqueue.init();
//============================
//  loan_feedback Router
//============================
let LoanFeedback = require('./loan_feedback');
let loanFeedback = new LoanFeedback(policyRouter);
loanFeedback.init();

//=============================
//   loan_distribute Router
//=============================
let LoanDistribute = require('./loan_distribute');
let loanDistribute = new LoanDistribute(policyRouter);
loanDistribute.init();

//=============================
//   loan_assistant Router
//=============================
let LoanAssistant = require('./loan_assistant');
let loanAssistant = new LoanAssistant(policyRouter);
loanAssistant.init();

//=============================
//   loanApplication Router
//=============================
let LoanApplication = require('./loan_application');
let loanApplication = new LoanApplication(policyRouter);
loanApplication.init();

//=============================
//   employee Router
//=============================
let Employee = require('./employee');
let employee = new Employee(policyRouter);
employee.init();

//=============================
//   loan_charts Router
//=============================
let LoanCharts = require('./loan_charts');
let loanCharts = new LoanCharts(policyRouter);
loanCharts.init();

//=============================
//   advice_feedback Router
//=============================
let AdviceFeedback = require('./advice_feedback');
let adviceFeedback = new AdviceFeedback(policyRouter);
adviceFeedback.init();

//=============================
//   help_center Router
//=============================
let HelpCenter = require('./help_center');
let helpCenter = new HelpCenter(policyRouter);
helpCenter.init();
//============================
//  system Router
//============================
let System = require('./system');
let system = new System(policyRouter);
system.init();

//============================
//  organization Router
//============================
let Organization = require('./organization');
let organization = new Organization(policyRouter);
organization.init();

//============================
//  info personal Router
//============================
let InfoPersonal = require('./info_personal');
let infoPersonal = new InfoPersonal(policyRouter);
infoPersonal.init();

//============================
//  info enterprise Router
//============================
let InfoEnterprise = require('./info_enterprise');
let infoEnterprise = new InfoEnterprise(policyRouter);
infoEnterprise.init();

//============================
//  enterprise Router
//============================
let Enterprise = require('./enterprise');
let enterprise = new Enterprise(policyRouter);
enterprise.init();

//============================
//  collectionMgr Router
//============================
let CollectionMgr = require('./collection_mgr');
let collectionMgr = new CollectionMgr(policyRouter);
collectionMgr.init();


//============================
//  client_bundle_version Router
//============================
let ClientBundleVersion = require('./client_bundle_version');
let clientBundleVersion = new ClientBundleVersion(policyRouter);
clientBundleVersion.init();

//============================
//  activity Router
//============================
let Activity = require('./activity');
let activity = new Activity(policyRouter);
activity.init();

//============================
//  activity Router
//============================
let LoanApplicationV2 = require('./loan_application_v2');
let loanApplicationV2 = new LoanApplicationV2(policyRouter);
loanApplicationV2.init();

//============================
//  loanProduct Router
//============================
let LoanProduct = require('./loanProduct');
let loanProduct = new LoanProduct(policyRouter);
loanProduct.init();
//=========================
//   Carrousel Router
//=========================
let Carrousel = require('./carrousel');
let carrousel = new Carrousel(policyRouter);
carrousel.init();

//=========================
//   userCreaditEval Router
//=========================
let UserCreaditEval = require('./user_creadit_eval');
let userCreaditEval = new UserCreaditEval(policyRouter);
userCreaditEval.init();

const LoanApplicationOutLand = require('./loan_application_outland');
new LoanApplicationOutLand(policyRouter).init();

//=========================
//   Loan_application_outland_group Router
//=========================
let Loan_application_outland_group = require('./loan_application_outland_group');
new Loan_application_outland_group(policyRouter).init();

//=========================
//   systemorg Router
//=========================
let systemOrg = require('./system_org');
new systemOrg(policyRouter).init();

// AUTO-GENERATED-ROUTERS:START
const AssistanceRequest = require('./assistanceRequest');
new AssistanceRequest(policyRouter).init();

const AssistanceFaq = require('./assistanceFaq');
new AssistanceFaq(policyRouter).init();

const AssistanceSubscribe = require('./assistanceSubscribe');
new AssistanceSubscribe(policyRouter).init();

const AssistanceStatistic = require('./assistanceStatistic');
new AssistanceStatistic(policyRouter).init();

const AssistanceAgent = require('./assistanceAgent');
new AssistanceAgent(policyRouter).init();

const LoanApplicationInsureOrder = require('./loanApplicationInsureOrder');
new LoanApplicationInsureOrder(policyRouter).init();
// AUTO-GENERATED-ROUTERS:END

const landSupervisory = require('./land_supervisory');
new landSupervisory(policyRouter).init();

const loanApplicationLandtype = require('./loan_application_landtype');
new loanApplicationLandtype(policyRouter).init();

const EstimatedOutput = require('./estimatedOutput');
let estimatedOutput = new EstimatedOutput(policyRouter);
estimatedOutput.init();
module.exports = router;

//=========================
//   info collect draft Router
//=========================
let infoCollectDraft = require('./info_collect_draft');
new infoCollectDraft(policyRouter).init();

//=========================
//   info collect draft Router
//=========================
let info_collect_history = require('./info_collect_history');
new info_collect_history(policyRouter).init();

//=========================
//   info iguopin draft Router
//=========================
let iguopin = require('./iguopin');
new iguopin(policyRouter).init();

//=========================
//   info collect draft Router
//=========================
let country_collect = require('./country_collect');
new country_collect(policyRouter).init();

const GisSrv = require('./gis');
new GisSrv(policyRouter).init();

const AssetsSrv = require('./assets');
new AssetsSrv(policyRouter).init();

const CgbcAi = require('./ai');
new CgbcAi(policyRouter).init();

//=========================
//   contractOwner Router
//=========================
let ContractOwner = require('./contractOwner');
let contractOwner = new ContractOwner(policyRouter);
contractOwner.init();

//=========================
//   capital3 Router
//=========================
let Capital3 = require('./capital3');
let capital3 = new Capital3(policyRouter);
capital3.init();

let LoanApplicationV3 = require('./loan_application_v3');
let loanApplicationV3 = new LoanApplicationV3(policyRouter);
loanApplicationV3.init();

const loan_application_fund_receive = require('./loan_application_fund_receive');
new loan_application_fund_receive(policyRouter).init();

const land_origin_fund_receive = require('./land_origin_fund_receive');
new land_origin_fund_receive(policyRouter).init();

const loan_application_disaster_claims = require('./loan_application_disaster_claims');
new loan_application_disaster_claims(policyRouter).init()

const Cxwq_insurance = require('./cxwq_insurance');
new Cxwq_insurance(policyRouter).init();

const grainManage_insurance = require('./grainManage');
new grainManage_insurance(policyRouter).init();

const loan_app_supervise_insurance = require('./loan_app_supervise');
new loan_app_supervise_insurance(policyRouter).init();

const loan_application_grain_depot = require('./loan_application_grain_depot');
new loan_application_grain_depot(policyRouter).init();

const Villager = require('./villager');
new Villager(policyRouter).init();

const User = require('./user');
new User(policyRouter).init();

const loan_application_customer_visit = require('./loan_application_customer_visit');
new loan_application_customer_visit(policyRouter).init();

const loan_village_info = require('./loan_village_info');
new loan_village_info(policyRouter).init();

const cxwq_circulations = require('./cxwq_circulations');
new cxwq_circulations(policyRouter).init();

const statis = require('./statis');
new statis(policyRouter).init();

const infoCheck = require('./info_check');
new infoCheck(policyRouter).init();

const last_certificate = require('./last_certificate');
new last_certificate(policyRouter).init();

//金服线上服务站
const ServiceSite = require('./service_site');
new ServiceSite(policyRouter).init();

const assistanter = require('./assistanter');
new assistanter(policyRouter).init();

const commission = require('./commission');
new commission(policyRouter).init();

const notices = require('./notices');
new notices(policyRouter).init();

module.exports = router;
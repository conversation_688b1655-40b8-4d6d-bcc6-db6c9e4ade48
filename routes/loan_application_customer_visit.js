/** 
 * newsRouter 
 * <AUTHOR>
 * 2019-10-18
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:loan_application_customer_visit');
const moment = require('moment');
const loanApplicationCustomerVisitService = require('../services/loanApplicationCustomerVisit');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const {assert,formatParas} = require('../utils/general');
const {cloneDeep} = require('lodash');
const areaService = require('../services/area')

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getCustomerVisits({getOne,hadVisitTimes}, req, res) {
    const method = 'DisasterClaims';
    debug(method, '[Enter]')
    try {
      const opts = {getOne};
      assert(!getOne || req.query._id,'E_APP_customer_visit_001','para _id is required')
      opts.uId = req.user && req.user.userid;
      process.env.NODE_ENV ==='local-dev' && req.query.uId && ( opts.uId = req.query.uId);
      // assert(opts.uId,'E_EDIT_DISASTER_CLAIMS_002','PERMISSION ERROR');

      hadVisitTimes && assert( req.query.customerName , 'E_CUSTOMER_VISIT_LIST_100','customerName is required' );
      hadVisitTimes && assert( req.query.customerMobile , 'E_CUSTOMER_VISIT_LIST_101','customerMobile is required' );
      hadVisitTimes && assert( req.query.visitAreaCode , 'E_CUSTOMER_VISIT_LIST_102','visitAreaCode is required' );
      hadVisitTimes && ( req.query.status = '3' );
      const config = [
        '_id','status',
        {from:'customerName',to:'basicInfo.customerName',rule:'contain'},
        // {from:'customerName',to:'basicInfo.customerName',rule:'contain'},
        {from:'customerMobile',to:'basicInfo.customerMobile',rule:'contain'},
        {from:'channel',to:'basicInfo.channel',rule:'numberEq'},
        {from:'type',to:'basicInfo.type',rule:'eq'},
        {from:'areaCode',to:'areaCode',rule:'startWith'},
        {from:'visitAreaCode',to:'basicInfo.visitAreaCode',rule:'startWith'},
        {from:'operatorName',to:'operatorName',rule:'contain'},
        {from:'statusIn',to:'status',rule:'in'},
        {from:'skip',dv:0},
        {from:'limit',dv:'10'},
        {from:'$sort',dv:{ lastModTime: -1 },fs:'json'},
      ];

      const condition = {
        ...formatParas(config,req.query),
        archived: false,
      };
      hadVisitTimes && ( condition.limit = 'unlimited' );
      // opts.uId = req.user && req.user.userid;
      // opts.roleId = req.query.roleId || req.headers['x-role-id'];
      // opts.tId = req.query.tId || req.Client && req.Client.tId ;
      opts.uId && ( condition.operator = opts.uId )
      const result = await loanApplicationCustomerVisitService.list(condition, opts);
      hadVisitTimes && ( result.count = result.total , delete result.result , delete result.total );
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async enumCustomerVisits(req, res) {
    const method = 'enumCustomerVisits';
    debug(method, '[Enter]')
    try {
      const result = await loanApplicationCustomerVisitService.enums({}, {});
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async createOrEditCustomerVisit({edit}, req, res) {
    const method = 'createOrEditDisasterClaims';
    debug(method, '[Enter]')
    try {
      const condition = cloneDeep(req.body),opts = {};
      opts.uId = req.user && req.user.userid;
      opts.roleId = req.headers['x-role-id'];
      process.env.NODE_ENV ==='local-dev' && req.body.uId && ( opts.uId = req.body.uId);
      edit && assert(condition._id,'E_EDIT_DISASTER_CLAIMS_001','_id is required');
      // assert(opts.uId,'E_EDIT_DISASTER_CLAIMS_002','PERMISSION ERROR');
      const result = await loanApplicationCustomerVisitService.createOrEdit(condition,opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  getAreaList(req, res) {
    let method = 'getAreaList'
    debug(method, '[Enter]')
    try {
      let condition = {
        limit: req.query.limit || 10,
        skip: req.query.skip || 0,
        archived: false
      }
      // let userProfile = getUserFromReqCtx(req);
      // let opts = {}
      // if (!userProfile) {
      //   throw {
      //     httpCode: 401,
      //     errorCode: 'ESALES030',
      //     reason: '用户未登录'
      //   }
      // }

      if (req.query.current)
        condition.current = req.query.current

      if (req.query.subLevel)
        condition.subLevel = req.query.subLevel

      if (req.query.upLevel)
        condition.upLevel = req.query.upLevel

      condition.traversal = req.query.traversal || false

      areaService.getAreaList(condition, {}).then(data => {
        debug(method, '[Exit](success)', data)
        res.status(200).send(data)
      }).fail(error => {
        debug.error(method, '[Exit](failed)', error)
        res.set('Warning', `199 - ${error.errorCode || 'EAQUERY061'} ${new Date()}`)
        res.status(error.httpCode || 500).send(error.message || error)
      })
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || 'EAQUERY061'} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }


  getAreaInfo(req, res) {
    let method = 'getAreaList'
    debug(method, '[Enter]')
    try {

      areaService.getAreaInfo(req.body, {}).then(data => {
        debug(method, '[Exit](success)', data)
        res.status(200).send(data)
      }).fail(error => {
        debug.error(method, '[Exit](failed)', error)
        res.set('Warning', `199 - ${error.errorCode || 'EAQUERY061'} ${new Date()}`)
        res.status(error.httpCode || 500).send(error.message || error)
      })
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || 'EAQUERY061'} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {

    this._policyRouter.get('/api/v1.0/loan/application/customer/visit/list',
      (...paras)=>this.getCustomerVisits({},...paras), {
      name: 'app.disasterClaims.list',
      role: PERMISSION_ROLE.EVERYONE
    });

    this._policyRouter.get('/api/v1.0/loan/application/customer/visit/times',
        (...paras)=>this.getCustomerVisits({hadVisitTimes:true},...paras), {
          name: 'app.disasterClaims.list',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.get('/api/v1.0/loan/application/customer/visit/detail',
        (...paras)=>this.getCustomerVisits({getOne:true},...paras), {
          name: 'app.disasterClaims.detail',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.get('/api/v1.0/loan/application/customer/visit/enum',
        (...paras)=>this.enumCustomerVisits(...paras), {
          name: 'app.disasterClaims.detail',
          role: PERMISSION_ROLE.EVERYONE
        });


    this._policyRouter.post('/api/v1.0/loan/application/customer/visit/add',
        (...paras)=>this.createOrEditCustomerVisit({},...paras), {
          name: 'app.disasterClaims.add',
          role: PERMISSION_ROLE.EVERYONE
        });

    this._policyRouter.put('/api/v1.0/loan/application/customer/visit/edit',
        (...paras)=>this.createOrEditCustomerVisit({edit:true},...paras), {
          name: 'app.disasterClaims.edit',
          role: PERMISSION_ROLE.EVERYONE
        });

    // public api
    this._policyRouter.get('/api/v1.0/area/list', this.getAreaList.bind(this), {
      name: 'area.getAreaList',
      role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.post('/api/v1.0/area/info', this.getAreaInfo.bind(this), {
      name: 'area.getAreaList',
      role: PERMISSION_ROLE.EVERYONE
    })

    return this;
  }
}

module.exports = Router;
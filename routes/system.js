'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:routes:system');
const systemService = require('../services/system');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class LoanBill {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async postFailedReport(req, res) {
    let method = 'postFailedReport'
    debug(method, '[Enter]')
    try {
      if (!req.body || !req.body.client || !req.body.module || !req.body.action || !req.body.device || !req.body.data) {
        throw {
          errorCode: 'E_SYS_REPORT__20',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      let condition = {
        client: req.body.client,
        module: req.body.module,
        action: req.body.action,
        device: req.body.device,
        data: req.body.data
      };

      if (req.body.userAgent) {
        condition.userAgent = req.body.userAgent;
      }
      if (req.body.uId) {
        condition.uId = req.body.uId;
      }

      let opts = {};

      let result = await systemService.postFailedReport(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this;

    self._policyRouter.post('/api/v1.0/system/failed/report', self.postFailedReport.bind(self), {
      name: 'System.postFailedReport',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = LoanBill
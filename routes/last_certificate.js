/**
 * newsRouter
 * <AUTHOR>
 * 2019-10-18
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:certificate');
const lastCertificateService = require('../services/last_certificate');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const { assert, formatParas } = require('../utils/general')
const { cloneDeep } = require('lodash');

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async listCertificate({  }, req, res) {
    const method = 'getLastCertificate';
    debug(method, '[Enter]')
    try {

      // const config = [
      //   '_id','operator',
      //   'status', 'version',
      //   { from: 'operatorName', rule: 'contain' },
      //   { from: 'createdTimeStart', to: 'createdTime', rule: 'gte', fs: 'toMinDay' },
      //   { from: 'createdTimeEnd', to: 'createdTime', rule: 'lte', fs: 'toMaxDay' },
      //   { from: 'lastModTimeStart', to: 'lastModTime', rule: 'gte', fs: 'toMinDay' },
      //   { from: 'lastModTimeEnd', to: 'lastModTime', rule: 'lte', fs: 'toMaxDay' },
      //   { from: 'effectiveTimeStart', to: 'effectiveTime', rule: 'gte', fs: 'toMinDay' },
      //   { from: 'effectiveTimeEnd', to: 'effectiveTime', rule: 'lte', fs: 'toMaxDay' },
      //   { from: 'skip', dv: 0 },
      //   { from: 'limit', dv: 'unlimited' },
      //   { from: '$sort', dv: { version: -1 }, fs: 'json' },
      // ];
      const opts = {
        // getOne,
        uId: req.user && req.user.userid,
      };
      const condition = {
        // ...formatParas(config, req.query),
        // archived: false,
      };
      const result = await lastCertificateService.getLastCertificate(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {


    this._policyRouter.get('/api/v1.0/system/certificate/last',
      (...paras) => this.listCertificate({}, ...paras), {
        name: 'sys.certificate.list',
        role: PERMISSION_ROLE.EVERYONE
      });


    return this;
  }
}

module.exports = Router;
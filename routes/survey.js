/**
 * <AUTHOR>
 * 2019-05-14
 */
'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:routes:survey');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const surveySvc = require('../services/survey')
const moment = require('moment');

class Survery {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }
  /** 
   *
   * @api {get} /api/v1.0/survey/list 调查问卷列表
   * @apiVersion 1.0.0
   * @apiName getSurveyList
   * @apiGroup Survey
   * @apiPermission EVERYONE 
   *
   * @apiDescription 调查问卷列表
   
   * @apiParam {Number}    [skip=0]           跳过记录的条数
   * @apiParam {Number}    [limit=10]          限制条数
   * @apiParam {String}    start   起始时间
   * @apiParam {String}    end     结束时间
   * @apiParam {String}    estimatedStartTime   预计用款时间起始时间
   * @apiParam {String}    estimatedEndTime     预计用款时间结束时间
   * @apiParam {Number}    [grade= 0,1,2,3]   客户评价  0 非目标用户 1 一般客户 2 可关注客户 3 重点目标客户
   * @apiParam {String}    [orderStatus]         用款状态 true :已用款，false: 未用款
   * @apiExample Example usage:
   * * curl -i http://localhost/api/v1.0/survey/list
   *
   * @apiSuccess {Object[]}  result 结果数组
   * @apiSuccess {String}    result.sn 记录sn编号
   * @apiSuccess {String}    result._id 记录_id
   * @apiSuccess {Object}    result.user 
   * @apiSuccess {String}    result.user.realname 姓名
   * @apiSuccess {String}    result.user.mobile 用户手机号
   * @apiSuccess {Number}    result.user.grade  0 非目标用户 1 一般客户 2 可关注客户 3 重点目标客户
   * @apiSuccess {String}    result.user.estimatedTime  预计用款时间
   * @apiSuccess {String}    result.createdTime 创建时间
   * @apiSuccess {Boolean}   result.orderStatus 用款状态 true :已用款，false: 未用款
   * @apiSuccessExample {json} Response (example):
   * 
   *      HTTP/1.1 200 OK
   *     {
   *       result: [{
   *        sn:"xxxxxx",
   *        _id:"xxxxxx",
   *        user:{
   *          realname:"张三",
   *          mobile: "111111xxxxx",
   *          grade: "一般客户" 
   *      },
   *      createdTime:"2019-05-15T04:56:52.299Z"
  *      }]
   *     }
   *
   * @apiError ErrorQuery error params
   * 
   * @apiError MissingParameters The parameters must be given
   
   * @apiErrorExample {json} Error-Response:
	 *     HTTP/1.1 401 Not Acceptable
	 *     {
	 *       "errorCode": "E_CTRC_079",
	 *       "reason":"用户未登录"
   * }
   * @apiError(Error 50x) InternalServerError   服务器内部错误
   * 
   */
  async getSurveyList(req, res) {
    let method = 'getSurveyList';
    debug(method, '[Enter]');
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CTRC_079',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        creator: req.user.userid,
        limit: req.query.limit || 10,
        skip: req.query.skip || 0,
        archived: false,
        type: 1
      }
      if (req.query.start && req.query.end) {
        condition.createdTime = {
          $gte: moment(req.query.start).toDate(),
          $lte: moment(req.query.end).toDate()
        };
      }
      if (req.query.estimatedStartTime && req.query.estimatedEndTime) {
        condition["user.estimatedTime"] = {
          $gte: moment(req.query.estimatedStartTime).toDate(),
          $lte: moment(req.query.estimatedEndTime).toDate()
        };
      }
      if (req.query.grade) {
        condition["user.grade"] = req.query.grade;
      }
      if (req.query.orderStatus === "true") {
        condition.orderStatus = true;
      }
      if (req.query.orderStatus === "false") {
        condition.orderStatus = false;
      }
      let opts = {};
      let result = await surveySvc.getSurveyList(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /** 
   *
   * @api {get} /api/v1.0/survey/lately 获取最近问卷创建
   * @apiVersion 1.0.0
   * @apiName getLatelySurvey
   * @apiGroup Survey
   * @apiPermission EVERYONE 
   *
   * @apiDescription 获取最近问卷创建
   
   * 
   * @apiExample Example usage:
   * * curl -i http://localhost/api/v1.0/survey/lately
   *
   * @apiSuccess {Object}  result 
   * @apiSuccess {String}    result.total 问卷总数量
   * @apiSuccess {String}    result.createdTime 创建时间
   * @apiSuccess {String}    result.pending 未处理
   * @apiSuccess {String}    result.processed 已处理
   * @apiSuccess {String}    result.lastMonth 最近一个月
   * @apiSuccessExample {json} Response (example):
   * 
   *      HTTP/1.1 200 OK
   *     {
   *       result: {
   *          total: 50,
   *          createdTime: "2019-05-15T04:56:52.299Z",
   *          pending: 10,
   *          processed: 30,
   *          lastMonth:40
   *        }
   *     }
   *
   * @apiError ErrorQuery error params
   * 
   * @apiError MissingParameters The parameters must be given
   
   * @apiErrorExample {json} Error-Response:
	 *     HTTP/1.1 401 Not Acceptable
	 *     {
	 *       "errorCode": "E_CTRC_079",
	 *       "reason":"用户未登录"
   * }
   * @apiError(Error 50x) InternalServerError   服务器内部错误
   * 
   */
  async getLatelySurvey(req, res) {
    let method = 'getLatelySurvey';
    debug(method, '[Enter]');
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CTRC_079',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let condition = {
        creator: req.user.userid,
        limit: 'unlimited',
        type: 1,
        archived: false
      }
      let opts = {};
      let result = await surveySvc.getLatelySurvey(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  /** 
   *
   * @api {post} /api/v1.0/survey/create 创建问卷调查
   * @apiVersion 1.0.0
   * @apiName createSurvey
   * @apiGroup Survey
   * @apiPermission EVERYONE 
   *
   * @apiDescription 创建问卷调查
   * @apiParam {Object} user 用户基本信息
   * @apiParam {String} user.realname 真实姓名
   * @apiParam {String} user.IDCard 身份证号
   * @apiParam {Number} user.relationship 与户主关系
   * @apiParam {String} user.mobile 手机号
   * @apiParam {String} user.credit 额度
   * @apiParam {Number} user.areaCode 区域编码
   * @apiParam {String} user.estimatedTime 预计用款时间, 到日 e.g., 2019-06-01
   * @apiParam {Number} user.grade 客户评价 0 非目标用户 1 一般客户 2 可关注客户 3 重点目标客户
   * @apiParam {Number} longitude 经度
   * @apiParam {Number} latitude 纬度
   * @apiParam {String} address 坐标位置 对应地址信息
   * @apiParam {Number} type 问卷类型, 1 吉林用户贷款意愿问卷
   * @apiParam {Object} basic 基础信息
   * @apiParam {Boolean} basic.smartphone 是否有智能机
   * @apiParam {Boolean} basic.appInstalled 是否安装APP
   * @apiParam {Number} basic.loanWill  贷款意愿 0 不想贷款 1 一般 2 强烈
   * @apiParam {Object} planting 种植情况
   * @apiParam {Number} planting.rice 水稻种植面积 亩
   * @apiParam {Number} planting.maize 玉米种植面积 亩
   * @apiParam {String} planting.else  其他描述
   * @apiParam {Object} breeding 养殖情况
   * @apiParam {Number} breeding.pig 猪
   * @apiParam {Number} breeding.cow 牛
   * @apiParam {Number} breeding.sheep 羊
   * @apiParam {String} breeding.else  其他描述
   * @apiParam {Object} loan 贷款信息
   * @apiParam {Number} loan.ABC 农行贷款 Agricultural Bank China
   * @apiParam {Number} loan.ABCRate 农行贷款利率 
   * @apiParam {Number} loan.PSBC 邮储贷款 Postal Savings Bank of China
   * @apiParam {Number} loan.PSBCRate 农行贷款利率 
   * @apiParam {Number} loan.creditAssociation 信用社贷款
   * @apiParam {Number} loan.creditAssociationRate 信用社贷款
   * @apiParam {Number} loan.villageBank 村镇银行贷款
   * @apiParam {Number} loan.villageBankRate 村镇银行贷款
   * @apiParam {Object[]} loan.else 其他描述
   * @apiParam {String} loan.else.bank 其他银行名称
   * @apiParam {Number} loan.else.amount 其他银行贷款金额
   * @apiParam {Number} loan.else.rate 其他银行贷款利率
   * @apiParam {Boolean} loan.badLoans 有无不良贷款
   * @apiParam {Boolean} loan.appealInvolved 是否涉诉
   * @apiParam {Object} images 图片信息
   * @apiParam {Object[]} images.house 房屋照片
   * @apiParam {Object[]} images.breeding 养殖照片
   * @apiParam {Object[]} images.machine 农机照片
   * @apiParam {Object[]} images.else 其他照片
   * @apiParam {Object} images.house.geoLoc 房屋照片
   * @apiParam {string} images.house.geoLoc.type 类型 Point
   * @apiParam {Number[]} images.house.geoLoc.coordinates 经纬度 [longitude, latitude]
   * @apiParam {Object} image 图片
   * @apiParam {String} image.url 图片
   * @apiParam {Number} image.height 图片
   * @apiParam {Number} image.width 图片
   * @apiParam {Object} thumbnail 缩略图
   * @apiParam {String} thumbnail.url 图片
   * @apiParam {Number} thumbnail.height 图片
   * @apiParam {Number} thumbnail.width 图片
   * 
   * @apiExample Example usage:
   *    application/json
   *    {
          "user": {
            "realname": "张三",
            "IDCard": "612322199001206720",
            "relationship": 1,
            "mobile": "13812345566",
            "credit": "30000.00",
            "areaCode": "2012201",
            "grade": 2,
            "estimatedTime": "2019-01-01"
          },
          "longitude": 127.0001212,
          "latitude": 36.7878686,
          "address": "x省x市x区xxxx",
          "basic": {
            "smartphone": true,
            "appInstalled": true,
            "loanWill": 2
          },
          "planting": {
            "rice": 10,
            "maize": 5,
            "else": "水果2亩"
          },
          "breeding": {
            "pig": 10,
            "cow": 20,
            "sheep": 30,
            "else": "暂无"
          },
          "loan": {
            "ABC": 0,
            "ABCRate": 0.04,
            "PSBC": 0,
            "PSBCRate": 0.05,
            "creditAssociation": 0,
            "creditAssociationRate": 0.1,
            "villageBank": 0,
            "villageBankRat": 0.01,
            "else": [
              {
                "bank": "招商银行",
                "amount": 0,
                "rate": 0
              }
            ],
            "badLoans": false,
            "appealInvolved": false
          },
          "images": {
            "house": [
              {
                "geoLoc":{
                  "type": "Point",
                  "coordinates": [120.78787, 36.000001]
                },
                "image": {
                  "url": "www.baidu.com",
                  "height": 500,
                  "width": 500
                },
                "thumbnail": {
                  "url": "www.baidu.com",
                  "height": 500,
                  "width": 500
                }
              }
            ],
            "breeding": [
              {
                "image": {
                  "url": "www.baidu.com",
                  "height": 500,
                  "width": 500
                },
                "thumbnail": {
                  "url": "www.baidu.com",
                  "height": 500,
                  "width": 500
                }
              }
            ],
            "machine": [
              {
                "image": {
                  "url": "www.baidu.com",
                  "height": 500,
                  "width": 500
                },
                "thumbnail": {
                  "url": "www.baidu.com",
                  "height": 500,
                  "width": 500
                }
              }
            ],
            "else": [
              {
                "image": {
                  "url": "www.baidu.com",
                  "height": 500,
                  "width": 500
                },
                "thumbnail": {
                  "url": "www.baidu.com",
                  "height": 500,
                  "width": 500
                }
              }
            ]
          }
   *  }
   * @apiSuccess {Number}    create 创建记录
   * @apiSuccess {String='SUCCESS'}    status 状态
   * 
   * @apiSuccessExample {json} Response (example):
   * 
   *    
   *      HTTP/1.1 200 OK
   *     {
   *       "create": 1,
   *       "status": "SUCCESS"   
   *     }
   *
   * @apiError ErrorQuery error params
   * 
   * @apiError MissingParameters The parameters must be given
   
   * @apiErrorExample {json} Error-Response:
	 *     HTTP/1.1 406 Not Acceptable
	 *     {
	 *       "errorCode": "E_CREATE_0192",
	 *       "reason": "invalid params "
	 *     }
   * @apiErrorExample {json} Error-Response:
	 *     HTTP/1.1 401 Not Acceptable
	 *     {
	 *       "errorCode": "E_CTRC_079",
	 *       "reason":"用户未登录"
   * }
   * @apiError(Error 50x) InternalServerError   服务器内部错误
   * 
   */
  async createSurvey(req, res) {
    let method = 'createSurvey';
    debug(method, '[Enter]');
    try {
      if (!req.body || !req.body.user || !req.body.basic || !req.body.loan || !req.body.images || !req.body.images.house) {
        throw {
          errorCode: 'E_CREATE_0192',
          httpCode: 406,
          reason: 'invalid param'
        };
      }
      if (req.body.images.house.lenght < 1) {
        throw {
          errorCode: 'E_CREATE_0192',
          httpCode: 406,
          reason: 'invalid param'
        };
      }
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CTRC_079',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let payload = {
        realname: req.body.user.realname,
        IDCard: req.body.user.IDCard,
        relationship: req.body.user.relationship,
        mobile: req.body.user.mobile,
        credit: req.body.user.credit,
        areaCode: req.body.user.areaCode,
        grade: req.body.user.grade,
        loanWill: req.body.basic.loanWill,
        longitude: req.body.longitude,
        latitude: req.body.latitude,
        address: req.body.address
      }
      for (const key in payload) {
        if (payload[key] == undefined) {
          throw {
            errorCode: 'E_CREATE_0192',
            httpCode: 406,
            reason: `${key} is required`
          };
        }
      }

      if (req.body.user.estimatedTime) {
        req.body.user.estimatedTime = moment(req.body.user.estimatedTime).utc().format();
      }

      let condition = {
        creator: req.user.userid,
        user: req.body.user,
        address: req.body.address,
        geoLoc: {
          type: "Point",
          coordinates: [req.body.longitude, req.body.latitude]
        },
        type: req.body.type || 1,
        content: {
          basic: req.body.basic,
          planting: req.body.planting || {},
          breeding: req.body.breeding || {},
          loan: req.body.loan
        },
        images: req.body.images
      };

      let opts = {};
      let result = await surveySvc.createSurvey(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /** 
  *
  * @api {put} /api/v1.0/survey/update 修改问卷调查
  * @apiVersion 1.0.0
  * @apiName updateSurvey
  * @apiGroup Survey
  * @apiPermission EVERYONE 
  *
  * @apiDescription 创建问卷调查
  * @apiParam {String} id  问卷调查编号 _id
  * @apiParam {Object} user 用户基本信息
  * @apiParam {String} user.realname 真实姓名
  * @apiParam {String} user.IDCard 身份证号
  * @apiParam {Number} user.relationship 与户主关系
  * @apiParam {String} user.mobile 手机号
  * @apiParam {String} user.credit 额度
  * @apiParam {Number} user.areaCode 区域编码
  * @apiParam {Number} user.grade 客户评价 0 非目标用户 1 一般客户 2 可关注客户 3 重点目标客户
  * @apiParam {String} user.estimatedTime 预计用款时间, 到日 e.g., 2019-06-01  
  * @apiParam {Number} type 问卷类型, 1 吉林用户贷款意愿问卷
  * @apiParam {Object} basic 基础信息
  * @apiParam {Boolean} basic.smartphone 是否有智能机
  * @apiParam {Boolean} basic.appInstalled 是否安装APP
  * @apiParam {Number} basic.loanWill  贷款意愿 0 不想贷款 1 一般 2 强烈
  * @apiParam {Object} planting 种植情况
  * @apiParam {Number} planting.rice 水稻种植面积 亩
  * @apiParam {Number} planting.maize 玉米种植面积 亩
  * @apiParam {String} planting.else  其他描述
  * @apiParam {Object} breeding 养殖情况
  * @apiParam {Number} breeding.pig 猪
  * @apiParam {Number} breeding.cow 牛
  * @apiParam {Number} breeding.sheep 羊
  * @apiParam {String} breeding.else  其他描述
  * @apiParam {Object} loan 贷款信息
  * @apiParam {Number} loan.ABC 农行贷款 Agricultural Bank China
  * @apiParam {Number} loan.ABCRate 农行贷款利率 
  * @apiParam {Number} loan.PSBC 邮储贷款 Postal Savings Bank of China
  * @apiParam {Number} loan.PSBCRate 农行贷款利率 
  * @apiParam {Number} loan.creditAssociation 信用社贷款
  * @apiParam {Number} loan.creditAssociationRate 信用社贷款
  * @apiParam {Number} loan.villageBank 村镇银行贷款
  * @apiParam {Number} loan.villageBankRate 村镇银行贷款
  * @apiParam {Object[]} loan.else 其他描述
  * @apiParam {String} loan.else.bank 其他银行名称
  * @apiParam {Number} loan.else.amount 其他银行贷款金额
  * @apiParam {Number} loan.else.rate 其他银行贷款利率
  * @apiParam {Boolean} loan.badLoans 有无不良贷款
  * @apiParam {Boolean} loan.appealInvolved 是否涉诉
  * @apiParam {Object} images 图片信息
  * @apiParam {Object[]} images.house 房屋照片
  * @apiParam {Object[]} images.breeding 养殖照片
  * @apiParam {Object[]} images.machine 农机照片
  * @apiParam {Object[]} images.else 其他照片
  * @apiParam {Object} images.house.geoLoc 房屋照片
  * @apiParam {string} images.house.geoLoc.type 类型 Point
  * @apiParam {Number[]} images.house.geoLoc.coordinates 经纬度 [longitude, latitude]
  * @apiParam {Object} image 图片
  * @apiParam {String} image.url 图片
  * @apiParam {Number} image.height 图片
  * @apiParam {Number} image.width 图片
  * @apiParam {Object} thumbnail 缩略图
  * @apiParam {String} thumbnail.url 图片
  * @apiParam {Number} thumbnail.height 图片
  * @apiParam {Number} thumbnail.width 图片
  * 
  * @apiExample Example usage:
  *    application/json
  *    {
  *      "id":"5cdd38bead51ee188594d37a",
         "user": {
           "realname": "张三",
           "IDCard": "612322199002306233",
           "mobile": "13812345566",
           "credit": "30000.00",
           "areaCode": "2012201",
           "grade": 2,
           "estimatedTime": "2019-01-01"
         },
         "basic": {
           "smartphone": true,
           "appInstalled": true,
           "loanWill": 2
         },
         "planting": {
           "rice": 10,
           "maize": 5,
           "else": "水果2亩"
         },
         "breeding": {
           "pig": 10,
           "cow": 20,
           "sheep": 30,
           "else": "暂无"
         },
         "loan": {
           "ABC": 0,
           "ABCRate": 0.04,
           "PSBC": 0,
           "PSBCRate": 0.05,
           "creditAssociation": 0,
           "creditAssociationRate": 0.1,
           "villageBank": 0,
           "villageBankRat": 0.01,
           "else": [
             {
               "bank": "招商银行",
               "amount": 0,
               "rate": 0
             }
           ],
           "badLoans": false,
           "appealInvolved": false
         },
         "images": {
           "house": [
             {
               "geoLoc":{
                  "type": "Point",
                  "coordinates": [120.78787, 36.000001]
                },
               "image": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               },
               "thumbnail": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               }
             }
           ],
           "breeding": [
             {
               "image": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               },
               "thumbnail": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               }
             }
           ],
           "machine": [
             {
               "image": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               },
               "thumbnail": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               }
             }
           ],
           "else": [
             {
               "image": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               },
               "thumbnail": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               }
             }
           ]
         }
  *  }
  * @apiSuccess {Number}    update 创建记录
  * @apiSuccess {String='SUCCESS'}    status 状态
  * 
  * @apiSuccessExample {json} Response (example):
  * 
  *    
  *      HTTP/1.1 200 OK
  *     {
  *       "update": 1,
  *       "status": "SUCCESS"   
  *     }
  *
  * @apiError ErrorQuery error params
  * 
  * @apiError MissingParameters The parameters must be given
  
  * @apiErrorExample {json} Error-Response:
  *     HTTP/1.1 406 Not Acceptable
  *     {
  *       "errorCode": "E_CREATE_0192",
  *       "reason": "invalid params "
  *     }
  * @apiErrorExample {json} Error-Response:
  *     HTTP/1.1 401 Not Acceptable
  *     {
  *       "errorCode": "E_CTRC_079",
  *       "reason":"用户未登录"
  * }
  * @apiError(Error 50x) InternalServerError   服务器内部错误
  * 
  */
  async updateSurvey(req, res) {
    let method = 'updateSurvey';
    debug(method, '[Enter]');
    try {
      if (!req.body || !req.body.user || !req.body.basic || !req.body.loan || !req.body.images || !req.body.images.house) {
        throw {
          errorCode: 'E_CREATE_0192',
          httpCode: 406,
          reason: 'invalid param'
        };
      }
      if (req.body.images.house.lenght < 1) {
        throw {
          errorCode: 'E_CREATE_0192',
          httpCode: 406,
          reason: 'invalid param'
        };
      }
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CTRC_079',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let payload = {
        id: req.body.id,
        realname: req.body.user.realname,
        relationship: req.body.user.relationship,
        IDCard: req.body.user.IDCard,
        mobile: req.body.user.mobile,
        credit: req.body.user.credit,
        areaCode: req.body.user.areaCode,
        grade: req.body.user.grade,
        loanWill: req.body.basic.loanWill
      }
      for (const key in payload) {
        if (payload[key] == undefined) {
          throw {
            errorCode: 'E_CREATE_0192',
            httpCode: 406,
            reason: `${key} is required`
          };
        }
      }
      req.body.user.IDCard = req.body.user.IDCard.toUpperCase()

      if (req.body.user.estimatedTime) {
        req.body.user.estimatedTime = moment(req.body.user.estimatedTime).utc().format();
      }

      let condition = {
        user: req.body.user,
        type: req.body.type,
        content: {
          basic: req.body.basic,
          planting: req.body.planting || {},
          breeding: req.body.breeding || {},
          loan: req.body.loan
        },
        images: req.body.images,
        lastModTime: new Date()
      };

      let opts = {
        id: req.body.id
      };
      let result = await surveySvc.updateSurvey(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  /** 
  *
  * @api {put} /api/v1.0/survey/update/order/status 修改问卷调查状态
  * @apiVersion 1.0.0
  * @apiName updateOrderStatus
  * @apiGroup Survey
  * @apiPermission EVERYONE 
  *
  * @apiDescription 修改问卷调查状态
  * @apiParam {String} id  问卷调查编号 _id
  * 
  * @apiExample Example usage:
  *    application/json
  *    {
  *      "id":"5cdd38bead51ee188594d37a"
  *  }
  * @apiSuccess {Number}    update 创建记录
  * @apiSuccess {String='SUCCESS'}    status 状态
  * 
  * @apiSuccessExample {json} Response (example):
  * 
  *    
  *      HTTP/1.1 200 OK
  *     {
  *       "update": 1,
  *       "status": "SUCCESS"   
  *     }
  *
  * @apiError ErrorQuery error params
  * 
  * @apiError MissingParameters The parameters must be given
  
  * @apiErrorExample {json} Error-Response:
  *     HTTP/1.1 406 Not Acceptable
  *     {
  *       "errorCode": "E_CREATE_0192",
  *       "reason": "invalid params "
  *     }
  * @apiErrorExample {json} Error-Response:
  *     HTTP/1.1 401 Not Acceptable
  *     {
  *       "errorCode": "E_CTRC_079",
  *       "reason":"用户未登录"
  * }
  * @apiError(Error 50x) InternalServerError   服务器内部错误
  * 
  */
  async updateOrderStatus(req, res) {
    let method = 'updateSurvey';
    debug(method, '[Enter]');
    try {
      if (!req.body || !req.body.id) {
        throw {
          errorCode: 'E_CREATE_0192',
          httpCode: 406,
          reason: 'invalid param'
        };
      }
      let opts = {
        id: req.body.id
      };
      let condition = {
        orderStatus: true
      }
      let result = await surveySvc.updateOrderStatus(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  /** 
   *
   * @api {get} /api/v1.0/survey 调查问卷详情
   * @apiVersion 1.0.0
   * @apiName getSurveyByid
   * @apiGroup Survey
   * @apiPermission EVERYONE 
   *
   * @apiDescription 调查问卷详情
   
   * @apiParam {String} id 问卷编号_id
   * @apiParam {Number} [type= 1,2] 1 只给基础信息+content信息。 2 返回images信息。
   * @apiExample Example usage:
   * * curl -i http://localhost/api/v1.0/survey
   *
   * @apiSuccess {Object}  result 
   * @apiSuccessExample {json} Response (example):
   * 
   *      HTTP/1.1 200 OK
   *     {
              "_id": "5cdd38bead51ee188594d37a",
              "geoLoc": {
                  "type": "Point",
                  "coordinates": [
                      125.330677,
                      43.883961
                  ]
              },
              "user": {
                  "realname": "张三",
                  "IDCard": "621322199003206540",
                  "relationship": 1,
                  "mobile": "13812345566",
                  "credit": "30000.00",
                  "areaCode": "220822102",
                  "grade": 2,
                  "region": {
                      "district": "通榆县",
                      "town": "双岗镇",
                      "city": "白城市",
                      "province": "吉林省"
                  }
              },
              "creator": "5c89eec0039cfedc89ce8100",
              "address": "吉林省长春市朝阳区人民大街111号",
              "type": 1,
              "basic": {
                  "smartphone": true,
                  "appInstalled": true,
                  "loanWill": 2
              },
              "planting": {
                  "rice": 10,
                  "maize": 5,
                  "else": "水果2亩"
              },
              "breeding": {
                  "pig": 10,
                  "cow": 20,
                  "sheep": 30,
                  "else": "暂无"
              },
              "loan": {
                  "ABC": 0,
                  "ABCRate": 0.04,
                  "PSBC": 0,
                  "PSBCRate": 0.05,
                  "creditAssociation": 0,
                  "creditAssociationRate": 0.1,
                  "villageBank": 0,
                  "villageBankRat": 0.01,
                  "else": [
                      {
                          "bank": "招商银行",
                          "amount": 0,
                          "rate": 0
                      }
                  ],
                  "badLoans": false,
                  "appealInvolved": false
              },
              "sn": "DY2019051600001",
              "createdTime": "2019-05-16T10:17:34.272Z",
              "lastModTime": "2019-05-16T10:17:34.272Z",
              "archived": false,
              "__v": 0,
          "images": {
            "house": [
             {
               "geoLoc":{
                  "type": "Point",
                  "coordinates": [120.78787, 36.000001]
                },
               "image": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               },
               "thumbnail": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               }
             }
           ],
           "breeding": [
             {
               "image": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               },
               "thumbnail": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               }
             }
           ],
           "machine": [
             {
               "image": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               },
               "thumbnail": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               }
             }
           ],
           "else": [
             {
               "image": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               },
               "thumbnail": {
                 "url": "www.baidu.com",
                 "height": 500,
                 "width": 500
               }
             }
           ]
         }
   * }
   *
   * @apiError ErrorQuery error params
   * 
   * @apiError MissingParameters The parameters must be given
   
   * @apiErrorExample {json} Error-Response:
	 *     HTTP/1.1 406 Not Acceptable
	 *     {
	 *       "errorCode": "S_LIST_069",
	 *       "reason":"invalid param id"
   * }
   * @apiError(Error 50x) InternalServerError   服务器内部错误
   * 
   */
  async getSurveyByid(req, res) {
    let method = 'getSurveyByid';
    debug(method, '[Enter]');
    try {
      if (!req.query || !req.query.id) {
        throw {
          errorCode: 'S_LIST_069',
          httpCode: 406,
          reason: 'invalid param id '
        }
      }
      let condition = {
        _id: req.query.id,
        archived: false
      }
      let opts = {
        type: req.query.type
      };
      let result = await surveySvc.getSurveyByid(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {get} /api/v1.0/survey/verify 手机号+姓名校验
   * @apiVersion 1.0.0
   * @apiName verifySurvey
   * @apiGroup survey
   * @apiPermission authenticated 
   *
   * @apiDescription 手机号+姓名校验
   * 
   * @apiParam {String}    realname         用户姓名
   * @apiParam {String}    mobile           用户手机号 
   * @apiParam {String}    IDCard           用户身份证号 
   * @apiParam {String}    [id]             编辑的时候传，创建校验可不传  
   *
   * @apiExample Example usage:
   * curl -i http://localhost/api/v1.0/survey/verify?realname=张三&mobile=13683888888
   *
   * @apiSuccess {Number}    code           响应码，0，成功
   * @apiSuccess {String}    msg            响应信息，校验成功
   * 
   * @apiSuccessExample {json} Response (example):
   * 
   *     HTTP/1.1 200 OK
   *     {
   *          "code": 0,
   *          "msg": '校验成功'
   *     }
   *
   * @apiError ErrorQuery error params
   * @apiErrorExample {json} Error-Response:
   *     HTTP/1.1 401 Not Acceptable
   *     {
   *       "errorCode": "E_FINANCE_LIST_101",
   *       "reason": "用户未登录"
   *     }
   * @apiErrorExample {json} Error-Response: 
   *     HTTP/1.1 406 Error Query
   *     {
   *       "errorCode": "E_VERIFY_885",
   *       "reason": "invalid params"
   *     }
   * @apiErrorExample {json} Error-Response: 
   *     HTTP/1.1 406 Error Query
   *     {
   *       "errorCode": "E_VERIFY_SURVEY_045",
   *       "reason": "该身份证号已存在，请核实。"
   *     }
   * @apiErrorExample {json} Error-Response: 
   *     HTTP/1.1 406 Error Query
   *     {
   *       "errorCode": "E_VERIFY_SURVEY_051",
   *       "reason": "该“姓名+手机号”已存在，请核实。" 
   *     }
   * 
   * @apiError(Error 50x) InternalServerError  服务器内部错误
   * 
   */
  async verifySurvey(req, res) {
    let method = 'verifySurvey';
    debug(method, '[Enter]');
    try {
      /* if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_VERIFY_898',
          httpCode: 401,
          reason: '用户未登录'
        }
      } */
      if (!req.query || !req.query.realname || !req.query.mobile || !req.query.IDCard) {
        throw {
          errorCode: 'E_VERIFY_905',
          httpCode: 406,
          reason: 'invalid param'
        };
      }
      let condition = {
        realname: req.query.realname,
        mobile: req.query.mobile,
        IDCard: req.query.IDCard
      }
      if (req.query.id) {
        condition.id = req.query.id;
      }
      let opts = {
        //userInfo: req.user
      };
      let result = await surveySvc.verifySurvey(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  // 获取修改历史记录
  async getTrackingList(req, res) {
    let method = 'getTrackingList';
    debug(method, '[Enter]');
    try {
      if (!req.query || !req.query.id || !req.query.target_t) {
        throw {
          errorCode: 'S_LIST_069',
          httpCode: 406,
          reason: 'invalid param'
        }
      }
      let condition = {
        target: req.query.id,
        target_t: req.query.target_t,
        limit: req.query.limit || 10,
        skip: req.query.skip || 0,
        $sort: {
          createdTime: -1,
        }
      }
      let opts = {};
      let result = await surveySvc.getTrackingList(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  // 问卷和订单关联关系
  async correlationLoanApplication(req, res) {
    let method = 'getTrackingList';
    debug(method, '[Enter]');
    try {
      if (!req.query || !req.query.id) {
        throw {
          errorCode: 'S_LIST_069',
          httpCode: 406,
          reason: 'invalid param'
        }
      }
      let condition = {
        id: req.query.id
      }
      let opts = {};
      let result = await surveySvc.correlationLoanApplication(condition, opts);

      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 获取补充资料和种植信息
  async getLoanCropsAndIncome(req, res) {
    let method = 'getLoanCropsAndIncome'
    debug(method, '[Enter]')
    try {
      if (!req.query || !req.query.aId) {
        throw {
          errorCode: 'E_SURVEY_DETAIL_252',
          httpCode: 406,
          reason: 'invalid params'
        }
      }
      let condition = {
        aId: req.query.aId,
        type: 2,
        archived: false
      };

      let opts = {};
      let result = await surveySvc.getLoanCropsAndIncome(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  init() {
    let self = this;

    self._policyRouter.get('/api/v1.0/survey/list', self.getSurveyList.bind(self), {
      name: 'Survery.getSurveyList'
    });

    self._policyRouter.get('/api/v1.0/survey/lately', self.getLatelySurvey.bind(self), {
      name: 'Survery.getLatelySurvey'
    });

    self._policyRouter.get('/api/v1.0/survey', self.getSurveyByid.bind(self), {
      name: 'Survery.getSurveyByid'
    });

    self._policyRouter.post('/api/v1.0/survey/create', self.createSurvey.bind(self), {
      name: 'Survery.createSurvey'
    });

    self._policyRouter.put('/api/v1.0/survey/update', self.updateSurvey.bind(self), {
      name: 'Survery.updateSurvey'
    });
    self._policyRouter.put('/api/v1.0/survey/update/order/status', self.updateOrderStatus.bind(self), {
      name: 'Survery.updateOrderStatus'
    });
    self._policyRouter.get('/api/v1.0/survey/verify', self.verifySurvey.bind(self), {
      name: 'Survery.verifySurvey'
    });

    self._policyRouter.get('/api/v1.0/survey/tracking/list', self.getTrackingList.bind(self), {
      name: 'Survery.getTrackingList',
    });
    self._policyRouter.get('/api/v1.0/survey/correlation/loan', self.correlationLoanApplication.bind(self), {
      name: 'Survery.correlationLoanApplication',
    });
    self._policyRouter.get('/api/v1.0/survey/crops/income', self.getLoanCropsAndIncome.bind(self), {
      name: 'survey.getLoanCropsAndIncome',
      //  role: PERMISSION_ROLE.EVERYONE
    });
    return self;
  }
}
module.exports = Survery
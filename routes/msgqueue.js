/**
 * <AUTHOR>
 * 2019-09-20
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:msgqueue');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE
const msgQueueSvc = require('../services/msgqueue');
class Carrousel {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
   * @api {get} /api/v1.0/msg/queue/list 获取当前推送的消息
   * @apiVersion 1.0.0
   * @apiName getMsgQueueList
   * @apiGroup msgQueue
   * @apiPermission EVERYONE 
   *
   * @apiDescription 获取当前推送的消息
   * 
   * @apiParam {String} clientId app的client _id
   * @apiParam {Number}    [skip=0]           跳过记录的条数
   * @apiParam {Number}    [limit=10]          限制条数
   * @apiParamExample {url} Request-Params-Example:
   * ?clientId=xxxxxxxxxx
   *
   * @apiExample Example usage:
   * curl -i http://localhost/api/v1.0/msg/queue/list
   *
   * @apiSuccess {Object[]}  result 结果数组
   * 
   * 
   * @apiSuccessExample {json} Response (example):
   * 
   * HTTP/1.1 200 OK
   * result:[{
   *  _id:"xxxxxx",
     *  msg_t:"Rongxin_loan:Push:Text",
     *  read:1,
     *  createdTime: "2019-05-15T04:56:52.299Z",
     *  title:"有新订单了"，
     *  context:"客户孙悟空，手机18600862727，于2019-09-09 18:18:18申请了订单，当前订单状态XXXX；"
   * }]
   *  
   * @apiError ErrorQuery error params
   * 
   * @apiError(Error 50x) InternalServerError   服务器内部错误
   * 
   */
  async getMsgQueueList(req, res) {
    let method = 'getMsgQueueList'
    debug(method, '[Enter]')
    try {
      if (!req.query || !req.query.clientId) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_065',
          reason: 'invalid param'
        };
      }
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'M_Queue_064',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        clientId: req.query.clientId,
        uId: req.user.userid,
        skip: req.query.skip || 0,
        limit: req.query.limit || 10,
      };
      let opts = {};

      let result = await msgQueueSvc.getMsgQueueList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
   * @api {get} /api/v1.0/msg/queue/unread 当前登录用户的未读消息
   * @apiVersion 1.0.0
   * @apiName getMsqQueryUnread
   * @apiGroup msgQueue
   * @apiPermission EVERYONE
   *
   * @apiDescription 当前登录用户的未读消息
   *
   * @apiParam {String} clientId app的client _id
   * @apiParamExample {url} Request-Params-Example:
   * ?clientId=xxxxxxxxxx
   *
   * @apiExample Example usage:
   * curl -i http://localhost/api/v1.0/msg/queue/unread
   *
   * @apiSuccess {Object[]}  result 结果数组
   *
   *
   * @apiSuccessExample {json} Response (example):
   *
   * HTTP/1.1 200 OK
   * result:{
   *  count: 50
   * }
   *
   * 
   *
   * @apiError ErrorQuery error params
   *
   * @apiError(Error 50x) InternalServerError   服务器内部错误
   *
   */
  async getMsgQueueUnread(req, res) {
    let method = 'getMsgQueueUnread'
    debug(method, '[Enter]')
    try {
      if (!req.query || !req.query.clientId) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_065',
          reason: 'invalid param'
        };
      }
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'M_Queue_064',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        clientId: req.query.clientId,
        uId: req.user.userid,
      };
      let opts = {};

      let result = await msgQueueSvc.getMsgQueueUnread(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  /**
     * @api {get} /api/v1.0/msg/queue/details 消息详情
     * @apiVersion 1.0.0
     * @apiName getMsgQueueDetails
     * @apiGroup msgQueue
     * @apiPermission EVERYONE
     *
     * @apiDescription 消息详情
     *
     * @apiParam {String} id  消息的_id
     * @apiParamExample {url} Request-Params-Example:
     * ?id=xxxxxxxxxx
     *
     * @apiExample Example usage:
     * curl -i http://localhost/api/v1.0/msg/queue/details
     *
     * @apiSuccess {Object[]}  result 结果数组
     *
     *
     * @apiSuccessExample {json} Response (example):
     *
     * HTTP/1.1 200 OK
     * result:{
     *   _id:"xxxxxx",
     *  msg_t:"Rongxin_loan:Push:Text",
     *  read:1,
     *  createdTime: "2019-05-15T04:56:52.299Z",
     *  title:"有新订单了"，
     *  context:"客户孙悟空，手机18600862727，于2019-09-09 18:18:18申请了订单，当前订单状态XXXX；"
     * }
     *
     * @apiError ErrorQuery error params
     *
     * @apiError(Error 50x) InternalServerError   服务器内部错误
     *
     */
  async getMsgQueueDetails(req, res) {
    let method = 'getMsgQueueDetails'
    debug(method, '[Enter]')
    try {
      if (!req.query.id) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_065',
          reason: 'invalid param'
        };
      }

      let condition = {
        id: req.query.id,
      };
      let opts = {};

      let result = await msgQueueSvc.getMsgQueueDetails(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
     * @api {get} /api/v1.0/msg/queue/del 删除消息
     * @apiVersion 1.0.0
     * @apiName delMsgQueue
     * @apiGroup msgQueue
     * @apiPermission EVERYONE
     *
     * @apiDescription 删除消息
     *
     * @apiParam {String} id  消息的_id
     * @apiParamExample {url} Request-Params-Example:
     * ?id=xxxxxxxxxx
     *
     * @apiExample Example usage:
     * curl -i http://localhost/api/v1.0/msg/queue/del
     *
     * @apiSuccess {Object[]}  result 结果数组
     *
     *
     * @apiSuccessExample {json} Response (example):
     *
     * HTTP/1.1 200 OK
     * result:{
     *   del: 1,
         status: "SUCCESS"
     * }
     *
     * @apiError ErrorQuery error params
     *
     * @apiError(Error 50x) InternalServerError   服务器内部错误
     *
     */
  async delMsgQueue(req, res) {
    let method = 'delMsgQueue'
    debug(method, '[Enter]')
    try {
      if (!req.query.id) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_065',
          reason: 'invalid param'
        };
      }
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'M_Queue_064',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        id: req.query.id,
        uId: req.user.userid
      };
      let opts = {};

      let result = await msgQueueSvc.delMsgQueue(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  /**
     * @api {get} /api/v1.0/msg/queue/empty 清空所有消息
     * @apiVersion 1.0.0
     * @apiName delMsgQueue
     * @apiGroup msgQueue
     * @apiPermission EVERYONE
     *
     * @apiDescription 删除消息
     *
     *@apiParam {String} clientId app的client _id
     * @apiParamExample {url} Request-Params-Example:
     * ?clientId=xxxxxxxxxx
     *
     * @apiExample Example usage:
     * curl -i http://localhost//api/v1.0/msg/queue/empty
     *
     * @apiSuccess {Object[]}  result 结果数组
     *
     *
     * @apiSuccessExample {json} Response (example):
     *
     * HTTP/1.1 200 OK
     * result:{status: "SUCCESS"}
     *
     * @apiError ErrorQuery error params
     *
     * @apiError(Error 50x) InternalServerError   服务器内部错误
     *
     */
  async emptyMsgqueue(req, res) {
    let method = 'emptyMsgqueue'
    debug(method, '[Enter]')
    try {
      if (!req.query.clientId) {
        throw {
          httpCode: 406,
          errorCode: 'E_CLIENT_R_065',
          reason: 'invalid param'
        };
      }
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'M_Queue_064',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        clientId: req.query.clientId,
        uId: req.user.userid
      };
      let opts = {};

      let result = await msgQueueSvc.emptyMsgqueue(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/msg/queue/list', self.getMsgQueueList.bind(self), {
      name: 'msgQueue.getMsgQueueList',
      role: PERMISSION_ROLE.EVERYONE
    })
    self._policyRouter.get('/api/v1.0/msg/queue/unread', self.getMsgQueueUnread.bind(self), {
      name: 'msgQueue.getMsgQueueUnread',
      role: PERMISSION_ROLE.EVERYONE
    })
    self._policyRouter.get('/api/v1.0/msg/queue/details', self.getMsgQueueDetails.bind(self), {
      name: 'msgQueue.getMsgQueueDetails',
      role: PERMISSION_ROLE.EVERYONE
    })
    self._policyRouter.get('/api/v1.0/msg/queue/del', self.delMsgQueue.bind(self), {
      name: 'msgQueue.delMsgQueue',
      role: PERMISSION_ROLE.EVERYONE
    })
    self._policyRouter.get('/api/v1.0/msg/queue/empty', self.emptyMsgqueue.bind(self), {
      name: 'msgQueue.emptyMsgqueue',
      role: PERMISSION_ROLE.EVERYONE
    })
    return self
  }
}

module.exports = Carrousel
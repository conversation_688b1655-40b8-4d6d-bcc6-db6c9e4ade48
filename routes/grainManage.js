/**
 * iguopin Router
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:grainManage');
const grainManageSvc = require('../services/grainManage');
const moment = require('moment');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async list(req, res) {
    let method = 'save'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      let query = req.query;
      const condition = {
        skip: query.skip || 0,
        limit: query.limit || 10,
        // tId:req.Client.tId,
        archived: false,
        $sort: query.$sort && JSON.parse(query.$sort) || { createdTime: -1 }
      };
      Object.assign(condition, query);
      condition["$sort"] = { createdTime: -1 };
      if (query.startTime) {
        let startTime = moment(query.startTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$gte = startTime;
        delete condition.startTime;
      }
      if (query.endTime) {
        let endTime = moment(query.endTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.createdTime = condition.createdTime || {};
        condition.createdTime.$lte = endTime;
        delete condition.endTime;
      }
      if (query.startOperateTime) {
        let startOperateTime = moment(query.startOperateTime).utc().format('YYYY-MM-DD HH:mm:ssZ');
        condition.operateTime = condition.operateTime || {};
        condition.operateTime.$gte = startOperateTime;
        delete condition.startOperateTime;
      }
      if (query.endOperateTime) {
        let endOperateTime = moment(query.endOperateTime).utc().add(1, 'd').format('YYYY-MM-DD HH:mm:ssZ');
        condition.operateTime = condition.operateTime || {};
        condition.operateTime.$lte = endOperateTime;
        delete condition.endOperateTime;
      }
      if (query.username) {
        condition.username = {
          '$regex': query.username,
          '$options': 'si'
        };
      }
      condition.areaCode && (condition.areaCode = new RegExp('^' + condition.areaCode));
      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };
      opts.uId = req.user && req.user.userid;
      opts.roleId = req.query.roleId || req.headers['x-role-id'];
      opts.tId = req.query.tId || req.Client && req.Client.tId ;
      delete condition.tId;

      let result = await grainManageSvc.list(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async detail(req, res) {
    let method = 'save'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_ENTERPRISE_034',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      let condition = { _id: req.query._id };
      let result = await grainManageSvc.list(condition, {});
      if (result.length === 0) {
        throw {
          errorCode: 'ERR_GRAIN_MANAGE_001',
          httpCode: 401,
          reason: 'not found'
        }
      }
      result = result.result[0];
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async create(req, res) {
    let method = 'create'
    debug(method, '[Enter]')
    try {
      // if (!req.user || !req.user.userid) {
      //   throw {
      //     errorCode: 'E_JT_ACC_033',
      //     httpCode: 401,
      //     reason: '用户未登录'
      //   }
      // }
      let body = req.body || {};
      body.operateTime && (body.operateTime = moment(body.operateTime, 'YYYY年MM月DD日').utc().format('YYYY-MM-DD HH:mm:ssZ'));
      let opts = {
        userid: req.user && req.user.userid,
        tId: req.Client && req.Client.tId
      };
      let condition = body;
      condition.operator = opts.userid;
      let result = await grainManageSvc.create(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} `);
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }


  init() {
    let self = this;

    self._policyRouter.get('/api/v1.0/loan/application/grainManage/list', self.list.bind(self), {
      name: 'grainManage.list',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.post('/api/v1.0/loan/application/grainManage/create', self.create.bind(self), {
      name: 'grainManage.create',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v1.0/loan/application/grainManage/detail', self.detail.bind(self), {
      name: 'grainManage.detail',
      role: PERMISSION_ROLE.EVERYONE
    });

    return self;
  }
}

module.exports = Router;
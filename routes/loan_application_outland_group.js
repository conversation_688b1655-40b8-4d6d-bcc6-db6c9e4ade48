/*
 * @Author: ysd
 * @Last Modified by: ysd
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:outland:group');
const outLandGroupService = require('../services/loanApplicationOutLandGroup');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class OutLand {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }


  async list(req, res) {
    const method = 'list';
    debug(method, '[Enter]');
    try {
      let tId = req.Client && req.Client.tId;
      if (!tId) {
        throw {
          errorCode: 'E_LIST_108',
          httpCode: 406,
          reason: 'clientId not find'
        }
      }
      let condition = { archived: false, limit: "unlimited" };
      const result = await outLandGroupService.list(condition);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async create(req, res) {
    const method = 'create';
    debug(method, '[Enter]');
    try {
      let tId = req.Client && req.Client.tId;
      if (!tId) {
        throw {
          errorCode: 'E_CREATE_108',
          httpCode: 406,
          reason: 'clientId not find'
        }
      }
      if (!req.body.name) {
        throw {
          errorCode: 'E_CREATE_108',
          httpCode: 406,
          reason: 'miss name'
        }
      }
      let condition = { name: req.body.name };
      const result = await outLandGroupService.create(condition);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  async archive(req, res) {
    const method = 'archive';
    debug(method, '[Enter]');
    try {
      let tId = req.Client && req.Client.tId;
      if (!tId) {
        throw {
          errorCode: 'E_CREATE_108',
          httpCode: 406,
          reason: 'clientId not find'
        }
      }
      if (!req.body.id) {
        throw {
          errorCode: 'E_CREATE_108',
          httpCode: 406,
          reason: 'miss id'
        }
      }
      let condition = { id: req.body.id };
      const result = await outLandGroupService.archive(condition);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }


  init() {

    this._policyRouter.get('/api/v1.0/loan/application/out/land/group/list', this.list.bind(this), {
      name: 'LoanApplicationOutLandGroup.list',
      // role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.post('/api/v1.0/loan/application/out/land/group/create', this.create.bind(this), {
      name: 'LoanApplicationOutLandGroup.list',
      // role: PERMISSION_ROLE.EVERYONE
    })

    this._policyRouter.post('/api/v1.0/loan/application/out/land/group/archive', this.archive.bind(this), {
      name: 'LoanApplicationOutLandGroup.list',
      // role: PERMISSION_ROLE.EVERYONE
    })


    return this
  }

}

module.exports = OutLand;
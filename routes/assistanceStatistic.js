'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:assistanceStatistic');
const assistanceStatisticService = require('../services/assistanceStatistic');

class AssistanceStatistic {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  /**
   * @api {get} /api/v1.0/assistance-statistic/detail 获取远程协助统计信息详情
   * @apiVersion 1.0.0
   * @apiName getAssistanceStatisticDetail
   * @apiGroup Queue
   * @apiPermission authenticated
   *
   * @apiDescription 获取远程协助统计信息详情
   *
   * @apiSuccess {String} _id 记录_id
   */
  async getAssistanceStatisticDetail(req, res) {
    const method = 'getAssistanceStatisticDetail';
    debug(method, '[Enter]');
    try {
      const condition = {
        userId: req.query.id,
      };

      if (req.query.startTime) {
        condition.startTime = req.query.startTime;
      }
      if (req.query.endTime) {
        condition.endTime = req.query.endTime;
      }
      const opts = {
        userid: req.user.userid,
      };

      const result = await assistanceStatisticService.getAssistanceStatisticDetail(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    const self = this;

    self._policyRouter.get('/api/v1.0/assistance-statistic/detail', self.getAssistanceStatisticDetail.bind(self), {
      name: 'queue.getAssistanceStatisticDetail',
    });

    return self;
  }
}

module.exports = AssistanceStatistic;

/*
 * @Author: wcy 
 * @Date: 2018-11-23 15:24:29 
 * @Last Modified by: wcy
 * @Last Modified time: 2018-12-10 11:40:57
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:carrousel');
const carrouselService = require('../services/carrousel');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class Carrousel {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getBannerList(req, res) {
    let method = 'getBannerList'
    debug(method, '[Enter]')
    try {
      if (!req.Client || !req.Client.tId) {
        throw {
          errorCode: 'E_CREATE_108',
          httpCode: 401,
          reason: 'clientId not find'
        }
      }
      if (!req.query || !req.query.catalogId) {
        throw {
          errorCode: 'E_CARROUSE_108',
          httpCode: 401,
          reason: 'invalid param'
        }
      }
      let condition = {
        limit: req.query.limit || 5,
        skip: req.query.skip || 0,
        archived: false,
        $sort: {
          weight: 1,
          lastModTime: -1
        },
        clientId: req.Client._id,
        catalogId: req.query.catalogId,
        status: 1
      }


      if (req.query.area) {
        condition.areaList = `/^${req.query.area}/`
      }


      let opts = {};

      let result = await carrouselService.getBannerList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this

    self._policyRouter.get('/api/v2.0/carrousel/list', self.getBannerList.bind(self), {
      name: 'carrousel.getBannerList',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = Carrousel
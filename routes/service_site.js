'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:serviceSite');
const serviceSiteSvc = require('../services/service_site');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const { STATUS, STATUS_MAP } = require('../utils/const/serviceSiteConst');
const requireMap = new Map([
  ["city", ["name", "areaCode", "address", "contact", "descImg"]],
  ["county", ["name", "areaCode", "address", "contact", "descImg"]],
  ["town", ["name", "areaCode", "address", "contact", "descImg", "workTime"]],
  ["village", ["name", "areaCode", "address"]]
])

class ServiceSite {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getSiteList(req, res) {
    let method = 'getSiteList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_SERVICE_SITE_027',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let condition = {
        limit: req.query.limit || 5,
        skip: req.query.skip || 0,
        archived: false,
        $sort: { createdTime: -1 }
      };
      if (req.query.type) {
        condition.type = req.query.type;
      }
      if (req.query.name) {
        condition.name = req.query.name;
      }
      if (req.query.areaCode) {
        condition.areaCode = `/${req.query.areaCode}/`;
      }
      if (req.query.status) {
        condition.status = req.query.status;
        if (req.query.status == "wait_approve") {
          condition.status = { $in: [STATUS.WAIT_APPROVE_COUNTY, STATUS.WAIT_APPROVE_CITY] };
        }
      }
      let opts = {
        employee: req.user.userid
      };
      if (req.headers["x-role-id"]) {
        opts.roleId = req.headers["x-role-id"];
      }

      let result = await serviceSiteSvc.getSiteList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getSiteDetail(req, res) {
    let method = 'getSiteDetail'
    debug(method, '[Enter]')
    try {
      if (!req.query || (!req.query.id && !req.query.areaCode)) {
        throw {
          errorCode: 'E_SERVICE_SITE_052',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      let condition = {
        id: req.query.id,
        areaCode: req.query.areaCode
      };

      let opts = {};
      let result = await serviceSiteSvc.getSiteDetail(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async putSite(req, res) {
    let method = 'putSite'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_SERVICE_SITE_091',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.body || !req.body._id) {
        throw {
          errorCode: 'E_SERVICE_SITE_098',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      if (req.body.contractImg) {
        let contractImg = req.body.contractImg;
        if (!contractImg || !Array.isArray(contractImg) || !contractImg.length) {
          throw {
            errorCode: 'E_SERVICE_SITE_107',
            httpCode: 406,
            reason: 'invalid parameter'
          }
        }
        for (let item of contractImg) {
          if (!item.uploadTime || !item.signBeginTime) {
            throw {
              errorCode: 'E_SERVICE_SITE_114',
              httpCode: 406,
              reason: '合同信息缺失'
            }
          }
          item.operator = req.user.userid;
        }
      }

      let condition = {
        id: req.body._id,
        body: { ...req.body }
      };
      delete condition.body.id;
      if (!Object.keys(condition.body).length) {
        throw {
          errorCode: 'E_SERVICE_SITE_110',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }

      let opts = {
        employeeId: req.user.userid
      };
      let result = await serviceSiteSvc.putSite(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async postSite(req, res) {
    let method = 'postSite'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_SERVICE_SITE_125',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      let { type, descImg } = req.body;
      let required = requireMap.get(type);
      if (!required) {
        throw {
          errorCode: 'E_SERVICE_SITE_141',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      for (let key of required) {
        if (!req.body[key]) {
          throw {
            errorCode: 'E_SERVICE_SITE_149',
            httpCode: 406,
            reason: 'invalid parameter'
          }
        }
        if (key == "descImg" && (!Array.isArray(descImg) || !descImg.length)) {
          throw {
            errorCode: 'E_SERVICE_SITE_157',
            httpCode: 406,
            reason: 'invalid parameter'
          }
        }
      }
      let condition = { ...req.body };
      let opts = {
        employeeId: req.user.userid
      }
      let result = await serviceSiteSvc.postSite(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async saveVillageInfo(req, res) {
    let method = 'saveVillageInfo'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_SERVICE_SITE_125',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.body || !req.body.areaCode) {
        throw {
          errorCode: 'E_SERVICE_SITE_186',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      let condition = { ...req.body };

      let opts = {
        employeeId: req.user.userid
      }
      let result = await serviceSiteSvc.saveVillageInfo(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async submitVillageSite(req, res) {
    let method = 'submitVillageSite'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_SERVICE_SITE_125',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.body.areaCode) {
        throw {
          errorCode: 'E_SERVICE_SITE_222',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      let condition = { ...req.body };

      let opts = {
        employeeId: req.user.userid
      }
      let result = await serviceSiteSvc.submitVillageSite(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async closeVillageSite(req, res) {
    let method = 'closeVillageSite'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_SERVICE_SITE_091',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.body || !req.body.id) {
        throw {
          errorCode: 'E_SERVICE_SITE_262',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      let condition = {
        id: req.body.id
      };

      let opts = {
        employeeId: req.user.userid
      };
      let result = await serviceSiteSvc.closeVillageSite(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async approveVillageSite(req, res) {
    let method = 'approveVillageSite'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_SERVICE_SITE_091',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.body || !req.body.id || !req.body.action) {
        throw {
          errorCode: 'E_SERVICE_SITE_299',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      if (req.body.action == "no" && !req.body.rejectReason) {
        throw {
          errorCode: 'E_SERVICE_SITE_305',
          httpCode: 406,
          reason: '请填写拒绝原因'
        }
      }
      let condition = { ...req.body };

      let opts = {
        employeeId: req.user.userid
      };
      let result = await serviceSiteSvc.approveVillageSite(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getVillageTrackings(req, res) {
    let method = 'getVillageTrackings'
    debug(method, '[Enter]')
    try {
      if (!req.query || !req.query.id) {
        throw {
          errorCode: 'E_SERVICE_SITE_332',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      let condition = {
        target: req.query.id,
        limit: 'unlimited'
      };
      let opts = {};
      let result = await serviceSiteSvc.getVillageTrackings(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getVillageDeatil(req, res) {
    let method = 'getSiteDetail'
    debug(method, '[Enter]')
    try {
      if (!req.query || (!req.query.id && !req.query.areaCode)) {
        throw {
          errorCode: 'E_SERVICE_SITE_361',
          httpCode: 406,
          reason: 'invalid parameter'
        }
      }
      let condition = {
        id: req.query.id,
        areaCode: req.query.areaCode
      };

      let opts = {};
      let result = await serviceSiteSvc.getVillageDeatil(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  async getDict(req, res) {
    let method = 'getSiteDetail'
    debug(method, '[Enter]')
    try {
      let result = [];
      for (let [key, value] of STATUS_MAP) {
        result.push({ key, value });
      }
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`)
      res.status(error.httpCode || 500).send(error.message || error)
    }
  }

  init() {
    let self = this;
    //获取所有服务站点列表
    self._policyRouter.get('/api/v1.0/work/site/list', self.getSiteList.bind(self), {
      name: 'serviceSite.getSiteList'
    })
    //获取所有服务站详情
    self._policyRouter.get('/api/v1.0/work/site/detail', self.getSiteDetail.bind(self), {
      name: 'serviceSite.getSiteDetail',
      role: PERMISSION_ROLE.EVERYONE
    })
    //编辑服务站基本信息
    self._policyRouter.put('/api/v1.0/work/site/update', self.putSite.bind(self), {
      name: 'serviceSite.putSite',
      role: PERMISSION_ROLE.EVERYONE
    })
    //市县乡，新建服务站
    self._policyRouter.post('/api/v1.0/work/site/create', self.postSite.bind(self), {
      name: 'serviceSite.postSite'
    })
    //村信息保存，村工作站新建
    self._policyRouter.post('/api/v1.0/work/site/village/save', self.saveVillageInfo.bind(self), {
      name: 'serviceSite.saveVillageInfo'
    })
    //村服务站提交审批
    self._policyRouter.put('/api/v1.0/work/site/village/submit', self.submitVillageSite.bind(self), {
      name: 'serviceSite.submitVillage'
    })
    //村服务站关站
    self._policyRouter.put('/api/v1.0/work/site/village/close', self.closeVillageSite.bind(self), {
      name: 'serviceSite.closeVillageSite'
    })
    //村服务站审批
    self._policyRouter.put('/api/v1.0/work/site/village/approve', self.approveVillageSite.bind(self), {
      name: 'serviceSite.approveVillageSite'
    })
    //村服务站审批日志
    self._policyRouter.get('/api/v1.0/work/site/village/trackings', self.getVillageTrackings.bind(self), {
      name: 'serviceSite.getVillageTrackings'
    })
    //村信息详情
    self._policyRouter.get('/api/v1.0/work/site/village/detail', self.getVillageDeatil.bind(self), {
      name: 'serviceSite.getVillageDeatil'
    })
    //工作站审批状态枚举
    self._policyRouter.get('/api/v1.0/work/site/dict', self.getDict.bind(self), {
      name: 'serviceSite.getDict',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = ServiceSite
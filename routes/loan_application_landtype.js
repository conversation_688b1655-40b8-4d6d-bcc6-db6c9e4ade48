/*
 * @Author: wcy
 * @Date: 2018-11-23 15:24:29
 * @Last Modified by: wcy
 * @Last Modified time: 2018-12-10 11:41:04
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:landtype');
const landTypeService = require('../services/loanApplicationLandType');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class LandType {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getAllLandListWithTypeHandler(req, res) {
    const method = 'getAllLandListWithTypeHandler';
    debug(method, '[Enter]');
    try {

      if (!req.query.aId) {
        throw {
          errorCode: 'E_OUT_LAND_002',
          httpCode: 406,
          reason: 'miss parameter'
        }
      }

      const opts = {
        outlandAssuranceIncludedIn:Bo<PERSON>an( parseInt( req.query.outlandAssuranceIncludedIn ) ),
        assuranceSn: req.query.assuranceSn,
      };
      const condition = {
          skip: req.query.skip || 0,
          limit: req.query.limit || 10,
          aId: req.query.aId,
          $sort: { lastModTime: -1 }
        };
      // if (req.Client && req.Client.tId) {
      //   condition.tId = req.Client.tId;
      // }

      const result = await landTypeService.getAllLandListWithTypeHandler(condition, opts);
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {

    this._policyRouter.get('/api/v1.0/loan/application/land/with/type', this.getAllLandListWithTypeHandler.bind(this), {
      name: 'LoanApplicationLandType.getAllLandListWithTypeHandler',
      role: PERMISSION_ROLE.EVERYONE
    })

    return this
  }

}

module.exports = LandType;
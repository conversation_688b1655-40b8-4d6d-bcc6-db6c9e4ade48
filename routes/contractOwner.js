'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:contract');
const contractOwnerService = require('../services/contractOwner');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  // 获取创新物权合同列表及签署人信息
  async getCxwqLoanContractList(req, res) {
    let method = 'getLoanContractList'
    debug(method, '[Enter]')
    try {

      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CON_085',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      if (!req.query || !req.query.loanId) {
        throw {
          errorCode: 'E_CON_076',
          httpCode: 406,
          reason: 'invalid params'
        }
      }
      let condition = {
        skip: 0,
        limit: 10,
        archived: false
      };
      let opts = {};
      condition.loanId = req.query.loanId;

      // opts.uId = req.user.userid;
      // opts.uId = '5f5ada1092dd0c2f94e98dc5';

      if (req.query.skip && parseInt(req.query.skip)) {
        condition.skip = req.query.skip;
      }

      if (req.query.limit && parseInt(req.query.limit)) {
        condition.limit = req.query.limit;
      }

      let result = await contractOwnerService.getCxwqLoanContractList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  //获取合同签署人状态列表
  async getContractOwnersList(req, res) {
    let method = 'getContractOwnersList'
    debug(method, '[Enter]')
    try {

      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_CTRC_OWNER_079',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      if (!req.query.loanId) {
        throw {
          errorCode: 'E_CTRC_OWNER_208',
          httpCode: 406,
          reason: 'invalid params'
        }
      }

      let condition = {
        loanId: req.query.loanId
      };
      let opts = {
        uId: req.user.userid //"5cb19242d1cd5b193a41ff07",
      };

      let result = await contractOwnerService.getContractOwnersList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 获取签署人列表
  async getContractCowners(req, res) {
    let method = 'getContractCowners'
    debug(method, '[Enter]')
    try {
      if (!req.query.aId) {
        throw {
          errorCode: 'E_CTRC_087',
          httpCode: 406,
          reason: 'invalid params'
        }
      }

      let condition = {
        aId: req.query.aId
      };
      let opts = {
        uId: req.user.userid
      };
      if (req.headers && req.headers['rongxin-land-area']) {
        opts.landHeader = { key: "rongxin-land-area", value: req.headers['rongxin-land-area'] };
      }

      let result = await contractOwnerService.getContractCowners(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this;

    self._policyRouter.get('/api/v2.0/loan/cxwq/contract/list', self.getCxwqLoanContractList.bind(self), {
      name: 'sigin.getCxwqLoanContractList',
      role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v2.0/contract/owners', self.getContractOwnersList.bind(self), {
      name: 'sigin.getContractOwnersList',
      // role: PERMISSION_ROLE.EVERYONE
    });

    self._policyRouter.get('/api/v2.0/contract/cowners', self.getContractCowners.bind(self), {
      name: 'contractCowner.getContractCowners',
      // role: PERMISSION_ROLE.EVERYONE
    });


    return self;
  }
}

module.exports = Router;
/**
 * <AUTHOR>
 * 2019-05-05
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:loan_feedback');
const loanFeedbackSvc = require('../services/loan_feedback');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE

class LoanFeedback {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  // 贷后订单
  async getLoanList(req, res) {
    let method = 'getLoanList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_NEWS_CREATE_106',
          httpCode: 401,
          reason: '用户未登录'
        }
      }

      let condition = {
        destiner: req.user.userid,
        status: {
          $in: ["waitLoan", "loaned"]
        },
        archived: false,
        tId: req.Client.tId,
        limit: req.query.limit || 10,
        skip: req.query.skip || 0,
        $sort: {
          createdTime: -1
        }
      };
      let opts = {};

      let result = await loanFeedbackSvc.getLoanList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  // 创建贷后反馈
  async createLoanFeeback(req, res) {
    let method = 'getLoanFeedbackList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_NEWS_CREATE_106',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.body || !req.body.type || !req.body.content || !req.body.loanId) {
        throw {
          errorCode: 'L_CREATE_93',
          httpCode: 406,
          reason: 'invalid param'
        };
      }
      let condition = req.body
      condition.destiner = req.user.userid
      let opts = {};

      let result = await loanFeedbackSvc.createLoanFeeback(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }
  // 查询贷后回访
  async getLoanFeedbackList(req, res) {
    let method = 'getLoanFeedbackList'
    debug(method, '[Enter]')
    try {
      if (!req.user || !req.user.userid) {
        throw {
          errorCode: 'E_NEWS_CREATE_106',
          httpCode: 401,
          reason: '用户未登录'
        }
      }
      if (!req.query || !req.query.loanId) {
        throw {
          errorCode: 'L_CREATE_93',
          httpCode: 406,
          reason: 'invalid param'
        };
      }
      let condition = {
        loanId: req.query.loanId,
        limit: 'unlimited',
        archived: false,
        $sort: {
          createdTime: -1
        },
        limit: req.query.limit || 10,
        skip: req.query.skip || 0,
      };
      let opts = {};

      let result = await loanFeedbackSvc.getLoanFeedbackList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  // 查询订单状态流程记录列表
  async getLoanStatusFlowList(req, res) {
    let method = 'getLoanStatusFlowList'
    debug(method, '[Enter]')
    try {
      if (!req.query || !req.query.loanId) {
        throw {
          errorCode: 'L_STATUS_FLOW_136',
          httpCode: 406,
          reason: 'invalid param'
        };
      }

      let condition = {
        loanId: req.query.loanId,
        limit: 'unlimited',
        archived: false,
        $sort: {
          createdTime: -1
        }
      };
      let opts = {};

      let result = await loanFeedbackSvc.getLoanStatusFlowList(condition, opts)
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {
    let self = this

    self._policyRouter.get('/api/v1.0/loan/list', self.getLoanList.bind(self), {
      name: 'loan_feedback.getLoanList',
      role: PERMISSION_ROLE.EVERYONE
    })
    self._policyRouter.post('/api/v1.0/feedback/create', self.createLoanFeeback.bind(self), {
      name: 'loan_feedback.createLoanFeeback',
      role: PERMISSION_ROLE.EVERYONE
    })
    self._policyRouter.get('/api/v1.0/feedback/list', self.getLoanFeedbackList.bind(self), {
      name: 'loan_feedback.getLoanFeedbackList',
      role: PERMISSION_ROLE.EVERYONE
    })

    self._policyRouter.get('/api/v1.0/status/flow/list', self.getLoanStatusFlowList.bind(self), {
      name: 'loan_feedback.getLoanStatusFlowList',
      role: PERMISSION_ROLE.EVERYONE
    })

    return self
  }
}

module.exports = LoanFeedback
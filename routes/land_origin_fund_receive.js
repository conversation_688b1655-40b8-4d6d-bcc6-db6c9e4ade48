/** 
 * newsRouter 
 * <AUTHOR>
 * 2019-10-18
 */

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:land_origin_fund_receive');
const landOriginFundReceiveService = require('../services/landOriginFundReceive');
const PERMISSION_ROLE = require('../services/permission').PERMISSION_ROLE;
const {assert,formatParas} = require('../utils/general')

class Router {
  constructor(policyRouter) {
    this._policyRouter = policyRouter;
  }

  async getLandOriginFundReceiveList({exportExcel,getOne,fundReceiveStatusIn},req, res) {
    const method = 'editLandOriginFundReceive';
    debug(method, '[Enter]')
    try {

      assert(!getOne || req.query._id,'E_APP_FUND_RECEIVE_001','para _id is required')
      fundReceiveStatusIn && ( req.query.fundReceiveStatusIn = fundReceiveStatusIn );
      // process.env.NODE_ENV !=='local-dev' && delete req.query.uId;
      // process.env.NODE_ENV !=='local-dev' && delete req.query.tId;
      // req.user && (req.query.uId = req.user.userid);
      // req.user && (req.query.tId = req.Client && req.Client.tId);
      // assert(req.query.uId,'E_LAND_ORIGIN_FUND_RECEIVE_003','uId is required')
      // assert(req.query.tId,'E_LAND_ORIGIN_FUND_RECEIVE_004','tId is required')
      assert(req.query.fundReceiveId,'E_LAND_ORIGIN_FUND_RECEIVE_005','fundReceiveId is required')
      // assert(req.query.uId,'E_LAND_ORIGIN_FUND_RECEIVE_000','您必须登录之后才能操作')
      // assert(req.query.tId,'E_LAND_ORIGIN_FUND_RECEIVE_000','您必须登录之后才能操作')
      const config = [
        'aId','_id','fundReceiveId',
        {from:'fundReceiveStatusIn',to:'fundReceiveStatus',rule:'in'},
        {from:'createdTimeStart',to:'createdTime',rule:'gte',fs:'toMinDay'},
        {from:'createdTimeEnd',to:'createdTime',rule:'lte',fs:'toMaxDay'},
        {from:'skip',dv:0},
        {from:'limit',dv:'10'},
        {from:'$sort',dv:{ createdTime: -1 },fs:'json'},
      ];
      const opts = {
        getOne,tId:req.query.tId,uId:req.query.uId,
        followFundReceiveStatus:req.query.followFundReceiveStatus && true || false,
        download:req.query.download !== 'false',
        formatOpts:{excelInfo:exportExcel},
      } ;
      const condition = {
        ...formatParas(config,req.query),
        archived: false,
      };
      //     {
      //   uId:query.uId,
      //   skip: query.skip || 0,
      //   limit: query.limit || 'unlimited',
      //   archived: false,
      //   $sort: query.$sort || { createdTime: -1 }
      // };
      // query.aId && ( condition.aId = query.aId );
      // query._id && ( condition._id = query.id );
      // const mName = exportExcel ? 'landOriginFundReceiveExport' : 'landOriginFundReceiveList';
      const result = await landOriginFundReceiveService.landOriginFundReceiveList(condition, opts);
      // if( exportExcel && opts.download ){
      //   res.set("Content-Disposition", `attachment;fileName=${encodeURIComponent(result.filename)}.xls`);
      //   res.set('Content-Type', 'application/x-xls');
      //   debug(method, '[Exit](success)');
      //   res.status(200).send(result.buffer);
      //   return;
      // }
      debug(method, '[Exit](success)', result);
      res.status(200).send(result);
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      res.set('Warning', `199 - ${error.errorCode || ''} ${new Date()}`);
      res.status(error.httpCode || 500).send(error.message || error);
    }
  }

  init() {

    this._policyRouter.get('/api/v1.0/land/origin/fund/receive/list',
        (...paras)=>this.getLandOriginFundReceiveList({},...paras), {
      name: 'fund.getLandOriginFundReceive.list.all',
      role: PERMISSION_ROLE.EVERYONE
    });

    // this._policyRouter.get('/api/v1.0/land/origin/fund/receive/excel',
    //     (...paras)=>this.getLandOriginFundReceiveList({exportExcel:true,fundReceiveStatusIn:'unused,new'},...paras), {
    //       name: 'fund.getLandOriginFundReceive.list.excel',
    //       role: PERMISSION_ROLE.EVERYONE
    //     });

    // this._policyRouter.post('/api/v1.0/land/origin/fund/receive/import',
    //     (...paras)=>this.importLandOriginFundReceive(...paras), {
    //       name: 'fund.editLandOriginFundReceive',
    //       role: PERMISSION_ROLE.EVERYONE
    //     });

    // this._policyRouter.put('/api/v1.0/land/origin/fund/receive/edit',
    //     (...paras)=>this.editLandOriginFundReceive(...paras), {
    //       name: 'fund.editLandOriginFundReceive',
    //       role: PERMISSION_ROLE.EVERYONE
    //     });

    // this._policyRouter.put('/api/v1.0/land/origin/fund/receive/confirm', this.landOriginFundReceiveConfirm.bind(this), {
    //   name: 'fund.confirmLandOriginFundReceive',
    //   role: PERMISSION_ROLE.EVERYONE
    // })

    // this._policyRouter.put('/api/v1.0/land/origin/fund/receive/unused/one', this.landOriginFundReceiveUnusedOne.bind(this), {
    //   name: 'fund.unusedOneLandOriginFundReceive',
    //   role: PERMISSION_ROLE.EVERYONE
    // })

    return this;
  }
}

module.exports = Router;
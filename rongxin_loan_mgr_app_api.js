/**
 * rongxin loan mgr app api
 * <AUTHOR>
 */

'use strict'

const logFactory = require('./utils/logFactory')
const logUtil = require('./utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api')
const express = require('express')
const http = require('http')
const config = require('config')
const env = require('./services/env')
const program = require('commander')
const MsgDispatcherInitializer = require('./utils/bootstrap/msgDispatcherInitializer');

// if (config.get('skywalking.enable')) {
//   require('skyapm-nodejs').start({
//     // Service name is showed in sky-walking-ui. Suggestion: set an unique name for each service, one
//     // service's nodes share the same code.
//     // this value cannot be empty.
//     serviceName: config.get('skywalking.serviceName'),
//     // Collector agent_gRPC/grpc service addresses.
//     // default value: localhost:11800
//     directServers: config.get('skywalking.directServers')
//   });
// }

//====================
// program parser
//====================
program
  .version('1.0.0')
  .usage('[options] ')
  .option('-h, --hostname [hostname]', 'Hostname or IP address for the service to bind, if not presented, use the one specfied in conf file')
  .option('-p, --port [port]', 'The service listening port, if not presented, use the one specified in conf file')
  .option('-l, --log-level [log-level]', 'The service log-level')
  .option('-m, --mode [mode]', 'The service process mode, production, stage or test')
  .parse(process.argv)

const bootstrap = require('nongfu.merchant.bootstrap')
let DSRegistry = require('nongfu.merchant.datasource').DSRegistry;
DSRegistry.ENV_CONF = config.get('DataProviders');

bootstrap.register(DSRegistry.INSTANCE);
bootstrap.register(new MsgDispatcherInitializer());

const app = express()

env.setServerMode(app.get('env'))
// const AmqpInitializer = require('./utils/bootstrap/amqpInitializer');
// bootstrap.register(AmqpInitializer);

bootstrap.init((error, result) => {
  if (error)
    process.exit(1)

  const router = require('./routes')

  app.use('/', router)

  let hostInEnv = process.env.NODE_HOSTNAME
  let portInEnv = process.env.NODE_PORT

  let host = hostInEnv || config.get('server').host
  let port = portInEnv || config.get('server').port

  let server = http.createServer(app)
  server.listen(port, host)
  debug.info(`Server start up on listening [host:${host}, port:${port}]`)
})

{"server": {"host": "localhost", "port": 37500}, "rongxin_dataService": {"host": "localhost", "port": "37400", "basicAuth": "Basic cm9uZ3hpbl9sb2FuOnBhc3N3MHJk"}, "asset_dataService": {"host": "localhost", "port": "8010", "basicAuth": "Basic cm9uZ3hpbl9hc3NldDpwYXNzdzByZA"}, "rongxin_loan_user_app_api_service": {"host": "rongxin-loan-user-app-api-ref.service.prod", "port": 80}, "credit_user_service": {"host": "rongxin-credit-user-api-jilin-ref.service.dev", "port": "80"}, "credit_user_service_general": {"host": "rongxin-credit-user-api-ref.service.dev", "port": "80"}, "DataProviders": {"RedisDataSource": {"DriverType": "redis", "host": "localhost", "port": "6279"}}, "decision_Service": {"host": "dev-decisionapi.cacfintech.com", "wd_apiCode": "81a8ee3d9d2447828b797b19be58d42e", "callback_host": "rongxin-loan-cms-api-ref.service.dev", "jx_apiCode": "55f25e8a44e245fa8dbb0ab014695760", "hlj_apiCode": "ea04f7b22076405bbf1fe8271b6dfc21", "cxwq_apiCode": "2132e91ed611434b8310ca6e1c45dbba", "cxwq_enterprise_apiCode": "fcad98ae9c1a4aea98457d3539482133", "enterprise_apiCode": "fcad98ae9c1a4aea98457d3539482133", "xiongan_apiCode": "28a278716bf54bfbb69e3e58506ff534", "zh_person_apiCode": "55f25e8a44e245fa8dbb0ab014695760", "zh_enterprise_apiCode": "fcad98ae9c1a4aea98457d3539482133"}, "rongxin_loan_cms_api_service": {"host": "dev.cms.api.loan.cacfintech.com", "port": 80}, "repoServer": {"host": "https://dev-repo.api.loan.cacfintech.com/"}, "aliOSS": {"endpoint": "https://oss-cn-beijing.aliyuncs.com", "access_key": "UjyKDL1Jo24Z6TCm", "access_key_secret": "wfVuLx7LsqIEwMqZ4WGd6ZPjUCyEEF", "bucket": "rongxin-loan-test", "bucket_pub": "rongxin-pub-test", "default_expires": 21600}, "rabbitmq": {"host": "localhost", "port": 5672, "failFast": false, "dontBlock": true}, "rabbitmq-config": {"exchanges": "dev-rongxin-loan-exchange", "queues": "dev-rongxin-mgr-loan-application-queue", "routeKeys": "dev-collection-info.bind"}, "apiManagement": {"host": "http://rongxin-loan-api-management-ref.service.test", "apiId": "5ea520a5a5e64d9874018154", "caller": "rongxin.loan.user.api"}, "skywalking": {"enable": false, "serviceName": "rongxin_loan_mgr_app_api", "directServers": "localhost:11800"}, "emchatAuth": {"appKey": "****************#cacfintech", "orgName": "****************", "appName": "cacfintech", "clientId": "YXA6u_xJhHLkQNmOImjhu_C9Lg", "clientSecret": "YXA6PV0fcu3w2QwQecpzHJBbamJQIDw"}, "aliyunAuth": {"host": "http://lundroid.market.alicloudapi.com", "appkey": "*********", "apisecret": "risk8e86n9escrf8gq77ewq1nwnfsmji", "appcode": "815fdc434b154a3688d4e8be97d5de81", "eidHost": "https://eid.shumaidata.com", "ocrHost": "http://ai-market-ocr-person-id.icredit.link/", "host2": "https://bankcard234.shumaidata.com/"}, "land_dataService": {"host": "rongxin-land-data-ref.service.dev", "port": "80"}, "eSignAuth": {"host": "https://smlo.tsign.cn/opentreaty-service", "appId": "**********", "appSecret": "a9c15b1fb26704639383a6eaabd72610", "JFappId": "**********", "JFappSecret": "0265e6667d3c810454da29a062c8bbe7"}, "baseUrl": "https://dev-userapp.api.loan.cacfintech.com", "cxwqBaseUrl": "https://dev-userapp.api.loan.cacfintech.com", "pgService": {"host": "***********", "port": 5433, "database": "postgis", "user": "<PERSON><PERSON>", "password": "znrx@2020"}, "rongxin_admin_api_service": {"host": "rongxin-loan-admin-api-ref.service.stage", "port": 80}, "assetVerifyService": {"host": "http://localhost:8000"}, "repo_Service": {"host": "rongxin-loan-repo-ref.service.dev"}, "shortUrl": {"scope": "dev-t.", "m": "dev-m.", "shortHost": "cacfintech.com", "longHost": "loan.cacfintech.com"}, "cxwqShortUrl": {"scope": "dev-t.", "m": "dev-m.", "shortHost": "cacfintech.com", "longHost": "loan.cacfintech.com"}, "rongxin_quanguo_api_usercredit": {"sm4key": "86C63180C2806ED1F47B859DE501215B", "host": "stage-user-api-credit.cacfintech.com", "port": "443"}, "rongxin_api_usercredit": {"sm4key": "86C63180C2806ED1F47B859DE501215B", "host": "localhost", "port": "8443"}, "mobile3elements": {"appCode": "815fdc434b154a3688d4e8be97d5de81"}}
{"server": {"host": "mgrapp.api.loan.cacfintech.com", "port": 37500}, "rongxin_dataService": {"host": "rongxin-loan-data-ref.service.prod", "port": "80", "basicAuth": "Basic cm9uZ3hpbl9sb2FuOnBhc3N3MHJk"}, "asset_dataService": {"host": "rongxin-asset-data-ref.service.prod", "port": "80", "basicAuth": "Basic cm9uZ3hpbl9hc3NldDpwYXNzdzByZA"}, "DataProviders": {"RedisDataSource": {"DriverType": "redis", "host": "rongxin-redis-m-svc.prod", "port": "6279"}}, "decision_Service": {"host": "rongxin-decision-ref.service.prod", "wd_apiCode": "81a8ee3d9d2447828b797b19be58d42e", "callback_host": "rongxin-loan-cms-api-ref.service.prod", "jx_apiCode": "55f25e8a44e245fa8dbb0ab014695760", "hlj_apiCode": "ea04f7b22076405bbf1fe8271b6dfc21", "enterprise_apiCode": "fcad98ae9c1a4aea98457d3539482133", "cxwq_apiCode": "2132e91ed611434b8310ca6e1c45dbba", "cxwq_enterprise_apiCode": "503b8c23f9d94c4aa62ac66cbf4fcbe6", "xiongan_apiCode": "28a278716bf54bfbb69e3e58506ff534", "zh_person_apiCode": "002a602e7f8d461b99c893a06c709be7", "zh_enterprise_apiCode": "f69c80d104434b2fb1e731e5449a7748"}, "rongxin_loan_cms_api_service": {"host": "rongxin-loan-cms-api-ref.service.prod", "port": 80}, "credit_user_service_general": {"host": "rongxin-credit-user-api-ref.service.prod", "port": "80"}, "repoServer": {"host": "https://repo.api.loan.cacfintech.com/"}, "aliOSS": {"endpoint": "https://oss-cn-beijing.aliyuncs.com", "access_key": "UjyKDL1Jo24Z6TCm", "access_key_secret": "wfVuLx7LsqIEwMqZ4WGd6ZPjUCyEEF", "bucket": "rongxin-loan", "bucket_pub": "rongxin-pub", "default_expires": 21600}, "rongxin_loan_user_app_api_service": {"host": "rongxin-loan-user-app-api-ref.service.prod", "port": 80}, "rongxin_admin_api_service": {"host": "rongxin-loan-admin-api-ref.service.prod", "port": 80}, "rongxin_dashboardService": {"host": "rongxin-loan-dashboard-api-ref.service.prod", "port": "80"}, "rabbitmq": {"host": "rongxin-rabbitmq-svc", "port": 5672, "username": "rabbitmq_management", "password": "3jY99yNyu8wqLVpr", "failFast": false, "dontBlock": false}, "rabbitmq-config": {"exchanges": "rongxin-loan-exchange", "queues": "rongxin-mgr-loan-application-queue", "routeKeys": "collection-info.bind"}, "apiManagement": {"host": "http://rongxin-loan-api-management-ref.service.prod", "apiId": "5ea520a5a5e64d9874018154", "caller": "rongxin.loan.user.api"}, "skywalking": {"enable": false, "serviceName": "rongxin_loan_mgr_app_api", "directServers": "localhost:11800"}, "emchatAuth": {"appKey": "****************#cacfintech", "orgName": "****************", "appName": "cacfintech", "clientId": "YXA6u_xJhHLkQNmOImjhu_C9Lg", "clientSecret": "YXA6PV0fcu3w2QwQecpzHJBbamJQIDw"}, "aliyunAuth": {"host": "http://lundroid.market.alicloudapi.com", "appkey": "*********", "apisecret": "risk8e86n9escrf8gq77ewq1nwnfsmji", "appcode": "815fdc434b154a3688d4e8be97d5de81", "eidHost": "https://eid.shumaidata.com", "ocrHost": "http://ai-market-ocr-person-id.icredit.link/", "host2": "https://bankcard234.shumaidata.com/"}, "land_dataService": {"host": "rongxin-land-data-ref.service.prod", "port": "80"}, "credit_user_service": {"host": "userapi-credit.jlncjf.com", "port": "", "protocol": "https"}, "eSignAuth": {"host": "https://o.tsign.cn/opentreaty-service", "appId": "**********", "appSecret": "dbc6d8ba8737cb4f569a16e7c8b731a6", "eidHost": "https://eid.shumaidata.com", "JFappId": "**********", "JFappSecret": "f3fd40c95330f85f958b782a51fd84f5"}, "baseUrl": "https://userapp.api.loan.cacfintech.com", "cxwqBaseUrl": "https://userapp.api.loan.cacfintech.com", "pgService": {"host": "rongxin-postgresql-svc", "port": 5433, "database": "postgis", "user": "<PERSON><PERSON>", "password": "znrx@2020"}, "assetVerifyService": {"host": "http://rongxin-asset-verify-api-ref.service.prod"}, "repo_Service": {"host": "rongxin-loan-repo-ref.service.prod"}, "shortUrl": {"scope": "t.", "m": "m.", "shortHost": "cacfintech.com", "longHost": "loan.cacfintech.com"}, "cxwqShortUrl": {"scope": "t.", "m": "m.", "shortHost": "cacfintech.com", "longHost": "loan.cacfintech.com"}, "rongxin_quanguo_api_usercredit": {"sm4key": "86C63180C2806ED1F47B859DE501215B", "host": "user-api-credit.cacfintech.com"}, "rongxin_api_usercredit": {"sm4key": "86C63180C2806ED1F47B859DE501215B", "host": "userapi-credit.jlncjf.com", "port": "443"}, "mobile3elements": {"appCode": "815fdc434b154a3688d4e8be97d5de81"}}
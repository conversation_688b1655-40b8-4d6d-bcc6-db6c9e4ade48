/**
* the util intent to get user from request.user
* <AUTHOR>
* 2018-01-30 
*/ 

'use strict'

const logFactory = require('./logFactory')
const logUtil = require('./logUtil')
const debug = logFactory(logUtil())('rongxin:loan.app.api:utils:getUserFromReq')

/**
 * get user from request.user
 * @param {*} req 
 * @return {Object}
 * {
 *  userid
 *  token
 * }
 */
function getUserFromReqCtx(req) {
  if (!req || !req.user)
    return

  return req.user

  // for dev
  /*  return {
      userid: 'xxxxxxxx',
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoi6LaF57qn566h55CG5ZGYIiwidGVuYW50IjoieXQiLCJpYXQiOjE1MTczNzUxNzIxNjYsImV4cCI6MTUxNzM3Nzc2NDE2NiwiYXVkIjoic3pub25nZnU6bWVyY2hhbnQ6and0OmFjY2VzcyIsImlzcyI6InN6bm9uZ2Z1Lm1lcmNoYW50LmFwcCIsInN1YiI6Ii01IiwianRpIjoiYjM1MmU0NjAtOGE4Zi00MDM3LTk4OGEtNGZiZWI1OWQ3YTMwIn0.29xroWio9IEXbObimBqxQ8VkmpN8UIX2f2uwv_qJyfQ'
    }*/
}

/**
 * get client from request.Client
 * @param {*} req
 * @return {Object}
 * {
 *  clientid
 *  scope
 *  token
 * }
 */
function getClientFromReqCtx(req) {
  if (!req || !req.Client)
    return

  return req.Client

  // for dev
  /*  return {
      clientid: '-5',
      scope: 'yt', //depercated since v2.1
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoi6LaF57qn566h55CG5ZGYIiwidGVuYW50IjoieXQiLCJpYXQiOjE1MTczNzUxNzIxNjYsImV4cCI6MTUxNzM3Nzc2NDE2NiwiYXVkIjoic3pub25nZnU6bWVyY2hhbnQ6and0OmFjY2VzcyIsImlzcyI6InN6bm9uZ2Z1Lm1lcmNoYW50LmFwcCIsInN1YiI6Ii01IiwianRpIjoiYjM1MmU0NjAtOGE4Zi00MDM3LTk4OGEtNGZiZWI1OWQ3YTMwIn0.29xroWio9IEXbObimBqxQ8VkmpN8UIX2f2uwv_qJyfQ'
    }*/
}

/**
 * get client from request.Client
 * @param {*} req
 * @return {Number}
 * 1(APP)/2(PC)
 */
function getDeviceFromReqCtx(req) {
  if (!req || !req.device)
    return

  return req.device

  // for dev
  /*  return {
      clientid: '-5',
      scope: 'yt', //depercated since v2.1
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoi6LaF57qn566h55CG5ZGYIiwidGVuYW50IjoieXQiLCJpYXQiOjE1MTczNzUxNzIxNjYsImV4cCI6MTUxNzM3Nzc2NDE2NiwiYXVkIjoic3pub25nZnU6bWVyY2hhbnQ6and0OmFjY2VzcyIsImlzcyI6InN6bm9uZ2Z1Lm1lcmNoYW50LmFwcCIsInN1YiI6Ii01IiwianRpIjoiYjM1MmU0NjAtOGE4Zi00MDM3LTk4OGEtNGZiZWI1OWQ3YTMwIn0.29xroWio9IEXbObimBqxQ8VkmpN8UIX2f2uwv_qJyfQ'
    }*/
}

//数据隔离条件
async function getEmployeeLimit(employeeGroupData,tId,employee,fieldName='areaCode',roleId=null,autoError=true){
    const list = (await employeeGroupData.getByCondition({ employee,archived:false,limit:'unlimited' }));//tId 先不加入条件了，影响范围太大了。但其实是应该加上的
    const employees = list.filter(v => !roleId || v.group === roleId);
    debug('findAreaLimitQuery',roleId, JSON.stringify({ employee,archived:false,limit:'unlimited' }), employees,list);
    if( autoError && employees.length === 0 ){
    throw {
      errorCode: 'E_OUT_LAND_006',
      httpCode: 406,
      reason: 'error operator'
    }
  }
  const areaLimit = employees.reduce((r,v)=>r.concat(v.areaList||[]),[]);
  const or = areaLimit.map( code=> ({[fieldName]:{'$regex': `^${code}`,'$options': 'si' }}) );
  debug('findAreaLimitAreaCode',JSON.stringify(or),employees);
  return or;
}


module.exports = {
  getUserFromReqCtx: getUserFromReqCtx,
  getClientFromReqCtx: getClientFromReqCtx,
  getDeviceFromReqCtx: getDeviceFromReqCtx,
  getEmployeeLimit,
}
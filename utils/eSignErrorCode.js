let ESIGN_ERRORCODE = new Map([
  [1435002, '参数错误'],
  [1435102, '查询项目开发者账户失败'],
  [1435103, '项目不存在'],
  [1435104, '查询项目配置失败'],
  [1435202, '用户中心查询账户失败'],
  [1435203, '创建账号失败'],
  [1435204, '账号不存在'],
  [1435205, '注销账号失败'],
  [1435206, '账号已存在'],
  [1435207, '企业账号已存在，且已关联经办人'],
  [1435302, '文件系统查询失败'],
  [1435303, '文件系统获取下载地址失败'],
  [1435304, '文件不存在'],
  [1435305, '文件类型不支持'],
  [1435306, '文件大小超过限制'],
  [1435402, '印章服务查询失败'],
  [1435403, '查询账户默认印章失败'],
  [1435502, '创建合同失败'],
  [1435503, '删除合同失败'],
  [1435504, '获取合同下载地址失败'],
  [1436002, '签署流至少应包含一个签署者信息'],
  [1436003, '签署人至少应指定一个签署文档'],
  [1436004, '签署者账户不存在'],
  [1436005, '签署者指定的签署文档不在文档列表中'],
  [1436006, '平台签署未指定任何签署文档'],
  [1436007, '静默签发起失败: 未找到账户默认印章'],
  [1436008, '签署付费账户不在签署流程中'],
  [1436009, '签署文档校验失败'],
  [1436010, '静默签账户未进行授权'],
  [1436011, '未指定盖章位置定位方式'],
  [1436012, '关键字签未指定关键字'],
  [1436013, '签署流创建失败'],
  [1436014, '签署流创建校验未通过'],
  [1436015, '签署流执行失败'],
  [1436016, '获取签署流详情失败'],
  [1436017, '静默授权失败'],
  [1436018, '撤销签署流失败'],
  [1436019, '获取签署文件下载地址失败']
]);

module.exports = {
  ESIGN_ERRORCODE
}
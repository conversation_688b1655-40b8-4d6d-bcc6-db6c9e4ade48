/**
 * msg dispatcher initializer
 * <AUTHOR>
 * 2018-05-24
 */

'use strict';

const logFactory = require('../logFactory');
const logUtil = require('../logUtil');
const debug = logFactory(logUtil())('rongxin:loan:api:services:bootstrap:amqpInitializer');
const AmqpClient = require('nongfu.merchant.msgfw').AmqpClient;
const config = require('config');

class Initializer {
  constructor() {
    this.amqpClient = null;
  }

  getName() {
    return 'AmqpInitializer';
  }

  getAmqpClient() {
    return this.amqpClient;
  }

  /**
   * initializer 
   */
  init(callback) {
    let method = 'init';
    debug(method, '[Enter]');

    try {
      let amqpClient = new AmqpClient({
        platform: "rongxin-mgr-app-api",
        log: logFactory(logUtil())('rongxin:loan:mgr:app:amqp'),
        redisClient: require('../../persistence/dataStore'),
        host: config.get('rabbitmq').host,
        port: config.get('rabbitmq').port,
        failFast: config.get('rabbitmq').failFast
      });
      if( config.get('rabbitmq').dontBlock ){
        amqpClient.connect();
        this.amqpClient = amqpClient;
        return callback();
      }
      amqpClient.connect().then(async () => {
        this.amqpClient = amqpClient;

        return callback();
      }).catch(error => {
        return callback(error);
      });
    } catch (error) {
      debug.error(method, '[Exit](error) Error occurs during initialize amqp client: ', error);
      return callback(error);
    }
  }
}

module.exports = new Initializer();
/**
 * msg dispatcher initializer
 * <AUTHOR>
 * 2018-05-24
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan:app:api:services:bootstrap:msgDispatcherInitializer');

const msgQueue = require('../../services/msgQueue');
const msgStore = require('../../services/msgStore');
const SystemDispatcher = require('../../services/messages/dispatchers/systemDispatcher');
const InternalMsgDispatcher = require('../../services/messages/dispatchers/internalMsgDispatcher');
const SmsYunZhiXunDispatcher = require('../../services/messages/dispatchers/smsYunZhiXunDispatcher');
const PushDispatcher = require('../../services/messages/dispatchers/pushDispatcher');
const SmsTianYanDispatcher = require('../../services/messages/dispatchers/smsTianYanDispatcher');
const SmsALiYunDispatcher = require('../../services/messages/dispatchers/smsALiYunDispatcher');
const MsgSvcRegistry = require('nongfu.merchant.msgfw').MsgSvcRegistry.INSTANCE;

class MsgDispatcherInitializer {
  constructor() {

  }

  getName() {
    return 'MsgDispatcherInitializer';
  }

  /**
   * initializer
   */
  init(callback) {
    let self = this;
    let method = 'init';
    debug(method, '[Enter]');

    try {
      let sms_opts = {
        msgQueue: msgQueue,
        msgStore: msgStore
      };
      MsgSvcRegistry.addDisptcher(new InternalMsgDispatcher(sms_opts));
      MsgSvcRegistry.addDisptcher(new SmsYunZhiXunDispatcher(sms_opts));
      MsgSvcRegistry.addDisptcher(new PushDispatcher(sms_opts));
      MsgSvcRegistry.addDisptcher(new SmsTianYanDispatcher(sms_opts));
      MsgSvcRegistry.addDisptcher(new SmsALiYunDispatcher(sms_opts));
      return callback();
    } catch (error) {
      debug.error(method, '[Exit](error) Error occurs during initialize msgSvcRegistry: ', error);
      return callback(error);
    }
  }
}

module.exports = MsgDispatcherInitializer;
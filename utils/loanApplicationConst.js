const APPLICATION_STATUS = {
  NEW: "new",
  NEW_LACK_CREDIT: "new_lack_credit",
  CERTIFIED: "certified",
  BIOPSY_APPROVED: "biopsy_approved",
  PRE_COMMENT: "pre_comment",
  PRE_CENSOR: "pre_censor",
  REVIEW: "review",
  UNPAID: "unpaid",
  WAIT_SIGN: "waitSign",
  CONTRACT_REVIEW: "contract_review",
  WAIT_LOAN: "waitLoan",
  WAIT_FILTRATE: "wait_filtrate",
  WAIT_DISPOSE: "wait_dispose",
  LOANED: "loaned",
  REJECTED_NEW_1: "rejected_new_1",
  REJECTED_NEW_2: "rejected_new_2",
  REJECTED_NEW_3: "rejected_new_3",
  REJECTED_NEW_4: "rejected_new_4",
  REJECTED_CERT: "rejected_cert",
  REJECTED_CREDIT: "rejected_credit",
  REJECTED_CENSOR: "rejected_censor",
  REJECTED_REVIEW: "rejected_review",
  REJECTED_WHITELIST: "rejected_whitelist",
  REJECTED_CONTRACT: "rejected_contract",
  REJECTED_FILTRATE: "rejected_filtrate",
  REJECTED_LOAN: "rejected_loan",
  DECLINE_CENSOR: "decline_censor",
  REJECTED_FINAL: "rejected_final",
  FINAL_REVIEW: "final_review",
  FINISHED: "finished",
  FINISHED_LOAN: "finished_loan",
  REGISTER_BANK_ACCOUNT: "register_bank_account",
  CREDIT_ACCESS: "credit_access",
  REJECTED_CREDIT_ACCESS: "rejected_credit_access",
  PRE_APPROVE: "pre_approve",
  REJECTED_BIOPSY: "rejected_biopsy",
  REJECTED_SUPPLEMENT: "rejected_supplement",
  REJECTED_PREREQ: "rejected_prereq",
  PRE_TRANSFEROR: "pre_transferor",
  TRANSFEROR: "transferor",
  APPLY_CREDIT: "apply_credit",
  WAIT_BANK_SIGN: "waitBankSign",
  REJECTED_BANK_SIGN: 'rejectedBankSign',
  WAIT_PHONE_CHECK: 'wait_phone_check',
  REJECTED_PHONE_CHECK: 'rejected_phone_check',
  REJECTED_CREDIT_INVALID: "rejected_credit_invalid",
  LOAN_APP_COLLECTION: "collection"
};

const APPLICATION_TRACKING_ACTION = {
  CERTIFIED: "certified",
  PRE_CENSOR: "pre_censor",
  REVIEW: "review",
  REVIEWED: "reviewed",
  WAIT_FILTRATE: "wait_filtrate",
  WAIT_DISPOSE: "wait_dispose",
  REJECTED_NEW_1: "rejected_new_1",
  REJECTED_NEW_2: "rejected_new_2",
  REJECTED_NEW_3: "rejected_new_3",
  REJECTED_NEW_4: "rejected_new_4",
  REJECTED_CERT: "rejected_cert",
  REJECTED_CENSOR: "rejected_censor",
  REJECTED_REVIEW: "rejected_review",
  REJECTED_WHITELIST: "rejected_whitelist",
  REJECTED_FINAL: "rejected_final",
  REJECTED_CREDIT: "rejected_credit",
  REJECTED_FILTRATE: "rejected_filtrate",
  DECLINE_CENSOR: "decline_censor",
  REJECTED_LOAN: "rejected_loan",
  CERT_SMS: "cert_sms",
  PAID: "paid",
  UNPAID: "unpaid",
  PAY_FAILED: "payFailed",
  LOANED: "loaned",
  CB_NOTIFY: "cb_notify",
  FINISHED: "finished",
  FINISHED_LOAN: "finished_loan",
  REJECTED_BIOPSY: "rejected_biopsy",
  REJECTED_SUPPLEMENT: "rejected_supplement",
  EXPENDITURE: "expenditure",
  WAIT_UNDERWRITING: "wait_underwriting",
  APPLY_CREDIT: "apply_credit",
  WAIT_BANK_SIGN: "waitBankSign",
  REJECTED_BANK_SIGN: 'rejectedBankSign',
  WAIT_PHONE_CHECK: 'wait_phone_check',
  REJECTED_PHONE_CHECK: 'rejected_phone_check',
  CALL_PHONE: 'call_phone',
  REJECTED_CREDIT_INVALID: "rejected_credit_invalid",
  LOAN_APP_COLLECTION: "collection"
};
const BILL_STATUS = {
  noRepayment: "0",
  payOff: "1",
  payPart: "2",
  overdue: "3"
};
const CERTIFY_GRADE = new Map([
  ['AAA', 10],
  ['AA', 11],
  ['A+', 12],
  ['A', 13],
  ['A-', 14],
  ['BBB', 15],
  ['BBB以下', 16],
  ['非信用户', 17]
]);
const LOAN_PRODUCT_TO_JINKONG = new Map([
  ["1034", "5bf7c3ca9a5b9c21cac77149"]
]);

const APP_STATUS_COMMENT = new Map([
  ['new', "新申请"],
  ['new_lack_credit', "新申请-无贷款额度"],
  ['certified', "待活体"],
  ['biopsy_approved', "待补充资料"],
  ["credit_access", "待授权签约"],
  ['wait_filtrate', "待筛选"],
  ['wait_dispose', "待处理"],
  ['pre_approve', "待预审（渤海）"],
  ['rejected_approve', "预审拒绝"],
  ['register_bank_account', "待开户"],
  ['pre_comment', "补充材料"],
  ['pre_censor', "待初审"],
  ['review', "待复审"],
  ['waitSign', "待合同签约"],
  ['contract_review', "合同审核"],
  ['final_review', "待信贷系统终审"],
  ['unpaid', "支付融资服务费"],
  ['waitLoan', "待放款"],
  ['loaned', "已放款"],
  ['wait_investigation_verify_1', '待尽调审核：村级服务站预审'],
  ['rejected_by_wait_investigation_verify_1', '尽调预审拒绝'],
  ['wait_investigation_verify_2', '待尽调审核：县级服务中心初审'],
  ['wait_investigation_verify_review_2', '登记部登记结果'],
  ['rejected_by_wait_investigation_verify_2', '尽调初审拒绝'],
  ['wait_investigation_verify_3', '待尽调审核：物权登记审核'],
  ['rejected_by_wait_investigation_verify_3', '登记尽调拒绝'],
  ['wait_investigation_verify_4', '待尽调审核：物权监管审核'],
  ['rejected_by_wait_investigation_verify_4', '监管尽调拒绝'],
  ['wait_investigation_verify_5', '待尽调审核：物权风控审核'],
  ['rejected_by_wait_investigation_verify_5', '风控尽调拒绝'],
  ['wait_investigation_verify_6', '待尽调审核：物权监委会审核'],
  ['rejected_by_wait_investigation_verify_6', '监委会尽调拒绝'],
  ['loan_verify_1', '待登记放款审批'],
  ['rejected_by_loan_verify_1', '登记放款审批拒绝'],
  ['loan_verify_2', '待监管放款审批'],
  ['rejected_by_loan_verify_2', '监管放款审批拒绝'],
  ['loan_verify_3', '待风控放款审批'],
  ['rejected_by_loan_verify_3', '风控放款审批拒绝'],
  ['loan_verify_4', '待生成鉴证报告'],
  ['rejected_by_loan_verify_4', '银行放款拒绝'],
  ['rejected_new_4', "地区暂未开通"],
  ['rejected_new_2', "申请人与实名不一致"],
  ['rejected_new_1', "实名失败"],
  ['rejected_cert', "信用评级不足"],
  ['rejected_new_3', "活体校验失败"],
  ['rejected_credit_access', "拒绝授信协议（渤海）"],
  ['rejected_whitelist', "银行白名单拒绝（渤海）"],
  ['decline_censor', "初审驳回"],
  ['rejected_censor', "初审拒绝"],
  ['rejected_review', "复审拒绝"],
  ['rejected_filtrate', "筛选拒绝"],
  ['rejected_eSign', "拒绝签署合同"],
  ['rejected_contract', "合同审核拒绝"],
  ['rejected_final', "终审拒绝"],
  ['rejected_loan', "放款拒绝"],
  ['finished', "提前终止授信（亿联）"],
  ['finished_loan', "账单已结清（渤海）"],
  ['wait_interview', "待面审"],
  ['wait_income', "待业务进件"],
  ['rejected_interview', "面审拒绝"],
  ['rejected_biopsy', "活体拒单"],
  ['rejected_supplement', "采集资料拒单"],
  ['pre_transferor', "待补充出让方信息"],
  ['transferor', "流转信息确认"],
  ['wait_underwriting', "待担保公司审批"],
  ['apply_credit', "待授信"],
  ['waitBankSign', "待签约银行合同"],
  ['rejectedBankSign', "拒绝签约银行合同"],
  ["wait_phone_check", "等待电核"],
  ["rejected_phone_check", "电核拒绝"],
  ["rejected_credit_invalid", "授信作废"],
  ['collection', "待信息采集"]
]);


const CONSUMER_TYPE = { PERSONAL: "01", ENTERPRISE: "02" };
const CONSUMER_MAP = new Map([
  ["01", "个人"],
  ["02", "企业"]
]);

module.exports = {
  APPLICATION_STATUS,
  APPLICATION_TRACKING_ACTION,
  CERTIFY_GRADE,
  LOAN_PRODUCT_TO_JINKONG,
  BILL_STATUS,
  APP_STATUS_COMMENT,
  CONSUMER_TYPE,
  CONSUMER_MAP
}
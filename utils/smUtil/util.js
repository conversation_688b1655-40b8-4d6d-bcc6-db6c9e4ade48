/*
 * @Description: sm工具包
 * @Author: zhu xue song
 * @Date: 2021-11-02 09:03:12
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-11-02 09:03:12
 * @FilePath: \rongxin.loan.user.app.api\utils\smUtil\util.js
 */
module.exports = {
  /**
   * 获得utf8编码格式的sm4key
   * @param {string} str 
   * @returns 
   */
  getSm4Key: ()=> {
    const str = "qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM1234567890";
    let sm4Key = '';
    for (let i = 0; i < 16; i++) {
      const randomNum = Number(Math.floor(Math.random()*(0 - str.length) + str.length));
      sm4Key += str.substring(randomNum, randomNum + 1);
    }
    return sm4Key;
  }
}
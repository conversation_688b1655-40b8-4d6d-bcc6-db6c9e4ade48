/*
 * @Description: sm加密
 * @Author: zhu xue song
 * @Date: 2021-11-02 10:35:20
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-11-03 15:38:03
 * @FilePath: \rongxin.loan.user.app.api\utils\smUtil\sm.js
 */
const { sm2, sm4 } = require('sm-crypto')
const SmConstant = require('./constant');
const defaultIv = '0123456789abcdeffedcba9876543210'

module.exports = {

  /**
   * sm2加密数据
   * 
   * @param {string|object} data 待加密得utf8得数据
   * @param {number} cipherMode 可选, 默认1 [ 0：C1C2C3 | 1：C1C3C2 ]
   * @returns 加密后的数据
   */
  sm2Encrypt: (data, publicKey, cipherMode = SmConstant.Mode.SM2.C1C3C2) => {
    if(typeof data !== 'string') {
      data = JSON.stringify(data);
    }
    let encryptData = sm2.doEncrypt(data, publicKey, cipherMode)
    return encryptData;
  },

  /**
   * sm2解密数据
   * 
   * @param {string} data 待解密的16进制数据（hex）
   * @param {number} cipherMode 可选, 默认1 [ 0：C1C2C3 | 1：C1C3C2 ]
   * @returns 解密后的数据
   */
  sm2Decrypt: (data, privateKey, cipherMode = SmConstant.Mode.SM2.C1C3C2) => {
    data = data.toLowerCase();
    let decryptData = sm2.doDecrypt(data, privateKey, cipherMode);
    try {
      return JSON.parse(decryptData);
    } catch (err) {
      return decryptData;
    }
  },

  /**
   * 
   * @param {object|string} data 待加密的utf8数据
   * @param {string} sm4Key 加密key(32位16进制字符串)
   * @param {object} options 配置选项
   *  - mode: 加密模式，默认cbc [ cbc | ecb ]
   *  - iv: 偏移，默认：0123456789abcdeffedcba9876543210， (32位16进制字符串)
   * @returns {string} 加密后的字符串
   */
  sm4Encrypt: (data, sm4Key, options = {}) => {
    const { mode = SmConstant.Mode.SM4.CBC, iv = defaultIv } = options;
    if(typeof data !== 'string') {
      data = JSON.stringify(data);
    }
    const encryptData = sm4.encrypt(data, sm4Key, { mode, iv })
    return encryptData;
  },

  /**
   * 
   * @param {string} data 加密的16进制数据（hex）
   * @param {string} sm4Key 加密key(32位16进制字符串)
   * @param {object} options 配置选项
   *  - mode: 加密模式，默认cbc [ cbc | ecb ]
   *  - iv: 偏移，默认：0123456789abcdeffedcba9876543210， (32位16进制字符串)
   * @returns 
   */
  sm4Decrypt: (data, sm4Key, options = {}) => {
    const { mode = SmConstant.Mode.SM4.CBC, iv = defaultIv } = options;
    if(typeof data !== 'string') {
      data = JSON.stringify(data);
    }
    data = data = data.toLowerCase();
    const decryptData = sm4.decrypt(data, sm4Key, { mode, iv });
    try {
      return JSON.parse(decryptData);
    } catch (err) {
      return decryptData;
    }
  },

  /**
   * 加签
   * 
   * @param {string} data utf8格式待签名数据
   * @param {string} privateKey 加密私钥
   * @param {object} options 选项
   *  - der: 默认false
   *  - hash: 默认false
   *  - userId: 默认''
   * @returns 
   */
  generalSignature: (data, privateKey, options = {}) => {
    let { der = false, hash = false, userId = '' } = options;
    if(typeof data !== 'string') {
      data = JSON.stringify(data);
    }
    return sm2.doSignature(data, privateKey, {
      der,
      hash,
      userId
    })
  },

  /**
   * 验签
   * 
   * @param {string} data utf8格式待签名数据
   * @param {string} signHex 16进制编码格式的签名字符串
   * @param {string} publicKey 公钥
   * @param {object} options 选项
   *  - der: 默认false
   *  - hash: 默认false
   *  - userId: 默认''
   * @returns 
   */
  verifySignature: (data, signHex, publicKey, options = {}) => {
    let { der = false, hash = false, userId = '' } = options;
    if(typeof data !== 'string') {
      data = JSON.stringify(data);
    }
    return sm2.doVerifySignature(data, signHex, publicKey, {
      der,
      hash,
      userId
    })
  },

  /**
   * 生成一个32位得16进制的字符串
   * @returns {string}
   */
  generalSm4Key: () => {
    return sm2.generateKeyPairHex().privateKey.substring(0, 32);
  }
}
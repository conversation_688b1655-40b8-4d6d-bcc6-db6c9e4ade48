/*
 * @Description: 三资加密
 * @Author: zhu xue song
 * @Date: 2021-10-25 16:11:49
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-11-03 15:00:19
 * @FilePath: \rongxin.loan.user.app.api\utils\smUtil\capital3.js
 */
const SmCrypto = require('./sm');
const SmConstant = require('./constant');
const config = require('config');
const sm4KeyHex = '86C63180C2806ED1F47B859DE501215B';

module.exports = {

  /**
   * sm4加密
   * 
   * @param {Object} data 待加密数据
   * @returns {string} encryptData
   *  - encryptData: hex编码格式的加密数据
   */
  sm4Encrypt: (data) => {
    let encryptData = SmCrypto.sm4Encrypt(data, sm4KeyHex,
      {
        mode: SmConstant.Mode.SM4.ECB
      });
    return encryptData;
  },

  /**
   * sm4解密
   * 
   * @param {string} data 加密数据（编码格式：hex）
   * @returns 
   */
  sm4Decrypt: (data) => {
    const decryptData = SmCrypto.sm4Decrypt(data, sm4KeyHex, 
      {
        mode: SmConstant.Mode.SM4.ECB
    })
    return decryptData;
  },
}
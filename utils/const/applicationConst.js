const LOAN_APP_STATUS = {
  LOAN_APP_CERTIFIED: 'certified',
  LOAN_APP_BIOPSY_APPROVED: 'biopsy_approved',
  LOAN_APP_PRECENSOR: 'pre_censor',
  <PERSON><PERSON><PERSON>_APP_WAIT_INTERVIEW: "wait_interview",
  LOAN_APP_WAIT_INCOME: "wait_income",
  LOAN_APP_PRE_APPROVE: 'pre_approve',
  LOAN_APP_REVIEW: 'review',
  LOAN_APP_UNPAID: 'unpaid',
  LOAN_APP_WAITLOAN: 'waitLoan',
  LOAN_APP_WAITSIGN: 'waitSign',
  LOAN_APP_CONTRACT_REVIEW: 'contract_review',
  LOAN_APP_LOANED: 'loaned',
  LOAN_APP_NEW: 'new',
  LOAN_APP_REJECTED_INTERVIEW: "rejected_interview",
  LOAN_APP_REJECTED_WHITELIST: "rejected_whitelist",
  <PERSON>OAN_APP_REJECTED_CENSOR: 'rejected_censor',
  <PERSON><PERSON><PERSON>_APP_DECLINE_CENSOR: 'decline_censor',
  <PERSON><PERSON><PERSON>_APP_REJECTED_NEW_1: 'rejected_new_1',
  LOAN_APP_REJECTED_NEW_2: 'rejected_new_2',
  LOAN_APP_REJECTED_NEW_3: 'rejected_new_3',
  LOAN_APP_REJECTED_REVIEW: 'rejected_review',
  LOAN_APP_REJECTED_LOAN: 'rejected_loan',
  LOAN_APP_REJECTED_CONTRACT: 'rejected_contract',
  LOAN_APP_FINAL_REVIEW: 'final_review',
  LOAN_APP_REJECTED_ESIGN: 'rejected_eSign',
  LOAN_APP_REJECTED_FINAL: 'rejected_final',
  LOAN_APP_REJECTED_BIOPSY: "rejected_biopsy",
  LOAN_APP_REJECTED_SUPPLEMENT: "rejected_supplement",
  LOAN_APP_COLLECTION: "collection"
};

const LOAN_APP_STATUS_MAP = new Map([
  ['new', "新申请"],
  ['new_lack_credit', "新申请-无贷款额度"],
  ['certified', "待活体"],
  ['biopsy_approved', "待完善资料"],
  ['wait_supplement_verify', '待审核资料'],
  ['wait_operator_collect', '待协理员信息采集'],
  ['rejected_operator_collect', '评级拒绝'],
  ['pre_censor', "待初审"],
  ['wait_underwriting', "待担保审定"],
  ['wait_filtrate', '待筛选'],
  ['wait_dispose', '待处理'],
  ['rejected_filtrate', '筛选拒绝'],
  ['review', "待复审"],
  ['waitSign', "待合同签约"],
  ['contract_review', "合同审核"],
  ['final_review', "待终审"],
  ['unpaid', "支付融资服务费"],
  ['waitLoan', "待放款"],
  // ['wait_info_collection',' 待采集信息'],
  ['rejected_by_info_collection', '信息采集拒绝'],
  ['wait_fund', '待贷款银行确认'],
  ['rejected_by_fund', '贷款银行拒绝'],
  ['wait_full_data', '待完善资料'],
  // ['wait_grant_sign',' 待授权签约'],
  ['wait_sign', ' 待授权签约'],
  ['wait_investigation_verify_1', '村级服务站预审'],
  ['rejected_by_wait_investigation_verify_1', '尽调预审拒绝'],
  ['wait_investigation_verify_2', '县级服务中心初审'],
  ['wait_investigation_verify_review_2', '登记部登记结果'],
  ['rejected_by_wait_investigation_verify_2', '尽调初审拒绝'],
  ['wait_investigation_verify_3', '物权登记审核'],
  ['rejected_by_wait_investigation_verify_3', '登记尽调拒绝'],
  ['wait_investigation_verify_4', '物权监管审核'],
  ['rejected_by_wait_investigation_verify_4', '监管尽调拒绝'],
  ['wait_investigation_verify_5', '物权风控审核'],
  ['rejected_by_wait_investigation_verify_5', '风控尽调拒绝'],
  ['wait_investigation_verify_6', '物权监委会审核'],
  ['rejected_by_wait_investigation_verify_6', '监委会尽调拒绝'],
  ['loan_verify_1', '待登记放款审批'],
  ['rejected_by_loan_verify_1', '登记放款审批拒绝'],
  ['loan_verify_2', '待监管放款审批'],
  ['rejected_by_loan_verify_2', '监管放款审批拒绝'],
  ['loan_verify_3', '待风控放款审批'],
  ['rejected_by_loan_verify_3', '风控放款审批拒绝'],
  ['loan_verify_4', '待生成鉴证报告'],
  ['rejected_by_loan_verify_4', '银行放款拒绝'],
  ['loaned', "已放款"],
  ['rejected_new_4', "地区暂未开通"],
  ['rejected_new_2', "申请人与实名不一致"],
  ['rejected_new_1', "实名失败"],
  ['rejected_cert', "信用评级不足"],
  ['rejected_new_3', "活体校验失败"],
  ['decline_censor', "初审驳回"],
  ['rejected_censor', "初审拒绝"],
  ['rejected_underwriting', "担保审定拒绝"],
  ['rejected_review', "复审拒绝"],
  ['rejected_eSign', "拒绝签署合同"],
  ['rejected_contract', "合同审核拒绝"],
  ['rejected_final', "终审拒绝"],
  ['rejected_loan', "放款拒绝"],
  ['finished', "提前终止授信（亿联）"],
  ['wait_interview', "待面审"],
  ['wait_income', "待业务进件"],
  ['rejected_interview', "面审拒绝"],
  ["credit_access", "待授权签约"],
  ['rejected_credit_access', "拒绝授信协议"],
  ['rejected_biopsy', "活体拒单"],
  ['rejected_supplement', "采集资料拒单"],
  ['wait_repayment', "待还款"],
  ['overdue', "已逾期"],
  ['settle_overdue', "逾期已结清"],
  ['pre_transferor', "待补充出让方信息"],
  ['transferor', "流转信息确认"],
  ['collection', "待信息采集"],
  ['apply_credit', "待银行授信"],
  ['wait_risk_check', '待授权查询'],
  ['rejected_enable_risk_check', '授权查询失效'],
  ['rejected_risk_check', '审核未通过'],
])

const LOAN_TRACK_STATUS_MAP = new Map([
  ['new', "新申请"],
  ['pre_censor', "待初审"],
  ['review', "待复审"],
  ['waitLoan', "待放款"],
  ['loaned', "放款通过"],
  ['rejected_new_1', "实名失败"],
  ['rejected_new_2', "申请人与实名不一致"],
  ['rejected_new_3', "活体校验失败"],
  ['rejected_new_4', "地区暂未开通"],
  ['rejected_cert', "信用评级不足"],
  ['rejected_censor', "初审拒绝"],
  ['rejected_review', "复审拒绝"],
  ['rejected_loan', "放款拒绝"],
  ['wait_filtrate', '初审通过'],
  ['wait_dispose', '筛选通过'],
  ['rejected_filtrate', '筛选拒绝'],
  // ['certified', "用户评级通过"],
  // ['pre_censor_log', "初审数据记录"],
  ['decline_censor', "初审驳回"],
  // ['cert_sms', "催促协理员采集信息"],
  ['paid', "已付款服务费待放款"],
  ['payFailed', "付款失败"],
  ['destined', "客户经理认领订单"],
  ['destined_cancel', "客户经理取消认领订单"],
  ['reviewed', "复审通过"],
  // ['cb_notify', "第三方回调通知"],
  ['rejected_contract', "合同审核拒绝"],
  ['unpaid', "支付融资服务费"],
  ['rejected_eSign', "拒绝签署合同"],
  ['contract_review', "合同审核"],
  ['rejected_credit', "用户额度暂无"],
  ['rejected_final', "终审拒绝"],
  ['final_review', "待信贷系统终审"],
  ['finished', "提前终止授信（亿联）"],
  ["credit_access", "待授权签约"],
  ['rejected_credit_access', "拒绝授信协议（渤海）"],
  ['rejected_whitelist', "银行白名单拒绝（渤海）"],
  ['bhApi_repo_pass', "渤海文件通过"],
  // ['bhApi_repo_halt', "渤海文件失败"],
  // ['bhApi_apply', "渤海授信申请"],
  // ['bhApi_result', "渤海资产负债比"],
  ['register_bank_account', "待开户（渤海）"],
  ['pre_approve', "授权签约成功"],
  ['finished_loan', "账单已结清（渤海）"],
  ['repay_trial', "还款试算"],
  ['repayement', "还款"],
  ['distribute_president', "待行长派单"],
  ['distribute_general_manager', "待总经理派单"],
  ['distribute_customer_manager', "待客户经理处理"],
  ['rejected_biopsy', "活体拒单"],
  ['rejected_supplement', "采集资料拒单"],
  ['pre_censor_destined', "客户经理认领"],
  ['pre_censor_destined_cancel', "客户经理取消认领"],
  ['wait_interview', "待面审"],
  ['rejected_interview', "面审拒绝"],
  ['wait_income', "待业务进件"],
])
let CHANNEL_MAP = new Map([
  ["device", "网点设备"],
  ["app", "手机app"]
])
module.exports = {
  LOAN_APP_STATUS,
  LOAN_APP_STATUS_MAP,
  LOAN_TRACK_STATUS_MAP,
  CHANNEL_MAP
}
/**
* counter utility
* <AUTHOR>
* 2018-02-01
*/

'use strict'
const XlsxPopulate = require('xlsx-populate');
const moment = require('moment')
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const assert = (success,errorCode,reason,httpCode=406)=>{if(!success) throw {errorCode,reason,httpCode}};
const dataUtil = require('../services/dataSvc/dataUtil');

const HANDLER_NAME = 'GENERAL_UTILS';
const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:application_v2:' + HANDLER_NAME);

const {
  loanApplication: loanApplicationData,
  cxwqCirculations: circulationData,
  landData,
  groups:groupsData,
  groupsV2:groupV2Data,
  employeeGroups:employeeGroupsData,
  employees:employeesData,
  userVerifys: userVerifyData,
  applicationSubcontractLand : applicationSubcontractLandData,
  loanApplicationConfirm: loanApplicationConfirmData,
  loanApplicationConfirmLand: loanApplicationConfirmLandData,
  loanLand: loanLandData,
} = dataUtil;

/*
为service类自动扩展流程固定的方法。流程固定是指固定由若干Handlers类依次处理的方法
支持两种格式：
1 传入config，格式示例：
const config = {
  createOrAddOrg:[CreateOrAddOrgHandler],
  createOrAddEmployee:[CreateOrAddEmployeeHandler],
  listEmployee:[ListEmployeeHandler],
  listOrg:[ListOrgHandler],
  validateAuth:[ValidateUserAuthHandler],
}
2 在service类中定义非async函数，函数返回值为数组，里面是若干Handlers类。所有不希望被自动格式化的函数名，都需要配置在exclude数组中
 */
function addHandlersForService(debug, config, exclude){

  config || ( config = Object.getOwnPropertyNames( this.constructor.prototype)
          .filter(k=>typeof this[k] === 'function' && !['constructor',...(exclude||[])].includes(k))
          .map(k=>({k,hs:this[k].call(this)}))
          .filter(({k,hs})=>Array.isArray(hs))
          .reduce((r,v)=>(r[v.k]=v.hs,r),{})
  )
  Object.entries(config).forEach( ([method,handlers])=>{
    this[ method ] = async function(input = {}, opts={},result={},error={}){
      debug.verbose(method, '[Enter]');
      const context = {input,opts,result,error};
      try {
        const svcHandlerMgrt = new SvcHandlerMgrt();
        handlers.forEach(cls=>svcHandlerMgrt.addHandler(new cls(context)));
        await svcHandlerMgrt.processAsync(context);
        debug(method, '[Exit](success): ', context.result);
        return context.result;
      } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        throw error;
      }
    }
  });
}


/*
  config示例：w是宽度，f是列中文,k是数据里的key值
  const config = [
    {w:5,f:'序号',k:'no'},
    {w:10,f:'类型',k:'actionName'},
    {w:10,f:'交易金额',k:'amount'},
    {w:10,f:'类型',k:'createdTime'},
  ];
 */
async function generalExportExcel(data,config){
  const workbook = await XlsxPopulate.fromBlankAsync();
  const sheet = workbook.sheet(0);
  config.map(v=>v.w).forEach((w,i)=>sheet.column(String.fromCharCode(65+i))
      .width(w).style("verticalAlignment", "center").style("horizontalAlignment", "center"));
  config.map((v,i)=>({f:v.f,sn:`${String.fromCharCode(65+i)}1`}))
      .forEach(({f,sn})=>(workbook.sheet("Sheet1").cell(sn).value(f)));
  data.forEach((v,i)=>config.forEach((cf,j)=>(
      workbook.sheet("Sheet1").cell(`${String.fromCharCode(65+j)}${i+2}`).value(v[cf.k]))))
  const buffer = await workbook.outputAsync({ type: "" });
  return buffer;
}

function formatParas(config,source,dest={}){
  config = config
      .map(v=>typeof v === 'string' && {from:v,to:v} || v)
      .map(v=>(v.opts = v.opts || {},v.fs = v.fs || {},v));//fs => formatSource
  const dvKeys = config.filter(v=>v.dv && !source[v.from]);
  // config = config.filter(v=>source[v.from]);
  config.filter(v=>source[v.from] && v.fs === 'toMinDay').forEach(v=>source[v.from]=moment(source[v.from]).format('YYYY-MM-DD 00:00:00Z'));
  config.filter(v=>source[v.from] && v.fs === 'toMaxDay').forEach(v=>source[v.from]=moment(source[v.from]).format('YYYY-MM-DD 23:59:59Z'));
  config.filter(v=>source[v.from] && v.fs === 'json').forEach(v=>source[v.from]=JSON.parse(source[v.from]));
  config.filter(v=>source[v.from] && v.fs === 'boolean').forEach(v=>source[v.from]=source[v.from]==='true');

  dvKeys.forEach(v=>source[v.from] = v.dv );
  config = config.filter(v=>source[v.from]);

  config.filter(v=>v.rule === 'startWith')
      .forEach(v=>v.rule=(from,to)=>( dest[to] = { '$regex': `^${source[from]}.*`, '$options': 'si' }) );
  config.filter(v=>v.rule === 'endWith')
      .forEach(v=>v.rule=(from,to)=>dest[to] = { '$regex': `.*${source[from]}$`, '$options': 'si' });
  config.filter(v=>v.rule === 'contain')
      .forEach(v=>v.rule=(from,to)=>dest[to] = { '$regex': `.*${source[from]}.*`, '$options': 'si' });
  config.filter(v=>v.rule === 'in').forEach(v=>v.rule=(from,to)=>
          ( dest[to] = dest[to] || {} , dest[to]['$in'] = source[from].split(v.opts.separator || ',') ));
  config.filter(v=>v.rule === 'nin').forEach(v=>v.rule=(from,to)=>
      ( dest[to] = dest[to] || {} , dest[to]['$nin'] = source[from].split(v.opts.separator || ',') ));
  config.filter(v=>v.rule === 'ne').forEach(v=>v.rule=(from,to)=>
      ( dest[to] = dest[to] || {} , dest[to]['$ne'] = source[from]));
  config.filter(v=>v.rule === 'eq').forEach(v=>v.rule=(from,to)=>
      ( dest[to] = dest[to] || {} , dest[to]['$eq'] = source[from]));
  config.filter(v=>v.rule === 'gte').forEach(v=>v.rule=(from,to)=>
      ( dest[to] = dest[to] || {} , dest[to]['$gte'] = source[from]));
  config.filter(v=>v.rule === 'lte').forEach(v=>v.rule=(from,to)=>
      ( dest[to] = dest[to] || {} , dest[to]['$lte'] = source[from]));
  config.filter(v=>v.rule === 'gt').forEach(v=>v.rule=(from,to)=>
      ( dest[to] = dest[to] || {} , dest[to]['$gt'] = source[from]));
  config.filter(v=>v.rule === 'lt').forEach(v=>v.rule=(from,to)=>
      ( dest[to] = dest[to] || {} , dest[to]['$lt'] = source[from]));

  config.filter(v=>!v.rule).forEach(v=>v.rule=(from,to)=>( dest[to] = source[from]));//默认值
  config.forEach(v=>v.to || (v.to = v.from));

  config.forEach(v=>v.rule.call(null,v.from,v.to));
  return dest;
}

//
// async function getAllMyLands(loanApplication,debug,method){
//   const userVerify = await userVerifyData.getOneByCondition({ uId:loanApplication.uId, IDCardStatus: 'approved', archived: false });
//   assert(userVerify,'E_GET_ALL_LAND_LIST_WITH_TYPE_001','该订单的用户未通过实名认证！')
//
//   const options = { landHeader: { key: "rongxin-land-area", value: loanApplication.area.substr(0, 2) } };
//   const { content: myLandsOrigin = [] } = process.env.NODE_ENV !=='local-dev' && (
//       await landData.getByUrl("/api/v1.0/contract/list", { cardNo: userVerify.IDCard }, options)
//   ) || { content: [] };
//   const myLands = ( await Promise.all( myLandsOrigin.map( async contract=>{
//     const families = await landData.getByUrl("/api/v1.0/contractor/families", { contractor: contract.code }, options);
//     const isCoOwner = families.some( _faimly=>_faimly.cardNo === userVerify.IDCard && _faimly.coOwner === '1' )
//     return isCoOwner ? contract : null;
//   } ) ) ).filter(v=>v);
//   debug(method,'contractList',myLands);
//   const allMyLands = (await Promise.all(myLands.map(async v=>{
//     const list = await landData.getByUrl("/api/v1.0/contract/land/list", { contract: v.grantNo }, options);
//     return list.map(vv=>( vv.contract=v,vv));
//   }))).reduce((res,v)=>res.concat(v),[]);
//   const allLandCodes = Array.from( new Set( allMyLands.map( v=>v.land ) ) );
//   debug(method,'contractLandList',allMyLands);
//   debug(method,'allLandCodes',allLandCodes);
//   return { allMyLands , myLands , userVerify ,  allLandCodes }
// }


async function getAllMyLands(loanApplication,debug,method){
  const userVerify = await userVerifyData.getOneByCondition({ uId:loanApplication.uId, IDCardStatus: 'approved', archived: false });
  assert(userVerify,'E_GET_ALL_LAND_LIST_WITH_TYPE_001','该订单的用户未通过实名认证！')

  const options = { landHeader: { key: "rongxin-land-area", value: loanApplication.area.substr(0, 2) } };
  const { content: myLandsOrigin = [] } = process.env.NODE_ENV !=='local-dev' && (
      await landData.getByUrl("/api/v1.0/contract/list", { cardNo: userVerify.IDCard }, options)
  ) || { content: [] };
  const myLands = ( await Promise.all( myLandsOrigin.map( async contract=>{
    const families = await landData.getByUrl("/api/v1.0/contractor/families", { contractor: contract.code }, options);
    const isCoOwner = families.some( _faimly=>_faimly.cardNo === userVerify.IDCard && _faimly.coOwner === '1' )
    !isCoOwner && debug( method,'filterByCoOwner',contract.code, families.filter(v=>v.coOwner === '1').map(v=>v.cardNo).join(',') );
    return isCoOwner ? contract : null;
  } ) ) ).filter(v=>v);
  debug(method,'contractList',myLands);
  const allMyLands = (await Promise.all(myLands.map(async v=>{
    const list = await landData.getByUrl("/api/v1.0/contract/land/list", { contract: v.grantNo }, options);
    return list.map(vv=>( vv.landCode = vv.land , vv.area = vv.contractArea , vv.contract=v, vv.request = { name:loanApplication.username , uniqueId:userVerify.IDCard }, vv));
  }))).reduce((res,v)=>res.concat(v),[]);
  const allLandCodes = Array.from( new Set( allMyLands.map( v=>( v.land ) ) ) );
  debug(method,'contractLandList',allMyLands);
  debug(method,'allLandCodes',allLandCodes);
  return { allMyLands , myLands , userVerify ,  allLandCodes }
}


async function isLandOccupy(landCodes,tId) {
  const result = [],paras = [...landCodes];
  while( paras.length > 0 ) result.push( ...( await isLandOccupyPart( tId,paras.splice( 0,100 ) ) ) );
  return Array.from( new Set( result ));
}

async function isLandOccupyPart(tId,landCodes){
  //查看流转地的地块占用
  const year = new Date().getMonth() > 9 ? new Date().getFullYear() : new Date().getFullYear() - 1;
  const current = moment().format('YYYY-MM-DD');
  const conditionLoanLand = {
    "lands.landCode":{$in:landCodes},limit:'unlimited',
    createdTime:{$gte:`${year}-11-01 00:00:00`},archived:false,signFinish:true,hadReleased:{$ne:true}
  }
  const loanLandCodeList = ( await loanLandData.getByCondition(conditionLoanLand) )
      .map(v=>v.lands.map(vv=>vv.landCode)).reduce((r,v)=>r.concat(v),[]) ;
  //查看转包地地块战胜
  const conditionSubcontract = {
    limit:'unlimited',archived:false,hadReleased:{$ne:true},//signFinish:false,
    landCode:{$in:landCodes}, circulationEndDate:{$gte:current} ,
  }
  const subcontractLandCodeList = ( await applicationSubcontractLandData.getByCondition(conditionSubcontract) )
      .map(v=>v.landCode);

  const conditionConfirm = {
    limit:'unlimited',archived:false,hadReleased:{$ne:true},//signFinish:false,
    landCode:{$in:landCodes}, circulationEndDate:{$gte:current} ,
  }
  const confirmLandCodeList = ( await loanApplicationConfirmLandData.getByCondition(conditionConfirm) )
      .map(v=>v.landCode);

  const conditionApp = {
    limit:'unlimited',archived:false,tId,
    'verifyInfo.occupyInfo.hadOccupy':'1',//{$ne:false},//signFinish:false,
    'verifyInfo.occupyInfo.hadReleased':'0',//{$ne:true} ,
    'verifyInfo.occupyInfo.circulationEndDate':{$gte:current} ,
    'verifyInfo.occupyInfo.lands.landCode':{$in:landCodes},
  };
  const appLandCodeList = tId && ( await loanApplicationData.getByCondition(conditionApp) )
      .map(v=>v.verifyInfo.occupyInfo.lands.map(vv=>vv.landCode)).reduce((r,v)=>r.concat(v),[])  || []; // 能查出来的，verifyInfo.occupyInfo.必然不会为空
  // console.log('debug233 a',JSON.stringify(conditionApp))
  // console.log('debug233 b',JSON.stringify(appLandCodeList))
  debug( `${HANDLER_NAME}LandOccupyCodes`,tId,JSON.stringify([ {loanLandCodeList , subcontractLandCodeList,confirmLandCodeList,appLandCodeList} ]))
  return [ ...loanLandCodeList , ...subcontractLandCodeList,...confirmLandCodeList,...appLandCodeList ] ;
}


async function parseEmployee(opts){
  // 根据 tId 和 employee id 和 角色 id 确认 employeeGroup对应的一条记录。此处假设两者组合是唯一的
  const { roleId:group , tId , uId:employee } = opts || {};
  debug(`parseEmployeePara`,tId,employee,group);
  if( !employee || !tId || !group )return {};
  const eg = await employeeGroupsData.getOneByCondition( { employee , tId , group , archived:false } );
  if( !eg )return {}
  const me = await employeesData.getById( employee , {cache : true , expire: 24 * 60 * 60 });
  const employeeGroupV2 = await groupV2Data.getById( eg.groupV2 );
  const employeeRole = group && await groupsData.getById( group ) || {};
  const {name:roleName} = employeeRole , {name:myName} = me , {_id:orgId,code:orgCode } =  employeeGroupV2;
  const {_id:employeeGroupId} = eg;
  debug(`parseEmployeeResult`,roleName , myName , orgId, orgCode , employeeGroupId);
  return { roleName , myName , orgId , orgCode , employeeGroupId , me , employeeGroupV2 , employeeRole , eg }
}


module.exports = {
  addHandlersForService,
  assert,
  getAllMyLands,
  generalExportExcel,
  formatParas,
  isLandOccupy,
  parseEmployee,
}
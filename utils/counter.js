/**
* counter utility
* <AUTHOR>
*/

'use strict'

const EventEmitter = require('events').EventEmitter

class Counter extends EventEmitter {
  constructor(start, target) {
    super()
    this._value = start || 0
    this._target = target || null
  }

  get value() {
    return this._value
  }

  set value(value) {
    let self = this
    if (self._target && value > self._target)
      self.emit('target', self)
    else
      self._value = value
  }
}

module.exports = Counter
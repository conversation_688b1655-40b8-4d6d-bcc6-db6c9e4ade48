/*
* @Author: wcy
*/

'use strict';

const path = require('path');

function _initLogOptions(options) {

  let pathStr = path.resolve(__dirname, '..');

  let pathArr = pathStr.split('/');

  options.service_qname = '[' + pathArr[pathArr.length - 1] + ']';

  if (!options.hasOwnProperty('timestamp')) {
    options.timestamp = '[' + new Date().toISOString() + ']';
  }

  options.pid = '[' + process.pid + ']';

  process.argv.forEach(
    function (arg) {
      let logKey = "--mode=";
      if (arg.indexOf(logKey) > -1) {
        let runner_id = arg.substring(logKey.length, arg.length).trim();
        options.runner_id = '[' + runner_id + ']';
      }
      
      let logLevelKey = "--log-level=";
      if (arg.indexOf(logLevelKey) > -1) {
        let logLevel = arg.substring(logLevelKey.length, arg.length).trim();
        options.logLevel = logLevel;
      }
    }
  );
}

let getLogOpts = function () {
  let options = {};
  _initLogOptions(options);
  return options;
}

exports = module.exports = getLogOpts;
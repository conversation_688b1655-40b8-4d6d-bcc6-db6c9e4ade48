/**
* the util intent to get user from request.user
* <AUTHOR>
*/ 

'use strict'

/**
 * get user from request.user
 * @param {*} req 
 * @return {Object}
 * {
 *  userid
 *  username
 *  scope
 *  token
 * }
 */
function getUserFromReqCtx(req) {
  if (!req || !req.user)
    return

  return req.user

  // for dev
  /*  return {
      userid: '-5',
      username: '超级管理员',
      scope: 'yt', //depercated since v2.1
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoi6LaF57qn566h55CG5ZGYIiwidGVuYW50IjoieXQiLCJpYXQiOjE1MTczNzUxNzIxNjYsImV4cCI6MTUxNzM3Nzc2NDE2NiwiYXVkIjoic3pub25nZnU6bWVyY2hhbnQ6and0OmFjY2VzcyIsImlzcyI6InN6bm9uZ2Z1Lm1lcmNoYW50LmFwcCIsInN1YiI6Ii01IiwianRpIjoiYjM1MmU0NjAtOGE4Zi00MDM3LTk4OGEtNGZiZWI1OWQ3YTMwIn0.29xroWio9IEXbObimBqxQ8VkmpN8UIX2f2uwv_qJyfQ'
    }*/
}

/**
 * get client from request.Client
 * @param {*} req
 * @return {Object}
 * {
 *  clientid
 *  scope
 *  token
 * }
 */
function getClientFromReqCtx(req) {
  if (!req || !req.Client)
    return

  return req.Client

  // for dev
  /*  return {
      clientid: '-5',
      scope: 'yt', //depercated since v2.1
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoi6LaF57qn566h55CG5ZGYIiwidGVuYW50IjoieXQiLCJpYXQiOjE1MTczNzUxNzIxNjYsImV4cCI6MTUxNzM3Nzc2NDE2NiwiYXVkIjoic3pub25nZnU6bWVyY2hhbnQ6and0OmFjY2VzcyIsImlzcyI6InN6bm9uZ2Z1Lm1lcmNoYW50LmFwcCIsInN1YiI6Ii01IiwianRpIjoiYjM1MmU0NjAtOGE4Zi00MDM3LTk4OGEtNGZiZWI1OWQ3YTMwIn0.29xroWio9IEXbObimBqxQ8VkmpN8UIX2f2uwv_qJyfQ'
    }*/
}

module.exports = {
  getUserFromReqCtx: getUserFromReqCtx,
  getClientFromReqCtx: getClientFromReqCtx
}
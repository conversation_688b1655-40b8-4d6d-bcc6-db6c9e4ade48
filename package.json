{"name": "rongxin.loan.mgr.app.api", "version": "1.0.0", "description": "", "main": "rongxin_loan_mgr_app_api.js", "scripts": {"clean": "echo 'do nothing right now'", "postclean": "rm -rf ./docs/apidoc/ && rm -rf ./docs/benchmark/", "install": "echo 'do nothing right now'", "postinstall": "[ -x \"$(command -v node-prune)\" ] && node-prune || echo \"skip node-prune\"", "codegen": "echo 'do nothing right now'", "build": "echo 'do nothing right now'", "postbuild": "apidoc -i ./routes/ -o ./docs/apidoc/", "start": "npm run start:local", "startwin": "cross-env DEBUG=* cross-env NODE_ENV=local-dev node rongxin_loan_mgr_app_api.js --log-level=debug --mode=local-dev", "unittest": "nyc mocha ./test/**/*", "clean:local": "npm run clean", "install:local": "npm install", "codegen:local": "npm run codegen", "build:local": "npm run build", "start:local": "ICTX_DUMP=true DEBUG=* NODE_ENV=local-dev node rongxin_loan_mgr_app_api.js --log-level=debug --mode=local-dev", "nodemon:local": "ICTX_DUMP=true DEBUG=* NODE_ENV=local-dev nodemon rongxin_loan_mgr_app_api.js --log-level=debug --mode=local-dev", "unittest:local": "npm run unittest", "clean:dev": "npm run clean", "install:dev": "npm install --prefer-offline --no-audit --progress=false --no-optional", "codegen:dev": "npm run codegen", "build:dev": "npm run build", "start:dev": "ICTX_DUMP=true DEBUG=* NODE_ENV=development node --abort-on-uncaught-exception rongxin_loan_mgr_app_api.js --log-level=debug --mode=development", "unittest:dev": "npm run unittest", "clean:test": "npm run clean", "install:test": "npm install --prefer-offline --no-audit --progress=false --no-optional", "codegen:test": "npm run codegen", "build:test": "npm run build", "start:test": "ICTX_DUMP=true DEBUG=* NODE_ENV=test node --abort-on-uncaught-exception rongxin_loan_mgr_app_api.js --log-level=debug --mode=test", "unittest:test": "npm run unittest", "clean:stage": "npm run clean", "install:stage": "npm install --prefer-offline --no-audit --progress=false --no-optional", "codegen:stage": "npm run codegen", "build:stage": "npm run build", "start:stage": "ICTX_DUMP=true DEBUG=rongxin* NODE_ENV=stage node --abort-on-uncaught-exception rongxin_loan_mgr_app_api.js --log-level=debug --mode=stage", "clean:cxwq-stage": "npm run clean", "install:cxwq-stage": "npm install --prefer-offline --no-audit --progress=false --no-optional", "codegen:cxwq-stage": "npm run codegen", "build:cxwq-stage": "npm run build", "start:cxwq-stage": "ICTX_DUMP=true DEBUG=rongxin* NODE_ENV=cxwq-stage node --abort-on-uncaught-exception rongxin_loan_mgr_app_api.js --log-level=debug --mode=cxwq-stage", "clean:demo": "npm run clean", "install:demo": "npm install --prefer-offline --no-audit --progress=false --no-optional", "codegen:demo": "npm run codegen", "build:demo": "npm run build", "start:demo": "ICTX_DUMP=true DEBUG=rongxin* NODE_ENV=demo node --abort-on-uncaught-exception rongxin_loan_mgr_app_api.js --log-level=info --mode=demo", "clean:benchmark": "npm run clean", "install:benchmark": "npm install --prefer-offline --no-audit --progress=false --no-optional", "codegen:benchmark": "npm run codegen", "build:benchmark": "npm run build", "start:benchmark": "ICTX_DUMP=true DEBUG=rongxin* NODE_ENV=benchmark node --abort-on-uncaught-exception rongxin_loan_mgr_app_api.js --log-level=debug --mode=benchmark", "clean:prod": "npm run clean", "install:prod": "npm install --prefer-offline --no-audit --progress=false --no-optional", "codegen:prod": "npm run codegen", "build:prod": "npm run build", "start:prod": "ICTX_DUMP=true DEBUG=rongxin* NODE_ENV=production node --abort-on-uncaught-exception rongxin_loan_mgr_app_api.js --log-level=debug --mode=production", "clean:test2": "npm run clean", "install:test2": "npm install --prefer-offline --no-audit --progress=false --no-optional", "codegen:test2": "npm run codegen", "build:test2": "npm run build", "startwin:test2": "cross-env ICTX_DUMP=true cross-env DEBUG=rongxin* cross-env NODE_ENV=development node --abort-on-uncaught-exception rongxin_loan_mgr_app_api.js --log-level=debug --mode=test2", "start:test2": "ICTX_DUMP=true DEBUG=rongxin* NODE_ENV=development node --abort-on-uncaught-exception rongxin_loan_mgr_app_api.js --log-level=debug --mode=test2"}, "author": "", "license": "ISC", "dependencies": {"ali-oss": "6.16.0", "body-parser": "1.18.2", "commander": "2.15.0", "compression": "1.7.2", "config": "1.30.0", "crypto-js": "^3.3.0", "dateformat": "^3.0.3", "debug": "2.6.8", "decimal.js": "^10.3.1", "express": "4.14.0", "joi": "^17.4.2", "jsonwebtoken": "7.4.1", "lodash": "^4.17.21", "moment": "2.21.0", "murmurhash": "0.0.2", "nongfu.merchant.bootstrap": "git+ssh://*************************/racoon/nongfu.merchant.bootstrap.git#stable", "nongfu.merchant.datasource": "git+ssh://*************************/racoon/nongfu.merchant.datasource.git#stable", "nongfu.merchant.msgfw": "git+ssh://*************************/racoon/nongfu.merchant.msgfw.git#stable", "nongfu.merchant.policyrouter": "git+ssh://*************************/racoon/nongfu.merchant.policyrouter.git#stable", "nongfu.merchant.svcfw": "git+ssh://*************************/racoon/nongfu.merchant.svcfw.git#stable", "orchestrator": "0.3.8", "parameter": "^3.6.0", "pg": "^8.7.1", "q": "1.5.1", "qrcode": "^1.4.4", "redis": "2.8.0", "sm-crypto": "^0.3.7", "superagent": "3.8.2", "uid-safe": "2.0.0", "xlsx-populate": "^1.21.0"}, "devDependencies": {"apidoc": "^0.17.7"}}
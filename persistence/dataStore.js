/**
 * Backend console wechat logon related dataStore
 * This part actually severs/belongs to nonghe.console, but wechat only allow us to input one endpoint
 * for ouath callback address, before we enable gateway, we need to host the logic in nonghe.mobile.web
 * to share the same oauth callback address
 * 
 * dataStore class is used to present the persistent layer for the cache data, permanence data in redis
 * The storage is based on redis key-value structure. The data structure details is below:
 *
 * [1]
 * |-------------------key------------------------|------Value------|
 *   <platform>:Tmp:wechat:QRCode:ticket:ticketId     scanResult
 * scanReuslt: {
 *                isScanned: <Boolean>,
 *                user: <userProfile>
 *             }
 *
 * A tempoaray storage for the QRCode scan result, the data record will be alive for 20 mins
 *
 * [2]
 *
 * |-------------------key------------------------|------Value------|
 *   <platform>:Tmp:transaction:id                       value 
 *
 * Temporary storage for tansacation interactions
 */

/**
 * Module dependences
 *
 */
const redis = require('redis');

const logFactory = require('../utils/logFactory')
const logUtil = require('../utils/logUtil')
const debug = logFactory(logUtil())('rongxin.loan.app.api:persistence/dataStore')
const Q = require('q');
const config = require('config')
const util = require('util')
const DSRegistry = require('nongfu.merchant.datasource').DSRegistry;

const USER_SESSION_KEY_PREFIX = 'rongxin:loan.app.api:session:user:';
const TWO_MINUTES_EXPIRATION = 2 * 60;

const CAPTCHA_KEY_PREFIX = 'rongxin:loan.app.api:session:captcha:';
const ONE_MINUTES_EXPIRATION = 1 * 60;

const RONGXIN_LOAN_PREFIX = 'rongxin:loan.app.api:'
const ONE_DAY_EXPIRATION = 60 * 60 * 24;


class DataStore {
  constructor() {

  }

  init() {
    this.client = DSRegistry.INSTANCE.getDS('RedisDataSource').getConnection();
  }

  setUserSession(session, options, callback) {
    if (!session) {
      return Q().nodeify(callback);
    }

    var qid = USER_SESSION_KEY_PREFIX + options.userId;

    if ('object' === typeof session) {
      session = JSON.stringify(session);
    }

    var deferred = Q.defer();
    this.client.set(qid, session, function (error, reply) {
      if (error) {
        return deferred.reject(error);
      }
      debug('Put the user access sessioner for qid - ' + qid + ' into temporary storage with returnCode: ', reply);
      deferred.resolve(reply);
    });

    this.client.expire(qid, TWO_MINUTES_EXPIRATION);
    return deferred.promise.nodeify(callback);

  };

  getUserSession(options, callback) {
    if (!options) {
      return Q().nodeify(callback);
    }

    var qid = USER_SESSION_KEY_PREFIX + options.userId;
    var deferred = Q.defer();
    this.client.get(qid, function (error, session) {
      if (error) {
        return deferred.reject(error);
      }
      if ('string' === typeof session) {
        try {
          session = JSON.parse(session);
        } catch (err) {
          // do nothing	
        }
      }
      debug('Get the session for qid' + qid + ' from temporary storage with result: ', session);
      deferred.resolve(session);
    });

    return deferred.promise.nodeify(callback);

  };

  removeUserSession(userId) {
    if (userId) {
      var qid = USER_SESSION_KEY_PREFIX + userId;
      this.client.del(qid);
    }
  }
  
  getClient() {
    return this.client;
  }

  setCaptchaSession(ticket, session, callback) {
    if (!session) {
      return Q().nodeify(callback);
    }

    var qid = CAPTCHA_KEY_PREFIX + ticket;

    if ('object' === typeof session) {
      session = JSON.stringify(session);
    }

    var deferred = Q.defer();
    this.client.set(qid, session, function (error, reply) {
      if (error) {
        return deferred.reject(error);
      }
      debug('Put the captcha session for qid - ' + qid + ' into temporary storage with returnCode: ', reply);
      deferred.resolve(reply);
    });

    this.client.expire(qid, ONE_MINUTES_EXPIRATION);
    return deferred.promise.nodeify(callback);
  };

  getCaptchaSession(ticket, callback) {
    if (!ticket) {
      return Q().nodeify(callback);
    }

    var qid = CAPTCHA_KEY_PREFIX + ticket;
    var deferred = Q.defer();
    var self = this;
    this.client.get(qid, function (error, session) {
      if (error) {
        return deferred.reject(error);
      }
      if ('string' === typeof session) {
        try {
          session = JSON.parse(session);
        } catch (err) {
          // do nothing	
        }
      }
      self.client.del(qid);
      debug('Get the captcha session for qid' + qid + ' from temporary storage with result: ', session);
      deferred.resolve(session);
    });

    return deferred.promise.nodeify(callback);

  };

  async set(key, value, opts) {
    let self = this;
    let method = 'dataStore.set';
    let defer = Q.defer();

    if (util.isObject(value))
      throw {
        errorCode: 'ECACHER185',
        reason: 'illegal input param [value]'
      };

    let expire = (opts && opts.expire) ? opts.expire : TWO_MINUTES_EXPIRATION;

    self.client.set(`${RONGXIN_LOAN_PREFIX}${key}`, value, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        defer.reject(error);
      }

      debug(method, '[Exit](success)', reply);
      defer.resolve(reply);
    });
    self.client.expire(`${RONGXIN_LOAN_PREFIX}${key}`, expire);

    return defer.promise;
  }

  async get(key) {
    let self = this;
    let method = 'dataStore.get';
    let defer = Q.defer();

    self.client.get(`${RONGXIN_LOAN_PREFIX}${key}`, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        defer.reject(error);
      }

      debug(method, '[Exit](success)', reply);
      defer.resolve(reply);
    });

    return defer.promise;
  }

  async del(key) {
    let self = this;
    let method = 'dataStore.del';
    let defer = Q.defer();

    self.client.del(`${RONGXIN_LOAN_PREFIX}${key}`, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        defer.reject(error);
      }

      debug(method, '[Exit](success)', reply);
      defer.resolve(reply);
    });

    return defer.promise;
  }

  async hmset(key, value, opts) {
    let self = this;
    let method = 'dataStore.hmset';
    let defer = Q.defer();

    let expire = (opts && opts.expire) ? opts.expire : TWO_MINUTES_EXPIRATION;

    self.client.hmset(`${RONGXIN_LOAN_PREFIX}${key}`, value, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        defer.reject(error);
      }

      debug(method, '[Exit](success)', reply);
      defer.resolve(reply);
    });
    self.client.expire(`${RONGXIN_LOAN_PREFIX}${key}`, expire);

    return defer.promise;
  }

  async hgetall(key) {
    let self = this;
    let method = 'dataStore.hgetall';
    let defer = Q.defer();

    self.client.hgetall(`${RONGXIN_LOAN_PREFIX}${key}`, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        defer.reject(error);
      }

      debug(method, '[Exit](success)', reply);
      defer.resolve(reply);
    });

    return defer.promise;
  }

  async pureHset(key, value, opts) {
    let self = this;
    let method = 'dataStore.pureSet';
    let defer = Q.defer();

    let expire = (opts && opts.expire) ? opts.expire : TWO_MINUTES_EXPIRATION;

    self.client.hmset(`${key}`, value, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        defer.reject(error);
      }

      debug(method, '[Exit](success)', reply);
      defer.resolve(reply);
    });
    self.client.expire(`${key}`, expire);

    return defer.promise;
  }
  async pureGet(key) {
    let self = this;
    let method = 'dataStore.pureGet';
    let defer = Q.defer();

    self.client.get(key, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        defer.reject(error);
      }

      debug(method, '[Exit](success)', reply);
      defer.resolve(reply);
    });

    return defer.promise;
  }
  async pureSet(key, value, opts) {
    let self = this;
    let method = 'dataStore.pureSet';
    let defer = Q.defer();

    let expire = (opts && opts.expire) ? opts.expire : TWO_MINUTES_EXPIRATION;

    self.client.set(key, value, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        defer.reject(error);
      }

      debug(method, '[Exit](success)', reply);
      defer.resolve(reply);
    });
    self.client.expire(key, expire);

    return defer.promise;
  }
  async pureDel(key) {
    let self = this;
    let method = 'dataStore.pureDel';
    let defer = Q.defer();

    self.client.del(key, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        defer.reject(error);
      }

      debug(method, '[Exit](success)', reply);
      defer.resolve(reply);
    });

    return defer.promise;
  }
  async pureHgetall(key) {
    let self = this;
    let method = 'dataStore.pureGet';
    let defer = Q.defer();

    self.client.hgetall(`${key}`, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        defer.reject(error);
      }

      debug(method, '[Exit](success)', reply);
      defer.resolve(reply);
    });

    return defer.promise;
  }

  async incr(key, opts) {
    let self = this;
    let method = 'dataStore.incr';
    let defer = Q.defer();
    let existsDefer = Q.defer();

    let expire = (opts && opts.expire) ? opts.expire : ONE_DAY_EXPIRATION;
    let exists = false;
    self.client.exists(`${RONGXIN_LOAN_PREFIX}${key}`, (error, reply) => {
      if (error) {
        debug.error(method, '[Exit](error)', error);
        existsDefer.reject(error);
      }
      exists = reply;
      debug(method, '[Exit](success)', reply);
      existsDefer.resolve(reply);
    });
    existsDefer.promise
      .then(self.client.incr(`${RONGXIN_LOAN_PREFIX}${key}`, (error, reply) => {
        if (error) {
          debug.error(method, '[Exit](error)', error);
          defer.reject(error);
        }

        if (!exists) self.client.expire(`${RONGXIN_LOAN_PREFIX}${key}`, expire);
        debug(method, '[Exit](success)', reply);
        defer.resolve(reply);
      }))

    return defer.promise;
  }
}

let dataStore = new DataStore();
dataStore.init();
module.exports = exports = dataStore;
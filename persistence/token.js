/**
 * token cache
 * <AUTHOR>
 */

'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:persistence:token');
const redisData = require('./dataStore');

const ACCESS_TOKEN_KEY = 'rongxin:loan:passport:access_token:';

class SMS {
  constructor() {

  }

  /**
   * get token
   * redis key example: rongxin:loan:mgr:app:apiaccess_token:sub:token
   * @returns null || { sub: 'xxxxxxx', scope: 'xxxxxx' }
   */
  async getAccessToken(sub, token) {
    let method = 'getAccessToken';
    let result = null;

    try {
      debug.verbose(method, '[Enter]');

      result = await redisData.pureHgetall(ACCESS_TOKEN_KEY + `${sub}:${token}`);
      debug.verbose(method, '[Exit](success)', result);
      return result;
    } catch (error) {
      debug.warning(method, '[Exit](failed)', error);
      return result;
    }
  }
}

module.exports = new SMS();

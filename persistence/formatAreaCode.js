/**
 * @summary get format area code
 * <AUTHOR>
 *
 * Created at     : 2018-08-15 10:50:26 
 * Last modified  : 2018-11-27 10:49:08
 */

'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:persistence:formatAreaCode');
const redisData = require('./dataStore');
const areasData = require('../services/dataSvc/dataUtil').dictAreas;

const EXPIRE_TIME = 2 * 60 * 60 * 24 * 365;
const PREFIX = 'dict:areaCode:';

class AreaCode {
  constructor() {

  }

  async getFormatAreaCode(areaCode) {
    let method = 'getFormatAreaCode';
    let result = null;

    try {
      if (!areaCode) {
        debug(method, '[Exit](continue)');
        return {
          area: '',
          region: {}
        }
      }
      debug.verbose(method, '[Enter]');
      let areaCodeKey = PREFIX + areaCode;

      result = await redisData.pureHgetall(areaCodeKey);
      if (result) {
        result = JSON.parse(result.data);
        debug.verbose(method, '[Exit](success)', result);
				return result;
      }
      let url = "/v1.0/area/code/format";
      result = await areasData.getByUrl(url,{areaCode});
      redisData.pureHset(areaCodeKey, {data: JSON.stringify(result)}, { expire: EXPIRE_TIME });
      debug.verbose(method, '[Exit](success)', result);
      return result;
    } catch (error) {
      debug.warning(method, '[Exit](failed)', error);
      return result;
    }
  }

}

module.exports = new AreaCode();

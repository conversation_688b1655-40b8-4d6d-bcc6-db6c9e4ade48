/**
 * @summary limit request
 * <AUTHOR>
 */

'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:persistence:commissionLimit');
const redisData = require('./dataStore');
const MERCHANT_PREFIX_LIMIT = 'rongxin:loan:mgr:app:api:commission:limit:'
const EXPIRE_TIME = 120; //120s

class RequestLimit {
  constructor() {

  }

  async isLock(key) {
    let method = 'isLock';
    try {
      debug.verbose(method, '[Enter]');
      let keys = `${MERCHANT_PREFIX_LIMIT}${key}`;
      let result = await redisData.pureGet(keys);
      if (result) {
        debug.verbose(method, '[Exit](success)', result);
        return true;
      }
      debug.verbose(method, '[Exit](failed)', result);
      return false;
    } catch (error) {
      debug.warning(method, '[Exit](failed)', error);
      return false;
    }
  }

  async lock(key) {
    let method = 'lock';
    let result = null;

    try {
      debug.verbose(method, '[Enter]');
      let keys = `${MERCHANT_PREFIX_LIMIT}${key}`;
      result = await redisData.pureSet(keys, 1, {
        expire: EXPIRE_TIME
      });
      debug.verbose(method, '[Exit](success)', result);
      return result;
    } catch (error) {
      debug.warning(method, '[Exit](failed)', error);
      return result;
    }
  }

  async unlock(key) {
    let method = 'unlock';
    let result = null;

    try {
      debug.verbose(method, '[Enter]');
      let keys = `${MERCHANT_PREFIX_LIMIT}${key}`;
      result = await redisData.pureDel(keys);
      debug.verbose(method, '[Exit](success)', result);
      return result;
    } catch (error) {
      debug.warning(method, '[Exit](failed)', error);
      return result;
    }
  }

}

module.exports = new RequestLimit();
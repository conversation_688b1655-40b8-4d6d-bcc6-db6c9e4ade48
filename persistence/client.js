/*
 * @Author: qays
 * @Date: 2021-08-23 17:47:11
 * @LastEditTime: 2023-07-18 14:53:21
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.mgr.app.api\persistence\client.js
 */
/**
 * client cache
 * <AUTHOR>
 */

'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:dashboard.api:persistence:client');
const redisData = require('./dataStore');
const clientData = require('../services/dataSvc/dataUtil').clients;

const KEY_PREFIX = 'rongxin:loan:client:';

class Client {
  constructor() {}

  /**
   * get token
   * redis key example: rongxin:loan:client:xxxxx
   * @returns null || { sub: 'xxxxxxx', scope: 'xxxxxx' }
   */
  async getClient(cId) {
    let method = 'getClient';
    let result = null;

    try {
      debug.verbose(method, '[Enter]');

      result = await redisData.pureHgetall(KEY_PREFIX + `${cId}`);
      if (!result) {
        result = await clientData.getOneByCondition({
          _id: cId
        });
        await redisData.pureHset(KEY_PREFIX + `${cId}`, result, {expire: 2 * 60 * 60});
      }

      debug.verbose(method, '[Exit](success)', result);
      return result;
    } catch (error) {
      debug.warning(method, '[Exit](failed)', error);
      return result;
    }
  }
}

module.exports = new Client();
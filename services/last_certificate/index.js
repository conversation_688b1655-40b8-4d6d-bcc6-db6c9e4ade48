'use strict'

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.admin.api:services:certificate:index');
const GetLastCertificateHandler = require('./getLastCertificateHandler');

const {addHandlersForService} = require('../../utils/general');

class Server {
  constructor() {
    addHandlersForService.call(this,debug);
  }


  getLastCertificate(){
    return [GetLastCertificateHandler]
  }
}
module.exports = new Server();
'use strict';

const HANDLER_NAME = 'getCertificateListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.admin.api:services:tenant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { certificate:certificateData } = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general');
const moment = require('moment');
const formatAreaCode = require('../../persistence/formatAreaCode');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input:{_id,tId},opts:{ getOne,parentName,parentFromCodes } } = this.context;
      const condition = { archived:false , status:1 , $sort:{ version:-1 } };
      const last = await certificateData.getOneByCondition( condition );
      await Promise.all( [last].filter(v=>v).map( async it=>{
        it.createdTime = moment( it.createdTime ).format("YYYY-MM-DD HH:mm:ss");
        it.lastModTime = moment( it.lastModTime ).format("YYYY-MM-DD HH:mm:ss");
        it.effectiveTime = moment( it.effectiveTime ).format("YYYY-MM-DD HH:mm:ss");
      } ) );
      this.context.result = {last};
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
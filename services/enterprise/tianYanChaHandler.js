/*
 * @Author: q 
 * @Date: 2020-4-28 18:08:34
 * @Last Modified by: q
 */


'use strict';

const HANDLER_NAME = 'tianYanChaHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:enterprise:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const config = require('config');
const agent = require('superagent');
const moment = require('moment');
const redisData = require('../../persistence/dataStore');
const MOBILE_URL = "/api/v1.0/internal/exec";

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opt = self.context.opts;
      
      let TycNum = await redisData.incr(`TYC_${opt.userid}_${moment().format('YYYYMMDDHH')}`);
      if(TycNum<=opt.tycNumMax){

        let apiManagementConfig = config.get("apiManagement");
        let requestUrl = `${apiManagementConfig.host}${MOBILE_URL}`
        let payload = {
          apiId: apiManagementConfig.apiId,
          caller: apiManagementConfig.caller,
          payload: condition
        }
        debug(method, '[Exit](payload)', payload)
        let execData = await agent.post(requestUrl).send(payload);
        let result = execData.body;
        debug(method, "[TYC]result:",result && result.data && result.data.data && !result.data.data.result && result.data.data.error_code=='300000')
        if(result && result.data && result.data.data && !result.data.data.result && result.data.data.error_code=='300000'){
          result.data.data.reason = '搜索无结果';
        }
        debug(method, '[Exit](success)', result)
        self.context.result = result;
      }else{
        throw {
          errorCode: 'E_TYC_57',
          httpCode: 406,
          reason: 'frequency overflow'
        };
      }
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
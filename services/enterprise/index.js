/*
 * @Author: q 
 * @Date: 2020-4-28 18:06:30
 * @Last Modified by: q
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:enterprise:services:enterprise:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const TianYanChaHandler = require('./tianYanChaHandler');
class Service {
  constructor() {

  }

  async tianYanCha(input, _opts) {
    let method = 'tianYanCha';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new TianYanChaHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();
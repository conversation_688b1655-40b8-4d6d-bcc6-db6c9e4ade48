/*
* @Author: wcy
* @Date:   2017-08-10 17:58:14
* @Last Modified by:   wcy
* @Last Modified time: 2017-08-10 17:59:41
*/

'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug      = logFactory(logUtil())('rongxin:loan:mgr:app:api:services:baseWorkFlow');

class BaseWorkflow {
  constructor(allStates, workflowPipeline) {
    this.allStates = allStates;
    this.workflowPipeline = workflowPipeline;
  }

  /**
   * validate whether input state is a valid state
   * @param {*} state 
   * @param {*} allStates 
   * @return {Boolean}
   */
  validateState(state, allStates) {
    let method = 'validateState';
    if (!state) {
      debug(method, '[Exit](error)', 'invalid state');
      return false;
    }

    allStates = allStates || this.allStates;

    let result = false;
    allStates.some((validState) => {
      if (state.toLowerCase() === validState.toLowerCase()) {
        result = true;
        return true;
      }
    });

    debug(method, '[Exit](success)', result);
    return result;
  }

  /**
   * get workflow node definition
   * @param {*} from 
   * @param {*} to 
   * @param {*} workflowPipeline 
   * @return { Object || null }
   */
  getWorkflowConnection(from, to, workflowPipeline) {
    let method = 'getWorkflowConnection';
    if (!from || !to) {
      debug(method, '[Exit](error)', 'invalid state');
      return null;
    }

    workflowPipeline = workflowPipeline || this.workflowPipeline;

    let conn = null;

    workflowPipeline.some((workflowNode) => {
      if (from.toLowerCase() === workflowNode.from.toLowerCase() && to.toLowerCase() === workflowNode.to.toLowerCase()) {
        conn = workflowNode;
        return true;
      }
    });

    debug(method, '[Exit](success)', conn);
    return conn;
  }

  /**
   * validate whether the caller has permission to do the action
   * @param {*} caller 
   * @param {*} orderOwner 
   * @param {*} callerRole 
   * @param {*} workflowNode 
   */
  validatePermission(caller, orderOwner, callerRole, workflowNode) {
    let method = 'validatePermission';

    if (!caller) {
      debug(method, '[Exit](error)', 'invalid params');
      return false;
    }

    let result = false;

    workflowNode.permittedRole.some((role) => {
      if (role === 'Operator') {
        if (callerRole.isOperator) {
          result = true;
          return true;
        }
      }

      if (role === 'OrderOwner') {
        if (caller === orderOwner) {
          result = true;
          return true;
        }
      }
    });

    debug(method, '[Exit](success)', result);
    return result;
  }
}

module.exports = BaseWorkflow;
'use strict';

const HANDLER_NAME = 'GetLandSupervisoryRecordDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const {
  landSupervisoryRecord:landSupervisoryRecordData,
  employees:employeesData,
  loanApplication:loanApplicationData,
  userVerify:userVerifyData,
} = require('../dataSvc/dataUtil');

const config = require('config');
const IMAGE_BASE_URL = config.get('repoServer.host');
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const GetAllLandListWithTypeHandler = require('../loanApplicationLandType/getAllLandListWithTypeHandler');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {id} = this.context.input;
      const data = await landSupervisoryRecordData.getById(id);
      data.readAllGuidelines = true;
      await landSupervisoryRecordData.putById(id,data);

      //操作人
      const user = data.operator && await employeesData.getOneByCondition({_id: data.operator,"archived": false });
      data.operator = user && user.username || '';
      data.areaInfo = await formatAreaCode.getFormatAreaCode(data.areaCode)
      //照片
      await Promise.all((data.photos||[]).map(formatImg).reduce((res,v)=>res.concat(v),[]));


    data.lastModTime = moment(data.lastModTime).format('YYYY-MM-DD HH:mm:ss');
    data.createdTime = moment(data.createdTime).format('YYYY-MM-DD HH:mm:ss');
    //guidelines 时间
    ( data.guidelines || [] ).forEach(guideline =>
        guideline.createdTime = moment(guideline.createdTime).format('YYYY-MM-DD HH:mm:ss') );


      this.context.result = data;
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

function formatImg(item) {
  const promise = [];
  if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({ fileName: item.thumbnail.url }).then(data => {
      item.thumbnail.url = data
    }));
  }
  if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({ fileName: item.image.url }).then(data => {
      item.image.url = data
    }));
  }
  return promise;
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'getLandSupervisoryRecordListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  landSupervisoryRecord:landSupervisoryRecordData,
  employees:employeesData,
  employeeGroups:employeeGroupData,
  loanApplication:loanApplicationData,
  userVerifys:userVerifyData,
  LoanSvrLand:LoanSvrLandData,
  fundInvestor: fundInvestorData,
} = require('../dataSvc/dataUtil');
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const GetAllLandListWithTypeHandler = require('../loanApplicationLandType/getAllLandListWithTypeHandler');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const {getEmployeeLimit} = require('../../utils/getUserFromReq')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {input:condition,opts:{getLandData,tId,uId,roleId}} = this.context;
      const areaCodeLimitOr = uId && await getEmployeeLimit(employeeGroupData,tId,uId,'orderAreaCode',roleId);
      uId && ( condition['$or'] = areaCodeLimitOr );
      await this.bankLimit( uId , roleId , tId , condition )
      debug(method,'condition:::',1,JSON.stringify(condition));
      const result = await landSupervisoryRecordData.getListAndCountByCondition(condition);
      debug(method,2,result.result.length);
      //操作人
      await Promise.all(result.result.filter(v=>v.operator).map(async item => {
        const user = await employeesData.getOneByCondition({_id: item.operator,"archived": false });
        item.operator = user && user.username || '';
      }));
      //地理信息
      await Promise.all(result.result.map(async it => it.areaInfo = await formatAreaCode.getFormatAreaCode(it.areaCode)));
      //照片
      await Promise.all(result.result.map(async item => await Promise.all((item.photos||[]).map(formatImg).reduce((r, v) => r.concat(v), []))));

      result.result.forEach(item => {
        item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss');
        item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
        //guidelines 时间
        ( item.guidelines || [] ).forEach(guideline =>
            guideline.createdTime = moment(guideline.createdTime).format('YYYY-MM-DD HH:mm:ss') );
      });
      getLandData && await this.addLandData(result);
      this.context.result = result;
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async bankLimit( employee , group , tId , condition  ){
    // 待提纯
    tId = '600fe47561f0e675643c5fa1';
    const query = { tId , status:{ $in:[ 'loaned' , 'finished_loan' ] } , limit:'unlimited' };
    if( !employee || group !== '5eb8fefdc6ecfe44d4ecaed4' )return query;//只对银行客户经理进行处理
    const eg = await employeeGroupData.getOneByCondition({ employee , tId , group , archived: false });
    // debug('debug233 employee ',employee,group,eg && eg.fundId,tId)
    if( !eg || !eg.fundId )return query;
    query['$or'] = [].concat( eg.areaList || [] )//兼容 areaList 不是数组的情况
        .map(code=>({"area":{ '$regex': `^${code}`, '$options': 'si' }})) ;
    const funds = ( await fundInvestorData.getByCondition( { limit:'unlimited', parentIdList:eg.fundId  } ) ).map( v=>v._id );
    funds.push( eg.fundId );
    query["verifyInfo.loanAndLawInfos.fund.id"] = {$in:funds };
    // debug('debug233 query ',JSON.stringify(query))
    const appList = await loanApplicationData.getByCondition( query ) , idList = appList.map(v=>v._id) ;
    condition && ( condition.aId = { $in:idList } )
    return idList;
  }

  async addLandData(result){
    const {result:list} = result;//.map(v=>Object({},v));//copy for json
    if(list.length === 0)return result.lands = [];
    const lands = ( await Promise.all( Array.from(new Set(list.map(v=>v.aId).filter(v=>v))).map(async id=>{
      const application = await loanApplicationData.getById(id);
      const {uId} = list.find(v=>v.aId===id);
      const userVerify = await userVerifyData.getOneByCondition({ uId, IDCardStatus: 'approved', archived: false });
      if( !application || !userVerify )return;
      const context = { input:{aId:application._id},opts:{},result:{},error: {} };
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetAllLandListWithTypeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      const {lands} = context.result;
      Object.values(lands).reduce((res,v)=>
          res.concat(v),[]).forEach(v=>(v.aId = application._id,v.orderAreaCode=application.area));
      // Object.values(lands).reduce((res,v)=>
      //     res.concat(v),[]).forEach(v=>(v.username = application.username,v.userMobile));
      return lands;
    })) ).filter(v=>v).reduce( (res,it)=>
        ( Object.entries(it).forEach(([k,arr])=>res[k] = (res[k] || []).concat(arr)) , res ),{} );//最后一步，合并lands里的三个数组
    const landList = Object.values(lands).reduce((res,v)=>res.concat(v),[]);
    landList.forEach(it=>
        it.supervisories=list.filter(v=>v.landCode===it.landCode).map(v=>({...v})));//copy for json.stringify
    landList.forEach(v=>v.postion = v.postion || []);
    result.lands = landList.filter(v=>v.landCode);
  }

  undoAsync(done) {
    done()
  }
}


function formatImg(item) {
  const promise = [];
  if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({ fileName: item.thumbnail.url }).then(data => {
      item.thumbnail.url = data
    }));
  }
  if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({ fileName: item.image.url }).then(data => {
      item.image.url = data
    }));
  }
  return promise;
}

module.exports = Handler
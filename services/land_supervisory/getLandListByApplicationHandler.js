'use strict';

const HANDLER_NAME = 'getLandListByApplicationHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  landSupervisoryRecord:landSupervisoryRecordData,
  employees:employeesData,
  employeeGroups:employeeGroupData,
  loanApplication:loanApplicationData,
  userVerifys:userVerifyData,
  LoanSvrLand:LoanSvrLandData,
} = require('../dataSvc/dataUtil');
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const GetAllLandListWithTypeHandler = require('../loanApplicationLandType/getAllLandListWithTypeHandler');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const {getEmployeeLimit} = require('../../utils/getUserFromReq')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {input:condition,opts:{getLandData,tId,uId,roleId}} = this.context;
      const areaCodeLimitOr = uId && await getEmployeeLimit(employeeGroupData,tId,uId,'area',roleId);
      uId && ( condition['$or'] = areaCodeLimitOr );
      debug(method,1,JSON.stringify(condition));
      const list = await loanApplicationData.getByCondition(condition);
      const result = {};
      result.applications = list;

      result.lands = ( condition.username || condition.area ) && await this.addLandData(list);
      this.context.result = result;
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async addLandData(list){
    if(list.length === 0)return [];
    const lands = ( await Promise.all( list.map(async application=>{
      const {uId} = application;
      const userVerify = await userVerifyData.getOneByCondition({ uId, IDCardStatus: 'approved', archived: false });
      if( !application || !userVerify )return;
      const context = { input:{aId:application._id},opts:{},result:{},error: {} };
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetAllLandListWithTypeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      const {lands} = context.result;
      Object.values(lands).reduce((res,v)=>
          res.concat(v),[]).forEach(v=>(v.aId = application._id,v.orderAreaCode=application.area));
      // Object.values(lands).reduce((res,v)=>
      //     res.concat(v),[]).forEach(v=>(v.username = application.username,v.userMobile=application.userMobile));
      return lands;
    })) ).filter(v=>v).reduce( (res,it)=>
        ( Object.entries(it).forEach(([k,arr])=>res[k] = (res[k] || []).concat(arr)) , res ),{} );//最后一步，合并lands里的三个数组
    // Object.values(lands).forEach(arr=>arr.forEach(it=>
    //     it.supervisories=list.filter(v=>v.landCode===it.landCode).map(v=>({...v}))));//copy for json.stringify
    const landList = Object.values(lands).reduce((res,v)=>res.concat(v),[]);
    await Promise.all( landList.map(async it=>{
      it.supervisories = await landSupervisoryRecordData.getByCondition({landCode:it.landCode}) ;
      await Promise.all(it.supervisories.map(async item => await Promise.all((item.photos||[]).map(formatImg).reduce((r, v) => r.concat(v), []))));
    }));
    landList.forEach(v=>v.postion = v.postion || []);
    return landList.filter(v=>v.landCode);;
  }

  undoAsync(done) {
    done()
  }
}


function formatImg(item) {
  const promise = [];
  if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({ fileName: item.thumbnail.url }).then(data => {
      item.thumbnail.url = data
    }));
  }
  if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({ fileName: item.image.url }).then(data => {
      item.image.url = data
    }));
  }
  return promise;
}

module.exports = Handler
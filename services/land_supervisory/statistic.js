/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'statisticHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const userVerifysData = require('../dataSvc/dataUtil').userVerifys;
const employeeData = require('../dataSvc/dataUtil').employees;
const url = '/v1.0/loan/applications/fund/receive/amount/group/by/user';

const {
  landSupervisoryRecord: landSupervisoryRecordData,
  loanApplication: loanApplicationData,
  fundReceive: fundReceiveData ,
  employeeGroups: employeeGroupData,
  fundInvestor: fundInvestorData ,
  LoanSvrLand: LoanSvrLandData,
} = require('../dataSvc/dataUtil');
const moment = require('moment');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input , opts:{roleId} } = this.context;
      input.appTId = '600fe47561f0e675643c5fa1';
      const result = await fundReceiveData.getByUrl(url,{archived: false,onlyClosed:0,groupBy:'requestUniqueId'});

      // await this.bankLimit( uId , roleId , input.appTId , input )
      const statistic = await landSupervisoryRecordData.getByUrl('/v1.0/managedLandSupervisoryRecord/statistic', input);

      ( roleId !== '5eb8fefdc6ecfe44d4ecaed4' || true )  // 不确定银行经理业务是否要平移过来
        && ( statistic.applicationLoanedStatistic.applicationCount = result && result.total || 0 ) ;//|| statistic.applicationCount

      debug(`${HANDLER_NAME}Statistic`,statistic);
      Object.assign(statistic,statistic.applicationLoanedStatistic);
      debug(method, '[Exit](success)',statistic);
      this.context.result = statistic;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async bankLimit( employee , group , tId , condition ){
    // 待提纯
    condition = condition || {};
    if( !employee || group !== '5eb8fefdc6ecfe44d4ecaed4' )return condition;//只对银行客户经理进行处理
    const eg = await employeeGroupData.getOneByCondition({ employee , tId , group , archived: false });
    if( !eg || !eg.fundId )return condition;
    condition.areaCodeLimit = [].concat( eg.areaList || [] ).join(',');//兼容 areaList 不是数组的情况
    const funds = ( await fundInvestorData.getByCondition( { limit:'unlimited', parentIdList:eg.fundId  } ) ).map( v=>v._id );
    funds.push( eg.fundId );
    condition.fundLimit = funds.join(',');
  }

  undoAsync(done) {
    done();
  }
}


function formatImg(item) {
  const promise = [];
  if (item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({ fileName: item.thumbnail.url }).then(data => {
      item.thumbnail.url = data
    }));
  }
  if (item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({ fileName: item.image.url }).then(data => {
      item.image.url = data
    }));
  }
  return promise;
}

async function getUserName(uId) {
  let user = await employeeData.getOneByCondition({
    _id: uId,
    "archived": false
  });
  if (!user) {
    throw {
      errorCode: 'PARAMS_ERROR',
      httpCode: 406,
      reason: 'user not find'
    };
  }
  return user.username;
}


module.exports = Handler;
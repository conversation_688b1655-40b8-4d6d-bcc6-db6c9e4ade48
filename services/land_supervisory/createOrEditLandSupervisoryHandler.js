'use strict';

const HANDLER_NAME = 'CreateOrEditLandSupervisoryHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  landSupervisoryRecord:landSupervisoryRecordData,
  loanApplication:loanApplicationData,
  infoCollectHistory:infoData,
  employees:employeesData,
} = require('../dataSvc/dataUtil');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let {input:one,opts:{uId}} = this.context;
      one.guidelines = one.guidelines || [];
      typeof one.readAllGuidelines === 'undefined' && ( one.readAllGuidelines = true);
      one.lastModTime = new Date();
      one.operator = uId;
      const { username:operatorName } = uId && await employeesData.getById(uId, {cache : true , expire: 24 * 60 * 60 }) || {}
      const app = one.aId && await loanApplicationData.getById( one.aId );
      const infoId = app && app.addons && app.addons.info && app.addons.info.mId;
      const info = infoId && await infoData.getById( infoId );
      const requestInfo = {
        username: app && app.username,
        userMobile: app && app.userMobile,
        requestName: info && info.name,
        requestType: info && info.type,
        operatorName,
      };
      Object.assign( one,requestInfo,{hadClosed:false} );
      one._id && await landSupervisoryRecordData.putById(one._id,one);
      one._id || ( one = await landSupervisoryRecordData.post(one) );
      this.context.result = {success:'ok',_id:one._id,};
      debug(method, '[Exit](success)', this.context.result,one);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler

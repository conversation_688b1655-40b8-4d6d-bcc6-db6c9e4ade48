'use strict';

const HANDLER_NAME = 'sendSMSHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const internalMsgDispatcher = require('../messages/dispatchers/internalMsgDispatcher');
const MsgSvcRegistry = require('nongfu.merchant.msgfw').MsgSvcRegistry.INSTANCE;
const Froms = require('../messages/messageConfig').MessageFroms;
const templateRender = require('../messages/templateRender');
const applicationData = require('../dataSvc/dataUtil').loanApplication;
let templateId = "SMS_232911558"
const smsALiYunDispatcher = require('../messages/dispatchers/smsALiYunDispatcher')
const EXCEPTION = new Map([
  ["1", "害虫病"],
  ["2", "跑冒滴漏"],
  ["3", "杂草"],
  ["4", "受灾"],
  ["5", "其他"],
])


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      // 判断是否需要发送站内提醒
      if (input.status != 2) {
        debug(method, '[Exit](success)');
        return done();
      }
      let application = await applicationData.getById(input.aId);

      let applicationFrom = Froms.Rongxin_Platform_SystemSpace_LoanApplicationModule;
      let InternalMsgDispatcher = MsgSvcRegistry.getDisptcher(internalMsgDispatcher.QNAME);
      let temp = '61dfe7083584a08a624d4af1';
      let content = templateRender.redenerIternalTemplate(temp, {
        text: EXCEPTION.get(input.exception) || "",
        url: application._id
      });
      let result = await InternalMsgDispatcher.sendMsg(applicationFrom, application.uId, content, { tag: 'rongxin:loan:application' });
      debug(method, 'Sms msg sent', result);

      if (!result.msgQueue || !result.msgQueue) {
        throw {
          httpCode: 500,
          errorCode: 'INTERNALGSMS_FAL055',
          reason: 'failed to send internal msg'
        };
      }

      //发送短信
      let SmsALiYunDispatcher = MsgSvcRegistry.getDisptcher(smsALiYunDispatcher.QNAME);
      content = {
        templateid: templateId,
        param: "",
        signName: "黑龙江省创新农业物权",
        caller: "rongxin_userapp"
      };
      let smsResult = await SmsALiYunDispatcher.send_Sms(application.userMobile, content, {});
      debug(method, 'Sms msg sent', smsResult);

      if (!smsResult.msgQueue || !smsResult.msgQueue) {
        throw {
          httpCode: 500,
          errorCode: 'INTERNALGSMS_FAL055',
          reason: 'failed to send internal msg'
        };
      }


      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
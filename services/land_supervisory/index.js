'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const CreateOrEditLandSupervisoryHandler = require("./createOrEditLandSupervisoryHandler");
const GetLandSupervisoryListHandler = require("./getLandSupervisoryListHandler");
const GetLandSupervisoryDetailHandler = require("./getLandSupervisoryDetailHandler");
const GetLandListByApplicationHandler = require('./getLandListByApplicationHandler');
const statisticHandler = require('./statistic');
const SendSMSHandler = require('./sendSMSHandler')

class Service {
  constructor() {

  }

  async createOrEdit(input, _opts) {
    const method = 'createOrEditLandSupervisory';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new CreateOrEditLandSupervisoryHandler(context));
      svcHandlerMgrt.addHandler(new SendSMSHandler(context))
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLandSupervisoryList(input, _opts) {
    const method = 'getLandSupervisoryList';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetLandSupervisoryListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLandSupervisoryDetail(input, _opts) {
    const method = 'getLandSupervisoryDetail';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetLandSupervisoryDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLandListByApplicationHandler(input, _opts) {
    const method = 'getLandListByApplicationHandler';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetLandListByApplicationHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async statistic(input, _opts) {
    let method = 'get_land_supervisory_record_statistic';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new statisticHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();
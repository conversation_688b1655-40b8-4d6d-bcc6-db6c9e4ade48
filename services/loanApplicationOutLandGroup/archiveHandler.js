/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'archiveLoanApplicationOutLandGroupHanlder';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const aliOssSvc = require('../aliOssSvc');
const formatAreaCode = require('../../persistence/formatAreaCode');

const {
  loanApplicationOutLandGroup: loanApplicationOutLandGroup,
  loanApplicationOutLand: loanApplicationOutLandData,
} = require('../dataSvc/dataUtil');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input } = this.context;
      let result = await loanApplicationOutLandGroup.putById(input.id, { archived: true });
      //删除分组后，把分组下的测外地设置为默认分组
      let lands = await loanApplicationOutLandData.getByCondition({ groupId: input.id, limit: "unlimited" });
      await Promise.all(lands.map(async land => {
        loanApplicationOutLandData.putById(land._id, { groupId: null });
      }))
      debug(method, '[Exit](success)');
      this.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}



module.exports = Handler;
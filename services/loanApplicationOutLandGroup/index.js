/**
 * LoanSupplement Svc index
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:laonApplictionOutLandGroup:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;

const listHandler = require('./listHandler');
const createHandler = require('./createHandler');
const archiveHandler = require('./archiveHandler');

class Service {
  constructor() {

  }

  async list(input, _opts) {
    const method = 'listHandler';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new listHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async create(input, _opts) {
    const method = 'create';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new createHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async archive(input, _opts) {
    const method = 'archive';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new archiveHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


}

module.exports = new Service();
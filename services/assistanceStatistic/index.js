'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin.loan.mgr.app.api:services:assistanceStatistic:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetAssistanceStatisticDetailHandler = require('./getAssistanceStatisticDetailHandler');

class Service {
  constructor() {

  }

  async getAssistanceStatisticDetail(input, _opts) {
    const method = 'getAssistanceStatisticDetail';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetAssistanceStatisticDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

}

module.exports = new Service();

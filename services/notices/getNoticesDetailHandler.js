/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'getNoticesDetailHandler';
const { forIn } = require('lodash');
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const noticesData = require('../dataSvc/dataUtil').notices;
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;
const aliOssSvc = require('../aliOssSvc');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = await self.context.input;
      let opts = self.context.opts;


      let result = await noticesData.getById(condition.id);
      if (result.attachPath) {
        result.attachFullPath = await aliOssSvc.getFile({ fileName: result.attachPath })
      }
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
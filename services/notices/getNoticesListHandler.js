/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'getNoticesListHandler';
const { forIn } = require('lodash');
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const noticesData = require('../dataSvc/dataUtil').notices;
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = await self.context.input;
      let opts = self.context.opts;

      // 当前角色的区域
      let roles = await employeeGroupsData.getOneByCondition({
        employee: opts.userInfo.userid,
        group: opts.roleId,
        archived: false
      });

      if (!roles) {
        throw {
          httpCode: 406,
          errorCode: 'E_NOTICES_R_043',
          reason: '当前角色被禁用或无效！'
        }
      }
      let areaList = roles.areaList;
      let areaMap = new Map();
      //拿到用户所在的区域
      for (const area in areaList) {

        if (areaList[area].length > 2) {
          areaMap.set(areaList[area].substr(0, 2))
        }
        if (areaList[area].length > 4) {
          areaMap.set(areaList[area].substr(0, 4))
        }
        if (areaList[area].length > 6) {
          areaMap.set(areaList[area].substr(0, 6))
        }
        if (areaList[area].length > 9) {
          areaMap.set(areaList[area].substr(0, 9))
        }
        areaMap.set(areaList[area])
      }
      let areas = [...areaMap.keys()]

      condition.areaList = {
        $in: areas
      }

      let result = await noticesData.getListAndCountByCondition(condition);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
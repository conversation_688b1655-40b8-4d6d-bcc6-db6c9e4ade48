'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetLoanApplicationsHandler = require('./baseOperations/getLoanApplicationsHandler');

const GetLoanApplicationDetailHandler = require('./baseOperations/getLoanApplicationDetailHandler');
const FormatAreaCodeHandler = require('./baseOperations/formatAreaCodeHandler');
const FormatApplicatEnumHandler = require('./baseOperations/formatApplicatEnumHandler');
const FormatAppUserVerifyHandler = require('./baseOperations/formatAppUserVerifyHandler');

const PreGetLoanAppListHander = require('./baseOperations/preGetLoanAppListHander');

const SpecialCodeScreentHander = require('./baseOperations/specialCodeScreentHander');


const PublicAttachLoanHistoryHandler = require('./baseOperations/publicAttachLoanHistoryHandler')

class Service {
  constructor() {}

  async getLoanApplicationList(input, _opts) {
    let method = 'getLoanApplicationList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new PreGetLoanAppListHander(context));
      svcHandlerMgrt.addHandler(new SpecialCodeScreentHander(context));
      svcHandlerMgrt.addHandler(new GetLoanApplicationsHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatEnumHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }



  async getLoanApplicationDetail(input, _opts) {
    let method = 'getLoanApplicationDetail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetLoanApplicationDetailHandler(context));
      svcHandlerMgrt.addHandler(new PublicAttachLoanHistoryHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatEnumHandler(context));
      svcHandlerMgrt.addHandler(new FormatAppUserVerifyHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


}

let svc = new Service();
module.exports = svc;
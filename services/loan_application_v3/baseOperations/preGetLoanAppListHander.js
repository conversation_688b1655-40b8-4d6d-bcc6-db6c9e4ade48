/*
 * @Author: qays
 * @Date: 2023-02-01 09:08:50
 * @LastEditTime: 2024-01-10 14:12:40
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.dashboard.api\services\loanApplication\baseOperations\preGetLoanAppListHander.js
 */
'use strict';

const HANDLER_NAME = 'preGetLoanAppListHander';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const { userVerifys } = require('../../dataSvc/dataUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const GROUP_ROLE = require('../../../services/permission').GROUP_ROLE;
const groupV2Data = require('../../dataSvc/dataUtil').groupV2;
const { FUND, PRODUCT } = require('../../../utils/fundConst');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _opts = self.context.opts;
      let _input = self.context.input;

      if (_input.idCard) {
        const userInfo = await userVerifys.getOneByCondition({ IDCard: _input.idCard, archived: false, IDCardStatus: 'approved' });
        _input.uId = userInfo ? userInfo.uId : null; // null代表查无此人，订单列表为空
        delete self.context.input.idCard;
      }

      if (!_opts.role) {
        debug(method, '[Exit](success)', 'do nothing');
        return done();
      }

      if (!_opts || !_opts.userInfo || !_opts.userInfo.roles) {
        debug(method, '[Exit](success)', 'userInfo not exists');
        return done();
      }

      if (!Array.isArray(_opts.userInfo.roles) || _opts.userInfo.roles.length < 1) {
        debug(method, '[Exit](success)', 'userInfo roles not exists');
        return done();
      }

      let roles = _opts.userInfo.roles;
      let _role = _opts.role;

      let flag = false;
      let realRole = null;
      roles.forEach(item => {
        if (item.name === _role) {
          flag = true;
          realRole = item;
          return;
        }
      });

      if (!flag) {
        throw {
          errorCode: 'E_LOAN_APP_ROLE_055',
          httpCode: 401,
          reason: 'invalid user'
        }
      }

      //客户经理主管
      if (_role === GROUP_ROLE.ACCOUNTMANAGERLEADER) {
        if (realRole.areaList && realRole.areaList.length > 0) {
          if (!_input.$or) {
            _input.$or = [];
          }
          realRole.areaList.forEach(item => {
            let area = {
              area: '/^' + item + '/'
            };
            _input.$or.push(area);
          });
          self.context.input = _input;

          debug(method, '[Exit](success)', self.context.input);
          return done();
        }
      }

      //商城运营
      if (_role === GROUP_ROLE.MERCHANTOPERATOR) {
        _input.pId = {
          $in: [PRODUCT.YYN_NZD_ID, PRODUCT.JT_NJD_ID]
        }
      }

      //客户经理
      if (_role === GROUP_ROLE.ACCOUNTMANAGER) {
        if (realRole.areaList && realRole.areaList.length > 0) {
          if (!_input.$or) {
            _input.$or = [];
          }
          realRole.areaList.forEach(item => {
            let area = {
              area: '/^' + item + '/'
            };
            _input.$or.push(area);
          });

          // _input.status = 'pre_censor';
          // _input.destined = false;




          self.context.input = _input;

          debug(method, '[Exit](success)', self.context.input);
          return done();
        }

        // 如果是九台的客户经理只允许农机贷的pId 
        if (_opts.groupV2) {
          let groupInfo = await groupV2Data.getById(_opts.groupV2);
          if (groupInfo && groupInfo.name == '')
            if (groupInfo && groupInfo.addons && groupInfo.addons.partnerId) {
              _input.pId = "5f238b9901cb039a76e7f4eb"; // 农机贷
            }
        }
      }

      //区域审核员
      if (_role === GROUP_ROLE.REGIONALAPPROVER || _role === GROUP_ROLE.RISKAPPROVER || _role == GROUP_ROLE.COUNTYASSESSOR) {
        _input.fund = {
          $in: [FUND.JH_FUND_ID, FUND.GF_FUND_ID, FUND.YZ_FUND_ID, FUND.JL_FUND_ID, FUND.JTYH_FUND_ID, FUND.BH_FUND_ID, FUND.DF_FUND_ID, FUND.ZGYH_FUND_ID]
        }
        _input.pId = { $ne: PRODUCT.YYN_NZD_ID };
        if (realRole.areaList && realRole.areaList.length > 0) {
          if (!_input.$or) {
            _input.$or = [];
          }
          realRole.areaList.forEach(item => {
            let area = {
              area: '/^' + item + '/'
            };
            _input.$or.push(area);
          });
          self.context.input = _input;

          debug(method, '[Exit](success)', self.context.input);
          return done();
        }

      }
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
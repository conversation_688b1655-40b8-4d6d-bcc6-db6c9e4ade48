/**
 * @summary FormatAreaCodeHandler
 * <AUTHOR>
 *
 * Created at     : 2018-11-26 11:18:55 
 * Last modified  : 2018-12-13 16:43:09
 */

'use strict';

const HANDLER_NAME = 'FormatAreaCodeHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../../persistence/formatAreaCode');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }
      let promises = [];

      for (let item of _result) {
        promises.push(formatAreaCode.getFormatAreaCode(item.area).then(data => {
          item.region = data && data.region || {};
          item.location = data && data.area || "";
        }));

        promises.push();

        if (item.approveAmount) {
          item.approveAmount = item.approveAmount / 100;
        }
        if (item.totalFee) {
          item.totalFee = item.totalFee / 100;
        }
      }

      await Promise.all(promises);

      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
/**
 * @summary FormatAppUserVerifyHandler
 * <AUTHOR>
 *
 * Created at     : 2018-12-12 10:58:30 
 * Last modified  : 2018-12-13 16:43:02
 */

'use strict';

const HANDLER_NAME = 'FormatApplicatEnumHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const userIdentityData = require('../../dataSvc/dataUtil').userIdentity;
const userData = require('../../dataSvc/dataUtil').user;
const DocumentKeys = ["frontIDCardImage", "backIDCardImage", "handHeldIDCardImage"];
class <PERSON>ler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _result = self.context.result;
      if (!_result || !_result.uId) {
        debug(method, '[Exit](continue)')
        return done()
      }
      let user = await userData.getById(_result.uId);
      let userIdentity = await userIdentityData.getOneByCondition({
        uId: _result.uId,
        IDCardStatus: "approved",
        archived: false,
        $sort: {
          lastModTime: -1
        },
        skip: 0,
        limit: 1
      })
      if (!userIdentity || !user) {
        debug(method, '[Exit](continue)');
        return done();
      }
      userIdentity.location = _result.location || "";
      userIdentity.userMobile = user.mobile || "";
      let documentImages = [];
      for (let item of DocumentKeys) {
        if (userIdentity[item]) {
          documentImages.push({
            type: item,
            value: userIdentity[item]
          })
        }
      }
      _result.documentImages = documentImages;
      _result.userInfo = userIdentity || {};

      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item.key === key) return item.value;
    }
    return '';
  }
}

module.exports = Handler
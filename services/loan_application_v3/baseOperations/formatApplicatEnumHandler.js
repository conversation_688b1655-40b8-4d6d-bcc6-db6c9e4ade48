/**
 * @summary FormatApplicatEnumHandler
 * <AUTHOR>
 *
 * Created at     : 2018-11-26 14:48:43 
 * Last modified  : 2018-12-13 16:42:58
 */

'use strict';

const HANDLER_NAME = 'FormatApplicatEnumHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const dateFormat = require('dateformat');
const loanProduct = require('../../dataSvc/dataUtil').loanProducts;
const employeeData = require('../../dataSvc/dataUtil').employees;
const fundData = require('../../dataSvc/dataUtil').funds;
const loanProductMap = require('../../../data/loan_product');
const { CHANNEL_MAP } = require('../../../utils/const/applicationConst');
const aliOssSvc = require('../../aliOssSvc');

const moment = require('moment');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }

      let time_offset = Math.abs(new Date().getTimezoneOffset());

      for (let item of _result) {
        item.amount /= 100;
        // if(item.approveAmount)
        //   item.approveAmount /= 100;
        if (item.createdTime) {
          item.formatCreatedTime = dateFormat(new Date(item.createdTime) + time_offset * 60 * 1000, "yyyy-mm-dd HH:MM:ss") || item.createdTime;
        }
        if (item.creditTime) {
          item.creditTime = moment(item.creditTime).format("YYYY-MM-DD");
        }
        if (item.creditEndTime) {
          item.creditRemainingTime = ['finished_loan', 'finished'].includes(item.status) ? 0 : moment(item.creditEndTime).diff(moment(), 'days');
          item.creditEndTime = moment(item.creditEndTime).format("YYYY-MM-DD");
        }
        item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss')
        let product = await loanProduct.getById(item.pId, { cache: true, expire: 24 * 60 * 60 });
        if (product && product._id) {
          product.amount = item.amount;
          product.apr = product.loanRate + product.serviceRate;
          // item.loanTerm = product.borrowPeriod;
          let REPAY_TYPE = loanProductMap.REPAY_TYPE.get(item.repay_t) || {};
          if (item.repayment) {
            REPAY_TYPE = loanProductMap.REPAY_TYPE.get(item.repayment) || {};
          }
          product.repay_t = REPAY_TYPE.name;
          product.type = product.name
          if (item.loanTerm && item.loanTerm != product.borrowPeriod) {
            product.borrowPeriod = item.loanTerm;
          }
          item.productInfo = product;
        }
        item.type = product && product.name || "红本贷"

        if (item.fund) {
          let fund = await fundData.getById(item.fund, { cache: true, expire: 24 * 60 * 60 });
          item.fundName = fund.name || '';
        }

        if (item.addons && item.addons.riskReportFile) {
          item.addons.riskReportFile = await aliOssSvc.getFile({
            fileName: item.addons.riskReportFile
          });
        }

        if (item.destiner) {
          const employeeInfo = await employeeData.getById(item.destiner, { cache: true, expire: 24 * 60 * 60 });
          item.destinerName = employeeInfo ? employeeInfo.username : '';
          item.destinerMobile = employeeInfo && employeeInfo.mobile || "";
        }

        if (item.ascription) { //业绩归属
          let ascriptionData = await employeeData.getById(item.ascription, { cache: true, expire: 24 * 60 * 60 });
          item.ascriptionName = ascriptionData && ascriptionData.username || '';
          item.employeeMobile = ascriptionData && ascriptionData.mobile || '';
        }

        if (item.channel) {
          item.channelName = CHANNEL_MAP.get(item.channel);
        }

        item.loanCustomerTypeDesc = item.loanCustomerType === 1 ? "老客户订单" : "新客户订单";
        item.customerRoleTypeDesc = item.customerRoleType === 1 ? "是" : "否";
      }

      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item._id === key) return item.name;
    }
    return '';
  }
}

module.exports = Handler
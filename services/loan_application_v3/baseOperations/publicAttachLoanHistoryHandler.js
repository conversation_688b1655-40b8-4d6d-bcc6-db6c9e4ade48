'use strict';

const HANDLER_NAME = 'publicAttachLoanHistoryHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:application_v2:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanProductData = require('../../dataSvc/dataUtil').loanProducts;
const moment = require('moment')

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    let self = this;
    let method = self.getName();
    debug(method, '[Enter]');
    try {
      let loanInfo = self.context.result;

      let loanList = await loanApplicationData.getByCondition({
        limit: 'unlimited',
        tId: loanInfo.tId,
        uId: loanInfo.uId,
        status: { $in: ['finished_loan', 'finished', 'rejected_final'] },
        archived: false,
        $sort: { createdTime: 1 }
      });
      let loanHistory = [];
      let pMap = new Map();
      for (let item of loanList) {
        const pId = item.pId;
        if (!pMap.has(pId)) {
          const product = await loanProductData.getById(pId, { cache: true, expire: 24 * 60 * 60 });
          pMap.set(pId, product.name);
        }
        loanHistory.push({
          _id: item._id,
          status: item.status,
          createdTime: moment(item.createdTime).format("YYYY-MM-DD"),
          approveAmount: item.approveAmount / 100,
          actualLoan: item.actualLoan,
          pId: pId,
          productName: pMap.get(pId)
        });
      }
      loanInfo.loanHistory = loanHistory;

      self.context.result = loanInfo;
      debug(self.getName(), '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](error)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
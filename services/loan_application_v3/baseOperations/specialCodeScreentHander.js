/**
 * @summary UpdateLandInfoHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'updateCropsHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _input = self.context.input;

      const specialCode = _input.areaCodeSpecial;
      if (_input.areaCodeSpecial) {
        if (!_input.$or) { // 防止_input.$or没有值
          _input.$or = [];
        }
        let arrCodeList = [];
        if(!_input.$or || _input.$or.length == 0) { // 
          arrCodeList.push({ area: `/^${specialCode}/` })
        }
        const len = specialCode.length;
        // _input.$or：角色赋予本身的权限（限制其访问到非自己权限的订单）
        for (const codeReg of _input.$or) {
          // codeReg.area格式：'/^' + item + '/'
          // substring(2, codeReg.area.length - 1)：去掉前后的'/^'和'/'
          let code = codeReg.area.substring(2, codeReg.area.length - 1);
          const flag = loopValidRepeat(specialCode, arrCodeList);

          // 用户原有区域权限与筛选区域权限做对比
          // 举例：筛选区域码：'2201'，角色权限区域码：22, 2202, 220301, 220401001, 220101
          if(len >= code.length) { // 筛选区域码长度大于角色权限区域码
            // 2201 > 22 && 22.test(2201) => true: 加入2201
            // 2201 = 2202 && 2202.test(2201) => false: 不加入2201
            const regex = new RegExp(`^${code}`); // 筛选区域码必须以角色权限区域码为前缀
            if(!flag && regex.test(specialCode)) arrCodeList.push({ area: `/^${specialCode}/` })
          } else { // 筛选区域码长度小于角色权限区域码
            // 2201 < 220301 && 2201.test(220301) => false: 不加入220301
            // 2201 < 220401001 && 2201.test(220401001) => false: 不加入220401001
            // 2201 < 220101 && 2201.test(220101) => true: 加入220101
            const regex = new RegExp(`^${specialCode}`);
            if(!flag && regex.test(code)) arrCodeList.push({ area: `/^${code}/` })
          }
        }
        // areaCodeSpecial有值，但是没有任何权限匹配上，就给一个默认值，默认它不显示任何数据
        if(arrCodeList.length == 0) {
          arrCodeList = [{ area: `/^-1/` }]
        }
        _input.$or = arrCodeList;
        delete self.context.input.areaCodeSpecial;
      }


      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

// arr中是否已经包含此code
function loopValidRepeat(code, arr) {
  for (const it of arr) {
    const area = it.area;
    if(area === '/^' + code + '/') {
      return true;
    }
  }
}

module.exports = Handler
/*
 * @Author: qays
 * @Date: 2023-02-01 09:08:50
 * @LastEditTime: 2024-03-08 13:54:02
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.dashboard.api\services\loanApplication\baseOperations\getLoanApplicationDetailHandler.js
 */
'use strict';

const HANDLER_NAME = 'getLoanApplicationDetailHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanSupplementData = require('../../dataSvc/dataUtil').loanSupplement;
const employeeData = require('../../dataSvc/dataUtil').employees;
const FUNDS = require('../../../utils/fundConst').FUND;
const aliOssSvc = require('../../../services/aliOssSvc');
const loanApplicationTrackingData = require('../../dataSvc/dataUtil').loanApplicationTracking;
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input.id;
      const result = await loanApplicationData.getById(id);
      self.context.result = result;
      const tracking = await loanApplicationTrackingData.getOneByCondition({
        target: id,
        action: { $in: ['finished_loan', 'finished'] },
        archived: false,
        $sort: { createdTime: 1 }
      });
      if (tracking && tracking._id) {
        self.context.result.finishedTime = moment(tracking.createdTime).format("YYYY-MM-DD");
      }
      if (result.type = "detj") {
        const loanTracking = await loanApplicationTrackingData.getOneByCondition({
          target: id,
          action: 'loaned',
          archived: false
        });
        if (loanTracking && loanTracking.parameters) {
          result.loanedTime = moment(loanTracking.parameters.PAY_DATE).format("YYYY-MM-DD");
        }
      }
      let tracking_review = await loanApplicationTrackingData.getOneByCondition({
        target: id,
        target_t: 1,
        action: "master_review",
        archived: false,
      })
      if (tracking_review) {
        const employeeInfo = await employeeData.getById(tracking_review.source, { cache: true, expire: 24 * 60 * 60 });
        tracking_review.employeeInfo = employeeInfo
        result.tracking_review = tracking_review
      }


      if (!self.context.result) {
        throw {
          httpCode: 404,
          errorCode: 'E_LOAN_APP_D_030',
          reason: 'not found'
        };
      }

      if (FUNDS.JT_FUND_ID == result.fund) {
        if (result.addons && result.addons.jtData && result.addons.jtData.settledFile) {
          result.settledFile = await aliOssSvc.getFile({ fileName: result.addons.jtData.settledFile });
        }
      }

      const supplement = await loanSupplementData.getOneByCondition({ aId: id, $sort: { createdTime: -1 }, archived: false });
      if (supplement && supplement.idValidStart && supplement.idValidEnd) {
        if (!result.addons) result.addons = {};
        result.addons.idValidStart = supplement.idValidStart;
        result.addons.idValidEnd = supplement.idValidEnd;
      }
      if (supplement && supplement.marriage) {
        result.marriage = supplement.marriage;
        result.spouse = supplement.spouse
      }
      if (result.destiner) {
        const employeeInfo = await employeeData.getById(result.destiner, { cache: true, expire: 24 * 60 * 60 });
        result.destinerName = employeeInfo ? employeeInfo.username : '';
      }
      if (result.historyOperator) {
        result.historyOperatorName = [];
        result.historyOperator.map(async (res) => {
          let historyInfo = await employeeData.getById(res, { cache: true, expire: 24 * 60 * 60 });
          result.historyOperatorName.push(historyInfo ? historyInfo.username : '');
        })
      }

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
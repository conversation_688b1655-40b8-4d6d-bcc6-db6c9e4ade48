'use strict';

const HANDLER_NAME = 'getLoanApplicationsHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;

      self.context.result = {
        result: [],
        total: 0
      };
      if(condition.uId !== null) {
        self.context.result = await loanApplicationData.getListAndCountByCondition(condition);
      }

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
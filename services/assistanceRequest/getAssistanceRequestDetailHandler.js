'use strict';

const HANDLER_NAME = 'GetAssistanceRequestDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin.loan.mgr.app.api:services:assistanceRequest:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeeData = require('../dataSvc/dataUtil').employees;
const assistanceRequestData = require('../dataSvc/dataUtil').assistanceRequest;
const assistanceAgentData = require('../dataSvc/dataUtil').assistanceAgent;
const userVerifyData = require('../dataSvc/dataUtil').userVerifys;
const msgQueueData = require('../dataSvc/dataUtil').msgQueue;

class <PERSON><PERSON> extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    const self = this;
    const method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      const condition = self.context.input;
      const opts = self.context.opts;

      let assistanceRequest;
      if (condition.id) {
        assistanceRequest = await assistanceRequestData.getById(condition.id);
      } else {
        const msg = await msgQueueData.getById(condition.msgId);
        assistanceRequest = await assistanceRequestData.getOneByCondition({
          msgId: msg.msgRef,
          archived: false,
        });
      }

      if (!assistanceRequest) {
        throw {
          errorCode: 'E_GET_ASSISTANCE_REQUEST_DETAIL_HANDLER_038',
          httpCode: 406,
          reason: 'Assistance request not found'
        }
      }

      const employee = await employeeData.getOneByCondition({
        tId: opts.tId,
        archived: false,
        _id: assistanceRequest.agentId
      });

      const agent = await assistanceAgentData.getOneByCondition({
        tId: opts.tId,
        agentId: assistanceRequest.agentId,
        archived: false,
      });

      if (!employee || !agent) {
        throw {
          errorCode: 'E_GET_ASSISTANCE_REQUEST_DETAIL_HANDLER_061',
          httpCode: 406,
          reason: 'employee or agent not found'
        }
      }

      const user = await userVerifyData.getOneByCondition({
        uId: assistanceRequest.userId,
        IDCardStatus: "approved",
        archived: false,
      });

      self.context.result = {
        ...assistanceRequest,
        queueTenantId: 'EGCEAA',
        queueId: agent.queueId,
        isEnable: agent.isEnable,
        isBusy: agent.isBusy,
        username: user ? user.realname : '',
        agentMobile: employee.mobile,
        agentName: employee.username,
      };
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;

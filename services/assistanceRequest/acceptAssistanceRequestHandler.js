'use strict';

const HANDLER_NAME = 'AcceptAssistanceRequestHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin.loan.mgr.app.api:services:assistanceRequest:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistanceRequestData = require('../dataSvc/dataUtil').assistanceRequest;

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    const self = this;
    const method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      const input = self.context.input;
      const body = input.body;
      body.lastModTime = new Date();

      const data = await assistanceRequestData.putById(input.id, body);
      self.context.result = data;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;

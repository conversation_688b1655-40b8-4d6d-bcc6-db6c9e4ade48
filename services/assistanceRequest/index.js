'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin.loan.mgr.app.api:services:assistanceRequest:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetAssistanceRequestListHandler = require('./getAssistanceRequestListHandler');
const GetAssistanceRequestDetailHandler = require('./getAssistanceRequestDetailHandler');
const AcceptAssistanceRequestHandler = require('./acceptAssistanceRequestHandler');
const CloseAssistanceRequestHandler = require('./closeAssistanceRequestHandler');
const UpdateAssistanceRequestHandler = require('./updateAssistanceRequestHandler');
const RemoveAssistanceRequestHandler = require('./removeAssistanceRequestHandler');

class Service {
  constructor() {

  }

  async getAssistanceRequestList(input, _opts) {
    const method = 'getAssistanceRequestList';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetAssistanceRequestListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getAssistanceRequestDetail(input, _opts) {
    const method = 'getAssistanceRequestDetail';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetAssistanceRequestDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async acceptAssistanceRequest(input, _opts) {
    const method = 'acceptAssistanceRequest';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new AcceptAssistanceRequestHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async closeAssistanceRequest(input, _opts) {
    const method = 'closeAssistanceRequest';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new CloseAssistanceRequestHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async updateAssistanceRequest(input, _opts) {
    const method = 'updateAssistanceRequest';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new UpdateAssistanceRequestHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async removeAssistanceRequest(input, _opts) {
    const method = 'removeAssistanceRequest';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new RemoveAssistanceRequestHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();

'use strict';

const HANDLER_NAME = 'loanApplicationCustomerVisitListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    loanApplicationCustomerVisit:loanApplicationCustomerVisitData,
    infoCollectHistory:infoCollectHistoryData,
    employeeGroups: employeeGroupData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');
const { getEmployeeLimit } = require('../../utils/getUserFromReq')
const moment = require('moment');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:condition,opts,opts:{getOne}} = this.context;
        debug(`${HANDLER_NAME}Query`,condition);

        const beginList = await loanApplicationCustomerVisitData.getByCondition({status:'1',limit:'unlimited'});
        //第二次零时自动完成
        await Promise.all( beginList.map(async it=>{
            // new Date( moment( it.createdTime ).add( 1 , 'd' ).format( 'YYYY-MM-DD 00:00:00' ) )
            const finishTime = new Date( new Date( moment(it.createdTime).format('YYYY-MM-DD 00:00:00') ).getTime() + 24*3600*1000 );
            Date.now() > finishTime.getTime() && await loanApplicationCustomerVisitData.putById(it._id,{status:'2',finishTime})
        }) );
        const result = await loanApplicationCustomerVisitData.getListAndCountByCondition(condition),list = result.result;

        const formatOpts = {}
        this.context.opts.formatList = list;
        this.context.result = result;

        assert(!getOne || list.length ,'E_FUND_CUSTOMER_VISIT_DETAIL_001','record of this id not found')
        getOne && ( this.context.result = list[0] );
        // this.context.result = this.context.result || {list,uId,username};
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
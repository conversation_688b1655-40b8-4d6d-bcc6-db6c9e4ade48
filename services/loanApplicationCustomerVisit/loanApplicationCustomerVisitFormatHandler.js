'use strict';

const HANDLER_NAME = 'loanApplicationCustomerVisitFormattHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    loanApplicationCustomerVisit:loanApplicationCustomerVisitData,
    infoCollectHistory:infoCollectHistoryData,
    employees:employeeData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const moment = require('moment');
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const Decimal = require('decimal.js'),unitDenominator = 100,unitDenominator2 = 1000000;

const numberMapping = [
    'basicInfo.type','basicInfo.isFamiliar','basicInfo.isDistribution','basicInfo.isApprovalMode','basicInfo.channel',
    'basicInfo.isNeedDemand','basicInfo.businessType',
    'grainFundNeeds.circulationMode','grainFundNeeds.preFundSources','grainFundNeeds.lastYearHadInsurance',
    'grainFundNeeds.insuranceType','grainFundNeeds.experience','grainFund.quotaAmountType',
    'movablesFundNeeds.hadBankResources','movablesFundNeeds.resourceType','movablesFundNeeds.preFundSources','movablesFundNeeds.quotaAmountType',
    'negotiationInfo.customerDescType',
];
const loanApplicationCustomerVisitEnum = require('./loanApplicationCustomerVisitEnum')
const { grainFundNeedsCropTypeEnumDic,cropTypeEnumDic  } = loanApplicationCustomerVisitEnum;

class Handler extends BaseHandler {
    constructor(context) {
        super(context)
    }

    getName() {
        return HANDLER_NAME
    }

    async doAsync(done) {
        const method = `${this.getName()}.doAsync`
        debug(method, '[Enter]')
        try {
            const {opts:{formatList,formatOpts}} = this.context;
            await this.formatList(formatList || [],formatOpts||{});
            debug(method, '[Exit](success)');
            return done();
            } catch (error) {
            debug.error(method, '[Exit](failed)', error);
            return done(error);
        }
    }

    async formatList(list,formatOpts){
        await Promise.all(list.filter(v=>v).map(async (v,i)=>{

            // await this.formatOssFiles(v);
            const employee = v.operator && await employeeData.getById(v.operator, {cache : true , expire: 24 * 60 * 60 });
            v.employeeName = employee && employee.username;
            v.areaInfo = v.areaCode && await formatAreaCode.getFormatAreaCode(v.areaCode);
            v.basicInfo && v.basicInfo.visitAreaCode && ( v.basicInfo.visitAreaInfo = await formatAreaCode.getFormatAreaCode(v.basicInfo.visitAreaCode))
            v.grainFundNeeds && v.grainFundNeeds.areaCode && ( v.grainFundNeeds.areaInfo = await formatAreaCode.getFormatAreaCode(v.grainFundNeeds.areaCode) );
            v.movablesFundNeeds && v.movablesFundNeeds.areaCode && ( v.movablesFundNeeds.areaInfo = await formatAreaCode.getFormatAreaCode(v.movablesFundNeeds.areaCode) );

            numberMapping.forEach(k=>{
                const [node,name] = k.split('.');
                v[node] && typeof v[node][name] !== 'undefined' && `${v[node][name]}` && ( v[node][name] = Number(v[node][name]) );
            });

            v.basicInfo && v.basicInfo.visitAreaCode && ( v.basicInfo.visitAreaInfo = await formatAreaCode.getFormatAreaCode(v.basicInfo.visitAreaCode))
            v.negotiationInfo && v.negotiationInfo.customerDescType && // 兼容已有历史数据
                ( v.negotiationInfo.customerDesc = v.negotiationInfo.customerDesc
                    || loanApplicationCustomerVisitEnum.negotiationInfoCustomerDescTypeEnumDic[ v.negotiationInfo.customerDescType ] || '' )
            // v.app = await loanApplicationData.getById(v.aId);

            // v.createdTime = moment(v.createdTime).format('YYYY-MM-DD HH:mm:ss');
            // v.lastModTime = moment(v.lastModTime).format('YYYY-MM-DD HH:mm:ss');

        }));
        return list;
    }

    async formatOssFiles(v){
        const {verifyInfo,requestInfo} = v && v.extendInfo || {verifyInfo:{}} ;
        const handleSupplements = verifyInfo && verifyInfo.handleSupplements;
        const ossFiles = [
            // ...v.photos || []
        ];

        await Promise.all( ossFiles.filter(v=>v).map(formatImg) );
        return v;
    }

    undoAsync(done) {
        done()
    }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
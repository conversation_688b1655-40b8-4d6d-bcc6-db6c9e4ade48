'use strict';

const HANDLER_NAME = 'loanApplicationCustomerVisitCreateOrEditHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    employees:employeeData,
    employeeGroups:employeeGroupData,
    loanApplication:loanApplicationData,
    loanApplicationCustomerVisit:loanApplicationCustomerVisitData,
    infoCollectHistory:infoCollectHistoryData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');
const moment = require('moment');

const stringMapping = [
    'basicInfo.type','basicInfo.isFamiliar','basicInfo.isDistribution','basicInfo.isApprovalMode','basicInfo.channel',
    'basicInfo.isNeedDemand','basicInfo.businessType',
    'grainFundNeeds.circulationMode','grainFundNeeds.preFundSources','grainFundNeeds.lastYearHadInsurance',
    'grainFundNeeds.insuranceType','grainFundNeeds.experience','grainFund.quotaAmountType',
    //'grainFundNeeds.cropTypes','movablesFundNeeds.cropType',
    'movablesFundNeeds.hadBankResources','movablesFundNeeds.resourceType','movablesFundNeeds.preFundSources','movablesFundNeeds.quotaAmountType',
    'negotiationInfo.customerDescType',
]

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:one,opts:{uId:operator,roleId}} = this.context;
        const { username:operatorName } = operator && await employeeData.getById(operator, {cache : true , expire: 24 * 60 * 60 }) || {};

        stringMapping.forEach(k=>{
            const [node,name] = k.split('.');
            one[node] && typeof one[node][name] !== 'undefined' && ( one[node][name] = `${one[node][name]}` );
        })
        // 如果枚举值是 number，业务层调data查询时，会变成字符串查询。所以枚举值应该是字符串最方便
        // 以最小工作量计，此处进行一道强转

        const employeeGroupQeury = { employee:operator,archived:false,limit:'unlimited' };
        roleId && ( employeeGroupQeury.group = roleId );
        debug(`${HANDLER_NAME}employeeGroupQeury`,employeeGroupQeury);
        // debug(`${HANDLER_NAME}employeeGroupResult`,await employeeGroupData.getOneByCondition(employeeGroupQeury));
        const { areaList=[] } = operator && await employeeGroupData.getOneByCondition(employeeGroupQeury) || {};
        debug(`${HANDLER_NAME}employeeGroupAreaList`,areaList);
        Object.assign(one,{operator,operatorName,areaCode:[].concat(areaList).shift(),lastModTime:new Date()});
        assert(['1','2','3'].includes(one.status),'E_CONSUMER_VISIT_1','status error');
        const old = one._id && await loanApplicationCustomerVisitData.getById(one._id);
        one._id && assert( old , 'E_CONSUMER_VISIT_2','_id is not exist');
        one._id && assert( one.status !== '1' , 'E_CONSUMER_VISIT_3','status had finished' )
        // one._id && assert( parseInt(one.status) !== parseInt(old.status) + 1 ,'E_CONSUMER_VISIT_3','status error!!')
        one._id || ( one.status = '1' , one.createdTime = new Date());
        old && ( one.finishTime = old.finishTime );
        one.status !== '1' && ( one.finishTime = one.finishTime || new Date() );
        one.status === '2' && assert( one.basicInfo && one.basicInfo.finishedVisitLocation && one.basicInfo.finishedVisitLocation.trim() , 'E_CONSUMER_VISIT_5' , '结束拜访时间不能为空' )
        // debug('debug233',one._id,typeof one.status,one.status,moment(one.finishTime).format('YYYY-MM-DD hh:mm:ss'))
        one._id && Object.assign(one,await loanApplicationCustomerVisitData.putById(one._id,one));
        one._id || Object.assign(one,await loanApplicationCustomerVisitData.post(one));

        this.context.result = {success:'ok',one};
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
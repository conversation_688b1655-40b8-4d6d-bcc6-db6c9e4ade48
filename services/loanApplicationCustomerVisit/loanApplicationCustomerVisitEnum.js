'use strict';

const HANDLER_NAME = 'loanApplicationCustomerVisitEnum';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const cropTypeEnumList =
    [
        { label:'玉米' , value:'corn' },
        { label:'大豆' , value:'soybean' },
        { label:'水稻' , value:'rice' },
        // { label:'其他' , value:'other' },
    ];
const cropTypeEnumDic = cropTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );

const movablesFundCropTypeEnumList = [
    ...cropTypeEnumList,
    { label:'煤炭' , value:'coal' },
    { label:'中草药' , value:'herbal' },
    { label:'其他' , value:'other' },
]
const movablesFundCropTypeEnumDic = movablesFundCropTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );


const basicInfoTypeEnumList = [
    { label:'种植大户' , value:1 },
    { label:'家庭农场' , value:2 },
    { label:'农民专业合作社' , value:3 },
    { label:'村集体合作社' , value:4 },
    { label:'合伙企业' , value:5 },
    { label:'有限公司' , value:6 },
    { label:'股份公司' , value:7 },
    { label:'其他组织' , value:100 },
];

const basicInfoTypeEnumDic = basicInfoTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );

const basicInfoIsDistributionEnumList =
    [
        { label:'否' , value:0 },
        { label:'是，电子资料' , value:0b01 },
        { label:'是，纸质资料' , value:0b10 },
        { label:'是，电子及纸质资料' , value:0b11 },
    ];
const basicInfoIsDistributionEnumDic = basicInfoIsDistributionEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );

const basicInfoChannelEnumList =
    [
        { label:'银行推荐' , value:1 },
        { label:'县域中心领导推荐' , value:2 },
        { label:'物权公司推荐' , value:2 },
        { label:'客户自荐' , value:4 },
        { label:'陌生拜访' , value:7 },
    ];
const basicInfoChannelEnumDic = basicInfoChannelEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );

const basicInfoBusinessTypeEnumList = [
    { label:'粮食预期收益权融资' , value:1 },
    { label:'动产质押融资' , value:2 },
]
const basicInfoBusinessTypeEnumDic = basicInfoBusinessTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );

const basicIsApprovalModeEnumList =
    [
        { label:'否' , value:0 },
        { label:'是，能配合线上签约及监管' , value:1 },
    ];
const basicIsApprovalModeEnumDic = basicIsApprovalModeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );
const basicIsNeedDemandEnumList =
    [
        { label:'否' , value:0 },
        { label:'是' , value:1 },
        { label:'有，待进一步沟通' , value:2 },
        { label:'其它情况' , value:3 },
    ];
const basicIsNeedDemandEnumDic = basicIsNeedDemandEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );
const basicIsFamiliarEumList =
[
    { label:'新' , value:0 },
    { label:'老' , value:1 },
];
const basicIsFamiliarEumDic = basicIsFamiliarEumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );


const grainFundNeedsCirculationModeEnumList = [
    { label:'自种' , value:1 },
    { label:'托管' , value:2 },
];
const grainFundNeedsCirculationModeEnumDic = grainFundNeedsCirculationModeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );
const grainFundNeedsCropTypeEnumList = cropTypeEnumList;
const grainFundNeedsCropTypeEnumDic = grainFundNeedsCropTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );
const grainFundNeedsLastYearHadInsuranceEnumList = [
    { label:'是' , value:1 },
    { label:'否' , value:0 },
];
const grainFundNeedsLastYearHadInsuranceEnumDic = grainFundNeedsLastYearHadInsuranceEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );
const grainFundNeedsInsuranceTypeEnumList = [
    { label:'无' , value:1 },
    { label:'大灾险' , value:2 },
    { label:'完全成本险' , value:3 },
    { label:'收入险' , value:4 },
];
const grainFundNeedsInsuranceTypeEnumDic = grainFundNeedsInsuranceTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );
const grainFundQuotaAmountTypeEnumList = [
    { label:'无' , value:1 },
    { label:'100万以下' , value:2 },
    { label:'100万-500万' , value:3 },
    { label:'500万-1000万' , value:4 },
    { label:'1000万-3000万' , value:5 },
    { label:'3000万-5000万' , value:6 },
    { label:'5000万以上' , value:7 },
];
const grainFundQuotaAmountTypeEnumDic = grainFundQuotaAmountTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );

const grainFundExperienceEnumList = [
    { label:'1年' , value:1 },
    { label:'2年' , value:2 },
    { label:'3年' , value:3 },
    // { label:'4年' , value:4 },
    { label:'5年' , value:5 },
    { label:'6-10年' , value:6 },
    { label:'10-20年' , value:10 },
    // { label:'20年以上' , value:20 },
];
const grainFundExperienceEnumDic = grainFundExperienceEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );

const movablesFundNeedsQuotaAmountTypeEnumList = [
    { label:'无' , value:1 },
    { label:'1000万以下' , value:2 },
    { label:'1000万-3000万' , value:3 },
    { label:'3000万-5000万' , value:4 },
    { label:'5000万-1亿' , value:5 },
    { label:'1亿以上' , value:6 },
];
const movablesFundNeedsQuotaAmountTypeEnumDic = movablesFundNeedsQuotaAmountTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );

const movablesFundNeedsHadBankResourcesEnumList =
    [
        { label:'无' , value:0 },
        { label:'有' , value:1 },
        { label:'不确定' , value:2 },
    ];
const movablesFundNeedsHadBankResourcesEnumDic = movablesFundNeedsHadBankResourcesEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );
const movablesFundNeedsResourceTypeEnumList =
    [
        { label:'自有' , value:1 },
        { label:'租赁' , value:2 },
    ];
const movablesFundNeedsResourceTypeEnumDic = movablesFundNeedsResourceTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );

const grainFundNeedsPreFundSourcesEnumList =
    [
        { label:'自有资金' , value:0b001 },
        { label:'自有资金+银行贷款' , value:0b011 },
        { label:'自有资金+民间借贷' , value:0b101 },
        { label:'自有资金+银行贷款+民间借贷' , value:0b111 },
    ];
const grainFundNeedsPreFundSourcesEnumDic = grainFundNeedsPreFundSourcesEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );
const movablesFundNeedsPreFundSourcesEnumList =
    [
        { label:'自有资金' , value:0b001 },
        { label:'自有资金+银行贷款' , value:0b011 },
        { label:'自有资金+民间借贷' , value:0b101 },
        { label:'自有资金+银行贷款+民间借贷' , value:0b111 },
    ];
const movablesFundNeedsPreFundSourcesEnumDic = movablesFundNeedsPreFundSourcesEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );
const movablesFundNeedsCropTypeEnumList = cropTypeEnumList;
const movablesFundNeedsCropTypeEnumDic = movablesFundNeedsCropTypeEnumList.reduce( (r,v)=>( r[v.key]=v.label , r ) , {} );

const negotiationInfoCustomerDescTypeEnumList = [
    { label:'客户同意合作' , value:1 },
    { label:'客户有意向合作' , value:2 },
    { label:'客户暂无意向合作' , value:3 },
    { label:'客户对业务模式的反馈' , value:4 },
];
const negotiationInfoCustomerDescTypeEnumDic = negotiationInfoCustomerDescTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );


module.exports = {
    cropTypeEnumList,cropTypeEnumDic,basicInfoTypeEnumList,basicInfoTypeEnumDic,basicInfoIsDistributionEnumList,
    basicInfoIsDistributionEnumDic,basicInfoChannelEnumList,basicInfoChannelEnumDic,
    basicInfoBusinessTypeEnumList,basicInfoBusinessTypeEnumDic,
    basicIsApprovalModeEnumList, basicIsApprovalModeEnumDic,basicIsNeedDemandEnumList,basicIsNeedDemandEnumDic,
    basicIsFamiliarEumList,basicIsFamiliarEumDic,
    grainFundNeedsCirculationModeEnumList,grainFundNeedsCirculationModeEnumDic,grainFundNeedsCropTypeEnumList,grainFundNeedsCropTypeEnumDic,
    grainFundNeedsLastYearHadInsuranceEnumList,grainFundNeedsLastYearHadInsuranceEnumDic,grainFundNeedsInsuranceTypeEnumList,grainFundNeedsInsuranceTypeEnumDic,
    grainFundQuotaAmountTypeEnumList,grainFundQuotaAmountTypeEnumDic,
    grainFundExperienceEnumList,grainFundExperienceEnumDic,
    movablesFundNeedsHadBankResourcesEnumList,movablesFundNeedsHadBankResourcesEnumDic,
    movablesFundNeedsResourceTypeEnumList,movablesFundNeedsResourceTypeEnumDic,
    movablesFundCropTypeEnumList,movablesFundCropTypeEnumDic,movablesFundNeedsCropTypeEnumList,movablesFundNeedsCropTypeEnumDic,
    movablesFundNeedsQuotaAmountTypeEnumList,movablesFundNeedsQuotaAmountTypeEnumDic,
    grainFundNeedsPreFundSourcesEnumList,grainFundNeedsPreFundSourcesEnumDic,movablesFundNeedsPreFundSourcesEnumList,movablesFundNeedsPreFundSourcesEnumDic,
    negotiationInfoCustomerDescTypeEnumList,negotiationInfoCustomerDescTypeEnumDic,
}
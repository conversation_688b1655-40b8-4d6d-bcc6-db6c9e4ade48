'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:index');
// const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const loanApplicationCustomerVisitListHandler = require("./loanApplicationCustomerVisitListHandler");
const loanApplicationDisasterClaimsFormatHandler = require('./loanApplicationCustomerVisitFormatHandler');
const loanApplicationDisasterClaimsCreateOrEditHandler = require('./loanApplicationCustomerVisitCreateOrEditHandler');
const loanApplicationCustomerVisitEnumHandler = require('./loanApplicationCustomerVisitEnumHandler');

const {addHandlersForService} = require('../../utils/general')


class Service {
  constructor() {
    addHandlersForService.call(this,debug);
  }

  createOrEdit(){
    return [loanApplicationDisasterClaimsCreateOrEditHandler]
  }

  list(){
    return [loanApplicationCustomerVisitListHandler,loanApplicationDisasterClaimsFormatHandler]
  }

  enums(){
    return [loanApplicationCustomerVisitEnumHandler];
  }


}

module.exports = new Service();
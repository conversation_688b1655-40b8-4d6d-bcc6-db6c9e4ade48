/**
 * <AUTHOR>
 * 2019-05-05
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:carrousel:services:loan_charts:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetLoanChartsListHandler = require('./getLoanChartsListHandler');
const GetAssistanterChartsListHandler = require('./getAssistanterChartsListHandler');

class Service {
  constructor() {

  }

  async getLoanChartsList(input, _opts) {
    let method = 'getLoanChartsList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetLoanChartsListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getAssistanterChartsList(input, _opts) {
    let method = 'getAssistanterChartsList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetAssistanterChartsListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();
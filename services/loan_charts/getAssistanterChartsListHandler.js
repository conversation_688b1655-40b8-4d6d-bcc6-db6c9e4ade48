/**
 * <AUTHOR>
 * 2019-05-05  
 */

'use strict';

const HANDLER_NAME = 'getAssistanterChartsListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:loan_charts:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const formatAreaCode = require('../../persistence/formatAreaCode');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let result = await loanApplicationData.getByUrl("/v1.0/assistanter/charts/list", input);
      let promise = [];
      for (const item of result.result) {
        if (item.loan_total && item.total) {
          item.orderScale = (item.loan_total / item.total).toFixed(2) * 100
        }
        promise.push(formatAreaCode.getFormatAreaCode(item.area).then(data => {
          item.areaName = data
        }));
      }
      await Promise.all(promise);
      self.context.result = result || {};
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
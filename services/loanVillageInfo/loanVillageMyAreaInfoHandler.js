'use strict';

const HANDLER_NAME = 'loanVillageMyAreaInfoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    employees:employeeData,
    employeeGroups:employeeGroupData,
    loanApplication:loanApplicationData,
    loanVillageInfo:loanVillageInfoData,
    groups:groupData,
    dictAreas:dictAreaData,
    infoCollectHistory:infoCollectHistoryData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');
const moment = require('moment');
class Handler extends BaseHandler {
    constructor(context) {
        super(context)
    }

    getName() {
        return HANDLER_NAME
    }

    async doAsync(done) {
        const method = `${this.getName()}.doAsync`
        debug(method, '[Enter]')
        try {
            const {input,opts:{uId:employee,roleId:group,tId}} = this.context;
            assert( employee , 'E_VILLAGE_MY_AREA_001' , '请先登陆' );
            assert( group , 'E_VILLAGE_MY_AREA_002' , '角色不能为空' );
            const {_id,areaList=[]} = await employeeGroupData.getOneByCondition( {archived:false,group,employee,...tId && {tId} || {}} ) || [];
            debug(`${HANDLER_NAME}AllAreaCode`,areaList);
            const citiesCode = areaList.filter(code=>code.length >= 4 ).map(code=>code.substr(0,4));
            if( citiesCode.length === 0 )return 0;
            const cities = await dictAreaData.getByCondition({archived:false,limit:'unlimited',code:{$in:citiesCode},level:2})  || [];
            const $or = areaList.filter(code=>code.length >= 6 ).map( code=>({code:`/^${code}/`}) );
            debug(`${HANDLER_NAME}Query`,group,employee,tId,_id,$or)
            const villages = $or.length && await dictAreaData.getByCondition({archived:false,limit:'unlimited',$or,level:5})  || [];
            debug(`${HANDLER_NAME}AreaCodes`,villages.length, $or);
            const townCodes = Array.from( new Set( villages.map(v=>v.code.substr(0,9) )));
            const towns = await dictAreaData.getByCondition({archived:false,limit:'unlimited',$or,level:4,code:{$in:townCodes}});
            const counties = Array.from( new Set( townCodes.map(v=>v.substr(0,6) )));
            const result2 = await Promise.all( counties.map( async code=>({
                ...await dictAreaData.getOneByCondition({code}),
                children: towns.filter(v=>v.code.indexOf(code) === 0 ).map( town=>({
                    ...town,children: villages.filter(v=>v.code.indexOf(town.code) === 0 ),
                }) ),
            })) );
            // console.log('debug233',towns,villages.map(v=>v.code));
            await Promise.all( towns.map( async code=>{
                const v = villages.filter(v=>v.code.indexOf(code) === 0 );
                // v.length > 100 && console.log('debug233 v.code ',v.code)
            } ) );
            const flag = areaList.some(code=>code.length===4);
            const result1 = await Promise.all( cities.map( async city=>{
                const children = await dictAreaData.getByCondition({archived:false,limit:'unlimited',code:`/^${city.code}/`,level:3})  || [];
                children.forEach( v=>Object.assign( v , {children:[]},result2.find( vv=>vv.code === v.code ) || {} ) );
                // const children = result2.filter( v=>v.code.indexOf(city.code) === 0 );
                //     await Promise.all( counties.map( async county=>({
                //     ...county,
                //     children: towns.filter(v=>v.code.indexOf(county.code) === 0 ).map( town=>({
                //         ...town,children: villages.filter(v=>v.code.indexOf(town.code) === 0 ),
                //     }) ),
                // })) );
                return { ...city , children };
            } ) );
            const result = flag ? result1 : result2

            // ({
            //     town: await dictAreaData.getOneByCondition({code}),
            //     villages: villages.filter(v=>v.code.indexOf(code) === 0 ),
            // }) )

            this.context.result = result;
            debug(method, '[Exit](success)', this.context.result);
            return done();
        } catch (error) {
            debug.error(method, '[Exit](failed)', error);
            return done(error);
        }
    }

    undoAsync(done) {
        done()
    }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
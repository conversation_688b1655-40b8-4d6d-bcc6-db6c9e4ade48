'use strict';

const HANDLER_NAME = 'loanVillageInfoListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    loanVillageInfo:loanVillageInfoData,
    infoCollectHistory:infoCollectHistoryData,
    employeeGroups: employeeGroupData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');
const { getEmployeeLimit } = require('../../utils/getUserFromReq')
const moment = require('moment');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:condition,opts,opts:{getOne,uId,roleId}} = this.context;
        debug(`${HANDLER_NAME}Query`,condition);

        const { getEmployeeLimit } = require('../../utils/getUserFromReq');

        const areaCodeLimitOr = uId && await getEmployeeLimit(employeeGroupData, '' , uId, 'areaCode', roleId);
        uId && areaCodeLimitOr.length && ( condition['$or'] = areaCodeLimitOr);
        debug(`${HANDLER_NAME}AreaLimit`, uId, roleId , condition);

        const result = await loanVillageInfoData.getListAndCountByCondition(condition),list = result.result;

        const formatOpts = {}
        this.context.opts.formatList = list;
        this.context.result = result;

        assert(!getOne || list.length ,'E_VILLAGE_INFO_001','record of this id not found')
        getOne && ( this.context.result = list[0] );
        // this.context.result = this.context.result || {list,uId,username};
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
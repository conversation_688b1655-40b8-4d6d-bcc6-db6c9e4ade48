'use strict';

const HANDLER_NAME = 'loanVillageInfoAssistantListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    employees:employeeData,
    employeeGroups:employeeGroupData,
    loanApplication:loanApplicationData,
    loanVillageInfo:loanVillageInfoData,
    groups:groupData,
    infoCollectHistory:infoCollectHistoryData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');
const { getEmployeeLimit } = require('../../utils/getUserFromReq')
const moment = require('moment');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:{areaCode},opts:{uId:operator,roleId}} = this.context;
        assert( areaCode && areaCode.length >= 9 , 'E_VILLAGE_ASSISTANT_001' , '村编码是必要的' );

        const {_id:group} = await groupData.getOneByCondition({archived:false,type:3,name:'BusinessAssistant'});//不会为空

        // console.log('debug233',{archived:false,group,tId:'5cf4806e0a31202046ef6294',areaList:`/^${areaCode}/`,limit:'unlimited'})
        const result = await employeeGroupData.getByCondition( {archived:false,group,tId:'5cf4806e0a31202046ef6294',areaList:`/^${areaCode}/`,limit:'unlimited',$sort:{createdTime:-1}} ) ;
        await Promise.all( result.map(async v=>{
            const employee = v.employee && await employeeData.getById(v.employee, {cache : true , expire: 24 * 60 * 60 });
            v.employeeName = employee && employee.employee;
        }) )
        this.context.result = result;
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:index');
// const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const loanVillageInfoListHandler = require("./loanVillageInfoListHandler");
const loanVillageInfoAssistantListHandler = require("./loanVillageInfoAssistantListHandler");
const loanVillageInfoFormatHandler = require('./loanVillageInfoFormatHandler');
const loanVillageInfoCreateOrEditHandler = require('./loanVillageInfoCreateOrEditHandler');
const loanVillageInfoEnumHandler = require('./loanVillageInfoEnumHandler');
const loanVillageMyAreaInfoHandler = require('./loanVillageMyAreaInfoHandler');
const loanVillageInfoMyStatisticsHandler = require('./loanVillageInfoMyStatisticsHandler');

const {addHandlersForService} = require('../../utils/general')


class Service {
  constructor() {
    addHandlersForService.call(this,debug);
  }

  createOrEdit(){
    return [loanVillageInfoCreateOrEditHandler]
  }

  list(){
    return [loanVillageInfoListHandler,loanVillageInfoFormatHandler]
  }

  listAssistant(){
    return [loanVillageInfoAssistantListHandler]
  }

  loanVillageMyAreaInfo(){
    return [loanVillageMyAreaInfoHandler]
  }

  loanVillageStatistics(){
    return [loanVillageInfoMyStatisticsHandler]
  }

  enums(){
    return [loanVillageInfoEnumHandler];
  }


}

module.exports = new Service();
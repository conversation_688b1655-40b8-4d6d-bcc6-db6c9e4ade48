'use strict';

const HANDLER_NAME = 'loanVillageInfoMyStatisticsHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    loanVillageInfo:loanVillageInfoData,
    infoCollectHistory:infoCollectHistoryData,
    employees:employeesData,
    employeeGroups: employeeGroupData,
    dictAreas:dictAreaData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');
const { getEmployeeLimit } = require('../../utils/getUserFromReq')
const redisData = require('../../persistence/dataStore');
const moment = require('moment');
const config = require('config');
const superagent = require('superagent');
const {host,port} = config.get('credit_user_service_general');
const url = `http://${host}:${port}/api/v1.0/credit/rating/farmer/rating/statistics`;
const KEY_PREFIX_AREA_ = 'loanVillageInfoMyStatisticsDicArea_'
const KEY_PREFIX_EMPLOYEE_ = 'loanVillageInfoMyStatisticsEmployee_'
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:{areaCode,assistant:assistant2,tId,tId2:tIdGrade},opts,opts:{assistant,uId,roleId}} = this.context;
        debug.info(`${HANDLER_NAME}Paras`,assistant,uId,roleId,tId);

        const beginTime1 = Date.now();
        const condition = {} , defaultTid = '634e0d453064b9229aef516b';
        areaCode && ( condition.areaCode = { '$regex': `^${areaCode}.*`, '$options': 'si' } );
        assistant2 && ( condition.assistant = assistant2 );
        Object.keys( condition ).length === 0 && assistant && ( condition.assistant = assistant );
        assert( Object.keys( condition ).length , 'E_STATISTICES_001','请先登陆' );
        Object.assign( condition , {archived:false,limit:'unlimited'} );
        const result = await loanVillageInfoData.getByCondition(condition);
        const villages = Array.from( new Set( result.map(v=>v.areaCode) ) );
        const towns = Array.from( new Set( villages.map(v=>v.substr(0,9) ) ) );
        const assistants = Array.from( new Set( result.map(v=>v.assistant).filter(v=>v) ) );

        const beginTime2 = Date.now();
        const { employeesCount , myAreaCodes } = await this.getEmployeeInfo( uId , roleId , tId );
        const { townCount , villageCount } = await this.getDicInfo( myAreaCodes );
        // const allTownCodeByUser = Array.from( new Set( townCodeByUser.concat(villageCodeByUser.map( v=>v.substr(0,9) ) ) ) );

        debug.info(`${HANDLER_NAME}Cost`,Date.now() - beginTime2 , Date.now() - beginTime1 );
        debug.info(`${HANDLER_NAME}gradeInfoQuery`,townCount , villageCount,employeesCount,myAreaCodes);
        const {body:{data:gradeInfo}} = await superagent.post(url).set("TenantId", tIdGrade || tId || defaultTid ).send({ areacode:myAreaCodes});
        debug(`${HANDLER_NAME}gradeInfoResult`,gradeInfo);

        const res = {
            villageInfo: {count: villages.length, areaCodes: villages},
            townInfo: {count: towns.length, areaCodes: towns},
            assistants:{count: assistants.length, assistants },
            gradeInfo,//:{ farmerCount:0,ratingCount:0,notCount:0 },
            employeeInfo:{ employeesCount,townCount,villageCount }
        };

        this.context.result = res;
        // this.context.result = this.context.result || {list,uId,username};
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  async getEmployeeInfo(uId,roleId,tId){
    const info = [ uId , roleId , tId ].filter(v=>v).join('_') , key = `${KEY_PREFIX_EMPLOYEE_}${info}`;
    const existInfo = await redisData.pureGet(key)
    const cacheTime = Number( existInfo && existInfo.employeeCacheTime ) || 0 ;
    debug.info(`${HANDLER_NAME}RedisReadGetEmployeeInfo `,key , 'exist:' , existInfo , cacheTime , Date.now() - cacheTime );
    // 只缓存1分钟，否则管理员信息有可能更改
    if( existInfo && Math.abs( Date.now() - cacheTime ) < 60000 )return JSON.parse( existInfo );
    // KEY_PREFIX_EMPLOYEE_
    const myAreaCodeQuery = { limit:'unlimited', archived: false , employee:uId,group:roleId,...tId && { tId } || {} };
    const myAreaCodes = ( uId && roleId && await employeeGroupData.getByCondition( myAreaCodeQuery ) || [ {areaList:[]} ] )
        .reduce((r,v)=>r.concat(v.areaList||[]),[]).filter(v=>v);
    debug.info(`${HANDLER_NAME}myAreaCodes`,JSON.stringify(myAreaCodeQuery),myAreaCodes);
      // const { employeeGroups:employeeGroupsData } = require('./services/dataSvc/dataUtil') ;
      const ec = { group:'5eb8ff2ec6ecfe44d4ecaed9', areaList:myAreaCodes.join(',') , ...tId && { tId } || {}};
      const {total:employeesCount} = await employeeGroupData.getByUrl('/v1.0/employee/count',ec );
    // const ec = { group:'5eb8ff2ec6ecfe44d4ecaed9', limit:'unlimited',archived:false,...tId && { tId } || {}};
    // tId && ( ec.tId = tId );
    // ec.$or =  myAreaCodes.map(v=>({areaList:{ '$regex': `^${v}`}}));
    // const employeeTime = Date.now();
    // let employees = myAreaCodes.length && await employeeGroupData.getByCondition(ec) ||[];
    // debug.info(`${HANDLER_NAME}employeesCost`,Date.now()-employeeTime);
    debug.info(`${HANDLER_NAME}employeesQuery`,JSON.stringify(ec),employeesCount,myAreaCodes);
    // debug(`${HANDLER_NAME}employeesId`,employees.map(v=>v._id).join(','));
    // await Promise.all( employees.map(async v=>v.employeeRef = await employeesData.getById( v.employee , {cache : true , expire: 24 * 60 * 60 }) ) );
    // employees = employees.filter(v=>v.employeeRef && !v.employeeRef.isRevoked && !v.employeeRef.archived );
    // debug.info(`${HANDLER_NAME}employeesFilter`,employees.length);
    const result = { employeesCount , myAreaCodes , employeeCacheTime:Date.now() };
    const dataSourceStr = JSON.stringify(result);
    debug.info(`${HANDLER_NAME}RedisWriteGetEmployeeInfo `,key , 'exist:' , dataSourceStr  )
    await redisData.pureSet(key, dataSourceStr , { expire: 60 * 1000 })
    return result
  }

  async getDicInfo( myAreaCodes ){
    // 考虑到大部分场景，一个人员只会有一个区域，所以此处直接以排序后的组合为键值
    const codes = myAreaCodes.sort( (a,b)=>a.localeCompare( b ) ).join('_');
    const key = `${KEY_PREFIX_AREA_}${codes}`
    const existInfo = await redisData.pureGet(key);
    const cacheTime = Number( existInfo && existInfo.villageCacheTime ) || 0 ;
    debug(`${HANDLER_NAME}RedisReadGetDicInfo `,key , 'exist:' , existInfo , cacheTime , Date.now() - cacheTime );
    // if( existInfo && Math.abs( Date.now() - cacheTime ) < 30 * 60000 )return JSON.parse( existInfo );
    // 只要dic.area 表的数据不变，缓存就可以长期使用
    if( existInfo )return JSON.parse( existInfo );
    const dictQuery = { limit:'unlimited',archived:false,level:{$in:[4,5]} } ;
    dictQuery.$or = myAreaCodes.map(v=>({code:{ '$regex': `^${v}`}}));
    debug(`${HANDLER_NAME}dictQuery`,dictQuery)
    const dictListByUser = myAreaCodes.length && await dictAreaData.getByCondition( dictQuery ) || [];
    debug(`${HANDLER_NAME}dictQueryCode`,dictListByUser.map(v=>v.code));
    const townCodeByUser = Array.from( new Set( dictListByUser.filter(v=>v.level === 4 ).map(v=>v.code) ) );
    const villageCodeByUser = Array.from( new Set( dictListByUser.filter(v=>v.level === 5 ).map(v=>v.code) ) );
    const townCount = townCodeByUser.length, villageCount = villageCodeByUser.length;
    const result = { townCount , villageCount , villageCacheTime:Date.now() };
    const dataSourceStr = JSON.stringify(result)
    debug(`${HANDLER_NAME}RedisWriteGetDicInfo `,key , 'exist:' , dataSourceStr  )
    await redisData.pureSet(key, dataSourceStr , { expire: 30 * 60 * 1000 })
    return result
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
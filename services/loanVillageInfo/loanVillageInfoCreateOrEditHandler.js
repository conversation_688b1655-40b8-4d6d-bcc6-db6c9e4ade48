'use strict';

const HANDLER_NAME = 'loanVillageInfoCreateOrEditHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    employees:employeeData,
    employeeGroups:employeeGroupData,
    loanApplication:loanApplicationData,
    loanVillageInfo:loanVillageInfoData,
    groups:groupData,
    infoCollectHistory:infoCollectHistoryData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');
const moment = require('moment');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:one,opts:{uId:operator,roleId}} = this.context;
        assert( one && one.areaCode && one.areaCode.length >= 9 , 'E_VILLAGE_CREATE_001' , '村编码是必要的' );

        const {_id:group} = await groupData.getOneByCondition({archived:false,type:3,name:'BusinessAssistant'});//不会为空
        // // console.log('debug233',{archived:false,employee:app.accountManagerId,group})
        // const {employee:assistant} = await employeeGroupData.getOneByCondition( {archived:false,group,tId:'5cf4806e0a31202046ef6294',areaList:`/^${one.areaCode}/`} ) || {};
        // assert(assistant,'E_VILLAGE_CREATE_002','没找到该村的协理员！');
        const employees = ( await employeeGroupData.getByCondition( { archived:false,group,tId:'5cf4806e0a31202046ef6294',areaList:`/^${one.areaCode}/` , limit:'unlimited'} ) ).map(v=>v.employee);
        debug(`${HANDLER_NAME}EmployeeQuery`,JSON.stringify({ archived:false,group,tId:'5cf4806e0a31202046ef6294',areaList:`/^${one.areaCode}/` , limit:'unlimited'}),employees.map(v=>v.employee));
        one.assistant = one.assistant || employees.find(v=>1);
        // assert( one.assistant , 'E_VILLAGE_CREATE_003a', '协理员不存在' );
        one.assistant && assert( employees.includes(one.assistant) , 'E_VILLAGE_CREATE_003b', '协理员必须是本村的' );
        const old = await loanVillageInfoData.getOneByCondition({archived:false,areaCode:one.areaCode});
        !one._id && assert( !old , 'E_VILLAGE_CREATE_004' , '这个村的信息已经添加了' );

        one.cropTypes = one.cropTypes || [];
        one.industry = one.industry || [];
        one.operator = operator;
        one._id || ( one.createdTime = new Date());
        one.lastModTime = new Date();
        // debug('debug233',one._id,typeof one.status,one.status,moment(one.finishTime).format('YYYY-MM-DD hh:mm:ss'))
        one._id && Object.assign(one,await loanVillageInfoData.putById(one._id,one));
        one._id || Object.assign(one,await loanVillageInfoData.post(one));

        this.context.result = {success:'ok',one};
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
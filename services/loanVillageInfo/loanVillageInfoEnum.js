'use strict';

const HANDLER_NAME = 'loanVillageInfoEnum';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const cropTypeEnumList =
    [
        { label:'玉米' , value:'corn' },
        { label:'大豆' , value:'soybean' },
        { label:'水稻' , value:'rice' },
        { label:'其他' , value:'other' },
    ];
const cropTypeEnumDic = cropTypeEnumList.reduce( (r,v)=>( r[v.value]=v.label , r ) , {} );

module.exports = {
    cropTypeEnumList,cropTypeEnumDic,
}
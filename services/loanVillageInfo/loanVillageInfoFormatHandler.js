'use strict';

const HANDLER_NAME = 'loanVillageInfoFormattHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    loanApplicationCustomerVisit:loanApplicationCustomerVisitData,
    infoCollectHistory:infoCollectHistoryData,
    employees:employeeData,
    creditUserData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const moment = require('moment');
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const Decimal = require('decimal.js'),unitDenominator = 100,unitDenominator2 = 1000000;


class Handler extends BaseHandler {
    constructor(context) {
        super(context)
    }

    getName() {
        return HANDLER_NAME
    }

    async doAsync(done) {
        const method = `${this.getName()}.doAsync`
        debug(method, '[Enter]')
        try {
            const {opts:{formatList,formatOpts}} = this.context;

            await this.formatList(formatList || [],formatOpts||{});
            debug(method, '[Exit](success)');
            return done();
            } catch (error) {
            debug.error(method, '[Exit](failed)', error);
            return done(error);
        }
    }

    // async test1(){
    //     const areacode = '140829202205',status = '1';
    //     const result = await creditUserData.getByUrl(`/api/v1.0/credit/rating/farmer/rating/count/${areacode}/${status}`)
    //     console.log('debug233:',result)
    // }

    async formatList(list,formatOpts){
        await Promise.all(list.filter(v=>v).map(async (v,i)=>{

            // await this.formatOssFiles(v);
            const employee = v.operator && await employeeData.getById(v.operator, {cache : true , expire: 24 * 60 * 60 });
            v.employeeName = employee && employee.username;
            const assistant = v.assistant && await employeeData.getById(v.assistant, {cache : true , expire: 24 * 60 * 60 });
            v.assistantName = assistant && assistant.username;
            v.finishedCount = v.areaCode && await creditUserData.getByUrl(`/api/v1.0/credit/rating/farmer/rating/count/${v.areaCode}/${1}`) || 0
            v.areaInfo = v.areaCode && await formatAreaCode.getFormatAreaCode(v.areaCode);

            v.createdTime = moment(v.createdTime).format('YYYY-MM-DD HH:mm:ss');
            v.lastModTime = moment(v.lastModTime).format('YYYY-MM-DD HH:mm:ss');

        }));
        return list;
    }

    async formatOssFiles(v){
        // const {verifyInfo,requestInfo} = v && v.extendInfo || {verifyInfo:{}} ;
        // const handleSupplements = verifyInfo && verifyInfo.handleSupplements;
        const ossFiles = [
            // ...v.photos || []
        ];

        await Promise.all( ossFiles.filter(v=>v).map(formatImg) );
        return v;
    }

    undoAsync(done) {
        done()
    }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
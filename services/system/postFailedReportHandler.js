'use strict';

const HANDLER_NAME = 'PostFailedReportHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:system:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const failedReportsData = require('../dataSvc/dataUtil').failedReports;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let result = await failedReportsData.post(input);
      if (!result || !result._id) {
        throw {
          errorCode: 'E_FAILED_REPORT_030',
          httpCode: 406,
          reason: 'Create sysFailedReports record failed',
        }
      }
      self.context.result = result;

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
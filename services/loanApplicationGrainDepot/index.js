'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:grain:depot:index');
// const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const CreateOrEditGrainDepotHandler = require("./CreateOrEditGrainDepotHandler");
const LoanApplicationGrainDepotFormatHandler = require("./LoanApplicationGrainDepotFormatHandler");
const LoanApplicationGrainDepotListHandler = require("./LoanApplicationGrainDepotListHandler");

const {addHandlersForService} = require('../../utils/general')


class Service {
  constructor() {
    addHandlersForService.call(this,debug);
  }

  grainDepotList(){
    return [LoanApplicationGrainDepotListHand<PERSON>,LoanApplicationGrainDepotFormatHandler]
  }

  createGrainDepot(){
    return [CreateOrEditGrainDepotHandler];
  }


}

module.exports = new Service();
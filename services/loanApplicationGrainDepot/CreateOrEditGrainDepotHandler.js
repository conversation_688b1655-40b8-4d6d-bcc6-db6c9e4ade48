/**
 * @summary loanAppWorkflows
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'CreateOrEditGrainDepotHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const {BaseHandler} = require('nongfu.merchant.svcfw');
const LoanApplicationFundReceiveFormatHandler = require('./LoanApplicationGrainDepotFormatHandler');
const moment = require('moment');
const config = require('config');
const REPO_PACK_URL = `${config.get('repo_Service').host}/api/v1.0/file/cxwq/pay/token`;
const agent = require('superagent');
const Decimal = require('decimal.js'),unitDenominator = 100;

const {
  grainDepot:grainDepotData,

} = require('../dataSvc/dataUtil');

const {assert} = require('../../utils/general')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let {input:one,opts:{uId:operator}} = this.context;
      one.operator = operator;
      one.lastModTime = new Date();
      one._id && ( one = await grainDepotData.putById(one._id,one) );
      one._id || ( one = await grainDepotData.post(one) );

      this.context.result = {success:'ok',one}
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};

module.exports = Handler;

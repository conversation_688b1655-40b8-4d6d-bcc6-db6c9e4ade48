/*
 * @Author: wcy 
 * @Date: 2018-11-23 15:29:21 
 * @Last Modified by: wcy
 * @Last Modified time: 2018-12-10 11:45:35 
 */


'use strict';

const HANDLER_NAME = 'getHelpCenterDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:news:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const helpCentersDate = require('../dataSvc/dataUtil').helpCenters;
const moment = require('moment');
const config = require('config');
const BASE_URL = config.get('repoServer.host');
class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]');
    try {
      let id = self.context.input.id;
      let result = await helpCentersDate.getById(id);
      if (!result || !result._id) {
        throw {
          errorCode: 'E_NEWS_DETAILS_37',
          httpCode: 401,
          reason: 'data is not found'
        }
      }
      if (result.video && result.video.length > 0) {
        for (const item in result.video) {
          result.video[item] = BASE_URL.concat(result.video[item])
        }
      }
      result.createdTime = moment(result.createdTime).format("YYYY/MM/DD")
      await helpCentersDate.putById(id, {
        pageView: (~~result.pageView + 1)
      });
      debug(method, '[Exit](success)', result);
      self.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
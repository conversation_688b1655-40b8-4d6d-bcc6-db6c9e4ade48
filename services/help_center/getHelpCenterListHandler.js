/*
 * @Author: wcy 
 * @Date: 2018-11-23 15:29:21 
 * @Last Modified by: wcy
 * @Last Modified time: 2018-12-10 11:45:35
 */


'use strict';

const HANDLER_NAME = 'getNewsListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:news:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const helpCentersDate = require('../dataSvc/dataUtil').helpCenters;
const newsCatalogsData = require('../dataSvc/dataUtil').newsCatalogs
const moment = require('moment');
class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]');
    try {
      const condition = self.context.input
      let result = [];
      // 通过分类找到所有文章
      let catalogs = await newsCatalogsData.getByCondition({
        type: 2,
        tId: condition.tId,
        clientId: condition.cId,
        archived: false,
        $sort: {
          weight: 1,
          lastModTime: -1
        }
      });

      for (const item of catalogs) {
        let newList = {};
        newList.sectionTitle = item.name;
        newList.questionList = [];
        let news = await helpCentersDate.getByCondition({
          tId: condition.tId,
          clientId: condition.cId,
          archived: false,
          catalogId: item._id,
          status: 1,
          $sort: {
            weight: 1,
            lastModTime: -1
          }
        })
        newList.questionList = news;
        result.push(newList);
      }

      debug(method, '[Exit](success)', result);
      self.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
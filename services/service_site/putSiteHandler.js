'use strict';

const HANDLER_NAME = 'PutSiteHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:serviceSite:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const serviceSiteData = require('../dataSvc/dataUtil').serviceSite;
const { STATUS, STATUS_MAP } = require('../../utils/const/serviceSiteConst');
const notAllowStatus = [STATUS.WAIT_APPROVE_CITY, STATUS.WAIT_APPROVE_COUNTY];

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let { id, body } = input;

      let oldData = await serviceSiteData.getById(id);
      if (oldData && oldData.type == "village" && notAllowStatus.includes(oldData.status)) {
        throw {
          errorCode: 'E_UPDATE_SITE_030',
          httpCode: 406,
          reason: '当前状态下，村工作站不能修改'
        }
      }

      if ((body.type && body.type !== oldData.type) || (body.areaCode && body.areaCode !== oldData.areaCode)) {
        throw {
          errorCode: 'E_UPDATE_SITE_038',
          httpCode: 406,
          reason: '工作站类型和基本地区不能更新！'
        }
      }
      body.lastModTime = new Date();
      let result = await serviceSiteData.putById(id, body);
      self.context.result = result;

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
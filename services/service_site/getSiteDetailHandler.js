'use strict';

const HANDLER_NAME = 'getSiteDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:serviceSite:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const serviceSiteData = require('../dataSvc/dataUtil').serviceSite;
const villageData = require('../dataSvc/dataUtil').village;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let result;
      if (input.id) {
        result = await serviceSiteData.getById(input.id);
      } else {
        result = await serviceSiteData.getOneByCondition({ areaCode: input.areaCode, archived: false });
      }

      if (result && result.villageId) {
        let villages = await villageData.getByUrl("/v1.0/managedLoanVillage/list", { id: result.villageId });
        let village = villages && villages.result && villages.result[0] || {};
        if (village.loan) {
          village.loan.useAmountUpperLimit = village.loan.useAmountUpperLimit || false;
          village.loan.villageBalance = Math.max(village.loan.loanAmountUpperLimit - village.loan.loanedAmount, 0);
          let overLoan = village.allLoanApplications.filter(loan => loan.status.includes("finished"));
          village.loan.overCount = overLoan.length;
          village.loan.curCount = village.allLoanApplications.length - overLoan.length;
        }
        result.villageInfo = village;
      }
      self.context.result = result || {};

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'GetVillageDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:serviceSite:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const serviceSiteData = require('../dataSvc/dataUtil').serviceSite;
const villageData = require('../dataSvc/dataUtil').village;
const formatAreaCode = require('../../persistence/formatAreaCode');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let query = {};
      if (input.id) {
        query = { id: input.id };
      } else {
        query = { areaCode: input.areaCode };
      }

      let villages = await villageData.getByUrl("/v1.0/managedLoanVillage/list", query);
      let result = villages && villages.result && villages.result[0] || {};
      if (result) {
        if (result.areaCode) {
          let areaData = await formatAreaCode.getFormatAreaCode(result.areaCode)
          result.areaInfo = areaData || {};
        }
        if (result.loan) {
          result.loan.useAmountUpperLimit = result.loan.useAmountUpperLimit || false;
          result.loan.villageBalance = Math.max(result.loan.loanAmountUpperLimit - result.loan.loanedAmount, 0);
          let overLoan = result.allLoanApplications.filter(loan => loan.status.includes("finished"));
          result.loan.overCount = overLoan.length;
          result.loan.curCount = result.allLoanApplications.length - overLoan.length;
        }
      }

      self.context.result = result;

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'SubmitVillageSiteHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:serviceSite:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const serviceSiteData = require('../dataSvc/dataUtil').serviceSite;
const villageData = require('../dataSvc/dataUtil').village;
const { STATUS, STATUS_MAP } = require('../../utils/const/serviceSiteConst');
const siteTrackingData = require('../dataSvc/dataUtil').serviceSiteTracking;
const notAllowStatus = [STATUS.WAIT_APPROVE_CITY, STATUS.WAIT_APPROVE_COUNTY];

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let { destiner, areaCode } = input;

      let villageInfo = await villageData.getOneByCondition({ areaCode, archived: false });
      if (!villageInfo) {
        throw {
          errorCode: 'E_SUBMIT_VILLAGE_037',
          httpCode: 406,
          reason: '请先保存村基本信息'
        }
      }
      let site = await serviceSiteData.getOneByCondition({ areaCode, type: "village", archived: false });
      if (!site) {
        throw {
          errorCode: 'E_SUBMIT_VILLAGE_045',
          httpCode: 406,
          reason: '未找到该区域村级工作站'
        }
      }
      if (!site.contractImg || !Array.isArray(site.contractImg) || !site.contractImg.length) {
        throw {
          errorCode: 'E_SUBMIT_VILLAGE_052',
          httpCode: 406,
          reason: '合同材料缺失,不能提交审批'
        }
      }
      for (let item of site.contractImg) {
        if (!item.uploadTime || !item.signBeginTime) {
          throw {
            errorCode: 'E_SUBMIT_VILLAGE_060',
            httpCode: 406,
            reason: '合同签约信息缺失,不能提交审批'
          }
        }
      }
      if (notAllowStatus.includes(site.status)) {
        throw {
          errorCode: 'E_SUBMIT_VILLAGE_052',
          httpCode: 406,
          reason: '工作站正在处理中，不能再次提交审批'
        }
      }
      let payload = {
        status: STATUS.WAIT_APPROVE_COUNTY,
        action: "apply_open",
        lastModTime: new Date()
      }
      if (destiner) {
        payload.destiner = destiner;
      }
      let result = serviceSiteData.putById(site._id, payload);

      let tracking = {
        src_t: "staff",
        source: opts.employeeId,
        target_t: 1,
        target: site._id,
        action: STATUS.WAIT_APPROVE_COUNTY,
        type: "apply_open",
        comments: "待县域站长审批"
      }
      await siteTrackingData.post(tracking);

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
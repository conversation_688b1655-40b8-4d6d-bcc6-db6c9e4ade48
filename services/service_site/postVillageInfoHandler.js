'use strict';

const HANDLER_NAME = 'PostVillageInfoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:serviceSite:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const serviceSiteData = require('../dataSvc/dataUtil').serviceSite;
const villageData = require('../dataSvc/dataUtil').village;
const siteTrackingData = require('../dataSvc/dataUtil').serviceSiteTracking;
const { STATUS, STATUS_MAP } = require('../../utils/const/serviceSiteConst');
const notAllowStatus = [STATUS.WAIT_APPROVE_CITY, STATUS.WAIT_APPROVE_COUNTY];

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;

      let { areaCode, name, address } = input;
      let villageSite = await serviceSiteData.getOneByCondition({ areaCode, archived: false });
      if (villageSite && notAllowStatus.includes(villageSite.status)) {
        throw {
          errorCode: 'E_VILLAGE_INFO_042',
          httpCode: 406,
          reason: '当前状态下，村信息不能修改'
        }
      }

      let village = {};
      if (input._id) {
        let id = input._id;
        delete input._id;
        input.lastModTime = new Date();
        let old = await villageData.getById(id);
        if (!old) {
          throw {
            errorCode: 'E_VILLAGE_INFO_049',
            httpCode: 406,
            reason: '村id错误'
          }
        }
        if (input.areaCode && input.areaCode !== old.areaCode) {
          throw {
            errorCode: 'E_VILLAGE_INFO_056',
            httpCode: 406,
            reason: '不允许修改村位置'
          }
        }
        village = await villageData.putById(id, input);
      } else {
        let old = await villageData.getOneByCondition({ areaCode, archived: false });
        if (old) {
          village = await villageData.putById(old._id, input);
        } else {
          village = await villageData.post(input);
        }
      }
      if (!village._id) {
        throw {
          errorCode: 'E_VILLAGE_INFO_065',
          httpCode: 406,
          reason: '保存村信息失败'
        }
      }

      let nextAction = "apply_open";
      if (!villageSite) {
        let sitePayload = {
          name,
          areaCode,
          address,
          lngAndLat: input.lngAndLat,
          villageId: village._id,
          type: "village",
          status: STATUS.NEW,
          action: nextAction
        }
        villageSite = await serviceSiteData.post(sitePayload);

        let tracking = {
          src_t: "staff",
          source: opts.employeeId,
          target_t: 1,
          target: villageSite._id,
          action: STATUS.NEW,
          type: nextAction,
          comments: "初始状态，未提交审批"
        }
        await siteTrackingData.post(tracking);
      }

      let result = { village, villageSite };
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
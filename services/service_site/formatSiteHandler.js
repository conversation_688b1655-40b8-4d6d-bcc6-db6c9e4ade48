'use strict';

const HANDLER_NAME = 'FormatSiteHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:serviceSite:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const moment = require('moment');
const aliOssSvc = require('../aliOssSvc');
const serviceSiteData = require('../dataSvc/dataUtil').serviceSite;
const villageData = require('../dataSvc/dataUtil').village;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const employeesData = require('../dataSvc/dataUtil').employees;
const { STATUS, STATUS_MAP } = require('../../utils/const/serviceSiteConst');
const formatAreaCode = require('../../persistence/formatAreaCode');
const { async } = require('q');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }
      let citySite;
      if (input.type && input.type !== "city") {
        citySite = await serviceSiteData.getByCondition({ type: "city", archived: false });
      }
      for (let item of _result) {
        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss");
        item.branchCompanyName = "";
        if (citySite && citySite.length) {
          let branchCompany = citySite.find(city => city.areaCode == item.areaCode.substr(0, 4));
          item.branchCompanyName = branchCompany && branchCompany.name || "";
        }
        if (item.descImg) {
          item.descImg.forEach(img => formatImg(img));
        }
        if (item.materialImg) {
          item.materialImg.forEach(img => formatImg(img));
        }
        if (item.contractImg) {
          item.contractImg.forEach(async (item) => {
            if (item.image && item.image.length) {
              item.image.forEach(async (img) => (img.allUrl = await aliOssSvc.getFile({ fileName: img.url })))
            }
            if (item.operator !== "系统") {
              let employee = await employeesData.getById(item.operator);
              item.operator = employee && employee.username || "";
            }
            item.uploadTime = moment(item.uploadTime).format("YYYY-MM-DD");
          });
        }
        let areaData = await formatAreaCode.getFormatAreaCode(item.areaCode)
        item.areaInfo = areaData || {};
        if (item.villageId && !item.villageInfo) {
          item.villageInfo = await villageData.getById(item.villageId);
        }
        if (item.villageId) {
          const assist = await assistantersData.getOneByCondition({
            areaList: item.areaCode,
            flowStatus: "finish_sign",
            isRevoked: false,
            archived: false
          });
          item.destinerName = assist ? assist.username : '';
          item.destinerMobile = assist ? assist.mobile : '';
          item.destinerId = assist ? assist._id : '';
        }
        item.statusName = STATUS_MAP.get(item.status);
      }

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {
  item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0 &&
    (item.thumbnail.allUrl = await aliOssSvc.getFile({ fileName: item.thumbnail.url }));
  item && item.image && item.image.url && item.image.url.indexOf('http') !== 0 &&
    (item.image.allUrl = await aliOssSvc.getFile({ fileName: item.image.url }));
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'CloseVillageSiteHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:serviceSite:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const serviceSiteData = require('../dataSvc/dataUtil').serviceSite;
const siteTrackingData = require('../dataSvc/dataUtil').serviceSiteTracking;
const { STATUS, STATUS_MAP } = require('../../utils/const/serviceSiteConst');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let { id } = input;

      let oldData = await serviceSiteData.getById(id);
      if (oldData.type !== "village" || oldData.status !== STATUS.OPEN) {
        throw {
          errorCode: 'E_CLOSE_SITE_030',
          httpCode: 406,
          reason: '当前状态下，工作站不能关闭！'
        }
      }
      let nextAction = "apply_close"
      let result = await serviceSiteData.putById(id, {
        action: nextAction,
        status: STATUS.WAIT_APPROVE_COUNTY,
        lastModTime: new Date()
      });
      let tracking = {
        src_t: "staff",
        source: opts.employeeId,
        target_t: 1,
        target: id,
        action: STATUS.WAIT_APPROVE_COUNTY,
        type: nextAction,
        comments: "待县域站长审批"
      }
      await siteTrackingData.post(tracking);

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
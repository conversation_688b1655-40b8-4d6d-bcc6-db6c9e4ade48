'use strict';

const HANDLER_NAME = 'ApproveVillageSiteHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:serviceSite:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const serviceSiteData = require('../dataSvc/dataUtil').serviceSite;
const siteTrackingData = require('../dataSvc/dataUtil').serviceSiteTracking;
const { STATUS, STATUS_MAP } = require('../../utils/const/serviceSiteConst');
let allowStatus = [STATUS.WAIT_APPROVE_COUNTY, STATUS.WAIT_APPROVE_CITY];
let nextStatusMap = new Map([
  ["wait_approve_county:yes:apply_open", STATUS.WAIT_APPROVE_CITY],
  ["wait_approve_city:yes:apply_open", STATUS.OPEN],
  ["wait_approve_county:no:apply_open", STATUS.REJECTED_APPROVE_COUNTY],
  ["wait_approve_city:no:apply_open", STATUS.REJECTED_APPROVE_CITY],
  ["wait_approve_county:yes:apply_close", STATUS.WAIT_APPROVE_CITY],
  ["wait_approve_city:yes:apply_close", STATUS.CLOSE],
  ["wait_approve_county:no:apply_close", STATUS.OPEN],
  ["wait_approve_city:no:apply_close", STATUS.OPEN]
]);

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let { id, action: approve_action, rejectReason, comments } = input;

      let oldData = await serviceSiteData.getById(id);
      if (oldData.type !== "village" || !allowStatus.includes(oldData.status)) {
        throw {
          errorCode: 'E_APPROVE_SITE_030',
          httpCode: 406,
          reason: '当前状态不能审批！'
        }
      }
      let nextStatus = nextStatusMap.get(`${oldData.status}:${approve_action}:${oldData.action}`);
      if (!nextStatus) {
        throw {
          errorCode: 'E_APPROVE_SITE_044',
          httpCode: 406,
          reason: '审批失败'
        }
      }
      let result = await serviceSiteData.putById(id, {
        status: nextStatus,
        lastModTime: new Date()
      });

      let trackingAction = nextStatus;
      if (oldData.action == "apply_close" && approve_action == "no") {
        if (oldData.status == STATUS.WAIT_APPROVE_COUNTY) {
          trackingAction = STATUS.REJECTED_APPROVE_COUNTY
        } else if (oldData.status == STATUS.WAIT_APPROVE_CITY) {
          trackingAction = STATUS.REJECTED_APPROVE_CITY
        }
      }
      let tracking = {
        src_t: "staff",
        source: opts.employeeId,
        target_t: 1,
        target: id,
        action: trackingAction,
        type: oldData.action,
        comments: comments
      }
      if (rejectReason) {
        tracking.parameters = {
          rejectReason
        }
      }
      await siteTrackingData.post(tracking);

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
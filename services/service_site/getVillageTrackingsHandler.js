'use strict';

const HANDLER_NAME = 'GetSiteListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:serviceSite:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const moment = require('moment');
const aliOssSvc = require('../aliOssSvc');
const siteTrackingData = require('../dataSvc/dataUtil').serviceSiteTracking;
const employeesData = require('../dataSvc/dataUtil').employees;
const { STATUS, STATUS_MAP } = require('../../utils/const/serviceSiteConst');
const formatAreaCode = require('../../persistence/formatAreaCode');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let result = await siteTrackingData.getByCondition(input);
      for (let item of result) {
        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss");
        item.typeName = (item.type == "apply_open" ? "建站" : "关站");
        item.actionName = STATUS_MAP.get(item.action);
      }
      self.context.result = result;

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
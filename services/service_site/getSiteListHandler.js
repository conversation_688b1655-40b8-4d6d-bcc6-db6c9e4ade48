'use strict';

const HANDLER_NAME = 'GetSiteListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:serviceSite:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const serviceSiteData = require('../dataSvc/dataUtil').serviceSite;
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;
const TENANT_LIST = require('../../utils/tenantConst').TENANT_LIST

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let { employee, roleId } = self.context.opts;

      if (roleId) {
        let employeeGroup = await employeeGroupsData.getOneByCondition({
          employee,
          group: roleId,
          archived: false
        });
        if (!employeeGroup) {
          throw {
            errorCode: 'E_SITE_LIST_037',
            httpCode: 406,
            reason: '错误的用户id'
          }
        }

        let codeList = employeeGroup.areaList.map(code => { return { areaCode: `/^${code}/` } });
        if (!input.areaCode) {
          input.$or = codeList;
        }
      }
      let result = await serviceSiteData.getListAndCountByCondition(input);
      self.context.result = result;

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'GetAssistanceSubscribeListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin.loan.mgr.app.api:services:assistanceSubscribe:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistanceSubscribeData = require('../dataSvc/dataUtil').assistanceSubscribe;
const assistanceRequestData = require('../dataSvc/dataUtil').assistanceRequest;
const userData = require('../dataSvc/dataUtil').user;
const userVerifyData = require('../dataSvc/dataUtil').userVerifys;

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    const self = this;
    const method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      const condition = self.context.input;
      const subscribeList = await assistanceSubscribeData.getListAndCountByCondition(condition);

      subscribeList.result = await Promise.all(subscribeList.result.map(async (item) => {
        const user = await userData.getById(item.userId);
        const userVerify = await userVerifyData.getOneByCondition({
          uId: item.userId,
          archived: false,
        });
        const request = await assistanceRequestData.getOneByCondition({
          userId: item.userId,
          agentId: item.agentId,
        });
        return {
          ...item,
          area: request ? request.areaCode : '',
          userMobile: user ? user.mobile : '',
          userName: userVerify ? userVerify.realname : '',
        };
      }));

      self.context.result = subscribeList;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;

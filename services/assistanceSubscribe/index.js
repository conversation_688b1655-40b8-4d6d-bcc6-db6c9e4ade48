'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin.loan.mgr.app.api:services:assistanceSubscribe:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const FormatAreaCodeHandler = require('../loan_application_v2/baseOperations/formatAreaCodeHandler');
const GetAssistanceSubscribeListHandler = require('./getAssistanceSubscribeListHandler');
const GetAssistanceSubscribeDetailHandler = require('./getAssistanceSubscribeDetailHandler');
const RemoveAssistanceSubscribeHandler = require('./removeAssistanceSubscribeHandler');

class Service {
  constructor() {

  }

  async getAssistanceSubscribeList(input, _opts) {
    const method = 'getAssistanceSubscribeList';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetAssistanceSubscribeListHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getAssistanceSubscribeDetail(input, _opts) {
    const method = 'getAssistanceSubscribeDetail';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetAssistanceSubscribeDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async removeAssistanceSubscribe(input, _opts) {
    const method = 'removeAssistanceSubscribe';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new RemoveAssistanceSubscribeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();

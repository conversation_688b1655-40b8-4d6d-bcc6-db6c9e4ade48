/**
 * <AUTHOR>
 * 2019-05-05
 */

'use strict';

const HANDLER_NAME = 'getLoanFeedbackListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:loan_feedback:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanFeedbacksData = require('../dataSvc/dataUtil').loanFeedbacks;
const FEEDBACKTYPE = require('../../data/feedback').FEEDBACKTYPE
const moment = require('moment');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let result = await loanFeedbacksData.getByCondition(input)
      for (const item of result) {
        item.typeName = FEEDBACKTYPE.get(item.type).value;
        item.createdTime = moment(item.createdTime).utc().add(8, 'h').format('YYYY/MM/DD HH:mm:ss');
      }
      self.context.result = result || [];
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
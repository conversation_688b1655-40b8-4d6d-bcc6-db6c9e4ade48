/**
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'getLoanStatusFlowsHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:loan_feedback:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanFeedbacksData = require('../dataSvc/dataUtil').loanFeedbacks;
const loanAppData = require('../dataSvc/dataUtil').loanApplication;
const loanAppTrackingData = require('../dataSvc/dataUtil').loanApplicationTracking;

const FEEDBACKTYPE = require('../../data/feedback').FEEDBACKTYPE
const moment = require('moment');
const ALLOW_ACTIONS = [
  "loaned",
  "rejected_interview",
  "waitLoan",
  "rejected_final",
  "wait_income",
  "rejected_loan"
];

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let feedbacks = await loanFeedbacksData.getByCondition(input);
      let application = await loanAppData.getById(input.loanId);

      let condition = {
        action: { $in: ALLOW_ACTIONS },
        target: input.loanId,
        limit: input.limit,
        archived: false,
        $sort: input.$sort
      };
      let result = await loanAppTrackingData.getByCondition(condition);
      for (const item of result) {
        let parameters = item.parameters || {};
        if (parameters.incomingTime) {
          parameters.incomingTime = moment(parameters.incomingTime).format("YYYY-MM-DD");
        }
        if (parameters.processTime) {
          parameters.processTime = moment(parameters.processTime).format("YYYY-MM-DD");
        }
        item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
      }

      if (feedbacks && feedbacks.length) {
        for (const item of feedbacks) {
          item.typeName = FEEDBACKTYPE.get(item.type).value;
          item.action = "feedback";
          item.target = item.loanId;
          item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
        }

        result = feedbacks.concat(result);
      }

      self.context.result = result || [];
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
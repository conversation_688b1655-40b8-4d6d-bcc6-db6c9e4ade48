'use strict';

const HANDLER_NAME = 'getUserLoanRecordListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:activity:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const activityUsersData = require('../dataSvc/dataUtil').activityUsers;
const formatAreaCode = require('../../persistence/formatAreaCode');
const LOAN_APP_STATUS_MAP = require('../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const moment = require('moment');
const REPAY_TYPE = new Map([
  ['001', {
    code: '001',
    name: '等额本金'
  }],
  ['002', {
    code: '002',
    name: '等额本息'
  }],
  ['003', {
    code: '003',
    name: '一次还本付息'
  }],
  ['004', {
    code: '004',
    name: '先付息一次还本'
  }],
]);

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      // let result = {};
      let sort = input.$sort &&  JSON.parse(input.$sort) || {createdTime:-1};
      let data = await activityUsersData.getByUrl('/v1.0/activity/user/loan/record',{
        "IDCard":input.IDCard,
        skip:input.skip || 0,
        limit:input.limit || 10,
        $sort: sort
      });

      // let promise = [];
      for(let item of data.result){
        
        let TYPE = REPAY_TYPE.get(item.productInfo.repay_t);
        item.repay_t = TYPE && TYPE.name || "";
        item.loanInfo.status = LOAN_APP_STATUS_MAP.get(item.loanInfo.status);
        item.loanInfo.createdTime = moment(item.loanInfo.createdTime).format("YYYY-MM-DD HH:mm");
      }
      // await Promise.all(promise);
      
      self.context.result = data;
      debug(method, "[list] ", self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
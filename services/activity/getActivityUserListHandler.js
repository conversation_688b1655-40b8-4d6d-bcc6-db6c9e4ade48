'use strict';

const HANDLER_NAME = 'getNewListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:infoPersonal:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const activityEventData = require('../dataSvc/dataUtil').activityEvent;
const activityUsersData = require('../dataSvc/dataUtil').activityUsers;
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;
const formatAreaCode = require('../../persistence/formatAreaCode');
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      // let result = {};
      let sort = input.$sort &&  JSON.parse(input.$sort) || {createdTime:-1};
      let employees = await employeeGroupsData.getOneByCondition({group:input.gId,employee:opts.userid});
      let areaCodeList = employees ? employees.areaList : [];
      let areaCode = input.areaCode;
      if(!input.areaCode){
        areaCode = ''+areaCodeList.join('|') + '';
      }
      let data = await activityUsersData.getByUrl('/v1.0/activity/user/list',{
        "hasAmount":1,
        "startTime":input.startTime,
        "endTime":input.endTime,
        "fastLoan":input.fastLoan,
        "survey":input.survey,
        "landRecord":input.landRecord,
        "name":input.name,
        "mobile":input.mobile,
        "areaCode":areaCode,
        "activityStartTime":input.activityStartTime,
        "activityEndTime":input.activityEndTime,
        skip:input.skip || 0,
        limit:input.limit || 10,
        $sort: sort
      });

      // let promise = [];
      for(let item of data.result){
        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm");
        item.areaName = '';
        let areaName = await formatAreaCode.getFormatAreaCode(item.areaCode);
        // item.IDCard = item.IDCard? item.IDCard[0]:"";
        // item.IDCard = item.IDCard.substring(0,2) + '**************' + item.IDCard.substring(16)
        // debug(method, '[Enter]areaName',areaName)
        item.areaName = areaName;
        // item.city = areaName.region.city;
      }
      // await Promise.all(promise);
      
      self.context.result = data;
      debug(method, "[list] ", self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
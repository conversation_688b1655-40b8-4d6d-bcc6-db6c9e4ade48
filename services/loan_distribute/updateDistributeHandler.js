'use strict';

const HANDLER_NAME = 'UpdateDistributeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:loan_distribute:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanDistributeData = require('../dataSvc/dataUtil').loanDistribute;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let tacking = await loanDistributeData.getOneByCondition({"assigned":input.distribute,"aId":input.aId,"handleStatus":false});
      debug(method, '[loanDistributeData](tacking)', tacking);
      if(tacking && tacking._id){
        let payload = {};
        payload.handleStatus = true;
        payload.handleTime = new Date();
        let data = await loanDistributeData.putById(tacking._id, payload);
        // self.context.result = data;
        debug(method, '[Exit](success)', data);
      }
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
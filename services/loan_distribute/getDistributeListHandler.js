'use strict';

const HANDLER_NAME = 'GetDistributeListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:loan_distribute:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const managerDistributeData = require('../dataSvc/dataUtil').managerDistribute;
const loanProduct = require('../dataSvc/dataUtil').loanProducts;
const APP_STATUS_MAP = require('../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const WDPRODUCTS = require('../../data/products').WDPRODUCTS
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let data = await managerDistributeData.getByCondition(condition);
      let items = [];
      let promise = [];
      for (let item of data.result) {
        let tmpItem = {};
        if (!item.loan[0]) {
          continue;
        }

        tmpItem.aId = item.loan[0]._id;
        tmpItem.sn = item.loan[0].sn;
        
        let product = WDPRODUCTS.get(item.loan[0].pId);
        promise.push(loanProduct.getById(item.loan[0].pId, {cache : true , expire: 24 * 60 * 60 }).then(data => {
          tmpItem.productName = data.name;
          tmpItem.product = data;
          
          if (product) {
            tmpItem.productType = product.name;
            tmpItem.type = product.type;
          }
        }))

        tmpItem.amount = item.loan[0].amount /100;
        tmpItem.username = item.loan[0].username;
        tmpItem.userMobile = item.loan[0].userMobile;
        tmpItem.createdTime = moment(item.loan[0].createdTime).format("YYYY-MM-DD HH:mm");
        let handleAssign = {"createdTime":""};
        for(let val of item.distribute){
          if(val.createdTime > handleAssign.createdTime){
            handleAssign = val;
          }
        }
        tmpItem.assignStatus = handleAssign.assignStatus;
        tmpItem.status = item.loan[0].status;
        tmpItem.statusChName = APP_STATUS_MAP.get(item.loan[0].status);
        tmpItem.groupName = '';
        
        for(let gVal of item.group_v2){
          if(handleAssign.gId == gVal._id){
            tmpItem.groupName = gVal.name;
          }
        }
        for(let eVal of item.employee){
          if(handleAssign.assigned == eVal._id){
            tmpItem.employee = eVal.username;
          }
        }
        items.push(tmpItem);
      }
      await Promise.all(promise);
      data.result = items;
      self.context.result = data;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'getStatisticsDistributeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loan_assistant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanDistributeData = require('../dataSvc/dataUtil').loanDistribute;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let opts = self.context.opts;
      let userid = self.context.input.userid;
      let gId = self.context.input.gId;
      let roleId = self.context.input.roleId;
      let condition = {
        assigned: userid,
        gId: gId,
        roleId: roleId,
        handleStatus: false,
        archived: false
      }
      const pending = await loanDistributeData.getCountByCondition(condition);
      
      condition = {
        assigned: userid,
        roleId: roleId,
        handleStatus: true
      }
      const processed = await loanDistributeData.getCountByCondition(condition);
      self.context.result = {
        pending: pending.count,
        processed: processed.count
      };
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
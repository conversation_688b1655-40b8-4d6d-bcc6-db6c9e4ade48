'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:loan_distribute:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const CreateDistributeHandler = require("./createDistributeHandler");
const UpdateDistributeHandler = require("./updateDistributeHandler");
const GetDistributeListHandler = require("./getDistributeListHandler");
const PostLoanTrackingHandler = require("./postLoanTrackingHandler");
const GetStatisticsDistributeHandler = require("./getStatisticsDistributeHandler");

class Service {
  constructor() {

  }

  async getDistributeList(input, _opts) {
    let method = 'getDistributeList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetDistributeListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async createDistributeProcess(input, _opts) {
    let method = 'createDistributeProcess';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new UpdateDistributeHandler(context));
      svcHandlerMgrt.addHandler(new CreateDistributeHandler(context));
      svcHandlerMgrt.addHandler(new PostLoanTrackingHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getStatisticsDistribute(input, _opts) {
    let method = 'getStatisticsDistribute';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetStatisticsDistributeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

}

module.exports = new Service();
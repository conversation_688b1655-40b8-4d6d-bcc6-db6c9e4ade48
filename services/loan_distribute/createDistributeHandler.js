'use strict';

const HANDLER_NAME = 'CreateDistributeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:loan_distribute:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanDistributeData = require('../dataSvc/dataUtil').loanDistribute;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      
      let tacking = await loanDistributeData.getOneByCondition({"aId":condition.aId,"handleStatus":false});
      if(tacking && tacking._id){
        throw {
          errorCode: 'E_DISTRIBUTE_30',
          httpCode: 406,
          reason: 'Repeated'
        }
      }
      let data = await loanDistributeData.post(condition);
      self.context.result = data;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'PostLoanTrackingHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:loan_distribute:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationTrackingData = require('../dataSvc/dataUtil').loanApplicationTracking;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let opts = self.context.opts;
      let input = self.context.input;
      let trackData = {
          "src_t":"staff",
          "target_t":1,
          "target":input.aId,
          "action":'distribute_'+input.assignStatus,
          "parameters":self.context.result,
          "source":input.distribute,
          "comments":''
      };
      let data = await loanApplicationTrackingData.post(trackData);
      // self.context.result = data;
      debug(method, '[Exit](success)', data);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
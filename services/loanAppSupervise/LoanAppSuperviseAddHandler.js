/**
 * @summary LandOriginFundReceiveConfirmHandler
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'LoanAppSuperviseAddHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const { BaseHandler } = require('nongfu.merchant.svcfw');
const {
  loanApplication: loanApplicationData,
} = require('../dataSvc/dataUtil');

const { assert } = require('../../utils/general')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input: { id, superviseInfo }, opts: { uId } } = this.context; //没填就默认是玉米
      assert(uId, 'E_LAND_APP_SUPERVISE_ADD_001', 'uId is required');
      assert(id, 'E_LAND_APP_SUPERVISE_ADD_002', 'id is required');
      const app = await loanApplicationData.getById(id);
      assert(app && app.uId === uId, 'E_LAND_APP_SUPERVISE_ADD_003', 'id is err');
      assert(Array.isArray(superviseInfo) && superviseInfo.length, 'E_LAND_APP_SUPERVISE_ADD_004', 'superviseInfo must be array and size must more than zero');
      assert(superviseInfo.every(v => v.cropType), 'E_LAND_APP_SUPERVISE_ADD_005', 'cropType in superviseInfo is required');
      const cropTypes = superviseInfo.map(v => v.cropType),
        allCropTypes = Object.keys(app.verifyInfo.assuranceInfo.data);
      assert(Array.from(new Set(cropTypes)).length === cropTypes.length, 'E_LAND_APP_SUPERVISE_ADD_006', 'cropType in superviseInfo cant duplicate');
      assert(cropTypes.every(v => allCropTypes.includes(v)), 'E_LAND_APP_SUPERVISE_ADD_007', 'cropType in superviseInfo is unknow in this app');

      Object.assign(app.verifyInfo, { superviseInfo });
      const update = {
        'verifyInfo.superviseInfo': superviseInfo,
        'verifyInfo.superviseExtendInfo.superviseStatus': 'hadRequest',
        'verifyInfo.superviseExtendInfo.hadRequest': true
      }
      await loanApplicationData.putById(id, update);

      // const list = result.result ;

      this.context.result = { app, success: 'ok' };
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};



// {
//     'name': 'fundReceiveInfo',
//     'type': `{
//           payBank: String,
//           payAccountNo: String,
//           amount: Number,
//           payAccountName: String,
//           remark: String,
//           validateBankAccount: Number,
//         }`,
//     'required': false,
//     'description': `fundReceiveInfo 支用申请信息
//     payBank 开户行
//     payAccountNo 银行账号
//     amount 支付金额（分）
//     payAccountName 出让方姓名
//     remark 备注
//     validateBankAccount 三要素验证 1 未验证 2 通过 3 未通过
//     `,
//     comma: true
// },
// {
//     'name': 'fundReceiveStatus',
//     'type': '{type: String, enum: ["unused","new", "verify", "finished","closed"]}',
//     'required': true,
//     'description': '土地补贴享有方 1出让方 2受让方',
//     comma: true
// },
// {
//     'name': 'fundReceiveId',
//     'type': 'Schema.Types.ObjectId',
//     'required': false,
//     'description': `支用关联ID`,
//     comma: true
// },


module.exports = Handler;
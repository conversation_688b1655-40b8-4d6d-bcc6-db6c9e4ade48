/**
 * @summary LandOriginFundReceiveConfirmHandler
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'LoanAppGrainListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const {BaseHandler} = require('nongfu.merchant.svcfw');
const {
  loanApplicationGrainSale:loanApplicationGrainSaleData,
  employeeGroups:employeeGroupData,
} = require('../dataSvc/dataUtil');
const { getEmployeeLimit } = require('../../utils/getUserFromReq');


const {assert} = require('../../utils/general')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {input:condition,opts,opts:{getOne}} = this.context;//没填就默认是玉米

      debug(`${HANDLER_NAME}findAreaLimitAreaPara`,opts.uId || 'null', opts.tId  || null,opts.roleId||'null');
      const areaCodeLimitOr = opts.uId && opts.tId && await getEmployeeLimit(employeeGroupData, opts.tId, opts.uId, 'requestAreaCode', opts.roleId);
      opts.uId && (condition['$or'] = areaCodeLimitOr);
      debug(`${HANDLER_NAME}findAreaLimitAreaCode`,condition['$or'] || 'null');

      const result = await loanApplicationGrainSaleData.getListAndCountByCondition(condition);

      const formatOpts = { appInfo:getOne };
      Object.assign(opts,{formatList:result.result,formatOpts});
      getOne && assert( result.result && result.result.length , 'E_LAND_APP_GRAIN_SALE_LIST_001','不存在的记录' )
      this.context.result = result;
      getOne && ( this.context.result = result.result[0] )
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};



// {
//     'name': 'fundReceiveInfo',
//     'type': `{
//           payBank: String,
//           payAccountNo: String,
//           amount: Number,
//           payAccountName: String,
//           remark: String,
//           validateBankAccount: Number,
//         }`,
//     'required': false,
//     'description': `fundReceiveInfo 支用申请信息
//     payBank 开户行
//     payAccountNo 银行账号
//     amount 支付金额（分）
//     payAccountName 出让方姓名
//     remark 备注
//     validateBankAccount 三要素验证 1 未验证 2 通过 3 未通过
//     `,
//     comma: true
// },
// {
//     'name': 'fundReceiveStatus',
//     'type': '{type: String, enum: ["unused","new", "verify", "finished","closed"]}',
//     'required': true,
//     'description': '土地补贴享有方 1出让方 2受让方',
//     comma: true
// },
// {
//     'name': 'fundReceiveId',
//     'type': 'Schema.Types.ObjectId',
//     'required': false,
//     'description': `支用关联ID`,
//     comma: true
// },


module.exports = Handler;

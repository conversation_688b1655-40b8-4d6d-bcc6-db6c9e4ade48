'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:index');
// const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const LoanAppSuperviseAddHandler = require("./LoanAppSuperviseAddHandler");
const LoanAppGrainListHandler = require("./LoanAppGrainListHandler");
const LoanAppGrainSaleAddHandler = require("./LoanAppGrainSaleAddHandler");
const LoanAppGrainSaleFormatHandler = require("./LoanAppGrainSaleFormatHandler");
const SendSMShandler = require("./sendSMShandler");

const { addHandlersForService } = require('../../utils/general')


class Service {
  constructor() {
    addHandlersForService.call(this, debug);
  }

  loanAppSuperviseAdd() {
    return [LoanAppSuperviseAddHandler, SendSMShandler]
  }


  loanAppGrainList() {
    return [LoanAppGrainListHandler, LoanAppGrainSaleFormatHandler]
  }


  loanAppGrainSaleAdd() {
    return [LoanAppGrainSaleAddHandler]
  }


}

module.exports = new Service();
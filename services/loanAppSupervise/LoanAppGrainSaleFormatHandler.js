'use strict';

const HANDLER_NAME = 'LoanAppGrainSaleFormatHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    fundReceive:fundReceiveData,
    userVerifys:userVerifyData,
    employees:employeeData,
    cxwqCirculations:cxwqCirculationData,
    cxwqCirculationFundReceive:cxwqCirculationFundReceiveData,
    userVerifys:userVerifysData,
    loanLand:loanLandData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const moment = require('moment');
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const Decimal = require('decimal.js'),unitDenominator = 100;

class Handler extends BaseHandler {
    constructor(context) {
        super(context)
    }

    getName() {
        return HANDLER_NAME
    }

    async doAsync(done) {
        const method = `${this.getName()}.doAsync`
        debug(method, '[Enter]')
        try {
            const {opts:{formatList,formatOpts}} = this.context;
            await this.formatList(formatList || [],formatOpts||{});
            debug(method, '[Exit](success)');
            return done();
            } catch (error) {
            debug.error(method, '[Exit](failed)', error);
            return done(error);
        }
    }

    async formatList(list,formatOpts={}){
        await Promise.all(list.filter(v=>v).map(async (v,i)=>{
            //先把架阤搭起来，如果将来需要可以填充
            // v.no = i+1;
            // v.actionName = actionNameDic[v.action];
            // v.statusText = statusText[v.status];
            // v.requestTypeName = COLLECT_COMPANY_TYPE_TEXT_MAP[v.requestType];
            v.createdTime = moment(v.createdTime).format('YYYY-MM-DD HH:mm:ss');
            v.lastModTime = moment(v.lastModTime).format('YYYY-MM-DD HH:mm:ss');
            v.areaInfo = v.requestAreaCode && await formatAreaCode.getFormatAreaCode(v.requestAreaCode);
            formatOpts.appInfo && ( v.app = v.aId && await loanApplicationData.getById( v.aId ) );
            const userVerify = v.app && await userVerifysData.getOneByCondition({uId:v.app.uId,IDCardStatus:"approved",archived:false});
            userVerify && ( v.idCard = userVerify.IDCard);
            await this.formatOssFiles(v);
            // v.payTime = v.payTime && moment(v.payTime).format('YYYY-MM-DD HH:mm:ss');
            // v.amount && ( v.amount = new Decimal(v.amount).div(unitDenominator).toString() );
            // v.payAmount && ( v.payAmount = new Decimal(v.payAmount).div(unitDenominator).toString() );
            // const { payTokenInfo={},bankPayInfo={},requestInfo=[]} = v.extendInfo && v.extendInfo.verifyInfo || {};
            // payTokenInfo.payAmount && ( payTokenInfo.payAmount = new Decimal(payTokenInfo.payAmount).div(unitDenominator).toString() );
            // bankPayInfo.payAmount && ( bankPayInfo.payAmount = new Decimal(bankPayInfo.payAmount).div(unitDenominator).toString() );
            // requestInfo.forEach(r=>{
            //     r.price && ( r.price = new Decimal(r.price).div(unitDenominator).toString() );
            //     r.amount && ( r.amount = new Decimal(r.amount).div(unitDenominator).toString() );
            // });
            //
            //
            // const app = v.aId && await loanApplicationData.getById(v.aId);
            // v.app = app;
            //
            // const employee = v.operator && await employeeData.getById(v.operator);
            // v.employeeName = employee && employee.username;
            //
            // v.areaInfo = v.requestAreaCode && await formatAreaCode.getFormatAreaCode(v.requestAreaCode);
            // await Promise.all((v.verifyRecord || []).map(async item=>{
            //     item.actionText = `${statusText[item.status]}${actionText[item.action]}`;
            //     item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
            //     item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss');
            //     const employee = item.operator && await employeeData.getById(item.operator);
            //     item.employeeName = employee && employee.username;
            // }));
            //
            // debug('debug233',formatOpts)
            // formatOpts.amount && await this.formatAmount(v);
            // formatOpts.appList && await this.formatNotCloseAppList(v);
        }));

    }

    async formatOssFiles(v){
        const ossFiles = ['contractPhotos','noticePhotos','voucherPhotos','otherPhotos']
            .map(k=>v[k]).filter(v=>v).reduce((r,v)=>r.concat(v),[]);
        await Promise.all( ossFiles.filter(v=>v).map(formatImg) );
    }

    undoAsync(done) {
        done()
    }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
/**
 * @summary LandOriginFundReceiveConfirmHandler
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'LoanAppGrainSaleAddHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const {BaseHandler} = require('nongfu.merchant.svcfw');
const {
  loanApplication:loanApplicationData,
  loanApplicationGrainSale:loanApplicationGrainSaleData,
  infoCollectHistory:infoCollectHistoryData,
} = require('../dataSvc/dataUtil');
const moment = require('moment');

const {assert} = require('../../utils/general')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {input:{sale,sale:{aId,cropType}},opts:{uId}} = this.context;//没填就默认是玉米
      assert(aId,'E_LAND_APP_GRAIN_SALE_ADD_001','aId is required');
      assert(uId,'E_LAND_APP_GRAIN_SALE_ADD_002','uId is required');
      const app = await loanApplicationData.getById(aId);
      assert(app && app.uId === uId,'E_LAND_APP_GRAIN_SALE_ADD_003','id is err');
      const allCropTypes = Object.keys( app.verifyInfo.assuranceInfo.data );
      assert( allCropTypes.includes(cropType) , 'E_LAND_APP_GRAIN_SALE_ADD_007','cropType in superviseInfo is unknow in this app');
      const infoId = app && app.addons && app.addons.info && app.addons.info.mId;
      const info = infoId && await infoCollectHistoryData.getById( infoId );
      sale.status = '0';
      sale.username = app.username
      sale.requestName = info && info.name;
      sale.requestMobile = app.userMobile;
      sale.requestAreaCode = app.area;
      sale.actualLoanAmount = `${app.actualLoan}`;
      sale.appSn = app.sn;
      sale.requestCompanyName = app.enterpriseName || ""
      sale.requestType = info && info.type || app.consumer_t;
      sale._id || ( sale.comments = [{username:app.username,content:'新申请',type:'新申请',operateTime:moment().format('YYYY-MM-DD hh:mm:ss')}] );
      sale.lastModTime = new Date();
      sale._id || Object.assign( sale , await loanApplicationGrainSaleData.post(sale) );
      sale._id && Object.assign( sale , await loanApplicationGrainSaleData.putById(sale._id,sale) );
      this.context.result = {sale,success:'ok'};
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};



// {
//     'name': 'fundReceiveInfo',
//     'type': `{
//           payBank: String,
//           payAccountNo: String,
//           amount: Number,
//           payAccountName: String,
//           remark: String,
//           validateBankAccount: Number,
//         }`,
//     'required': false,
//     'description': `fundReceiveInfo 支用申请信息
//     payBank 开户行
//     payAccountNo 银行账号
//     amount 支付金额（分）
//     payAccountName 出让方姓名
//     remark 备注
//     validateBankAccount 三要素验证 1 未验证 2 通过 3 未通过
//     `,
//     comma: true
// },
// {
//     'name': 'fundReceiveStatus',
//     'type': '{type: String, enum: ["unused","new", "verify", "finished","closed"]}',
//     'required': true,
//     'description': '土地补贴享有方 1出让方 2受让方',
//     comma: true
// },
// {
//     'name': 'fundReceiveId',
//     'type': 'Schema.Types.ObjectId',
//     'required': false,
//     'description': `支用关联ID`,
//     comma: true
// },


module.exports = Handler;

'use strict';

const HANDLER_NAME = 'sendSMShandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const MsgSvcRegistry = require('nongfu.merchant.msgfw').MsgSvcRegistry.INSTANCE;
const applicationData = require('../dataSvc/dataUtil').loanApplication;
let templateId = "SMS_234397434"
const smsALiYunDispatcher = require('../messages/dispatchers/smsALiYunDispatcher');
const employeeData = require('../dataSvc/dataUtil').employees;
const employeeGroupData = require('../dataSvc/dataUtil').employee_groups;
const dictAreasData = require('../dataSvc/dataUtil').dictAreas;



class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;


      let application = await applicationData.getById(input.id);
      if (!application) {
        throw {
          errorCode: 'E_SEND_SMS_037',
          httpCode: 406,
          reason: '订单不存在'
        }
      }
      let area = application.area.substring(0, 6);
      let city = await dictAreasData.getOneByCondition({ code: area, archived: false });
      //发送短信
      let SmsALiYunDispatcher = MsgSvcRegistry.getDisptcher(smsALiYunDispatcher.QNAME);
      let content = {
        templateid: templateId,
        param: JSON.stringify({
          city: city.name || "",
          name: application.username
        }),
        signName: "黑龙江省创新农业物权",
        caller: "rongxin_userapp"
      };

      // 监管部工作人员 产品说先写死相关人员的手机号
      let sendMobile = ["13304610456", "13359719888", "18646751701", "17382867127", "13267842165"];


      // 查询出跟订单想匹配的 县域中心工作人员 (客户经理)
      let employeeGroup = await employeeGroupData.getByCondition({
        role: "5eb8fee1c6ecfe44d4ecaed1",
        areaList: area,
        archived: false
      })
      let promises = [];
      for (let item of employeeGroup) {
        promises.push(employeeData.getOneByCondition({ "_id": item.employee, isRevoked: false, archived: false }).then(data => {
          sendMobile.push(data.mobile)
        }))
      }
      await Promise.all(promises);

      promises = [];
      sendMobile.forEach(item => {
        promises.push(SmsALiYunDispatcher.send_Sms(item, content, {}).then(data => {
          debug(method, `Sms msg sent ${item} :`, data);
        }))
      })

      await Promise.all(promises);



      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
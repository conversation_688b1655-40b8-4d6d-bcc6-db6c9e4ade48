/**
 * <AUTHOR>
 * 2019-09-22
 */

'use strict';

const HANDLER_NAME = 'getMsgQueueUnreadHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:msgQueue:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const msgQueueData = require('../dataSvc/dataUtil').msgQueue;
const msgStoreData = require('../dataSvc/dataUtil').msgStore;
const devicesData = require('../dataSvc/dataUtil').devices;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let uId = input.uId;
      let device = await devicesData.getByCondition({ uId: uId, clientId: input.clientId });
      if (device.length < 1) {
        throw {
          httpCode: 406,
          errorCode: 'M_Queue_Hanler_037',
          reason: 'NOT find device'
        }
      }
      let target = [];
      for (const key of device) {
        target.push(key._id)
      }

      // 用户设备可能不唯一
      let result = await msgQueueData.getCountByCondition({ target: { $in: target }, read: 1, archived: false, clientId: input.clientId });

      self.context.result = result || {};
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
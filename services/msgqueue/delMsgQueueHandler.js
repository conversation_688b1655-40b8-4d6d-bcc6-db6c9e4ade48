/**
 * <AUTHOR>
 * 2019-09-22
 */

'use strict';

const HANDLER_NAME = 'delMsgQueueHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:msgQueue:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const msgQueueData = require('../dataSvc/dataUtil').msgQueue;


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let _result = await msgQueueData.putById(input.id, { archived: true, target: input.uId })
      if (!_result) {
        throw {
          errorCode: 'M_Queue_035',
          httpCode: 406,
          reason: '删除消息失败'
        }
      }
      const result = {
        del: 1,
        status: "SUCCESS"
      }
      self.context.result = result;
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
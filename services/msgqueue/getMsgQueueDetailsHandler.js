/**
 * <AUTHOR>
 * 2019-09-22
 */

'use strict';

const HANDLER_NAME = 'getMsgQueueListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:msgQueue:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const msgQueueData = require('../dataSvc/dataUtil').msgQueue;
const msgStoreData = require('../dataSvc/dataUtil').msgStore;
const devicesData = require('../dataSvc/dataUtil').devices;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let _result = await msgQueueData.putById(input.id, {
        read: 3
      })
      if (!_result) {
        throw {
          errorCode: 'M_Queue_Detail_035',
          httpCode: 406,
          reason: '消息已读设置失败'
        }
      }

      let result = await msgStoreData.getById(_result.msgRef);

      self.context.result = {
        title: result.payload.title || '',
        context: result.payload.text || '',
        createdTime: result.createdTime
      };
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
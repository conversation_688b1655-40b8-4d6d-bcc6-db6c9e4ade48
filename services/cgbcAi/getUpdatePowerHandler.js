/*
 * @Description: 获得协理员是否还可以更新农户信息
 * @Author: zhu xue song
 * @Date: 2021-11-04 16:53:47
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-11-05 15:25:59
 * @FilePath: \rongxin.loan.mgr.app.api\services\cgbcAi\synchronizationHandler.js
 */
/**
 * 
 * <AUTHOR>
 */

 'use strict';

 const HANDLER_NAME = 'getUpdatePowerHandler';
 const logFactory = require('../../utils/logFactory');
 const logUtil = require('../../utils/logUtil');
 const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:cgbcAi:' + HANDLER_NAME);
 const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
 const infoFarmer = require('../dataSvc/dataUtil').infoFarmer;

 const UpdateLimit = 2; // 最多修改次数
 
 class Handler extends BaseHandler {
   constructor(context) {
     super(context)
   }
 
   getName() {
     return HANDLER_NAME
   }
 
   async doAsync(done) {
     let self = this;
     let method = `${self.getName()}.doAsync`
     debug(method, '[Enter]')
     try {
       const {id} = self.context.input;
       const farmerInfo = await infoFarmer.getById(id);
       let flag = false;
       if(farmerInfo) {
        if(farmerInfo.updateCount < UpdateLimit) {
          flag = true;
        }
       }
       self.context.result = { isCanUpdate: flag };
       debug(method, '[Exit](success)', self.context.result);
       return done();
     } catch (error) {
       debug.error(method, '[Exit](failed)', error);
       return done(error);
     }
   }
 
   undoAsync(done) {
     done()
   }
 }
 
 module.exports = Handler
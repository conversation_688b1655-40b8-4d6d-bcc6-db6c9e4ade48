/*
 * @Description: 大农金得请求接口封装
 * @Author: zhu xue song
 * @Date: 2021-11-08 10:22:23
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-11-08 13:58:31
 * @FilePath: \rongxin.loan.user.app.api\services\cgbcAi\cgbcRequest.js
 */
const superagent = require('superagent');
const config = require('config');
const host = 'https://aijd1.jlncjf.com:9978'; 

module.exports = {

  /**
   * 获取协理员待办理业务列表
   */
  getCheckList: async (body) => {
    try {
      const res = await superagent.post(`${host}/api/blade-business/njf/farmerbaseinfo/getEvaluateList`)
      .type('form')
      .send(body)
      return res ? res.body.data : null;
    } catch (err) {
      throw {
        errorCode: 406,
        errorMsg: 'request danongjin error'
      }
    }
  },
}
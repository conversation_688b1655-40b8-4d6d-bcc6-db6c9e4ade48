/*
 * @Description: 回调更新农户信息操作
 * @Author: zhu xue song
 * @Date: 2021-11-04 16:53:47
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-11-05 15:25:59
 * @FilePath: \rongxin.loan.user.app.api\services\cgbcAi\synchronizationHandler.js
 */
/**
 * 
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'getCheckListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:cgbcAi:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const cgbcRequest = require('./cgbcRequest');
// const tId_jinfu = '5fb364d400ad51b9e3efc702';
const tId_jinfu = 'EGCEAA';

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {areaCode} = self.context.input;
      const result = await cgbcRequest.getCheckList({ tenantId: tId_jinfu, xlyArea: areaCode })
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
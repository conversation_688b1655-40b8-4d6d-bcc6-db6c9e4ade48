/*
 * @Description: 
 * @Author: zhu xue song
 * @Date: 2021-11-04 16:53:47
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-11-05 14:45:47
 * @FilePath: \rongxin.loan.mgr.app.api\services\cgbcAi\index.js
 */
/**
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:cgbcAi:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetCheckListHandler = require("./getCheckListHandler");
const GetUpdatePowerHandler = require("./getUpdatePowerHandler");

class Service {
  constructor() {

  }

  async getCheckList(input, _opts) {
    let method = 'getCheckList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetCheckListHandler(context)); 
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getUpdatePower(input, _opts) {
    let method = 'getUpdatePower';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetUpdatePowerHandler(context)); 
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();
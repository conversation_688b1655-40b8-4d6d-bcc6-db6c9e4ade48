/**
 * <AUTHOR>
 * 2019-05-06
 */

'use strict';
const HANDLER_NAME = 'updateSurveyHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const SurveysData = require('../dataSvc/dataUtil').surveys;
const surveyTrackingsData = require('../dataSvc/dataUtil').surveyTrackings;
class Hand<PERSON> extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const contacts = self.context.input;
      const id = self.context.opts.id
      const oldSurvey = await SurveysData.getById(id);
      debug(method, '[SurveysDataputById](contacts)', JSON.stringify(contacts));
      const _result = await SurveysData.putById(id, contacts)
      if (!_result) {
        throw {
          errorCode: 'S_SURVEY_CREATE_033',
          httpCode: 406,
          reason: '问卷调查修改失败'
        }
      }
      if (contacts.user.grade != oldSurvey.user.grade) {
        // 添加 grade的记录
        const payload = {
          src_t: "mgr",
          source: id,
          target_t: 2,
          target: id,
          action: "SURVEY_GRADE_UPDATE",
          parameters: {
            grade: oldSurvey.user.grade
          },
          comments: "修改客户评价"
        }
        await surveyTrackingsData.post(payload);
      }
      if ((contacts.user.estimatedTime != oldSurvey.user.estimatedTime) && oldSurvey.content.basic.loanWill != 0) {
        // 添加 estimatedTime的记录
        const payload = {
          src_t: "mgr",
          source: id,
          target_t: 1,
          target: id,
          action: "SURVEY_ESTIMATEDTIME_UPDATE",
          parameters: {
            estimatedTime: oldSurvey.user.estimatedTime || ''
          },
          comments: "修改预计用款时间"
        }
        await surveyTrackingsData.post(payload);
      }
      const result = {
        update: 1,
        status: "SUCCESS"
      }
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
/**
 * <AUTHOR>
 * 2019-05-06
 */

'use strict';
const HANDLER_NAME = 'getSurveyListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const SurveysData = require('../dataSvc/dataUtil').surveys;
const SURVEY_USERGRADE = require('../../data/dict').SURVEY_USERGRADE
const moment = require('moment');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const contacts = self.context.input;
      contacts['$sort'] = {
        createdTime: -1
      };
      let _result = await SurveysData.getListAndCountByCondition(contacts);

      const total = _result.total;
      let result = [];
      for (let i = 0; i < _result.result.length; i++) {
        let survey = {};
        survey.sn = _result.result[i].sn;
        survey._id = _result.result[i]._id;
        survey.user = {};
        survey.user.realname = _result.result[i].user.realname;
        survey.user.mobile = _result.result[i].user.mobile;
        survey.user.grade = SURVEY_USERGRADE.find(data => {
          return data.value == _result.result[i].user.grade
        }).name;
        survey.user.estimatedTime = moment(_result.result[i].estimatedTime).utc().add(8, 'h').format('YYYY-MM-DD HH:mm');
        survey.createdTime = moment(_result.result[i].createdTime).utc().add(8, 'h').format('YYYY-MM-DD HH:mm');
        survey.orderStatus = _result.result[i].orderStatus;
        result.push(survey);
      }
      self.context.result.result = result;
      self.context.result.total = total;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
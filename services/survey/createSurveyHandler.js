/**
 * <AUTHOR>
 * 2019-05-06
 */

'use strict';
const HANDLER_NAME = 'createSurveyHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const SurveysData = require('../dataSvc/dataUtil').surveys;
const SURVEY_USERGRADE = require('../../data/dict').SURVEY_USERGRADE

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const contacts = self.context.input;
      const _result = await SurveysData.post(contacts)
      if (!_result) {
        throw {
          errorCode: 'S_SURVEY_CREATE_033',
          httpCode: 406,
          reason: '问卷调查创建失败'
        }
      }
      const result = {
        create: 1,
        status: "SUCCESS"
      }
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
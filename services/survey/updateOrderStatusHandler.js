/**
 * <AUTHOR>
 * 2019-05-06
 */

'use strict';
const HANDLER_NAME = 'updateSurveyHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const SurveysData = require('../dataSvc/dataUtil').surveys;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const contacts = self.context.input;
      const id = self.context.opts.id
      const _result = await SurveysData.putById(id, contacts)
      if (!_result) {
        throw {
          errorCode: 'S_SURVEY_CREATE_033',
          httpCode: 406,
          reason: '问卷调查修改失败'
        }
      }
      const result = {
        update: 1,
        status: "SUCCESS"
      }
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
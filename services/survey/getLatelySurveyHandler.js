/**
 * <AUTHOR>
 * 2019-05-06 
 */

'use strict';
const HANDLER_NAME = 'getLatelySurveyHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const SurveysData = require('../dataSvc/dataUtil').surveys;
const moment = require('moment');
const SURVEY_USERGRADE = require('../../data/dict').SURVEY_USERGRADE

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const contacts = self.context.input;
      let total = await SurveysData.getCountByCondition(contacts);
      contacts['$sort'] = {
        createdTime: -1
      }
      let result = await SurveysData.getOneByCondition(contacts);
      self.context.result = {};
      self.context.result.createdTime = '';
      if (total.count > 0) {
        self.context.result.createdTime = moment(result.createdTime).utc().add(8, 'h').format('YYYY/MM/DD HH:mm:ss');
      }

      contacts.orderStatus = true;
      let processed = await SurveysData.getCountByCondition(contacts)
      contacts.orderStatus = false;
      let pending = await SurveysData.getCountByCondition(contacts)
      let now = moment(new Date()).utc().format('YYYY/MM/DD');
      let months = moment(new Date()).utc().add(30, 'd').format('YYYY/MM/DD');
      delete contacts.$sort;
      contacts["user.estimatedTime"] = { $gte: new Date(now), $lt: new Date(months) };
      let lastMonth = await SurveysData.getCountByCondition(contacts);
      self.context.result.total = total.count;
      self.context.result.pending = pending.count;
      self.context.result.processed = processed.count;
      self.context.result.lastMonth = lastMonth.count;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;


/**
 * <AUTHOR>
 * 2019-05-06
 */

'use strict';
const HANDLER_NAME = 'getSurveyByidHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const SurveysData = require('../dataSvc/dataUtil').surveys;
const formatAreaCode = require('../../persistence/formatAreaCode');
const moment = require('moment');
const config = require('config');
const RELATIONSHIP = require('../../data/dict').RELATIONSHIP;
const surveyTrackingData = require('../dataSvc/dataUtil').surveyTrackings;
const aliOssSvc = require('../aliOssSvc');
const IMAGE_BASE_URL = config.get('repoServer').host;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const contacts = self.context.input;
      const opts = self.context.opts;

      let result = await SurveysData.getOneByCondition(contacts);

      if (result) {
        result.images = await self.getPath(result.images);
      }

      if (opts.type === "1" && result) {
        const content = result.content;
        delete result.content;
        delete result.images;
        result.basic = content.basic;
        result.planting = content.planting;
        result.breeding = content.breeding;
        result.loan = content.loan;
        if (result.user && result.user.areaCode) {
          result.user.region = {};
          const data = await formatAreaCode.getFormatAreaCode(result.user.areaCode);
          if (data && data.area) {
            result.user.region = data.region;
          }
          result.user.estimatedTime = result.user.estimatedTime ? moment(result.user.estimatedTime).add(8, 'h').format('YYYY/MM/DD') : "";
        }
        if (result.user && result.user.relationship) {
          result.user.relationshipName = RELATIONSHIP.find(data => {
            return data.value == result.user.relationship
          }).name
        }
      } else if (opts.type === "2" && result) {
        result = result.images;
      } else {
        const content = result.content;
        delete result.content;
        result.basic = content.basic;
        result.planting = content.planting;
        result.breeding = content.breeding;
        result.loan = content.loan;
        if (result.user && result.user.areaCode) {
          result.user.estimatedTime = result.user.estimatedTime ? moment(result.user.estimatedTime).add(8, 'h').format('YYYY/MM/DD') : "";
          result.user.region = {};
          const data = await formatAreaCode.getFormatAreaCode(result.user.areaCode);
          if (data && data.area) {
            result.user.region = data.region;
          }
        }
      }

      let gradeTrack = await surveyTrackingData.getCountByCondition({
        target: result._id,
        target_t: 2,
        action: "SURVEY_GRADE_UPDATE",
        limit: 'unlimited'
      });
      let estimatedTimeTrack = await surveyTrackingData.getCountByCondition({
        target: result._id,
        target_t: 1,
        action: "SURVEY_ESTIMATEDTIME_UPDATE",
        limit: 'unlimited'
      });
      result.gradeTrack = gradeTrack.count;
      result.estimatedTimeTrack = estimatedTimeTrack.count;

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }

  async getPath(images = {}) {
    debug('getPath', '[images]', images);
    const keys = ["house", "machine", "else", "breeding"];
    let promise = [];
    for (const val of keys) {
      if (images[val] && images[val].length) {
        for (let item of images[val]) {
          formatImg(item, promise);
        }
      }
    }
    await Promise.all(promise);
    debug('getPath', '[images](string)', JSON.stringify(images));

    return images;
  }

}

function formatImg(item, promise) {
  if (item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    if (item.thumbnail.url.indexOf('oss/') === 0) {
      promise.push(aliOssSvc.getFile({
        fileName: item.thumbnail.url
      }).then(data => {
        item.thumbnail.url = data
      }));
    } else {
      item.thumbnail.url = IMAGE_BASE_URL.concat(item.thumbnail.url);
    }
  }
  if (item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    if (item.image.url.indexOf('oss/') === 0) {
      promise.push(aliOssSvc.getFile({
        fileName: item.image.url
      }).then(data => {
        item.image.url = data
      }));
    } else {
      item.image.url = IMAGE_BASE_URL.concat(item.image.url);
    }
  }
}
module.exports = Handler;
/**
 * <AUTHOR>
 * 2019-05-06
 */

'use strict';
const HANDLER_NAME = 'GetTrackingListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const surveyTrackingData = require('../dataSvc/dataUtil').surveyTrackings
const moment = require('moment');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const contacts = self.context.input;

      let result = await surveyTrackingData.getListAndCountByCondition(contacts);
      for (const item of result.result) {
        if (contacts.target_t == 1) {
          item.parameters.estimatedTime = moment(item.parameters.estimatedTime).utc().add(8, 'h').format('YYYY/MM/DD')
        }
        item.createdTime = moment(item.createdTime).utc().add(8, 'h').format('YYYY/MM/DD HH:mm:ss')
      }
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
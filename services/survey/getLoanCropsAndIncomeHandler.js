'use strict';

const HANDLER_NAME = 'getLoanCropsAndIncomeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const surveysData = require('../dataSvc/dataUtil').surveys;
const cropsData = require('../dataSvc/dataUtil').crops;
const breedsData = require('../dataSvc/dataUtil').breeds;
const loanData = require('../dataSvc/dataUtil').loanApplication;
const userIdentityData = require('../dataSvc/dataUtil').userIdentity;

const InfoSourceTypeEnum = {
  ByAI: 'ai',
  By138: '138'
}
class <PERSON><PERSON> extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let data = await surveysData.getOneByCondition(condition);
      let loanInfo = await loanData.getById(condition.aId)

      if (!loanInfo || !loanInfo._id || !loanInfo.addons || !loanInfo.addons.contractorNo) {
        throw {
          errorCode: 'E_SURVEY_DETAIL_30',
          httpCode: 406,
          reason: 'loanApplication is not exists'
        }
      }
      const userIdentity = await userIdentityData.getOneByCondition({
        uId: loanInfo.uId,
        IDCardStatus: 'approved',
        archived: false
      });
      if (!userIdentity) {
        throw {
          errorCode: 'E_OUT_TRADE_YL_01',
          httpCode: 406,
          reason: 'user not identified'
        };
      }

      let balance = null;

      let num = 1; // 倍数
      const infoSourceType = loanInfo.infoSourceType;
      // if(InfoSourceTypeEnum.ByAI === infoSourceType) {
      //   const aiInfo = await infoFarmerData.getOneByCondition({
      //     idCard: userIdentity.IDCard
      //   });
      //   balance = aiInfo ? (aiInfo.info ? aiInfo.info['balance'] : null) : null;
      //   if(balance && balance.length !== 0) {
      //     balance = balance[0];
      //   }
      // } else {
      //   num = 10000;
      //   const dataBalance = await connectUserData.getByCondition({
      //     type: 'balance',
      //     idCard: userIdentity.IDCard
      //   })
      //   if (dataBalance && dataBalance.length > 0) {
      //     balance = dataBalance[0];
      //   }
      // }


      let income = null;
      if (balance) {
        // 从138获取的数据为万元 顾转化成元处理
        income = {
          breeding: {
            annual_total: parseFloat(balance.farm_out) * num
          },
          other: {
            annual_total: parseFloat(balance.other_in) * num
          },
          operate: {
            annual_total: parseFloat(balance.manage_in) * num
          },
          labor: {
            annual_total: parseFloat(balance.work_in) * num
          },
          subsidy: {
            annual_total: parseFloat(balance.subsidy_in) * num
          }
        }
      }
      const area = loanInfo.addons.contractorNo.substr(0, 6);
      // #region 种植信息
      let cropsInfo = await cropsData.getByCondition({
        area: area,
        archived: false,
        limit: 'unlimited',
        $sort: {
          "createdTime": 1
        }
      })

      // 产品新增逻辑 如果承包方编码匹配不出来 用订单中的区域来匹配
      if (!cropsInfo || cropsInfo.length < 1) {
        const loanArea = loanInfo.area.substr(0, 6);
        cropsInfo = await cropsData.getByCondition({
          area: loanArea,
          archived: false,
          limit: 'unlimited',
          $sort: {
            "createdTime": 1
          }
        })
      }

      let crops = [];
      if (!cropsInfo || cropsInfo.length < 1) {
        throw {
          errorCode: 'E_SURVEY_DETAIL_45',
          httpCode: 406,
          reason: 'crops is not exists'
        }
      }
      // 组出来对应信息
      for (const item of cropsInfo) {
        let crop = {
          name: item.name,
          area: null,
          type: item.type,
          acreYield: item.acreYield,
          price: item.price,
          income: 0
        }
        crops.push(crop)
      }
      // #endregion

      // #region 养殖信息
      let breedsInfo = await getBreedsByArea(area);
      if (!breedsInfo || breedsInfo.length < 1) {
        const loanArea = loanInfo.area.substr(0, 6);
        breedsInfo = await getBreedsByArea(loanArea);
      }

      if (!breedsInfo || breedsInfo.length < 1) {
        throw {
          errorCode: 'E_SURVEY_DETAIL_139',
          httpCode: 406,
          reason: 'breeds is not exists'
        }
      }

      const breeds = [];

      for (const item of breedsInfo) {
        let breed = {
          name: item.name,
          nameUnit: item.nameUnit,
          count: null,
          type: item.type,
          price: item.price,
          income: 0
        }
        breeds.push(breed)
      }
      // #endregion

      let result = {};
      if (data && data._id) {
        result._id = data._id;
        result.aId = data.aId;
        result.type = data.type;
        result.content = {
          income: data.content.income || {},
          crops: data.content.crops || crops,
          breeds: data.content.breeds || breeds,
          characteristic: data.content.characteristic || {}
        }
      } else {
        result.content = {
          income: income || {},
          crops: {},
          breeds,
          characteristic: {}
        }
      }
      self.context.result = result
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function getBreedsByArea(area) {
  if (area.length !== 6) {
    return [];
  }

  while (area.length >= 2) {
    const breedsInfo = await breedsData.getByCondition({
      area,
      archived: false,
      limit: 'unlimited',
      $sort: {
        "createdTime": 1
      }
    })

    if (breedsInfo && breedsInfo.length > 0) {
      return breedsInfo;
    }

    area = area.substr(0, area.length - 2);
  }

  return [];
}

module.exports = Handler
/**
 * <AUTHOR>
 * 2019-05-06
 */

'use strict';
const HANDLER_NAME = 'correlationLoanApplicationHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const surveyData = require('../dataSvc/dataUtil').surveys;
const surveyLoansData = require('../dataSvc/dataUtil').surveyLoans;
const LoanData = require('../dataSvc/dataUtil').loanApplication;
const loanProductData = require('../dataSvc/dataUtil').loanProducts;
const fundData = require('../dataSvc/dataUtil').funds;
const userIdentityData = require('../dataSvc/dataUtil').userIdentity;
const moment = require('moment');
const REPAY_TYPE = new Map([
  ['001', {
    code: '001',
    name: '等额本金'
  }],
  ['002', {
    code: '002',
    name: '等额本息'
  }],
  ['003', {
    code: '003',
    name: '一次还本付息'
  }],
  ['004', {
    code: '004',
    name: '先付息一次还本'
  }],
]);
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const input = self.context.input;
      const surveyInfo = await surveyData.getById(input.id);
      if (!surveyInfo || !surveyInfo._id) {
        throw {
          errorCode: 'S_Correlation_032',
          httpCode: 406,
          reason: 'invalid param'
        }
      }
      const surveyLoanList = await surveyLoansData.getByUrl('/v1.0/survey/loan/idcard', {
        IDCard: surveyInfo.user.IDCard
      });
      let promise = [];
      // 获取订单详情
      for (const item of surveyLoanList) {
        promise.push(LoanData.getById(item.aId).then(data => {
          item.loanInfo = data;
          item.loanInfo.createdTime =
            self.context.result.createdTime = moment(data.createdTime).utc().add(8, 'h').format('YYYY/MM/DD HH:mm:ss');
        }))
        if (item.groups.length > 0) {
          item.relationship = item.groups[0].relationship
        }
        item.loanCreatedTime = moment(item.loanCreatedTime).utc().add(8, 'h').format('YYYY/MM/DD HH:mm:ss');
        delete item.groups
      }
      await Promise.all(promise);
      promise = [];
      // 格式化订单中信息
      for (const item of surveyLoanList) {
        if (item.loanInfo) {
          if (item.loanInfo.pId) {
            promise.push(loanProductData.getById(item.loanInfo.pId, {cache : true , expire: 24 * 60 * 60 }).then(data => {
              const TYPE = REPAY_TYPE.get(data.repay_t);
              data.repay_t = TYPE && TYPE.name || "";
              data.type = data.name
              item.loanInfo.productInfo = data;
            }))
          }

          promise.push(userIdentityData.getOneByCondition({
            uId: item.loanInfo.uId,
            IDCardStatus: 'approved'
          }).then(data => {
            item.IDCard = data && data.IDCard || ""
          }));
        }
        if (item.loanInfo.fund) {
          promise.push(fundData.getById(item.loanInfo.fund, {cache : true , expire: 24 * 60 * 60 }).then(data => {
            item.loanInfo.fundName = data && data.name || "";
          }))
        }


      }
      await Promise.all(promise);
      let result = {
        survey: surveyInfo.user,
        loanInfo: surveyLoanList
      };
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
/**
 * <AUTHOR>
 * 2019-05-17
 */

'use strict';
const HANDLER_NAME = 'VerifySurveyHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const SurveysData = require('../dataSvc/dataUtil').surveys;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const input = self.context.input;
      const condition = {
        $or: [
          { "user.IDCard": input.IDCard },
          { "user.realname": input.realname, "user.mobile": input.mobile }
        ],
        archived: false,
        limit: 'unlimited'
      };
      let result = await SurveysData.getByCondition(condition);
      let oldSurvey = result;
      if (input.id) {
        oldSurvey = result.filter((item) => { return item._id != input.id });
      }
      
      if (oldSurvey.length > 0) {
        for(let survey of oldSurvey){
          if (survey.user.IDCard == input.IDCard) {
            throw {
              errorCode: 'E_VERIFY_SURVEY_045',
              httpCode: 406,
              reason: '该身份证号已存在，请核实。'
            }
          }else{
            throw {
              errorCode: 'E_VERIFY_SURVEY_051',
              httpCode: 406,
              reason: '该“姓名+手机号”已存在，请核实。'
            }
          }
        }
        
      }
      self.context.result = { code: 0, msg: "校验通过" };
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;


/**
 * ali oss svc client
 * <AUTHOR>
 */

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
// const FILE_PATH_CODE = require("../utils/filePathConst").FILE_PATH_CODE;
const debug = logFactory(logUtil())('rongxin_loan_repo:services:aliOSSSvc');
const OSS = require('ali-oss');
const config = require('config');
const path = require('path');
const moment = require('moment');
const env = require('./env');
const DEFAULT_EXP = config.get('aliOSS.default_expires')
class Svc {
  constructor() {
    this.client = {};
    this.name = 'aliOssSvc'
  }

  init() {
    this.client = {
      'private' : new OSS({
        endpoint: config.get('aliOSS.endpoint'),
        accessKeyId: config.get('aliOSS.access_key'),
        accessKeySecret: config.get('aliOSS.access_key_secret'),
        region: 'oss-cn-hangzhou',
        bucket: config.get('aliOSS.bucket'),
        secure: true
      }),
      'public' : new OSS({
        endpoint: config.get('aliOSS.endpoint'),
        accessKeyId: config.get('aliOSS.access_key'),
        accessKeySecret: config.get('aliOSS.access_key_secret'),
        region: 'oss-cn-hangzhou',
        bucket: config.get('aliOSS.bucket_pub'),
        secure: true
      })
    }
  }
  
  async aliOssGetFile(method, file, opts, action){
    try {
      if (!file.fileName) {
        debug(method, '[Exit](success)');
        return '';
      }
      
      opts.expires = ~~opts.expires ? ~~opts.expires : DEFAULT_EXP ;
      let fileName = file.fileName.replace('oss/',"");
      
      let security = fileName.match(/([a-z]+)\//)[1];
      security = security =='public' ? 'public' : 'private';
      let objectName = fileName.replace(security,"");
      
      let result = await this.client[security][action](objectName, opts);
      debug(method, '[Exit](success)');
      return result;
    } catch (err) {
      debug.error(method, '[Exit](failed)', err);
      throw err;
    }
  }

  /**
   * @param {*} fileName znrx/20190618/carrousel_1.jpg
   * @returns https://piccloan1.oss-cn-hzfinance.aliyuncs.com/znrx/20190618/carrousel_1.jpg?OSSAccessKeyId=LTAIZHfn7yYGdR4c&Expires=1560844101&Signature=LOFweNgrM5SP8c9tOWtahzXIIF0%3D
   */
  async getFile(file,opts={}) {
    let method = `${this.name}.getFile`;
    debug(method, '[Enter]');
    return this.aliOssGetFile( method, file, opts,'signatureUrl');
  }

  async getStream(file,opts={}) {
    let method = `${this.name}.getSteam`;
    debug(method, '[Enter]');
    return this.aliOssGetFile( method, file, opts,'getStream');
  }

  /**
   * @param {*} fileName znrx/20190618/carrousel_1.jpg
   * @returns https://piccloan1.oss-cn-hzfinance.aliyuncs.com/znrx/20190618/carrousel_1.jpg?OSSAccessKeyId=LTAIZHfn7yYGdR4c&Expires=1560844101&Signature=LOFweNgrM5SP8c9tOWtahzXIIF0%3D
   */
  async getSteamFile(file,opts={}) {
    let method = `${this.name}.getSteamFile`;
    debug(method, '[Enter]');
    return this.aliOssGetFile( method, file, opts,'get');
  }

}

const svc = new Svc();
svc.init();

module.exports = svc;
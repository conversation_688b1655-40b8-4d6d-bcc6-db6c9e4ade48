/**
 * <AUTHOR>
 * 2019-05-05  
 */

'use strict';

const HANDLER_NAME = 'getNextEmployeeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:employee:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeesData = require('../dataSvc/dataUtil').employees;
const employeeGroupData = require('../dataSvc/dataUtil').employeeGroups;
const groupV2Data = require('../dataSvc/dataUtil').groupsV2;
const NEXTGROUP = require('../../data/group').NEXTGROUP
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      const GROUP = NEXTGROUP.get(input.currentRole);
      if (!GROUP) {
        throw {
          httpCode: 406,
          errorCode: 'E_NEXTGROUP_R_065',
          reason: `${input.currentRole} is not next group`
        }
      }
      let condition = {
        group: GROUP.id,
        archived: false
      };
      let groupInfo = await employeeGroupData.getOneByCondition({
        group: input.currentRole,
        employee: opts.userInfo.userid,
        archived: false
      });

      if (GROUP.name === "BankAccountManager") {
        condition.groupV2 = groupInfo.groupV2
      }
      condition.tId = groupInfo.tId;
      let employeeGroup = await employeeGroupData.getByCondition(condition);
      let employeeInfo = [];
      let promises = [];
      for (const item of employeeGroup) {
        promises.push(employeesData.getById(item.employee, {cache : true , expire: 24 * 60 * 60 }).then(data => {
          data.role = item;
          employeeInfo.push(data);
        }))
      }
      await Promise.all(promises);
      promises = [];
      for (const item of employeeInfo) {
        item.groupName = GROUP.name;
        promises.push(groupV2Data.getById(item.role.groupV2).then(data => {
          item.groupV2Name = data.name;
          item.code = data.code;
        }))
      }
      await Promise.all(promises);
      self.context.result = employeeInfo || {};
      debug(method, '[Exit](success)', employeeInfo);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
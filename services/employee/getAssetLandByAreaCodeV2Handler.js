/*
 * @Description: 
 * @Author: zhu xue song
 * @Date: 2022-01-13 13:39:25
 * @LastEditors: zhu xue song
 * @LastEditTime: 2022-01-13 16:28:01
 * @FilePath: \rongxin.loan.user.app.api\services\user\getAssetLandByIdCardV2Handler.js
 */
'use strict';

const HANDLER_NAME = 'getAssetLandByAreaCodeV2Handler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:employee:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const drawSvrLand = require('../dataSvc/dataUtil').drawSvrLand;
const employeeGroupData = require('../dataSvc/dataUtil').employeeGroups;
const employeeData = require('../dataSvc/dataUtil').employees;
const { getEmployeeLimit } = require('../../utils/getUserFromReq');
const { TenantConst } = require('../../utils/const')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let { uId, skip, limit, tId, roleId, areaCode, CBFMC } = self.context.input;
      const employOtherInfo = await employeeData.getOneByCondition({ _id: uId, archived: false });
      if(!employOtherInfo) {
        self.context.result = { result: [], total: 0 };
        return done();
      }
      const mobile = employOtherInfo.mobile;
      const employeeNowInfo = await employeeData.getOneByCondition({ mobile, tId: TenantConst.Tenant.JLCHGS })
      if(!employeeNowInfo) {
        self.context.result = { result: [], total: 0 };
        return done();
      }
      const fiflters = await getEmployeeLimit(employeeGroupData, TenantConst.Tenant.JLCHGS, employeeNowInfo._id, 'DKBM', roleId) || {};
      const condition = {
        $or: [],
        skip,
        limit,
        archived: false,
      }
      for (const fiflter of fiflters) {
        condition.$or.push(fiflter);
      }

      if(areaCode) condition.DKBM = { $regex: `^${areaCode}` };
      if(CBFMC) condition.ZJRXM = CBFMC;
      
      const condition2 = {
        theRxApiParamsIsJson: 'YES',
        theRxApiParams: JSON.stringify(condition)
      }
      const lands = await drawSvrLand.getListAndCountByCondition(condition2);
      let result = {
        result: [],
        total: lands ? lands.total : 0 
      };
      for (const land of lands.result) {
        result.result.push({
          _id: land._id,
          name: land.name || '',
          CBFMC: land.ZJRXM,
          SCMJM: land.SCMJM,
          areaName: land.areaName,
        })
      }

      self.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
/*
 * @Description: 地图模式，所有的坐标
 * @Author: zhu xue song
 * @Date: 2022-01-13 13:39:25
 * @LastEditors: zhu xue song
 * @LastEditTime: 2022-01-19 17:57:39
 * @FilePath: \rongxin.loan.mgr.app.api\services\employee\getAssetLandMapHandler.js
 */
'use strict';

const HANDLER_NAME = 'getAssetLandMapHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:employee:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const userVerifysData = require('../dataSvc/dataUtil').userVerifys;
const drawSvrLand = require('../dataSvc/dataUtil').drawSvrLand;
const employeeData = require('../dataSvc/dataUtil').employees;
const employeeGroupData = require('../dataSvc/dataUtil').employeeGroups;

const { getEmployeeLimit } = require('../../utils/getUserFromReq');

const { TenantConst } = require('../../utils/const')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      const { areaCode, CBFMC, uId, roleId, skip, limit } = input;

      const employOtherInfo = await employeeData.getOneByCondition({ _id: uId, archived: false });
      if(!employOtherInfo) {
        self.context.result = { result: [], total: 0 };
        return done();
      }
      const mobile = employOtherInfo.mobile;
      const employeeNowInfo = await employeeData.getOneByCondition({ mobile, tId: TenantConst.Tenant.JLCHGS })
      if(!employeeNowInfo) {
        self.context.result = { result: [], total: 0 };
        return done();
      }
      const fiflters = await getEmployeeLimit(employeeGroupData, TenantConst.Tenant.JLCHGS, employeeNowInfo._id, 'DKBM', roleId) || {};
      const condition = {
        $or: [],
        skip,
        limit,
        archived: false,
      }
      for (const fiflter of fiflters) {
        condition.$or.push(fiflter);
      }

      if(areaCode) condition.DKBM = { $regex: `^${areaCode}` };
      if(CBFMC) condition.ZJRXM = CBFMC;
      const condition2 = {
        theRxApiParamsIsJson: 'YES',
        theRxApiParams: JSON.stringify(condition)
      }
      const lands = await drawSvrLand.getListAndCountByCondition(condition2);
      let result = [];
      for (const land of lands.result) {
        const payload = {
          name: land.name,
          CBFMC: land.ZJRXM,
          mobile: land.mobile,
          idCard: land.idCard,
          SCMJM: land.SCMJM, // 地块测量面积
          confirmationArea: land.confirmationArea || '', // 确权面积
          mobile: land.mobile,
          DKBM: [land.DKBM],
          areaName: land.areaName,
          vertexList: land.vertexList
        };
        result.push(payload);
      }

      self.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
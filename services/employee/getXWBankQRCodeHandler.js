/*
 * @Author: qays
 * @Date: 2023-12-04 14:21:51
 * @LastEditTime: 2023-12-15 14:08:51
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.mgr.app.api\services\employee\getXWBankQRCodeHandler.js
 */

'use strict';

const HANDLER_NAME = 'getNextEmployeeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:employee:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
// const employeesData = require('../dataSvc/dataUtil').employees;
const employeeGroupData = require('../dataSvc/dataUtil').employeeGroups;
const qrcode = require('qrcode');
const config = require("config")
const repoServer = config.get('repo_Service').host
const superagent = require('superagent');
const REQ_DEFAULT_TIMEOUT = 2 * 60 * 1000;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let { url } = self.context.input;
      let { uId } = self.context.opts;

      let role = "5eb8fee1c6ecfe44d4ecaed1"; // 客户经理
      let orgInfo = await employeeGroupData.getOneByCondition({
        group: role,
        employee: uId,
        archived: false
      });
      if (!orgInfo) {
        throw {
          errorCode: 'E_XWBANK_45',
          httpCode: 406,
          reason: '无客户经理权限无法分享二维码'
        }
      }
      let qrcodeStr = `${url}?o=${orgInfo.groupV2}&e=${uId}`;
      let qr_buffer = (await qrcode.toDataURL(qrcodeStr, { margin: 0, width: 400, heigth: 400, errorCorrectionLevel: "H" }));
      let _url = `${repoServer}/api/v1.0/upload/qrcode/base64`;
      let image = await superagent.post(_url).send({
        qr_buffer,
        filename:`xwyh_${uId}.jpg`,
        category:'CGM07'
      })
      debug(method,"getFileByUrl", ` upload image file`, image.body);
      // repo : qrcodeStr;
      
      self.context.result = {result: image.body, path_url: qrcodeStr};
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
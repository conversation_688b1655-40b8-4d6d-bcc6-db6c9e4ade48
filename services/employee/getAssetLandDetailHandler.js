/*
 * @Description: 吉林测绘地块详情
 * @Author: zhu xue song
 * @Date: 2022-01-13 13:39:25
 * @LastEditors: zhu xue song
 * @LastEditTime: 2022-01-17 17:47:00
 * @FilePath: \rongxin.loan.mgr.app.api\services\employee\getAssetLandDetailHandler.js
 */
'use strict';

const HANDLER_NAME = 'getAssetLandDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:user:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const userVerifysData = require('../dataSvc/dataUtil').userVerifys;
const drawSvrLand = require('../dataSvc/dataUtil').drawSvrLand;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      const land = await drawSvrLand.getById(input.landId);
      const result = {
        name: land.name,
        CBFMC: land.ZJRXM,
        mobile: land.mobile,
        idCard: land.idCard,
        SCMJM: land.SCMJM, // 地块测量面积
        confirmationArea: land.confirmationArea || '', // 确权面积
        // mobile: land.mobile,
        DKBM: [land.DKBM],
        areaName: land.areaName,
        vertexList: land.vertexList
      };
      debug(method, '[Exit] statusResult:', JSON.stringify(result));

      self.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
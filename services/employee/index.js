/**
 * <AUTHOR>
 * 2019-05-05
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:routes:employee:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const getNextEmployeeHandler = require("./getNextEmployeeHandler");
const GetAssistantListHandler = require('./getAssistantListHandler');
const GetAssistantDetailHandler = require('./getAssistantDetailHandler');
const GetAssetLandByAreaCodeV2Handler = require('./getAssetLandByAreaCodeV2Handler');
const GetAssetLandDetailHandler = require('./getAssetLandDetailHandler');
const GetAssetLandMapHandler = require('./getAssetLandMapHandler');
const GetXWBankQRCodeHandler = require('./getXWBankQRCodeHandler.js');

class Service {
  constructor() {

  }

  async getNextEmployee(input, _opts) {
    let method = 'getNextEmployee';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new getNextEmployeeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async getAssistantList(input, _opts) {
    let method = 'getAssistantList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetAssistantListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async getAssistantDetail(input, _opts) {
    let method = 'getAssistantDetail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetAssistantDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getAssetLandByAreaCodeV2(input, _opts) {
    let method = 'getAssetLandByAreaCodeV2';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetAssetLandByAreaCodeV2Handler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getAssetLandDetail(input, _opts) {
    let method = 'getAssetLandDetail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetAssetLandDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getAssetLandMap(input, _opts) {
    let method = 'getAssetLandDetail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetAssetLandMapHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getXWBankQRCode(input, _opts) {
    let method = 'getXWBankQRCode';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetXWBankQRCodeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();
/**
 * <AUTHOR>
 * 2019-05-05  
 */

'use strict';

const HANDLER_NAME = 'getAssistantListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:employee:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeesData = require('../dataSvc/dataUtil').employees;
const employeeGroupData = require('../dataSvc/dataUtil').employeeGroups;
const groupData = require('../dataSvc/dataUtil').groups;
const groupV2Data = require('../dataSvc/dataUtil').groupsV2;
const moment = require("moment")
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let employee = await employeesData.getById(opts.uId, {cache : true , expire: 24 * 60 * 60 });
      if (!employee || !employee.tId) {
        throw {
          errorCode: 'E_organization_List_36',
          httpCode: 406,
          reason: '用户信息不全'
        }
      }
      let employeeInfo = await employeesData.getByUrl("/v1.0/employee/tId/group/list", {
        tId: employee.tId,
        group: "5eb8ff2ec6ecfe44d4ecaed9",
        limit: input.limit,
        skip: input.skip
      });
      for (const item of employeeInfo.result) {
        item.gender = item.gender == "man" ? "男" : "女"
        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss")
        item.lastModTime = moment(item.lastModTime).format("YYYY-MM-DD HH:mm:ss")
        item.groupName = "";
        for (const group of item.groups) {
          item.groupName += group.description
        }
      }

      self.context.result = employeeInfo || [];
      debug(method, '[Exit](success)', employeeInfo);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
/**
 * <AUTHOR>
 * 2019-05-05  
 */

'use strict';

const HANDLER_NAME = 'getAssistantDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:employee:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeesData = require('../dataSvc/dataUtil').employees;
const formatAreaCode = require('../../persistence/formatAreaCode');
const moment = require('moment')
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let result = await employeesData.getByUrl("/v1.0/employee/group/list", {
        id: input.id
      });
      result.createdTime = moment(result.createdTime).format("YYYY-MM-DD HH:mm:ss")
      result.lastModTime = moment(result.lastModTime).format("YYYY-MM-DD HH:mm:ss")
      if (result.gender === "man") {
        result.gender = "男"
      } else if (result.gender === "woman") {
        result.gender = "女"
      } else {
        result.gender = "";
      }
      for (const item of result.groups) {
        if (item.name === "BusinessAssistant") {
          result.groupName = item.description
          let area = await formatAreaCode.getFormatAreaCode(item.areaList[0]);
          result.region = area.region
        }
      }
      self.context.result = result || {};
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
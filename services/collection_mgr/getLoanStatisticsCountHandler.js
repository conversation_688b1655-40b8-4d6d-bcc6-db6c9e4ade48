/*
 * @Author: q 
 * @Date: 2020-5-28 18:08:34
 * @Last Modified by: q
 */


'use strict';

const HANDLER_NAME = 'getLoanStatisticsCountHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:collection_mgr:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      
      let statistics = await loanApplicationData.getByUrl("/v1.0/loan/applications/collection/statistics",{"areaCode":condition.areaCode});
      self.context.result.statistics = statistics;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
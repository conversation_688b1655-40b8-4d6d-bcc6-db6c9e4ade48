/*
 * @Author: q 
 * @Date: 2020-4-28 18:08:34
 * @Last Modified by: q
 */


'use strict';

const HANDLER_NAME = 'getEmployeeListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:collection_mgr:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;
const employeeData = require('../dataSvc/dataUtil').employees;
const infoPersonalBasicData = require('../dataSvc/dataUtil').infoPersonalBasic;
const infoEnterpriseBasicData = require('../dataSvc/dataUtil').infoEnterpriseBasic;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      
      let employeeGroups = await employeeGroupsData.getByCondition({"areaList":condition.areaCode,"archived":false,$sort:{'_id':-1}});
      // let promise = [];
      let employee = [];
      for(let eItem of employeeGroups) {
        let empRes = await employeeData.getOneByCondition({"_id":eItem.employee,isRevoked:false});
        employee.push({"_id":empRes._id,"username":empRes.username,"mobile":empRes.mobile});
        // promise.push(employeeData.getOneByCondition({"_id":eItem.employee,isRevoked:false}).then((data)=>{
          //无序
        //   employee.push({"_id":data._id,"username":data.username,"mobile":data.mobile});
        // }));
      }
      // await Promise.all(promise);
      
      let statistics = {};
      // for(let item of employee){
      statistics.personalTotalCount = await infoPersonalBasicData.getCountByCondition({areaCode:condition.areaCode,archived:false});
      statistics.personalTotalCount = statistics.personalTotalCount && statistics.personalTotalCount.count || 0;

      statistics.personalCompleteCount = await infoPersonalBasicData.getByUrl('/v1.0/manager/info/personal/count',{areaCodeStr:condition.areaCode,mainStatus:1});
      statistics.personalCompleteCount = statistics.personalCompleteCount && statistics.personalCompleteCount.sumAll || 0;

      statistics.enterpriseTotalCount = await infoEnterpriseBasicData.getCountByCondition({areaCode:condition.areaCode,archived:false});
      statistics.enterpriseTotalCount = statistics.enterpriseTotalCount && statistics.enterpriseTotalCount.count || 0;

      statistics.enterpriseCompleteCount = await infoEnterpriseBasicData.getByUrl('/v1.0/manager/info/enterprise/count',{areaCodeStr:condition.areaCode,mainStatus:1});
      statistics.enterpriseCompleteCount = statistics.enterpriseCompleteCount && statistics.enterpriseCompleteCount.sumAll || 0;
      // }

      self.context.result = {employee,statistics};
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
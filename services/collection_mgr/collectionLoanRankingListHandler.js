/*
 * @Author: q 
 * @Date: 2020-6-12 15:28:34
 * @Last Modified by: q
 */


'use strict';

const HANDLER_NAME = 'collectionLoanRankingListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:collection_mgr:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const infoPersonalBasicData = require('../dataSvc/dataUtil').infoPersonalBasic;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      
      let result = await infoPersonalBasicData.getByUrl("/v1.0/collection/loan/mgr/ranking/list",{
        tId:condition.tId,
        areaCode:condition.areaCode,
        startTime:condition.startTime,
        endTime:condition.endTime
      });
      self.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
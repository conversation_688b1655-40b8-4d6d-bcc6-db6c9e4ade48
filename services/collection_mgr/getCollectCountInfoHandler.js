/*
 * @Author: q 
 * @Date: 2020-4-28 18:08:34
 * @Last Modified by: q
 */


'use strict';

const HANDLER_NAME = 'getCollectCountInfoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:collection_mgr:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const infoPersonalBasicData = require('../dataSvc/dataUtil').infoPersonalBasic;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      
      let enterpriseCount = await infoPersonalBasicData.getByUrl("/v1.0/manager/info/enterprise/count",{"areaCode":condition.areaCode});
      let personalCount = await infoPersonalBasicData.getByUrl("/v1.0/manager/info/personal/count",{"areaCode":condition.areaCode});
      self.context.result.enterpriseCount = enterpriseCount;
      self.context.result.personalCount = personalCount;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
/*
 * @Author: q 
 * @Date: 2020-5-28 18:08:34
 * @Last Modified by: q
 */


'use strict';

const HANDLER_NAME = 'getEmployeeLoanListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:collection_mgr:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeeData = require('../dataSvc/dataUtil').employees;
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      
      let employeeGroups = await employeeGroupsData.getByCondition({"areaList":condition.areaCode,"archived":false});
      let promise = [];
      let employee = [];
      for(let eItem of employeeGroups) {
        promise.push(employeeData.getOneByCondition({"_id":eItem.employee,isRevoked:false}).then((data)=>{
          if(data)
            employee.push({"_id":data._id,"username":data.username,"mobile":data.mobile});
        }));
      }
      await Promise.all(promise);

      let lastPromise = [];
      let statistics = {};
      // for(let item of employee){
      lastPromise.push(loanApplicationData.getCountByCondition({"area":condition.areaCode,"source":"spring_app","consumer_t":"01",archived:false}).then((data)=>{
        statistics.personalTotalCount = data && data.count || 0;
      }));
      lastPromise.push(loanApplicationData.getCountByCondition({"area":condition.areaCode,"source":"spring_app","consumer_t":"01","status":"loaned",archived:false}).then((data)=>{
        statistics.personalLoanedCount = data && data.count || 0;
      }));
      lastPromise.push(loanApplicationData.getCountByCondition({"area":condition.areaCode,"source":"spring_app","consumer_t":"01","status":"biopsy_approved",archived:false}).then((data)=>{
        statistics.personalBiopsyCount = data && data.count || 0;
      }));
      lastPromise.push(loanApplicationData.getCountByCondition({"area":condition.areaCode,"source":"spring_app","consumer_t":"02",archived:false}).then((data)=>{
        statistics.enterpriseTotalCount = data && data.count || 0;
      }));
      lastPromise.push(loanApplicationData.getCountByCondition({"area":condition.areaCode,"source":"spring_app","consumer_t":"02","status":"loaned",archived:false}).then((data)=>{
        statistics.enterpriseLoanedCount = data && data.count || 0;
      }));
      lastPromise.push(loanApplicationData.getCountByCondition({"area":condition.areaCode,"source":"spring_app","consumer_t":"02","status":"biopsy_approved",archived:false}).then((data)=>{
        statistics.enterpriseBiopsyCount = data && data.count || 0;
      }));
      lastPromise.push(loanApplicationData.getByUrl("/v1.0/loan/applications/query/group",{match:{"area":condition.areaCode,"source":"spring_app","consumer_t":"01","status":"loaned"},group:{actualLoan: { $sum: "$actualLoan" }}}).then((data)=>{
        statistics.personalLoanAmount = ((data && data.actualLoan || 0 )/10000).toFixed(2);
      }));
      lastPromise.push(loanApplicationData.getByUrl("/v1.0/loan/applications/query/group",{match:{"area":condition.areaCode,"source":"spring_app","consumer_t":"02","status":"loaned"},group:{actualLoan: { $sum: "$actualLoan" }}}).then((data)=>{
        statistics.enterpriseLoanAmount = ((data && data.actualLoan || 0 )/10000).toFixed(2);
      }));
      // }
      debug(method, '[Enter]statistics:',statistics)
      await Promise.all(lastPromise);

      self.context.result = {employee,statistics};
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
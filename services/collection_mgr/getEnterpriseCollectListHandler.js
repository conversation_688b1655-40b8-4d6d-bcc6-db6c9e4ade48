/*
 * @Author: q 
 * @Date: 2020-4-28 18:08:34
 * @Last Modified by: q
 */


'use strict';

const HANDLER_NAME = 'getEnterpriseCollectListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:collection_mgr:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const infoEnterpriseMainData = require('../dataSvc/dataUtil').infoEnterpriseMain;
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      if(input.type=='enterprise'){
        let data = await infoEnterpriseMainData.getByUrl('/v1.0/manager/info/enterprise/list',input);
        for(let item of data.result){
          item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss")
        }
        self.context.result = data;
      }
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
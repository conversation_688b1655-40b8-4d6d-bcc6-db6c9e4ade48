/*
 * @Author: q 
 * @Date: 2020-4-28 18:08:34
 * @Last Modified by: q
 */


'use strict';

const HANDLER_NAME = 'getPersonalLoanListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:collection_mgr:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const loanProductsData = require('../dataSvc/dataUtil').loanProducts;
const LOAN_APP_STATUS_MAP = require('../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      if(input.type=='01'){
        // let areaCode = new RegExp("^"+input.areaCode);
        let promise = [];
        let data = await loanApplicationData.getListAndCountByCondition({"area":input.areaCode,"consumer_t":"01","tId":input.tId,"archived":false,limit:input.limit,skip:input.skip,$sort:{createdTime:-1}});
        for(let item of data.result){
          promise.push(loanProductsData.getById(item.pId, {cache : true , expire: 24 * 60 * 60 }).then((data)=>{
            item.productName = data && data.name;
          }));
          item.amount = item.amount / 100;
          item.status = LOAN_APP_STATUS_MAP.get(item.status);
          item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss")
        }
        await Promise.all(promise);
        self.context.result = data;
      }
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
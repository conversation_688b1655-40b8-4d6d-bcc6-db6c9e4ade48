/*
 * @Author: q 
 * @Date: 2020-4-28 18:06:30
 * @Last Modified by: q
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:collection_mgr:services:collection_mgr:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetEmployeeCountHandler = require('./getEmployeeCountHandler'); //区域  协理员数
const GetCollectCountInfoHandler =  require('./getCollectCountInfoHandler'); //区域  关联个人采集数量
const GetCollectListHandler =  require('./getCollectListHandler'); //区域  采集单数量
const GetEmployeeListHandler =  require('./getEmployeeListHandler'); //
const GetPersonalCollectListHandler =  require('./getPersonalCollectListHandler'); //
const GetEnterpriseCollectListHandler =  require('./getEnterpriseCollectListHandler'); //

const GetLoanStatisticsCountHandler = require('./getLoanStatisticsCountHandler'); //区域  订单合计
const GetLoanPollHandler =  require('./getLoanPollHandler'); //区域  订单列表
const GetEmployeeLoanListHandler =  require('./getEmployeeLoanListHandler'); //
const GetPersonalLoanListHandler =  require('./getPersonalLoanListHandler'); //
const GetEnterpriseLoanListHandler =  require('./getEnterpriseLoanListHandler'); //
const CollectionLoanRankingListHandler =  require('./collectionLoanRankingListHandler'); //

//employee_group
class Service {
  constructor() {

  }

  async collectionMgrList(input, _opts) {
    let method = 'collectionMgrList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetEmployeeCountHandler(context));
      svcHandlerMgrt.addHandler(new GetCollectCountInfoHandler(context));
      svcHandlerMgrt.addHandler(new GetCollectListHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async collectionMgrEmployeeList(input, _opts) {
    let method = 'collectionMgrEmployeeList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetEmployeeListHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async collectionMgrVillageList(input, _opts) {
    let method = 'collectionMgrVillageList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetPersonalCollectListHandler(context));
      svcHandlerMgrt.addHandler(new GetEnterpriseCollectListHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async collectionLoanList(input, _opts) {
    let method = 'collectionMgrVillageList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetLoanStatisticsCountHandler(context));
      svcHandlerMgrt.addHandler(new GetLoanPollHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  
  async collectionLoanEmployeeList(input, _opts) {
    let method = 'collectionLoanEmployeeList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetEmployeeLoanListHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async collectionLoanVillageList(input, _opts) {
    let method = 'collectionLoanVillageList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetPersonalLoanListHandler(context));
      svcHandlerMgrt.addHandler(new GetEnterpriseLoanListHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async collectionLoanRankingList(input, _opts) {
    let method = 'collectionLoanRankingList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new CollectionLoanRankingListHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();
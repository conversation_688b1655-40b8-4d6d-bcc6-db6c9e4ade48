'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:index');
// const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const LandOriginFundReceiveListHandler = require("./LandOriginFundReceiveListHandler");
const LandOriginFundReceiveFormatHandler = require('./LandOriginFundReceiveFormatHandler');

const {addHandlersForService} = require('../../utils/general')


class Service {
  constructor() {
    addHandlersForService.call(this,debug);
  }

  landOriginFundReceiveList(){
    return [LandOriginFundReceiveListHandler,LandOriginFundReceiveFormatHandler]
  }

  // landOriginFundReceiveExport(){
  //   return [LandOriginFundReceiveListHandler,LandOriginFundReceiveFormatHandler,LandOriginFundReceiveExportHandler];
  // }
  //
  // landOriginFundReceiveImport(){
  //   return [LandOriginFundReceiveImportHandler,LandOriginFundReceiveRequestHandler];
  // }
  //
  // landOriginFundReceiveRequest(){
  //   return [LandOriginFundReceiveRequestHandler];
  // }
  //
  // landOriginFundReceiveConfirm(){
  //   return [LandOriginFundReceiveConfirmHandler];
  // }
  //
  // landOriginFundReceiveUnusedOne(){
  //   return [LandOriginFundReceiveUnusedHandler];
  // }

}

module.exports = new Service();
'use strict';

const HANDLER_NAME = 'LoanApplicationFundReceiveListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    fundReceive:fundReceiveData,
    userVerifys:userVerifyData,
    employees:employeeData,
    loanLand:loanLandData,
    cxwqCirculations:cxwqCirculationData,
    cxwqCirculationFundReceive:cxwqCirculationFundReceiveData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const moment = require('moment');
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const Decimal = require('decimal.js'),unitDenominator = 100;

const statusText = {
    "new":'待导入流转信息',
    verify_1:'待县级预审',
    verify_2:'待监管初审',
    verify_3:'待监管复审',
    verify_4:'待董事长终审',
    verify_5:'待生成支付令',
    verify_6:'待支付',
    rejected_verify_1:'县域预审拒绝',
    rejected_verify_2:'监管初审拒绝',
    rejected_verify_3:'监管复审拒绝',
    rejected_verify_4:'董事长终审拒绝',
    rejected_verify_5:'生成支付令拒绝',
    rejected_verify_6:'支用拒绝',
    finished:'支用完成',
    closed:'已结清',
}

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:condition,opts:{getOne,uId,tId,formatOpts,followFundReceiveStatus}} = this.context;
        debug(`${HANDLER_NAME}Para1`,condition.fundReceiveId,getOne,uId,tId,condition,formatOpts);

        followFundReceiveStatus && ( delete condition.fundReceiveStatus );//如果前端要求跟随订单状态，如此处理
        assert(condition.fundReceiveId,'E_LAND_ORIGIN_LIST_FUND_RECEIVE_000','fundReceiveId is required ');
        // assert(uId,'E_LAND_ORIGIN_LIST_FUND_RECEIVE_001','uId is required');
        // assert(tId,'E_LAND_ORIGIN_LIST_FUND_RECEIVE_002','tId is required');
        // const fundReceiveCondition = {uId,action:'land',archived:false,limit:'unlimited'};
        //此处归并了两个逻辑分支：a 指定了订单id，通常用于查询旧订单 b 自动寻找草稿状态的支用订单
        // condition.fundReceiveId && (fundReceiveCondition._id = condition.fundReceiveId);
        // condition.fundReceiveId || (fundReceiveCondition.status = 'new');//如果没指定订单编号，就查当前是草稿状态的支用订单
        const fundReceive = condition.fundReceiveId && await fundReceiveData.getById(condition.fundReceiveId);
        assert(fundReceive,'E_LAND_ORIGIN_LIST_FUND_RECEIVE_003','指定的支用订单id有误');
        // !condition.fundReceiveId && assert(fundReceive,'E_LAND_ORIGIN_LIST_FUND_RECEIVE_004','没有生成支用订单编号');
        // const appList = await loanApplicationData.getByCondition(
        //     {uId,tId,status:'loaned',archived:false,limit:'unlimited'});

        const result = await cxwqCirculationFundReceiveData.getListAndCountByCondition(
            {...condition,archived:false,fundReceiveId:fundReceive._id});
        // debug('debug233',result.total,result.result);
        const list = result.result = await Promise.all( result.result.map(async fundReceiveInfo=> //只要没有物理删除过数据，不可能为空
            Object.assign( await cxwqCirculationData.getById(fundReceiveInfo.cxwqId) , {fundReceiveInfo} ) ) );
        debug(`${HANDLER_NAME}Para2`,list.length ,list.map(v=>v._id));

        this.context.opts.formatOpts = {...formatOpts||{},formatOss:1};
        this.context.opts.formatList = list;
        this.context.result = null;

        assert(!getOne || list.length ,'E_LAND_ORIGIN_FUND_RECEIVE_DETAIL_001','record of this id not found')
        getOne && ( this.context.result = list[0]);

        const all = fundReceive && await cxwqCirculationFundReceiveData.getByCondition(
            {fundReceiveId:fundReceive._id,archived: false,limit:'unlimited'}) || [];
        fundReceive && ( fundReceive.statusName = statusText[fundReceive.status] );

        const totalArrearageFeesYuan = fundReceive.snapshotInfo && fundReceive.snapshotInfo.totalAmount
            && fundReceive.snapshotInfo.totalAmount.totalArrearageFeesYuan || '0';
        const totalAmountYuan = all
            .map(v=>v.amountYuan)
            .reduce((r,v)=>r.add(new Decimal(v)),new Decimal(0));
            // .div(new Decimal(100)).toString();
        this.context.result || ( this.context.result = {...result,totalArrearageFeesYuan,totalAmountYuan,fundReceive} );
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
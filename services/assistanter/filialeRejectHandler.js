/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'filialeRejectHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const assistantTrackingData = require('../dataSvc/dataUtil').assistantTracking;
const assistantProcessData = require('../dataSvc/dataUtil').assistantProcess;


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;


      let process = self.context.opts.process;


      if (process.status != "filiale_approve") {
        throw {
          errorCode: 'E_LOAN_STATUS_CHANGE_037',
          httpCode: 406,
          reason: '当前订单已被审核,请勿重复操作'
        }
      }

      // 如果状态不是通过则跳过
      if (condition.status != "reject") {

        debug(method, '[Exit](success)', "do notthing");
        return done();
      }

      let payload = {
        lastModTime: new Date(),
        status: "reject_filiale"
      };

      let trackingPayload = {
        assistantId: process.assistantId,
        flowStatus: process.flowStatus,
        status: "reject_filiale",
        "source": opts.uId,
        "target": condition.id,
        parameters: condition,
        "comments": condition.comments
      };

      let assistanters = await assistantersData.getById(process.assistantId);
      if (assistanters.flowStatus == "signing") {
        await assistantersData.putById(process.assistantId, { flowStatus: "unsigned", lastModTime: new Date() });
      }

      let result = await assistantProcessData.putById(condition.id, payload)
      await assistantTrackingData.post(trackingPayload);


      self.context.result = {
        success: true
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
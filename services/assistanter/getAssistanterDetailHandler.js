/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'getAssistanterDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const assistantProcessData = require('../dataSvc/dataUtil').assistantProcess;
const assistantTrackingData = require('../dataSvc/dataUtil').assistantTracking;
const employeeData = require('../dataSvc/dataUtil').employees;
const { ASSISTANTER_STATUS_MAP, FLOW_STATUS, PROCESS_FLOW_STATUS } = require('../../utils/assistanter_status')
const formatAreaCode = require('../../persistence/formatAreaCode');
const moment = require('moment');
const aliOssSvc = require('../aliOssSvc');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;

      let result = await assistantersData.getById(condition.id);
      let promises = []
      result.flowName = FLOW_STATUS.get(result.flowStatus) || "";
      result.createdTime = moment(result.createdTime).format("YYYY-MM-DD HH:mm:ss");
      result.isRevokedName = result.isRevoked == true ? "禁用" : "启用"
      if (result.eId) {
        let employee = await employeeData.getById(result.eId);
        if (employee) {
          result.photo = employee.photo || null;
        }
      }
      if (result.frontIDCardImage) {
        await formatImg(result.frontIDCardImage)
      }

      if (result.backIDCardImage) {
        await formatImg(result.backIDCardImage)
      }
      if (result.photo) {
        await formatImg(result.photo)
      }


      promises.push(formatAreaCode.getFormatAreaCode(result.areaList).then(data => {
        result.areaRegion = data.region
      }))
      result.signInfo = {
        signStatus: "未签约",
        signStart: "",
        signEnd: "",
        agreementUrl: ""
      }
      let signInfo = await assistantProcessData.getOneByCondition({
        flowStatus: "sign",
        status: "finish_sign",
        assistantId: result._id,
        archived: false
      })

      if (signInfo) {
        result.signInfo.signStatus = "已签约"
        result.signInfo.signStart = moment(signInfo.signStart).format("YYYY-MM-DD");
        result.signInfo.signEnd = moment(signInfo.signEnd).format("YYYY-MM-DD");

        if (signInfo.agreementUrl) {
          result.signInfo.agreementUrl = await aliOssSvc.getFile({ fileName: signInfo.agreementUrl })
        }

      }
      let tracking = await assistantTrackingData.getByCondition({
        assistantId: result._id,
        archived: false
      });

      for (const item of tracking) {
        item.flowName = PROCESS_FLOW_STATUS.get(item.flowStatus);
        item.statusName = ASSISTANTER_STATUS_MAP.get(item.action);
        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss")
      }
      result.flowInfo = tracking;
      await Promise.all(promises);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
async function formatImg(item) {
  item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0 &&
    (item.thumbnail.fullUrl = await aliOssSvc.getFile({ fileName: item.thumbnail.url }));
  item && item.image && item.image.url && item.image.url.indexOf('http') !== 0 &&
    (item.image.fullUrl = await aliOssSvc.getFile({ fileName: item.image.url }));
}
module.exports = Handler;
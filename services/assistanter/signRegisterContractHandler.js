/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'updateAssistanterHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const contractData = require('../dataSvc/dataUtil').contracts;
const contractTemplateData = require('../dataSvc/dataUtil').contractTemplates;
const flowData = require('../dataSvc/dataUtil').contractFlowsV2;
const contractFlowSignTasksV2 = require('../dataSvc/dataUtil').contractFlowSignTasksV2;
const eSignSvc = require('../eSignSvc');
const moment = require('moment');
const config = require('config');
const CALLBACK_URL = "/api/v1.0/eSign/assistanter/callback";
const configSealsData = require('../dataSvc/dataUtil').configSeals;
const assistantTrackingData = require('../dataSvc/dataUtil').assistantTracking;
const assistantProcessData = require('../dataSvc/dataUtil').assistantProcess;
const dictAreasData = require('../dataSvc/dataUtil').dictAreas;
const employeeData = require('../dataSvc/dataUtil').employees
const league = "YYN";


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;
      let assistanter = await assistantersData.getById(condition.id);
      if (!assistanter) {
        throw {
          errorCode: 'A_SIGN_REGISTER_43',
          httpCode: 406,
          reason: 'assistanter is not find'
        }
      }

      let process = await assistantProcessData.getOneByCondition({
        assistantId: assistanter._id,
        flowStatus: "sign",
        status: {
          $in: ["signing", "county_approve", "filiale_approve", "hq_approve", "finish_sign"]
        },
        archived: false
      })
      if (process) {
        throw {
          errorCode: 'A_SIGN_CONTRACT_PROCESS_69',
          httpCode: 406,
          reason: '该协理员已在进行合同签约请勿重复签约！'
        }
      }

      // 修改订单状态
      process = await assistantProcessData.post({
        assistantId: assistanter._id,
        areaList: assistanter.areaList,
        flowStatus: "sign",
        status: "signing",
        signStart: new Date(condition.signStart),
        signEnd: new Date(condition.signEnd),
      });



      opts.assistanter = assistanter;
      // 查询是否已经创建合同
      let contract = await contractData.getOneByCondition({
        loanId: process._id,
        uId: condition.id,
        type: 18,
        archived: false
      });
      let template = await contractTemplateData.getOneByCondition({
        type: 18,
        archived: false
      });
      if (!template) {
        throw {
          errorCode: 'A_SIGN_REGISTER_CONTRACT_56',
          httpCode: 406,
          reason: 'contract Template is not exists'
        }
      }
      //分公司盖章
      let city = assistanter.areaList.substr(0, 4);
      let cityInfo = await dictAreasData.getOneByCondition({ code: city });
      let sealInfo = await configSealsData.getOneByCondition({
        tId: opts.tId,
        area: city,
        archived: false,
      })

      // 未创建合同先创建合同
      if (!contract) {

        contract = await contractData.post({
          loanId: process._id,
          type: 18,
          uId: condition.id,
          archived: false,
          tId: template._id,
          name: template.name,
          signatory: 4,

        })
      }

      if (!contract.eDocId) {

        let employee = await employeeData.getById(opts.uId);
        self.context.opts.employee = employee;
        let today = new Date();
        let signStart = new Date(condition.signStart);
        let signEnd = new Date(condition.signEnd);
        let village = await dictAreasData.getOneByCondition({ code: assistanter.areaList });
        // 合同没生成过，在E签宝中创建合同
        let payload = {
          templateId: template.eTempId,
          name: template.name,
          simpleFormFields: {
            "jrywdlxy_1": cityInfo.name.replace("市", ""),
            "jrywdlxy_2": sealInfo.address,
            "jrywdlxy_3": sealInfo.legalName,
            "jrywdlxy_4": employee.username,
            "jrywdlxy_5": employee.mobile,
            "jrywdlxy_6": assistanter.username,
            "jrywdlxy_7": assistanter.IDCard,
            "jrywdlxy_8": assistanter.mobile,
            "jrywdlxy_9": assistanter.domicileAddress,
            "jrywdlxy_10": assistanter.presentAddress,
            "jrywdlxy_11": today.getFullYear(),
            "jrywdlxy_12": today.getMonth() + 1,
            "jrywdlxy_13": today.getDate(),
            "jrywdlxy_14": village.name,
            "jrywdlxy_15": village.name,
            "jrywdlxy_16": village.name,
            "jrywdlxy_17": village.name,
            "jrywdlxy_18": signStart.getFullYear(),
            "jrywdlxy_19": signStart.getMonth() + 1,
            "jrywdlxy_20": signStart.getDate(),
            "jrywdlxy_21": signEnd.getFullYear(),
            "jrywdlxy_22": signEnd.getMonth() + 1,
            "jrywdlxy_23": signEnd.getDate(),
            "jrywdlxy_24": sealInfo.legalName,
            "jrywdlxy_25": moment(today).format("YYYY-MM-DD"),
            "jrywdlxy_26": moment(today).format("YYYY-MM-DD"),
          }
        };

        const sSignContract = await eSignSvc.createDocByTemplate(payload, league);
        if (!sSignContract) {
          throw {
            errorCode: 'C_CREDIT_ACCESS_56',
            httpCode: 406,
            reason: 'CA contract is Create fail',
          }
        }

        contract = await contractData.putById(contract._id, {
          eDocId: sSignContract.docId,
          eDocUrl: sSignContract.docUrl,
          lastModTime: new Date()
        });
      }

      //查询是否创建了流程
      let flow = await flowData.getOneByCondition({
        loanId: process._id,
        type: 14,
        archived: false
      })

      // 不存在则创建签约流程
      if (!flow) {
        let flowRes = await eSignSvc.createMoreContractFlow({
          businessScene: "协理员入驻签约",
          signPlatform: '1',
          docList: [{
            docId: contract.eDocId,
            docName: `${assistanter.username}的${contract.name}`
          }],
          noticeUrl: `${config.get('baseUrl')}${CALLBACK_URL}`
        }, league);
        if (!flowRes) {
          throw {
            errorCode: 'E_CREATE_FLOW_042',
            httpCode: 406,
            reason: 'eSignFlow is fail'
          }
        }
        let contractFlow = {

          loanId: process._id,
          type: 14, // 入驻签约流程
          cId: [contract._id],
          eFlowId: flowRes.flowId,
          eBuzScene: "协理员入驻签约"
        };
        flow = await flowData.post(contractFlow);
        if (!flow) {
          throw {
            errorCode: 'E_CREATE_FLOW_042',
            httpCode: 406,
            reason: 'contractFlow is fail'
          }
        }
      }


      assistanter = await assistantersData.putById(condition.id, { flowStatus: "signing", lastModTime: new Date(), eFlowId: flow.eFlowId });
      // 添加日志
      await assistantTrackingData.post({
        assistantId: condition.id,
        source: opts.uId,
        "target": process._id,
        action: "signing",
        parameters: condition,
        comments: "签约中"
      })


      // 后续发送签约短信
      self.context.result = flow;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'countyApproveHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const assistantTrackingData = require('../dataSvc/dataUtil').assistantTracking;
const assistantProcessData = require('../dataSvc/dataUtil').assistantProcess;
const employeeData = require('../dataSvc/dataUtil').employees;
const employeeGroupData = require('../dataSvc/dataUtil').employeeGroups;
const config = require('config')
const superagent = require("superagent")
const createEmployeeUrl = `http://${config.get('rongxin_admin_api_service.host')}/api/v1.0/system/employee/create`;
const eSignSvc = require('../eSignSvc');
const contractData = require('../dataSvc/dataUtil').contracts;
const configSealsData = require('../dataSvc/dataUtil').configSeals;
const uuid = require('uid-safe');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;


      let process = await assistantProcessData.getById(condition.id)


      if (process.status != "filiale_approve") {
        throw {
          errorCode: 'E_LOAN_STATUS_CHANGE_037',
          httpCode: 406,
          reason: '当前订单已被审核,请勿重复操作'
        }
      }
      let assistanter = await assistantersData.getById(process.assistantId);
      self.context.opts.process = process;

      // 如果状态不是通过则跳过
      if (condition.status != "pass") {

        debug(method, '[Exit](success)', "do notthing");
        return done();
      }

      let payload = {
        lastModTime: new Date()
      };
      let assistanterPayload = {
        lastModTime: new Date()
      }

      let trackingPayload = {
        assistantId: process.assistantId,
        flowStatus: process.flowStatus,
        "source": opts.uId,
        "target": condition.id,
        parameters: condition,
        "comments": condition.comments
      };

      //  签约分公司审批通过 
      if (process.flowStatus == "sign") {
        // 审批通过 签约成功
        payload.status = "finish_sign";
        trackingPayload.status = "finish_sign";
        assistanterPayload.flowStatus = "finish_sign"

        // 分公司落章和归档
        //分公司盖章

        let contract = await contractData.getOneByCondition({
          loanId: process._id,
          uId: process.assistantId,
          type: 18,
          archived: false
        });

        let city = assistanter.areaList.substr(0, 4);

        let sealInfo = await configSealsData.getOneByCondition({
          tId: opts.tId,
          area: city,
          archived: false,
        })

        let taskId_JINFU = uuid.sync(24);
        let eSignCond_JINFU = {
          flowId: assistanter.eFlowId,
          thirdOrderNo: taskId_JINFU,
          accountId: sealInfo.organAccountId,
          sealId: sealInfo.sealId,
          signDocList: [{
            docId: contract.eDocId,
            posList: [{
              "signType": 1,
              "key": "盖章",
              "posX": 100
            }]
          }]
        };

        // 分公司静默签
        let autoRes = await eSignSvc.autoSignMoreContractFlowTask(eSignCond_JINFU, "YYN")
        debug(method, '[createContractFlowESign JINFU]', autoRes);





        // 开通协理员账号
        let employees = await employeeData.getOneByCondition({ mobile: assistanter.mobile, tId: "5fb364d400ad51b9e3efc702" });
        // 存在账号情况
        if (employees) {
          if (employees.isRevoked) {
            await employeeData.putById(employees._id, { isRevoked: false });
          }
          let employeeGroup = await employeeGroupData.getOneByCondition({
            employee: employees._id,
            group: "5eb8ff2ec6ecfe44d4ecaed9" //协理员角色
          });
          if (!employeeGroup) {
            await employeeGroupData.post({
              employee: employees._id,
              group: "5eb8ff2ec6ecfe44d4ecaed9",
              areaList: [assistanter.areaList]
            })
          } else {
            await employeeGroupData.putById(employeeGroup._id, {
              areaList: [assistanter.areaList],
              archived: false,
              lastModTime: new Date()
            })
          }

          assistanterPayload.isRevoked = false;
          assistanterPayload.eId = employees._id;
        } else {
          //创建协理员账号
          let payload = {
            mobile: assistanter.mobile,
            tId: "5fb364d400ad51b9e3efc702",
            username: assistanter.username,
            idCard: assistanter.IDCard,
            permanentAddress: assistanter.presentAddress,
            profession: assistanter.position,
            groups: [{
              group: "5eb8ff2ec6ecfe44d4ecaed9",
              areaList: [assistanter.areaList]
            }]
          };
          employees = await superagent.post(createEmployeeUrl).send(payload);

          assistanterPayload.isRevoked = false;
          assistanterPayload.eId = employees._id;
        }
        await sleep(2000)
        eSignSvc.archiveContractFlow({
          flowId: assistanter.eFlowId
        }, "YYN").then(data => {
          debug(method, '[archiveContractFlow](success)', data);
        });

      }

      // 解约审批通过
      if (process.flowStatus == "relieve") {
        // 审批通过 解约成功
        payload.status = "finish_relieve";
        trackingPayload.status = "finish_relieve";
        assistanterPayload.flowStatus = "finish_relieve"
        // 禁用协理员账号
        if (assistanter.eId) {
          await employeeData.putById(assistanter.eId, { isRevoked: true });
          assistanterPayload.isRevoked = true;
        }

        //同时将之前已签约的合同做废
        let signProcess = await assistantProcessData.getOneByCondition({
          flowStatus: "sign",
          status: "finish_sign",
          assistantId: process.assistantId,
          archived: false
        });
        if (signProcess) {
          await assistantProcessData.putById(signProcess._id, {
            status: "relieve",
            lastModTime: new Date()
          })
        }
      }

      await assistantProcessData.putById(condition.id, payload)
      await assistantTrackingData.post(trackingPayload);
      debug(method, "[assistanterPayload]", assistanterPayload)
      await assistantersData.putById(process.assistantId, assistanterPayload)

      self.context.result = {
        success: true
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}

async function sleep(time = 0) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve();
    }, time);
  })
};
module.exports = Handler;
/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'createAssistantersHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const assistantTrackingData = require('../dataSvc/dataUtil').assistantTracking
const config = require('config');
const querystring = require('qs');
const agent = require('superagent');
const VERIFY_URL = "https://mobile3elements.shumaidata.com/mobile/verify_real_name";
let APPCODE = config.get("mobile3elements").appCode;
const employeeGroupData = require('../dataSvc/dataUtil').employeeGroups;
const employeeData = require('../dataSvc/dataUtil').employees;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;
      condition.flowStatus = "unsigned"

      // 查询当前区域是否有协理员


      let assistanter = await assistantersData.getOneByCondition({
        areaList: condition.areaList,
        archived: false,
        flowStatus: { $in: ["signing", "finish_sign"] }
      });
      if (assistanter) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_CREATE_029',
          reason: '当前区域已存在协理员，请勿重复创建'
        }
      }

      assistanter = await assistantersData.getOneByCondition({
        archived: false,
        IDCard: condition.IDCard,
        mobile: condition.mobile,
        username: condition.username,
        flowStatus: { $in: ["unsigned", "signing", "finish_sign", "finish_relieve"] }
      });
      if (assistanter) {
        throw {
          httpCode: 406,
          errorCode: 'E_ASSISTANTER_CREATE_066',
          reason: '当前协理员信息已在其他区域添加，请勿重复创建'
        }
      }

      // 查询是否有开通账户的协理员
      let employeeGroup = await employeeGroupData.getOneByCondition({
        areaList: condition.areaList,
        group: "5eb8ff2ec6ecfe44d4ecaed9" //协理员角色
      });
      if (employeeGroup) {
        let employee = await employeeData.getById(employeeGroup.employee);
        if (employee && !employee.isRevoked) {
          if (employee.username != condition.username || employee.mobile != condition.mobile) {
            throw {
              httpCode: 406,
              errorCode: 'E_ASSISTANTER_CREATE_082',
              reason: '当前区域已存在协理员，请勿重复创建'
            }
          }
        }
      }

      // 三要素校验
      let payload = {
        idcard: condition.IDCard,
        mobile: condition.mobile,
        name: condition.username
      }
      payload = querystring.stringify(payload);

      let auth = { "Authorization": `APPCODE ${APPCODE}` };
      let resVer = {};
      // 测试要求先注释掉
      await agent.post(VERIFY_URL).set(auth).send(payload).then(data => {
        resVer = data;
      }).catch(error => {
        let res = error.response && error.response.body;
        debug(method, '[error]', error);
        throw {
          errorCode: 'E_VERIFY_045',
          httpCode: error.status,
          reason: res.message || '系统异常，请联系系统管理员。'
        };
      });

      let verifyResult = resVer && resVer.body;
      debug(method, '[verifyResult]', verifyResult);
      if (!verifyResult.result || verifyResult.result.res !== "1") {
        throw {
          errorCode: 'E_VERIFY_054',
          httpCode: 406,
          reason: "姓名，身份证，手机号不匹配，请核实"
        };
      }


      //创建协理员信息
      let result = await assistantersData.post(condition);

      // 创建tracking
      await assistantTrackingData.post({
        assistantId: result._id,
        source: opts.uId,
        action: "new",
        parameters: condition,
        comments: "新创建"
      })

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'relieveAssistantersHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const assistantTrackingData = require('../dataSvc/dataUtil').assistantTracking
const assistantProcessData = require('../dataSvc/dataUtil').assistantProcess;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;



      let assistanter = await assistantersData.getById(condition.id);
      if (!assistanter) {
        throw {
          errorCode: 'A_SIGN_REGISTER_43',
          httpCode: 406,
          reason: 'assistanter is not find'
        }
      }
      // let process = await assistantProcessData.getOneByCondition({
      //   assistantId: assistanter._id,
      //   flowStatus: "relieve",
      //   status: {
      //     $in: ["county_approve", "filiale_approve", "hq_approve", "finish_relieve"]
      //   }
      // })

      // if (process) {
      //   throw {
      //     errorCode: 'A_SIGN_CONTRACT_PROCESS_69',
      //     httpCode: 406,
      //     reason: '该协理员已在进行解约流程请勿重复解约！'
      //   }
      // }

      let process = await assistantProcessData.post({
        assistantId: assistanter._id,
        areaList: assistanter.areaList,
        flowStatus: "relieve",
        status: "county_approve",
        relieveTime: new Date(),
        relieveReason: condition.reason
      });

      let trackingPayload = {
        assistantId: process.assistantId,
        flowStatus: process.flowStatus,
        status: process.status,
        "source": opts.uId,
        "target": condition.id,
        parameters: condition,
        "comments": condition.comments
      };

      await assistantTrackingData.post(trackingPayload);

      self.context.result = {
        success: true
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
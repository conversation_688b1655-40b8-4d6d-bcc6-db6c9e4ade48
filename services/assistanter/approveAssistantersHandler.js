/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'approveAssistantersHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const assistantTrackingData = require('../dataSvc/dataUtil').assistantTracking


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;

      let assistanter = await assistantersData.getById(condition.id)

      let payload = {
        lastModTime: new Date()
      };

      let trackingPayload = {
        assistantId: condition.id,
        flowStatus: assistanter.flowStatus,
        "source": opts.uId,
        "target": condition.id,
        parameters: condition,
        "comments": input.comments
      };

      // 待县域审批 通过 待分公司审批
      if (assistanter.status == "county_approve") {
        payload.status = "filiale_approve";
        trackingPayload.status = "filiale_approve";
      }

      // 待分公司审批 通过 待总部审批
      if (assistanter.status == "filiale_approve") {
        payload.status = "hq_approve";
        trackingPayload.status = "hq_approve";
      }

      // 待总部审批 通过 已签约 
      if (assistanter.status == "hq_approve") {
        payload.status = "finish_sign";
        trackingPayload.status = "finish_sign";
      }



      if (payload.status !== input.status) {
        await requestLimit.unlock(input.id);
        throw {
          errorCode: 'E_LOAN_STATUS_CHANGE_037',
          httpCode: 406,
          reason: '当前订单已被处理，请处理其他订单'
        }
      }

      let result = await assistantersData.putById(condition.id, payload)
      await assistantTrackingData.post(trackingPayload);

      self.context.result = {
        success: true
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
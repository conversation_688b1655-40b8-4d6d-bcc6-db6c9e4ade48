/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'getAssistanterListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const assistantProcessData = require('../dataSvc/dataUtil').assistantProcess;
const { ASSISTANTER_STATUS_MAP, FLOW_STATUS } = require('../../utils/assistanter_status')
const formatAreaCode = require('../../persistence/formatAreaCode');
const employeeData = require('../dataSvc/dataUtil').employees;
const moment = require('moment');
const FLOWSTATUS = new Map([
  ["unsigned", "sign"],
  ["signing", "sign"],
  ["finish_sign", "relieve"],
  ["finish_relieve", "renewal"]
]);

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;

      let result = await assistantersData.getListAndCountByCondition(condition);
      let promises = [];
      for (const item of result.result) {

        item.flowName = FLOW_STATUS.get(item.flowStatus) || "";
        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss");

        item.isRevokedName = item.isRevoked == true ? "禁用" : "启用"
        promises.push(formatAreaCode.getFormatAreaCode(item.areaList).then(data => {
          item.areaRegion = data.region
        }))

        //当前是需要签约还是解约
        promises.push(assistantProcessData.getOneByCondition({
          flowStatus: FLOWSTATUS.get(item.flowStatus),
          status: { $in: ["signing", "county_approve", "filiale_approve", ] },
          assistantId: item._id,
          archived: false
        }).then(data => {
          if (data) {
            item.display = false;
          } else {
            item.display = true;
          }
          debug(method, `[${item.username}:display]`, item.display);
        }))

        // 判断是否签约
        promises.push(assistantProcessData.getOneByCondition({
          flowStatus: "sign",
          status: "finish_sign",
          assistantId: item._id,
          archived: false
        }).then(data => {
          if (data) {
            item.signStart = moment(data.signStart).format("YYYY-MM-DD")
            item.signEnd = moment(data.signEnd).format("YYYY-MM-DD")
          }
        }));

        // 最新审批状态
        promises.push(assistantProcessData.getOneByCondition({
          assistantId: item._id,
          archived: false,
          $sort: { createdTime: -1 }
        }).then(data => {
          if (data) {
            item.approveStatusName = ASSISTANTER_STATUS_MAP.get(data.status)
          }
        }))

        if (!item.eId) {
          let employee = await employeeData.getOneByCondition({
            mobile: item.mobile,
            tId: "5fb364d400ad51b9e3efc702",
            idCard: item.IDCard,
            archived: false
          });
          if (employee) {
            await assistantersData.putById(item._id, { eId: employee._id });
          }
        }

      }
      await Promise.all(promises);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
/*
 * @Author: wcy 
 * @Date: 2019-01-22 23:07:47 
 * @Last Modified by: wcy
 * @Last Modified time: 2019-03-22 10:07:51
 */

'use strict';

const HANDLER_NAME = 'sendSMSToAssistanterHandler';
const logUtil = require('../../utils/logUtil');
const logFactory = require('../../utils/logFactory');
const debug = logFactory(logUtil())(`rongxin:app.api:services:contract:${HANDLER_NAME}`);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const dataUtil = require('../dataSvc/dataUtil');
const aLiYunMsg = require('../messages/sendSmsALiYunMessage');
const assistantersData = require('../dataSvc/dataUtil').assistanters;

const env = require('../env');

// const Templateid = '426847'; 
const Templateid = 'SMS_196651102';

class <PERSON><PERSON> extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    let self = this;
    let method = `${HANDLER_NAME}.doAsync`;
    debug(method, '[Enter]');

    try {
      let input = self.context.input;
      let uId = self.context.opts.uId; //当前申请人的id
      let assistanter = self.context.opts.assistanter;
      let employee = self.context.opts.employee

      let mobile = assistanter.mobile;
      /**
       * 尊敬的用户您好， 用户 {
         1
       }
       发起了短信签约， 您有文件需要签署， 签署链接： http: //{2}loan.cacfintech.com/yyn/certification?uId={3}&flowId={4}  风险提示：请您电话确认信息真实性，如信息不实请忽略。
       */
      /** 短链： http://test-t.cacfintech.com/4dRPDA
       * 尊敬的用户您好， 用户 {1}
       发起了短信签约， 您有文件需要签署， 签署链接： http: //{2}cacfintech.com/{3}  风险提示：请您电话确认信息真实性，如信息不实请忽略。
       { account } 您好！ 有一份线上服务站合作协议需要您签署, 签署链接： http: //{subdomain}cacfintech.com/{path}

       */
      let scope = env.getServerMode() === 'production' ? 't.' : `${env.getServerMode()}-t.`;
      let queryString = self.getQueryString(input.signUrl);

      if (!queryString || !queryString.flowId || !queryString.uId || !mobile) {
        throw {
          httpCode: 406,
          errorCode: 'E_CON_SMS_059',
          reason: 'invalid params'
        };
      }
      // let qs_flowId = queryString['flowId'];
      // let qs_uId = queryString['uId'];
      // let param = userInfo.realname + ',' + scope + ',' + qs_uId + ',' + qs_flowId;
      let shortId = self.context.opts.shortId;
      // let param = userInfo.realname + '|' + scope + ',' + shortId;
      //let param = `${userInfo.realname}|http://${scope}cacfintech.com/${shortId}`
      let param = {
        account: employee.username,
        subdomain: scope,
        path: shortId
      };
      let signName = "益易农";



      let content = {
        payload: {
          PhoneNumbers: mobile,
          TemplateCode: Templateid,
          SignName: signName,
          TemplateParam: JSON.stringify(param),
        },

        caller: "rongxin_userapp"
      };

      let result = await aLiYunMsg.sendSms(content);
      debug(method, 'Sms msg sent', result.body);
      let msgRes = result.body;
      if (msgRes && msgRes.data.isSuccess == false) {
        throw {
          httpCode: 406,
          errorCode: 'MSG_ERROR',
          reason: msgRes.data.reason || 'failed to send internal msg'
        };
      }
      await assistantersData.putById(assistanter._id, { signUrl: `http://${scope}cacfintech.com/${shortId}` });
      self.context.result = {
        result: 'success'
      };
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    return done();
  }

  getQueryString(url) {
    let qs = url.split('?')[1], // 获取url中"?"符后的字串   
      args = {}, // 保存参数数据的对象
      items = qs.length ? qs.split("&") : [], // 取得每一个参数项,
      item = null,
      len = items.length;

    for (let i = 0; i < len; i++) {
      item = items[i].split("=");
      let name = decodeURIComponent(item[0]),
        value = decodeURIComponent(item[1]);
      if (name) {
        args[name] = value;
      }
    }
    return args;
  }
}

module.exports = Hanlder;
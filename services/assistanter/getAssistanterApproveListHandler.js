/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'getAssistanterApproveListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const assistantProcessData = require('../dataSvc/dataUtil').assistantProcess;
const { ASSISTANTER_STATUS_MAP, FLOW_STATUS, PROCESS_FLOW_STATUS } = require('../../utils/assistanter_status')
const formatAreaCode = require('../../persistence/formatAreaCode');
const moment = require('moment');
const APPROVE_ROLE = require('../../services/permission').APPROVE_ROLE;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;

      let opts = self.context.opts;
      //  根据传入的角色不同筛选不同的数据

      if (opts.role == APPROVE_ROLE.STATIONMASTER) {
        condition.status = "county_approve"
      }
      if (opts.role == APPROVE_ROLE.BUSINESSSUPERVISION) {
        condition.status = "filiale_approve"
      }

      let result = await assistantProcessData.getListAndCountByCondition(condition);
      let promises = [];
      for (const item of result.result) {
        item.statusName = ASSISTANTER_STATUS_MAP.get(item.status) || "";

        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss");

        item.signStart = moment(item.signStart).format("YYYY-MM-DD")
        item.signEnd = moment(item.signEnd).format("YYYY-MM-DD")

        promises.push(formatAreaCode.getFormatAreaCode(item.areaList).then(data => {
          item.areaRegion = data.region
        }))
        promises.push(assistantersData.getById(item.assistantId).then(data => {
          if (data) {
            item.flowName = FLOW_STATUS.get(data.flowStatus) || "";
            item.username = data.username;
            item.mobile = data.mobile;
            item.IDCard = data.IDCard;
            item.isRevokedName = data.isRevoked ? "禁用" : "启用"
          }
        }))

      }
      await Promise.all(promises);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'getUserPhotoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeesData = require("../dataSvc/dataUtil").employees;
const aliOssSvc = require('../aliOssSvc');
const assistantersData = require('../dataSvc/dataUtil').assistanters;


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {

      let opts = self.context.opts;
      let result = await employeesData.getById(opts.uId);
      if (result.photo) {
        await formatImg(result.photo)
      }

      let assistanter = await assistantersData.getOneByCondition({ mobile: result.mobile, flowStatus: "finish_sign", archived: false, isRevoked: false });
      if (assistanter) {
        if (assistanter.frontIDCardImage) {
          await formatImg(assistanter.frontIDCardImage)
        }

        if (assistanter.backIDCardImage) {
          await formatImg(assistanter.backIDCardImage)
        }

        result.assistanter = assistanter;
      }

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}

async function formatImg(item) {
  item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0 &&
    (item.thumbnail.fullUrl = await aliOssSvc.getFile({ fileName: item.thumbnail.url }));
  item && item.image && item.image.url && item.image.url.indexOf('http') !== 0 &&
    (item.image.fullUrl = await aliOssSvc.getFile({ fileName: item.image.url }));
}
module.exports = Handler;
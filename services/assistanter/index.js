/**
 * client Server index
 * <AUTHOR>
 */


'use strict'

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:area:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetAssistanterListHandler = require('./getAssistanterListHandler')
const CreateAssistantersHandler = require('./createAssistantersHandler')
const UpdateAssistanterHandler = require('./updateAssistanterHandler')
const GetAssistanterDetailHandler = require('./getAssistanterDetailHandler')
const PreDestineLoanAppHandler = require('./preDestineLoanAppHandler')
const SignRegisterContractHandler = require('./signRegisterContractHandler')
const SendSMSHandler = require('./sendSMSHandler')
const CreateShortUrlHandler = require('./createShortUrlHandler')
const SendSMSToAssistanterHandler = require('./sendSMSToAssistanterHandler')

const RelieveAssistantersHandler = require('./relieveAssistantersHandler')
const CountyApproveHandler = require('./countyApproveHandler');
const FilialeApproveHandler = require('./filialeApproveHandler');
const FilialeRejectHandler = require('./filialeRejectHandler');
const GetAssistanterApproveListHandler = require('./getAssistanterApproveListHandler')
const GetUserPhotoHandler = require('./getUserPhotoHandler');
const SetUserPhotoHandler = require('./setUserPhotoHandler');
const SetUserBankAccountHandler = require('./setUserBankAccountHandler')


class Server {
  constructor() {

  }

  async getAssistanterList(input, _opts) {
    let method = 'getAssistanterList';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new PreDestineLoanAppHandler(context));
      svcHandlerMgrt.addHandler(new GetAssistanterListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async createAssistanters(input, _opts) {
    let method = 'createAssistanters';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new CreateAssistantersHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async signRegisterContract(input, _opts) {
    let method = 'signRegisterContract';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new SignRegisterContractHandler(context));

      svcHandlerMgrt.addHandler(new SendSMSHandler(context));
      svcHandlerMgrt.addHandler(new CreateShortUrlHandler(context));
      svcHandlerMgrt.addHandler(new SendSMSToAssistanterHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async updateAssistanter(input, _opts) {
    let method = 'updateAssistanter';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new UpdateAssistanterHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getAssistanterDetail(input, _opts) {
    let method = 'getAssistanterDetail';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetAssistanterDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getAssistanterApproveList(input, _opts) {
    let method = 'getAssistanterApproveList';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new PreDestineLoanAppHandler(context));
      svcHandlerMgrt.addHandler(new GetAssistanterApproveListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async countyApprove(input, _opts) {
    let method = 'countyApprove';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new CountyApproveHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async filialeApprove(input, _opts) {
    let method = 'filialeApprove';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new FilialeApproveHandler(context));
      svcHandlerMgrt.addHandler(new FilialeRejectHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async relieveAssistanters(input, _opts) {
    let method = 'relieveAssistanters';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new RelieveAssistantersHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getUserPhoto(input, _opts) {
    let method = 'getUserPhoto';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetUserPhotoHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async setUserPhoto(input, _opts) {
    let method = 'setUserPhoto';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new SetUserPhotoHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async setUserBankAccount(input, _opts) {
    let method = 'setUserBankAccount';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new SetUserBankAccountHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}
module.exports = new Server();
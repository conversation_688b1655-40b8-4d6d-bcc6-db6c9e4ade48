/*
 * @Author: wcy 
 * @Date: 2019-01-22 23:07:47 
 * @Last Modified by: wcy
 * @Last Modified time: 2019-03-22 10:07:51
 */

'use strict';

const HANDLER_NAME = 'sendSMSHandler';
const logUtil = require('../../utils/logUtil');
const logFactory = require('../../utils/logFactory');
const debug = logFactory(logUtil())(`rongxin:app.api:services:contract:${HANDLER_NAME}`);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const TENANT_LIST = require('../../utils/tenantConst').TENANT_LIST;

const env = require('../env');


class Hanlder extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    let self = this;
    let method = `${HANDLER_NAME}.doAsync`;
    debug(method, '[Enter]');

    try {
      let input = self.context.input;
      let flow = self.context.result;
      let opts = self.context.opts;
      let league = "CG";

      let mode = "";
      if (env.getServerMode() === "development") {
        mode = 'dev-'
      } else if (env.getServerMode() === "production") {
        mode = '';
      } else {
        mode = env.getServerMode() + '-'
      }

      if (opts.tId == TENANT_LIST.TENANT_JF) {
        league = "YYN"
      }
      const H5_URL = 'http://' + mode + 'm.loan.cacfintech.com/yyn/certification';
      let signUrl = `${H5_URL}?flowId=${flow._id}&uId=${input.id}&type=${flow.type}&league=${league}`;

      input.signUrl = signUrl;
      self.context.opts.uId = input.id;

      self.context.result = {
        result: 'success'
      };
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    return done();
  }

  getQueryString(url) {
    let qs = url.split('?')[1], // 获取url中"?"符后的字串   
      args = {}, // 保存参数数据的对象
      items = qs.length ? qs.split("&") : [], // 取得每一个参数项,
      item = null,
      len = items.length;

    for (let i = 0; i < len; i++) {
      item = items[i].split("=");
      let name = decodeURIComponent(item[0]),
        value = decodeURIComponent(item[1]);
      if (name) {
        args[name] = value;
      }
    }
    return args;
  }
}

module.exports = Hanlder;
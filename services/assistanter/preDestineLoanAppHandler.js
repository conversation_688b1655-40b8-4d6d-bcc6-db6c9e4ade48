'use strict';

const HANDLER_NAME = 'preDestineLoanAppHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const APPROVE_ROLE = require('../../services/permission').APPROVE_ROLE;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _opts = self.context.opts;
      let _input = self.context.input;


      if (!_opts.role) {
        debug(method, '[Exit](success)', 'do nothing');
        return done();
      }

      if (!_opts || !_opts.userInfo || !_opts.userInfo.roles) {
        debug(method, '[Exit](success)', 'userInfo not exists');
        return done();
      }

      if (!Array.isArray(_opts.userInfo.roles) || _opts.userInfo.roles.length < 1) {
        debug(method, '[Exit](success)', 'userInfo roles not exists');
        return done();
      }

      let roles = _opts.userInfo.roles;
      let _role = _opts.role;

      let flag = false;
      let realRole = null;
      roles.forEach(item => {
        if (item.name === _role) {
          flag = true;
          realRole = item;
          return;
        }
      });

      if (!flag) {
        throw {
          errorCode: 'E_LOAN_APP_ROLE_055',
          httpCode: 401,
          reason: 'invalid user'
        }
      }



      //客户经理
      if (_role === APPROVE_ROLE.ACCOUNTMANAGER) {
        if (realRole.areaList && realRole.areaList.length > 0) {
          if (!_input.$or) {
            _input.$or = [];
          }
          realRole.areaList.forEach(item => {
            let area = {
              areaList: '/^' + item + '/'
            };
            _input.$or.push(area);
          });
          self.context.input = _input;

          debug(method, '[Exit](success)', self.context.input);
          return done();
        }

      }

      if (realRole.areaList && realRole.areaList.length > 0) {
        if (!_input.$or) {
          _input.$or = [];
        }
        realRole.areaList.forEach(item => {
          let area = {
            areaList: '/^' + item + '/'
          };
          _input.$or.push(area);
        });
        self.context.input = _input;

        debug(method, '[Exit](success)', self.context.input);
        return done();
      }

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
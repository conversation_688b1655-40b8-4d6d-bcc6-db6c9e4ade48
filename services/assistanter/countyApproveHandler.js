/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'countyApproveHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const assistantTrackingData = require('../dataSvc/dataUtil').assistantTracking;
const assistantProcessData = require('../dataSvc/dataUtil').assistantProcess;


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;

      let process = await assistantProcessData.getById(condition.id)

      if (process.status != "county_approve") {

        throw {
          errorCode: 'E_LOAN_STATUS_CHANGE_037',
          httpCode: 406,
          reason: '当前订单已被审核,请勿重复操作'
        }
      }

      let payload = {
        lastModTime: new Date()
      };

      let trackingPayload = {
        assistantId: process.assistantId,
        flowStatus: process.flowStatus,
        "source": opts.uId,
        "target": condition.id,
        parameters: condition,
        "comments": condition.comments
      };

      // 待县域审批 通过 待分公司审批
      if (condition.status == "pass") {
        payload.status = "filiale_approve";
        trackingPayload.status = "filiale_approve";
      }

      // 审批拒绝流程结束
      if (condition.status == "reject") {
        payload.status = "reject_county";
        trackingPayload.status = "reject_county";
        let assistanters = await assistantersData.getById(process.assistantId);
        if (assistanters.flowStatus == "signing") {
          await assistantersData.putById(process.assistantId, { flowStatus: "unsigned", lastModTime: new Date() });
        }
      }

      let result = await assistantProcessData.putById(condition.id, payload)
      await assistantTrackingData.post(trackingPayload);

      self.context.result = {
        success: true
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
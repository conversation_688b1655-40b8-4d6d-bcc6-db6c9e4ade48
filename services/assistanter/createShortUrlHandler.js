/*
 * @Author: wcy 
 * @Date: 2019-01-22 23:07:47 
 * @Last Modified by: wcy
 * @Last Modified time: 2019-03-22 10:18:50
 */

'use strict';

const HANDLER_NAME = 'createShortUrlHandler';
const logUtil = require('../../utils/logUtil');
const logFactory = require('../../utils/logFactory');
const debug = logFactory(logUtil())(`rongxin:app.api:services:contract:${HANDLER_NAME}`);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const dataUtil = require('../dataSvc/dataUtil');
const shortUrlsData = dataUtil.shortUrls;
const murHash = require('murmurhash');
const redisData = require('../../persistence/dataStore');

const APPEND_STR = '[DUPLICATED]';
const TEN_YEARS_EXPIRATION = 10 * 365 * 24 * 60 * 60;

class Hanlder extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    let self = this;
    let method = `${HANDLER_NAME}.doAsync`;
    debug(method, '[Enter]');

    try {
      let signUrl = self.context.input.signUrl;

      //使用murmurhash，计算signUrl的哈希值
      let hashValue = murHash.v3(signUrl);
      debug(method, 'hash by murmurhash', hashValue);

      //将10进制的哈希值转换为62进制表示，来减少字符串长度
      let shortId = self.string10to62(hashValue);
      debug(method, 'convert to 62 Decimal', shortId);

      let shortUrlInfo = await shortUrlsData.getOneByCondition({
        shortId: shortId,
        archived: false
      });

      //生成的shortId在数据库中不存在，保存，直接返回
      if (!shortUrlInfo) {
        let result = await shortUrlsData.post({
          shortId: shortId,
          originalLink: signUrl
        });
        await redisData.set('shortId:' + shortId, signUrl, {
          expire: TEN_YEARS_EXPIRATION
        });
        self.context.opts.shortId = shortId;
        debug(method, '[Exit](success), shortId not exists.', result);
        return done();
      }

      //生成的shortId在数据库中存在，则判断该shortId对应的原始网址是否与数据库中的原始网址一样
      //不一样
      if (shortUrlInfo.originalLink !== signUrl) {
        let new_signUrl = signUrl + APPEND_STR;
        let hash = murHash.v3(new_signUrl);
        let shortId = self.string10to62(hash);
        self.context.opts.shortId = shortId;
        let result = await shortUrlsData.post({
          shortId: shortId,
          originalLink: new_signUrl
        });
        await redisData.set('shortId:' + shortId, new_signUrl, {
          expire: TEN_YEARS_EXPIRATION
        });
        debug(method, '[Exit](success), shortId re-generate.', result);
        return done();
      }

      //一样
      self.context.opts.shortId = shortId;
      debug(method, '[Exit](success), shortId already exists.', shortId);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    return done();
  }

  getQueryString(url) {
    let qs = url.split('?')[1], // 获取url中"?"符后的字串   
      args = {}, // 保存参数数据的对象
      items = qs.length ? qs.split("&") : [], // 取得每一个参数项,
      item = null,
      len = items.length;

    for (let i = 0; i < len; i++) {
      item = items[i].split("=");
      let name = decodeURIComponent(item[0]),
        value = decodeURIComponent(item[1]);
      if (name) {
        args[name] = value;
      }
    }
    return args;
  }

  string10to62(number) {
    let chars = '0123456789abcdefghigklmnopqrstuvwxyzABCDEFGHIGKLMNOPQRSTUVWXYZ'.split(''),
      radix = chars.length,
      qutient = +number,
      arr = [];
    do {
      let mod = qutient % radix;
      qutient = (qutient - mod) / radix;
      arr.unshift(chars[mod]);
    } while (qutient);
    return arr.join('');
  }
}

module.exports = Hanlder;
/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'setUserBankAccountHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeesData = require("../dataSvc/dataUtil").employees;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const aliOssSvc = require('../aliOssSvc');
const aliyunAuthSvc = require('../aliyunAuth');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;

      // 查询当前登录人信息
      let employee = await employeesData.getById(opts.uId)
      if (!employee) {
        throw {
          httpCode: 406,
          errorCode: 'E_SET_BANKACCOUNT_30',
          reason: '未找到当前协理员信息'
        }
      }

      // 手机号唯一 关联的eId 可能有误
      let assistanter = await assistantersData.getOneByCondition({ mobile: employee.mobile, flowStatus: "finish_sign", archived: false, isRevoked: false });
      if (!assistanter) {
        throw {
          httpCode: 406,
          errorCode: 'E_SET_BANKACCOUNT_38',
          reason: '当前协理员未签约'
        }
      }
      let authRes = await aliyunAuthSvc.bankcard234(assistanter.username, "", input.bankCard, assistanter.IDCard)
      debug(method, '[aliyunAuthSvc]Res(success)', authRes)
      if (!authRes || !authRes.body || !authRes.receipt) {
        throw {
          errorCode: 'E_USER_IDENTIFY_SVC_052',
          httpCode: 406,
          reason: 'Failed to Auth'
        };
      }
      if (authRes && authRes.body && authRes.body.data) {
        authRes.body.resp = { res: authRes.body.data.msg, code: authRes.body.data.result, desc: authRes.body.data.desc }
      }

      if (authRes.body.resp.code !== 0) {
        throw {
          errorCode: 'E_UserEleVerify_054',
          httpCode: 406,
          reason: authRes.body.resp.desc
        }
      }

      let result = await assistantersData.putById(assistanter._id, input);

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}

async function formatImg(item) {
  item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0 &&
    (item.thumbnail.fullUrl = await aliOssSvc.getFile({ fileName: item.thumbnail.url }));
  item && item.image && item.image.url && item.image.url.indexOf('http') !== 0 &&
    (item.image.fullUrl = await aliOssSvc.getFile({ fileName: item.image.url }));
}
module.exports = Handler;
/**
* env service
*
* <AUTHOR>
*
*/

class Environment {
  constructor() {
  }

  /**
   * https://github.com/lorenwest/node-config/wiki/Configuration-Files#local-files
   * 由于"local"是关键字，所以在 local 模式下，其实际对应的值为 local-dev
   */
  static get LOCAL_MODE() {
    return 'local-dev'
  }

  static get DEVELOPMENT_MODE() {
    return 'development'
  }

  static get STAGE_MODE() {
    return 'stage'
  }

  static get PRODUCTION_MODE() {
    return 'production'
  }

  static get TEST_MODE() {
    return 'test'
  }

  static get DEMO_MODE() {
    return 'demo'
  }
  
	static get BENCHMARK_MODE() {
    return 'benchmark'
  }
  
  static get TEST2_MODE() {
    return 'test2'
  }

  setServerMode(mode) {
    if (!mode) {
      this.mode = Environment.DEVELOPMENT_MODE
      return
    }

    mode = mode.toLowerCase();
    if (mode !== Environment.LOCAL_MODE &&
      mode !== Environment.DEVELOPMENT_MODE &&
      mode !== Environment.PRODUCTION_MODE &&
      mode !== Environment.TEST_MODE &&
      mode !== Environment.DEMO_MODE &&
      mode !== Environment.STAGE_MODE &&
			mode !== Environment.BENCHMARK_MODE &&
      mode !== Environment.TEST2_MODE) {
				throw Error(`Invalid environemnt mode - "${mode}", the supported mode should be "local-dev", "development", "production" or "stage", "demo", "benchmark"`)
    }
    this.mode = mode
  }

  getServerMode() {
    return this.mode
  }
};

module.exports = new Environment();

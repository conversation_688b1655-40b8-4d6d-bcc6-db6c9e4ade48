'use strict';

const HANDLER_NAME = 'GetModuleInfoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:infoEnterprise:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const aliOssSvc = require('../aliOssSvc');

const INFOENTERPRISE = require('../../utils/infoEnterpriseConst').INFOENTERPRISE;

const infoEnterpriseBasicData = require('../dataSvc/dataUtil').infoEnterpriseBasic;
const infoEnterpriseLoanWillData = require('../dataSvc/dataUtil').infoEnterpriseLoanWill;
const infoEnterpriseOperationData = require('../dataSvc/dataUtil').infoEnterpriseOperation;
const infoEnterpriseDebtData = require('../dataSvc/dataUtil').infoEnterpriseDebt;
const infoEnterpriseFinanceData = require('../dataSvc/dataUtil').infoEnterpriseFinance;
const infoEnterpriseOtherData = require('../dataSvc/dataUtil').infoEnterpriseOther;
const infoEnterpriseImageData = require('../dataSvc/dataUtil').infoEnterpriseImage;

const infoEnterpriseMainData = require('../dataSvc/dataUtil').infoEnterpriseMain;

const svcData = {
  "basic": infoEnterpriseBasicData,
  "loanWill": infoEnterpriseLoanWillData,
  "operation": infoEnterpriseOperationData,
  "debt": infoEnterpriseDebtData,
  "finance": infoEnterpriseFinanceData,
  "other": infoEnterpriseOtherData,
  "image": infoEnterpriseImageData,
}

const PERSONAL_TMP = {
  "operation": ['someYear']
};

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;

      let result = await svcData[input.module].getOneByCondition({ mId: input.id ,archived:false});
      debug(method, '[continue] result:', result)
      if (!result) {
        throw {
          errorCode: 'E_GET_INFO_057',
          httpCode: 406,
          reason: `Not Found ${input.module}`
        };
      }

      let options = INFOENTERPRISE[input.module];
      if (options) {
        
        if (PERSONAL_TMP[input.module]) {
          Object.getOwnPropertyNames(options).forEach(value => {
            let showName = `${value}ShowName`;
            for (let tmpObj of PERSONAL_TMP[input.module]) {
              for (let item of result[tmpObj]) {
                  item[showName] = options[value][item[value]];
              }
            }
          })
        }else{
          Object.getOwnPropertyNames(options).forEach(value => {
            let showName = `${value}ShowName`
            result[showName] = options[value][result[value]];
          })
        }
      }

      if (input.module == "image") {
        await self.getPath(result);
      }

      self.context.result = result;
      debug(method, "[success]");
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  async getPath(images) {
    let promise = [];
    for (const key in images) {
      let item = images[key];
      if (!item.image && !Array.isArray(item)) {
        continue;
      }
      if (item.image) item = [item];
      for (let i of item) {
        formatImg(i, promise);
      }
    }
    await Promise.all(promise);
  }

}

function formatImg(item, promise) {
  promise.push(aliOssSvc.getFile({ fileName: item.thumbnail.url }).then(data => {
    item.thumbnail.url = data
  }));
  promise.push(aliOssSvc.getFile({ fileName: item.image.url }).then(data => {
    item.image.url = data
  }));
}

module.exports = Handler
/*
 * @Author: q 
 * @Date: 2020-3-12 10:50:05
 * @Last Modified by: q
 * @Last Modified time: 2020-3-12 10:50:05
 */

'use strict';

const HANDLER_NAME = 'createInfoEnterpriseHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;


const infoEnterpriseBasicData = require('../dataSvc/dataUtil').infoEnterpriseBasic;
const infoEnterpriseLoanWillData = require('../dataSvc/dataUtil').infoEnterpriseLoanWill;
const infoEnterpriseOperationData = require('../dataSvc/dataUtil').infoEnterpriseOperation;
const infoEnterpriseDebtData = require('../dataSvc/dataUtil').infoEnterpriseDebt;
const infoEnterpriseFinanceData = require('../dataSvc/dataUtil').infoEnterpriseFinance;
const infoEnterpriseOtherData = require('../dataSvc/dataUtil').infoEnterpriseOther;
const infoEnterpriseImageData = require('../dataSvc/dataUtil').infoEnterpriseImage;

const infoEnterpriseMainData = require('../dataSvc/dataUtil').infoEnterpriseMain;

const svcData = {
  "basic": infoEnterpriseBasicData,
  "loanWill": infoEnterpriseLoanWillData,
  "operation": infoEnterpriseOperationData,
  "debt": infoEnterpriseDebtData,
  "finance": infoEnterpriseFinanceData,
  "other": infoEnterpriseOtherData,
  "image": infoEnterpriseImageData,
}

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let module = input.module;
      if(input.module == 'basic'){
        let currentYear = new Date().toISOString().substring(0,4);
        input.companyAge = currentYear - input.registeredDate.substring(0,4) || '不满一年';
      }
      delete input.module;

      let result = await svcData[module].getOneByCondition({ mId: input.mId ,archived:false});
      if (result) {
        input.version = ~~result.version + 1;
        let arInput = {
          "archived":true,
          lastModTime: new Date()
        };
        
        await svcData[module].putById(result._id, arInput);
        
        result = await svcData[module].post(input);
        //新增版本
        
        //旧版本归档
      } else {
        result = await svcData[module].post(input);
      }
      
      let key = `status.${module}`;
      let payload = {
        [key]: 1,
        lastModTime: new Date(),
        exDraft: true, //是否存在草稿
        operator: input.operator
      }
      await infoEnterpriseMainData.putById(input.mId, payload);
      
      self.context.result = result;
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item.key === key) return item.value;
    }
    return '';
  }
}

module.exports = Handler
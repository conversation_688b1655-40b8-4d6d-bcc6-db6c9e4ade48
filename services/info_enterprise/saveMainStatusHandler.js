/*
 * @Author: q 
 * @Date: 2020-3-12 10:50:05
 * @Last Modified by: q
 * @Last Modified time: 2020-3-12 10:50:05
 */

'use strict';

const HANDLER_NAME = 'saveMainStatusHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const infoEnterpriseMainData = require('../dataSvc/dataUtil').infoEnterpriseMain;
const infoVersionData = require('../dataSvc/dataUtil').infoVersion;
const AmqpInitializer = require('../../utils/bootstrap/amqpInitializer');
const AmqpClient = AmqpInitializer.getAmqpClient();
const config = require('config');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let resultInfo = await infoEnterpriseMainData.getById(input.mId);
      if (!resultInfo || !resultInfo.status || !resultInfo.status.basic || !resultInfo.status.loanWill
        || !resultInfo.status.operation || !resultInfo.status.debt || !resultInfo.status.finance
        || !resultInfo.status.other || !resultInfo.status.image) {
        throw {
          errorCode: 'E_SAVE_MAIN_STATUS_049',
          httpCode: 406,
          reason: 'hang'
        };
      }
      let version = ~~resultInfo.version + 1;
      
      let enterpriseIds = await infoEnterpriseMainData.getByUrl("/v1.0/manager/collection/enterprise", {mId:input.mId});
      enterpriseIds = enterpriseIds[0];
      debug(method, '[Enter](enterpriseIds)',enterpriseIds);
      
      let historyVersion = await infoVersionData.getOneByCondition({ mId: input.mId ,type:"enterprise",archived:false});
      if(historyVersion && historyVersion._id){
        await infoVersionData.putById(historyVersion._id, {archived:true});
      }
      
      
      let versionData = {
        "mId":input.mId,
        "version":version,
        "type":'enterprise',
        "operator":input.operator,
        "locate":input.locate,
        "infoIds":{
          "basic":enterpriseIds.basic[enterpriseIds.basic.length-1],
          "debt":enterpriseIds.debt[enterpriseIds.debt.length-1],
          "finance":enterpriseIds.finance[enterpriseIds.finance.length-1],
          "image":enterpriseIds.image[enterpriseIds.image.length-1],
          "loanWill":enterpriseIds.loanWill[enterpriseIds.loanWill.length-1],
          "operation":enterpriseIds.operation[enterpriseIds.operation.length-1],
          "other":enterpriseIds.other[enterpriseIds.other.length-1]
        },
      };
      
      debug(method, '[Enter](versionData)',versionData);
      
      await infoVersionData.post(versionData);
      
      let payload = {
        locate: input.locate,
        mainStatus: 1,
        version: version,
        operator: input.operator,
        exDraft: false, //清除草稿
        lastModTime: new Date()
      }

      let result = await infoEnterpriseMainData.putById(input.mId, payload);
      self.context.result = result;
      
      // 根据创建的订单信息将数据写入MQ中
      const mqConfig = config.get("rabbitmq-config");
      if (!mqConfig) {
        debug(method, '[Exit](success)');
        return done();
      }
      const msg = [{
        mId: input.mId,
        version: version,
        source: input.source,
        type: "enterprise"
      }];
      debug(method, '[AmqpClient](pubConfirmMsg)', msg);
      await AmqpClient.pubConfirmMsg(mqConfig.exchanges, mqConfig.queues, mqConfig.routeKeys, JSON.stringify(msg));

      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item.key === key) return item.value;
    }
    return '';
  }
}

module.exports = Handler
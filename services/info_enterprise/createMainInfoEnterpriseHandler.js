/*
 * @Author: q 
 * @Date: 2020-3-12 10:50:05
 * @Last Modified by: q
 * @Last Modified time: 2020-3-12 10:50:05
 */

'use strict';

const HANDLER_NAME = 'createMainInfoEnterpriseHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const infoEnterpriseMainData = require('../dataSvc/dataUtil').infoEnterpriseMain;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let enterprisefo = await infoEnterpriseMainData.getOneByCondition({companyID: input.companyID});
      if(enterprisefo && enterprisefo.companyName){
        throw {
          errorCode: 'E_CREATE_MAIN_035',
          httpCode: 406,
          reason: '已采集过' + input.companyName + '企业信息，请核实'
        };
      }
      let body = {
        companyName: input.companyName,
        corporateMobile: input.corporateMobile,
        companyID: input.companyID,
        locate: input.locate,
        mainStatus: 0,
        'status.basic': 0,
        'status.loanWill': 0,
        'status.operation': 0,
        'status.debt': 0,
        'status.finance': 0,
        'status.other': 0,
        'status.image': 0,
        'operator': input.operator
      };

      let result = await infoEnterpriseMainData.post(body);
      input.mId = result._id;

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item.key === key) return item.value;
    }
    return '';
  }
}

module.exports = Handler
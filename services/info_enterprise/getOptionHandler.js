'use strict';

const HANDLER_NAME = 'getOptionHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:infoEnterprise:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const INFOENTERPRISE = require('../../utils/infoEnterpriseConst').INFOENTERPRISE;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let options = {};
      if(INFOENTERPRISE[input.module] && INFOENTERPRISE[input.module][input.field]){
        options = INFOENTERPRISE[input.module][input.field];
      }else{
        throw {
          errorCode: 'E_GET_OPTION_029',
          httpCode: 406,
          reason: 'field not find'
        };
      }
      let results = recursionOptions(options);
      self.context.result = results;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

function recursionOptions(options){
    let results = [];
    for(let key in options){
      if( typeof options[key] == 'string'){
        results.push({
          code:key,
          name:options[key]
        });
      }else{
        results.push({
          code:key,
          name:'',
          list:recursionOptions(options[key])
        });
      }
    }
    return results
}

module.exports = Handler
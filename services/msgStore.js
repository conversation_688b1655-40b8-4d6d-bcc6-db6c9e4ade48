/*
 * @Author: wcy
 * @Date:   2018-05-22 16:42:15
 * @Last Modified by:   wcy
 * @Last Modified time: 2018-05-22 17:29:06
 */
'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const logger = logFactory(logUtil())('rongxin:loan.app.api:services:msgStore');
const BaseMsgStore = require('nongfu.merchant.msgfw').BaseMsgStore;
const DataHelper = require('../services/dataSvc/dataUtil');
const smsStoresData = DataHelper.msgStore;

class MsgStore extends BaseMsgStore {

	constructor() {
		super();
	}

	async init() {

	}

	async create(messages) {
		return smsStoresData.post(messages);
	}

	async get(id) {
		return smsStoresData.getById(id);
	}

};

module.exports = new MsgStore();
'use strict';

const HANDLER_NAME = 'GetInsDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:cxwqIns:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const cxwqInsData = require('../dataSvc/dataUtil').cxwqInsurance;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let aId = self.context.input;
      let result = await loanApplicationData.getById(aId);
      if (!result) {
        throw {
          errorCode: 'E_INS_DETAIL_029',
          httpCode: 406,
          reason: '无效的订单id'
        }
      }
      let cxwqIns = await cxwqInsData.getOneByCondition({ aId, archived: false });
      result.cxwqIns = cxwqIns || {};
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
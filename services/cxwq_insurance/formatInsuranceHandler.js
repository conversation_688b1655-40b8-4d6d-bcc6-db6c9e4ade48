'use strict';

const HANDLER_NAME = 'FormatInsuranceHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:cxwqIns:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeeData = require('../dataSvc/dataUtil').employees;
const userVerifysData = require('../dataSvc/dataUtil').userVerifys;

const moment = require('moment');
const aliOssSvc = require('../aliOssSvc');
const formatAreaCode = require('../../persistence/formatAreaCode');
const insSourceMap = new Map([
  ["user", "贷户"],
  ["manager", "监管部"]
])

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }

      for (let loan of _result) {
        loan.approveAmount = parseFloat((loan.approveAmount / 100).toFixed(2));
        let areaData = await formatAreaCode.getFormatAreaCode(loan.area)
        loan.region = areaData && areaData.region || {};
        loan.location = areaData && areaData.area || "";
        loan.lastModTime = moment(loan.lastModTime).format("YYYY-MM-DD HH:mm:ss");
        loan.insStatus = "未上传";
        const userVerify = await userVerifysData.getOneByCondition({ uId: loan.uId, IDCardStatus: "approved",archived:false })
        userVerify && ( loan.idCard = userVerify.IDCard );
        if (loan.cxwqIns && loan.cxwqIns._id) {
          loan.insStatus = (loan.cxwqIns.status === "00" ? "未上传" : "已上传");
          loan.cxwqIns.createdTime = moment(loan.cxwqIns.createdTime).format("YYYY-MM-DD HH:mm:ss");
          loan.cxwqIns.source = insSourceMap.get(loan.cxwqIns.source);
          for (let ins of loan.cxwqIns.info) {
            ins.voucher.forEach(item => formatImg(item));
            if (ins.unitPrice) ins.unitPrice = parseFloat((ins.unitPrice / 100).toFixed(2));
            if (ins.totalPrice) ins.totalPrice = parseFloat((ins.totalPrice / 100).toFixed(2));
            if (ins.premium) ins.premium = parseFloat((ins.premium / 100).toFixed(2));
          }
        }



      }

      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}
async function formatImg(item) {
  item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0 &&
    (item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url }));
  item && item.image && item.image.url && item.image.url.indexOf('http') !== 0 &&
    (item.image.url = await aliOssSvc.getFile({ fileName: item.image.url }));
}

module.exports = Handler
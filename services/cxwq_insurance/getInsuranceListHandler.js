'use strict';

const HANDLER_NAME = 'getInsuranceListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:cxwqIns:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { 
  cxwqInsurance:cxwqInsData,
  loanApplication:loanApplicationData,
  employeeGroups:employeeGroupsData,
  groupsV2:groupV2Data,
} = require('../dataSvc/dataUtil');

const { parseEmployee } = require('../../utils/general')

class Hand<PERSON> extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;
      let curRole = await employeeGroupsData.getOneByCondition({ tId: opts.tId, employee: opts.employeeId, group: opts.roleId, archived: false });
      if (!curRole || !curRole.areaList) {
        throw {
          errorCode: 'E_CXWQ_INS_LIST_031',
          httpCode: 401,
          reason: '当前管理角色错误'
        }
      }
      if (condition.areaCode) {
        let isIn = curRole.areaList.find(code => new RegExp(`^${code}`).test(areaCode));
        if (!isIn) {
          self.context.result = { total: 0, result: [] };
          debug(method, '[Exit]');
          return done();
        }
      } else {
        condition.areaCode = curRole.areaList.join(",");
      }

      const { roleName , orgId , orgCode } = await parseEmployee(opts);
      const childrenOrgId = ( await groupV2Data.getByCondition({ tId:opts.tId,archived:false, code:{ '$regex': `^${orgCode}`, '$options': 'si' }, limit:'unlimited'}) ).map(v=>v._id) ;//拿到子孙县域
      orgId !== '60111486fda3812feb51fd00' && ( condition["limitOrgId"] = childrenOrgId.join(',') );
      debug(`${HANDLER_NAME}Query`,JSON.stringify(condition));
      let result = await loanApplicationData.getByUrl("/v1.0/loan/applications/cxwq/ins/list", condition);

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
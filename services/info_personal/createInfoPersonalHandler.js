/*
 * @Author: q 
 * @Date: 2020-3-12 10:50:05
 * @Last Modified by: q
 * @Last Modified time: 2020-3-12 10:50:05
 */

'use strict';

const HANDLER_NAME = 'createInfoPersonalHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const infoPersonalBasicData = require('../dataSvc/dataUtil').infoPersonalBasic;
const infoPersonalFamilyMemberData = require('../dataSvc/dataUtil').infoPersonalFamilyMember;
const infoPersonalProductMgrData = require('../dataSvc/dataUtil').infoPersonalProductMgr;
const infoPersonalPropertyData = require('../dataSvc/dataUtil').infoPersonalProperty;
const infoPersonalDebtData = require('../dataSvc/dataUtil').infoPersonalDebt;
const infoPersonalFamilyPaymentData = require('../dataSvc/dataUtil').infoPersonalFamilyPayment;
const infoPersonalEvaluationData = require('../dataSvc/dataUtil').infoPersonalEvaluation;
const infoPersonalImageData = require('../dataSvc/dataUtil').infoPersonalImage;

const infoPersonalMainData = require('../dataSvc/dataUtil').infoPersonalMain;

const employeesData = require('../dataSvc/dataUtil').employees;

const svcData = {
  "basic": infoPersonalBasicData,
  "familyMember": infoPersonalFamilyMemberData,
  "productMgr": infoPersonalProductMgrData,
  "property": infoPersonalPropertyData,
  "debt": infoPersonalDebtData,
  "familyPayment": infoPersonalFamilyPaymentData,
  "evaluation": infoPersonalEvaluationData,
  "image": infoPersonalImageData,
}

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let _result = self.context.result;
      if(_result && _result.status==-1){
        return done();
      }
      let module = input.module;
      delete input.module;
      
      if(module == 'familyMember'){
        let idCards = [];
        let idOnly = {};
        let idName = {};
        for(let member of input.members){
          idOnly[member.idCard] =  idOnly[member.idCard] + 1 || 1;
          if(idOnly[member.idCard] >1){
            let errorResult = {
              status:-1,
              message:"身份证号"+member.idCard+"重复，请核实！"
            };
            self.context.result = errorResult;
            return done();
          }
          idCards.push(member.idCard);
          idName[member.idCard] = member.name;
        }
        let memberInfo = await svcData[module].getOneByCondition({'members.idCard': {$in:idCards},archived:false});

        if(memberInfo && memberInfo.mId && memberInfo.mId != input.mId){
          let errorResult = {status:-1};
          
          debug(method, '[Exit](memberInfo)',memberInfo);
          for(let item of memberInfo.members){
            if(idName[item.idCard]){
              if(input.operator == memberInfo.operator){
                errorResult.message = idName[item.idCard]+"的身份证号"+item.idCard+"，已在"+memberInfo.members[0].name+"的家庭成员信息中，请核实！";
              }else{
                let employees = await employeesData.getById(memberInfo.operator, {cache : true , expire: 24 * 60 * 60 });
                errorResult.message = idName[item.idCard]+"的身份证号"+item.idCard+"，已被"+(employees && employees.username||'')+"协理员采集，请核实！";
              }
              self.context.result = errorResult;
              debug(method, '[Exit](errorResult)',errorResult);
              
              return done();
            }
          }
        }
        let basicDataRes = await infoPersonalMainData.getOneByCondition({ _id: input.mId ,archived:false});
        if(!idOnly[basicDataRes.idCard]){
          throw {
            errorCode: 'E_SAVE_MEMBER_087',
            httpCode: 406,
            reason: '户主信息不能为空，请补充后再点击保存'
          };
          // return;
        }
      }

      let result = await svcData[module].getOneByCondition({ mId: input.mId ,archived:false});
      if (result) {
        input.version = ~~result.version + 1;
        let arInput = {
          "archived":true,
          lastModTime: new Date()
        };
        
        await svcData[module].putById(result._id, arInput)
        result = await svcData[module].post(input);
        //新增版本
        
        //旧版本归档
        
        if(module == 'basic'){
          
          let familyMemberInfo = await svcData['familyMember'].getOneByCondition({ mId: input.mId ,archived:false});
          if(familyMemberInfo){
            let householder = {
              'members.0.name':input.name,
              'members.0.idCard':input.idCard,
              'members.0.mobile':input.mobile,
              'members.0.marital':input.marital,
              'members.0.education':input.education
            }
            await svcData['familyMember'].putById(familyMemberInfo._id, householder)
          }
          
        }
      } else {
        result = await svcData[module].post(input);
      }
      
      let key = `status.${module}`;
      let payload = {
        [key]: 1,
        lastModTime: new Date(),
        exDraft: true, //是否存在草稿
        operator: input.operator
      }
      if(module == 'basic') {
        payload.name = input.name;
        payload.mobile = input.mobile;
        payload.idCard = input.idCard;
      }
      await infoPersonalMainData.putById(input.mId, payload);

      self.context.result = result;
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item.key === key) return item.value;
    }
    return '';
  }
}

module.exports = Handler
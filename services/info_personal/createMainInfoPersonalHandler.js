/*
 * @Author: q 
 * @Date: 2020-3-12 10:50:05
 * @Last Modified by: q
 * @Last Modified time: 2020-3-12 10:50:05
 */

'use strict';

const HANDLER_NAME = 'createMainInfoPersonalHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const infoPersonalMainData = require('../dataSvc/dataUtil').infoPersonalMain;
const infoPersonalFamilyMemberData = require('../dataSvc/dataUtil').infoPersonalFamilyMember;
const employeesData = require('../dataSvc/dataUtil').employees;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let familyMember = await infoPersonalFamilyMemberData.getOneByCondition({'members.idCard': input.idCard,archived:false});
      let userinfo = await infoPersonalMainData.getOneByCondition({idCard: input.idCard});
      let errorResult = {};
      if(familyMember && familyMember._id){
        errorResult.status = -1;
        if(input.operator == familyMember.operator){
          errorResult.message = input.name+"的身份证号"+input.idCard+"，已在"+familyMember.members[0].name+"的家庭成员信息中，请核实！";
        }else{
          let employees = await employeesData.getById(familyMember.operator, {cache : true , expire: 24 * 60 * 60 });
          errorResult.message = input.name+"的身份证号"+input.idCard+"，已被"+(employees && employees.username||'')+"协理员采集，请核实！";
        }
        debug(method, '[Exit](errorResult)familyMember:',errorResult);
        self.context.result = errorResult;
        
        return done();
      }
      if(userinfo && userinfo._id){
        errorResult.status = -1;
        if(input.operator == userinfo.operator){
          errorResult.message = input.name+"的身份证号"+item.idCard+"，已在"+userinfo.members[0].name+"的家庭成员信息中，请核实！";
        }else{
          let employees = await employeesData.getById(userinfo.operator, {cache : true , expire: 24 * 60 * 60 });
          errorResult.message = input.name+"的身份证号"+item.idCard+"，已被"+(employees && employees.username||'')+"协理员采集，请核实！";
        }
        debug(method, '[Exit](errorResult)userinfo:',errorResult);
        self.context.result = errorResult;
        
        return done();
      }
      let body = {
        name: input.name,
        mobile: input.mobile,
        idCard: input.idCard,
        locate: input.locate,
        mainStatus: 0,
        'status.basic': 0,
        'status.familyMember': 0,
        'status.productMgr': 0,
        'status.property': 0,
        'status.debt': 0,
        'status.familyPayment': 0,
        'status.evaluation': 0,
        'status.image': 0,
        'operator': input.operator
      };

      let result = await infoPersonalMainData.post(body);
      input.mId = result._id;

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item.key === key) return item.value;
    }
    return '';
  }
}

module.exports = Handler
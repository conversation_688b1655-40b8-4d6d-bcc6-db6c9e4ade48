/*
 * @Author: q 
 * @Date: 2020-3-12 10:50:05
 * @Last Modified by: q
 * @Last Modified time: 2020-3-12 10:50:05
 */

'use strict';

const HANDLER_NAME = 'saveMainStatusHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const infoPersonalMainData = require('../dataSvc/dataUtil').infoPersonalMain;
const infoVersionData = require('../dataSvc/dataUtil').infoVersion;
const AmqpInitializer = require('../../utils/bootstrap/amqpInitializer');
const AmqpClient = AmqpInitializer.getAmqpClient();
const config = require('config');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let resultInfo = await infoPersonalMainData.getById(input.mId);
      if (!resultInfo || !resultInfo.status || !resultInfo.status.basic || !resultInfo.status.familyMember
        || !resultInfo.status.productMgr || !resultInfo.status.property || !resultInfo.status.debt
        || !resultInfo.status.familyPayment || !resultInfo.status.evaluation || !resultInfo.status.image) {
        throw {
          errorCode: 'E_SAVE_MAIN_STATUS_049',
          httpCode: 406,
          reason: 'hang'
        };
      }
      
      
      let version = ~~resultInfo.version + 1;
      
      let personalIds = await infoPersonalMainData.getByUrl("/v1.0/manager/collection/personal", {mId:input.mId});
      personalIds = personalIds[0];
      debug(method, '[Enter](personalIds)',personalIds);
      
      let historyVersion = await infoVersionData.getOneByCondition({ mId: input.mId ,type:"personal",archived:false});
      if(historyVersion && historyVersion._id){
        await infoVersionData.putById(historyVersion._id, {archived:true});
      }
      
      
      let versionData = {
        "mId":input.mId,
        "version":version,
        "type":'personal',
        "operator":input.operator,
        "locate":input.locate,
        "infoIds":{
          "basic":personalIds.basic[personalIds.basic.length-1],
          "debt":personalIds.debt[personalIds.debt.length-1],
          "evaluation":personalIds.evaluation[personalIds.evaluation.length-1],
          "image":personalIds.image[personalIds.image.length-1],
          "familyMember":personalIds.familyMember[personalIds.familyMember.length-1],
          "familyPayment":personalIds.familyPayment[personalIds.familyPayment.length-1],
          "productMgr":personalIds.productMgr[personalIds.productMgr.length-1],
          "property":personalIds.property[personalIds.property.length-1]
        },
      };
      
      debug(method, '[Enter](versionData)',versionData);
      
      await infoVersionData.post(versionData);
      
      let payload = {
        locate: input.locate,
        mainStatus: 1,
        version: version,
        operator: input.operator,
        exDraft: false, //清除草稿
        lastModTime: new Date()
      }

      let result = await infoPersonalMainData.putById(input.mId, payload);
      self.context.result = result;
      
      // 根据创建的订单信息将数据写入MQ中
      const mqConfig = config.get("rabbitmq-config");
      if (!mqConfig) {
        debug(method, '[Exit](error)mqConfig');
        return done();
      }
      const msg = [{
        mId: input.mId,
        version: version,
        source: input.source,
        type: "personal"
      }];
      debug(method, '[AmqpClient](pubConfirmMsg)', msg);
      await AmqpClient.pubConfirmMsg(mqConfig.exchanges, mqConfig.queues, mqConfig.routeKeys, JSON.stringify(msg));

      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item.key === key) return item.value;
    }
    return '';
  }
}

module.exports = Handler
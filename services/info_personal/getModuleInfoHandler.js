'use strict';

const HANDLER_NAME = 'GetModuleInfoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:infoPersonal:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const aliOssSvc = require('../aliOssSvc');
const INFOPERSONAL = require('../../utils/infoPersonalConst').INFOPERSONAL;

const infoPersonalBasicData = require('../dataSvc/dataUtil').infoPersonalBasic;
const infoPersonalFamilyMemberData = require('../dataSvc/dataUtil').infoPersonalFamilyMember;
const infoPersonalProductMgrData = require('../dataSvc/dataUtil').infoPersonalProductMgr;
const infoPersonalPropertyData = require('../dataSvc/dataUtil').infoPersonalProperty;
const infoPersonalDebtData = require('../dataSvc/dataUtil').infoPersonalDebt;
const infoPersonalFamilyPaymentData = require('../dataSvc/dataUtil').infoPersonalFamilyPayment;
const infoPersonalEvaluationData = require('../dataSvc/dataUtil').infoPersonalEvaluation;
const infoPersonalImageData = require('../dataSvc/dataUtil').infoPersonalImage;

const svcData = {
  "basic": infoPersonalBasicData,
  "familyMember": infoPersonalFamilyMemberData,
  "productMgr": infoPersonalProductMgrData,
  "property": infoPersonalPropertyData,
  "debt": infoPersonalDebtData,
  "familyPayment": infoPersonalFamilyPaymentData,
  "evaluation": infoPersonalEvaluationData,
  "image": infoPersonalImageData,
}
const PERSONAL_TMP = {
  "familyMember": ['members'],
  "productMgr": ['products'],
  "property": ['buildings', 'machines', 'vehicles'],
  "familyPayment": ['incomes', 'payouts']
};

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;

      let result = await svcData[input.module].getOneByCondition({ mId: input.id ,archived:false});
      debug(method, '[continue] result:', result)
      if (!result) {
        throw {
          errorCode: 'E_GET_INFO_057',
          httpCode: 406,
          reason: `Not Found ${input.module}`
        };
      }

      let options = INFOPERSONAL[input.module];
      if (options) {
        if (PERSONAL_TMP[input.module]) {
          Object.getOwnPropertyNames(options).forEach(value => {
            let showName = `${value}ShowName`;
            for (let tmpObj of PERSONAL_TMP[input.module]) {
              for (let item of result[tmpObj]) {
                if (input.module == 'productMgr' && value == 'sort') {
                  let optionKey = item[value];
                  item[showName] = options[value][optionKey.substr(0, 3)][optionKey];
                } else {
                  item[showName] = options[value][item[value]];
                }
              }
            }
          })
        } else {
          Object.getOwnPropertyNames(options).forEach(value => {
            let showName = `${value}ShowName`
            result[showName] = options[value][result[value]];
          })
        }
      }

      if (input.module == "image") {
        await self.getPath(result);
      }

      self.context.result = result;
      debug(method, "[success]");
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  async getPath(images) {
    let promise = [];
    for (const key in images) {
      let item = images[key];
      if (!item.image && !Array.isArray(item)) {
        continue;
      }
      if (item.image) item = [item];
      for (let i of item) {
        formatImg(i, promise);
      }
    }
    await Promise.all(promise);
  }

}

function formatImg(item, promise) {
  if (item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    if(item.thumbnail.url.indexOf('oss/') === 0){
      promise.push(aliOssSvc.getFile({fileName:item.thumbnail.url}).then(data => {
        item.thumbnail.url = data
      }));
    }else{
      item.thumbnail.url = IMAGE_BASE_URL.concat(item.thumbnail.url);
    }
  }
  if (item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    if(item.image.url.indexOf('oss/') === 0){
      promise.push(aliOssSvc.getFile({fileName:item.image.url}).then(data => {
        item.image.url = data
      }));
    }else{
      item.image.url = IMAGE_BASE_URL.concat(item.image.url);
    }
  }
}

module.exports = Handler
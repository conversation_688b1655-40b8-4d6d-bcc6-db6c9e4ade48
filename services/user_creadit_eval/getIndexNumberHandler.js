/**
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'GetIndexNumberHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:getIndexNumberHandler:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const userCreditRatingData = require('../dataSvc/dataUtil').userCreditRating;
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;
const groupData = require('../dataSvc/dataUtil').groups;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let condition = {
        archived: false
      };


      let roleInfo = await groupData.getById(opts.roleId);
      if (roleInfo && (roleInfo.name == 'AccountManager' || roleInfo.name == 'RegionalManager')) {

        let employeeInfo = await employeeGroupsData.getOneByCondition({
          archived: false,
          employee: opts.userInfo.userid,
          group: opts.roleId
        });

        condition.$or = [];
        employeeInfo.areaList.forEach(item => {
          let area = {
            areaCode: '/^' + item + '/'
          };
          condition.$or.push(area);
        });
      }


      let waitHandle = await userCreditRatingData.getCountByCondition({ ...condition, creditStatus: { $nin: [1, 2, 3] } });
      let completeHandle = await userCreditRatingData.getCountByCondition({ ...condition, creditStatus: { $in: [1, 2, 3] } });

      let result = {
        waitHandle: waitHandle.count || 0,
        completeHandle: completeHandle.count || 0,
      };
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
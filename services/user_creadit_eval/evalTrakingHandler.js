/**
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'evalTrakingHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:evalTrakingHandler:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const creditTrackingsData = require('../dataSvc/dataUtil').creditTrackings;
const moment = require('moment');
const COMMIT_WHITE = {
  '1':'whitelist',
  '2':'greylist',
  '3':'blacklist',
};

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let result = self.context.result;
      let opts = self.context.opts;
      
      let tracking = await creditTrackingsData.post({
        src_t : 'staff',
        source : result.operator,
        target_t : 2,
        target : result._id,
        action : COMMIT_WHITE[`${result.creditStatus}`],
        parameters : result,
        comments : opts.commit,
      })
      debug(method, '[Exit](tracking)', tracking);
      
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler

Array.prototype.remove = function(val) { 
  let index = this.indexOf(val); 
  if (index > -1) { 
    this.splice(index, 1); 
  } 
};
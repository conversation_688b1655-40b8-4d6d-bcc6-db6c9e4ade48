/**
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'getInfoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:getInfoHandler:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const userCreditRatingData = require('../dataSvc/dataUtil').userCreditRating;
const moment = require('moment');
const formatAreaCode = require('../../persistence/formatAreaCode');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input;
      
      let promises = [];
      let result = await userCreditRatingData.getById(id);
      // for(let item of result.result){
        result.createdTime = moment(result.createdTime).format('YYYY-MM-DD HH:mm:ss');
        promises.push(formatAreaCode.getFormatAreaCode(result.areaCode).then(data => {
          result.region = data && data.region || {};
          result.location = data && data.area || "";
        }));
      // }

      await Promise.all(promises);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
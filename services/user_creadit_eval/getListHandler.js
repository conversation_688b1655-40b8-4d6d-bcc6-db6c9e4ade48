/**
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'getListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:getListHandler:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const userCreditRatingData = require('../dataSvc/dataUtil').userCreditRating;
const moment = require('moment');
const formatAreaCode = require('../../persistence/formatAreaCode');
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;
const groupData = require('../dataSvc/dataUtil').groups;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;


      let roleInfo = await groupData.getById(opts.roleId);
      if (roleInfo && (roleInfo.name == 'AccountManager' || roleInfo.name == 'RegionalManager')) {

        let employeeInfo = await employeeGroupsData.getOneByCondition({
          archived: false,
          employee: opts.userInfo.userid,
          group: opts.roleId
        });

        condition.$or = [];
        employeeInfo.areaList.forEach(item => {
          let area = {
            areaCode: '/^' + item + '/'
          };
          condition.$or.push(area);
        });
      }
      let promises = [];
      let result = await userCreditRatingData.getListAndCountByCondition(condition);
      for (let item of result.result) {
        item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
        promises.push(formatAreaCode.getFormatAreaCode(item.areaCode).then(data => {
          item.region = data && data.region || {};
          item.location = data && data.area || "";
        }));
      }

      await Promise.all(promises);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
/**
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'evalFamilyUpdateHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:evalFamilyUpdateHandler:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const userFamilyCreditData = require('../dataSvc/dataUtil').userFamilyCredit;
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let result = self.context.result;
      let opts = self.context.opts;
      
      let familyList = await userFamilyCreditData.getByCondition({
        familyIdCard : result.idCard,
        limit : 'unlimited',
        archived : false
      })
      debug(method, '[GET](FamilyCreditData)', familyList);
      let n=0;
      for(let family of familyList){
        family.greylist.remove(result.idCard);
        family.blacklist.remove(result.idCard);
        if(result.creditStatus == 2){
          
          family.greylist.push(result.idCard);
          
        }else if(result.creditStatus == 3){
          
          family.blacklist.push(result.idCard);
          
        }
        family.creditStatus = 1;
        if(family.greylist.length>0){
          family.creditStatus = 2
        }
        if(family.blacklist.length>0){
          family.creditStatus = 3
        }
        family.lastModTime = new Date();
        await userFamilyCreditData.putById(family._id,family);
        opts.commit += `Family:${n+=1},FamilyCreditStatus:${family.creditStatus}|`;
        debug(method, '[Exit](family)', family);
      }
      
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler

Array.prototype.remove = function(val) { 
  let index = this.indexOf(val); 
  if (index > -1) { 
    this.splice(index, 1); 
  } 
};
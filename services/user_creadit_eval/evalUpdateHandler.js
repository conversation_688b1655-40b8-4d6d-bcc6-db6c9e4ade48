/**
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'evalUpdateHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:evalUpdateHandler:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const userCreditRatingData = require('../dataSvc/dataUtil').userCreditRating;
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let {
        id,
        maritalStatus,
        isPoverty,
        isBadHabit,
        isSeriousDisease,
        isPrivateLending,
        isOtherLocality,
        operator,
        status
      } = self.context.input;
      
      let promises = [];
      
      let creditStatus = 1;  //1白 2灰 3黑
      
      // 更新黑白灰评定规则
      if(isPoverty == 1 || isBadHabit == 1 || isSeriousDisease == 1 || isPrivateLending == 1) {
        creditStatus = 3;
      } else if(maritalStatus != 1 || isOtherLocality == 1) {
        creditStatus = 2;
      }

      let result = await userCreditRatingData.putById(id,{
        maritalStatus,
        isPoverty,
        isBadHabit,
        isSeriousDisease,
        isPrivateLending,
        isOtherLocality,
        creditStatus,
        operator,
        status,
        lastModTime: new Date()
      });
      
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
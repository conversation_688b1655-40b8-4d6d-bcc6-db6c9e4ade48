/**
 * <AUTHOR>
 * 2019-07-22
 */

'use strict';

const HANDLER_NAME = 'getClientBundleVersionHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const clientBundleVersionData = require('../dataSvc/dataUtil').clientBundleVersions;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      input.archived = false;
      input.status = true;
      input.$sort = {
        version: -1,
        build: -1
      }
      let result = await clientBundleVersionData.getOneByCondition(input);
      // result && result.file && result.file.path
        // && (result.file.path = result.file.path.replace(/^https:\/\//gi,''));
      self.context.result = result || null;
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
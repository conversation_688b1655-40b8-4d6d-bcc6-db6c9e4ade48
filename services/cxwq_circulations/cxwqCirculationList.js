'use strict';
const HANDLER_NAME = 'cxwqCirculationList';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const createShortUrlHandler = require('./createShortUrlHandler');
const formatAreaCode = require('../../persistence/formatAreaCode');
const {
  loanApplication:loanApplicationData,
  cxwqCirculations:circulationData,
  loanLand:landOriginData,
  userVerifys:userVerifysData,
  employeeGroups:employeeGroupData,
  contracts: contractsData,
  contractFlowsV2: contractFlowsV2Data,
  contractFlowSignTasksV2:contractFlowSignTasksV2Data,
  landData,
} = require('../dataSvc/dataUtil');
const {assert,isLandOccupy} = require('../../utils/general');
const env = require('../env');
const { getEmployeeLimit } = require('../../utils/getUserFromReq');
const updateLandOrigin = require('./updateLandOrigin');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      debug(method, '[Enter]', `context:`, JSON.stringify(this.context));
      const { input:condition, opts: { tId,uId,roleId } } = this.context;
      const areaCodeLimitOr = uId && await getEmployeeLimit(employeeGroupData, tId, uId, 'areaCode', roleId);
      uId && areaCodeLimitOr.length && (condition['$or'] = areaCodeLimitOr);
      debug(`${HANDLER_NAME}areaCodeLimit`, JSON.stringify( areaCodeLimitOr ) );
      debug(`${HANDLER_NAME}Query`, JSON.stringify( condition ) );
      const result = await circulationData.postListAndCountByCondition( condition );
      await Promise.all( result.result.map(async item=>{
        const type = ({ 1:20 , 2:26 })[ item.circulationType ];
        if( item.status !== 4 || item.signUrl || !type )return ;
        const contract =  await contractsData.getByCondition({ type, cxwqId: item._id , loanId: item.aId,archived:false   });
        // debug('debug233 contract:', item._id,item.circulationType,{ type, cxwqId: item._id , loanId: item.aId,archived:false   },contract.map(v=>v._id));
        const flow = contract.length && await contractFlowsV2Data.getByCondition({ cId: {$in:contract.map(v=>v._id)} , archived:false , status: 2, loanId: item.aId  }) || [];
        // contract.length && debug('debug233 flow:', { cId: contract._id , archived:false , status: 2, loanId: item.aId  },flow.map(v=>v._id))
        const task = flow.length && await contractFlowSignTasksV2Data.getOneByCondition( { fId:{$in:flow.map(v=>v._id)},status:2, archived:false } ) || [];
        // flow.length && debug('debug233 task:', { fId:flow._id,status:2, archived:false },task&&task._id)
        const {signUrl} = task || {};
        item.signUrl = signUrl;
        signUrl && await circulationData.putById( item._id , {signUrl} )
      }) );
      this.context.result = result;
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }


  undoAsync(done) {
    done()
  }
}

module.exports = Handler


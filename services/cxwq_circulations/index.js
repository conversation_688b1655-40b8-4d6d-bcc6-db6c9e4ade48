'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:index');
// const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const circulationAndLandInfo = require("./circulationAndLandInfoHandler");
const cxwqCirculationList = require( './cxwqCirculationList' )
const updateCirculations = require("./updateCirculations");
const updateLandOrigin = require('./updateLandOrigin');
const BindCirculationsAndOutLandHandler = require('./bindCirculationsAndOutLandHandler');

const {addHandlersForService} = require('../../utils/general')


class Service {
  constructor() {
    addHandlersForService.call(this,debug);
  }

  updateCirculationAndLand(){
    return [updateCirculations,updateLandOrigin]
  }

  listLands(){
    return [circulationAndLandInfo]
  }

  listCirculations(){
    return [cxwqCirculationList]
  }

  bindCirculationsAndOutLand() {
    return [BindCirculationsAndOutLandHandler]
  }

}

module.exports = new Service();
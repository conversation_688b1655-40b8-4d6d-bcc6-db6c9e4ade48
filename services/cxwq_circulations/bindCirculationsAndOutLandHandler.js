'use strict';
const HANDLER_NAME = 'bindCirculationsAndOutLandHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  loanApplication:loanApplicationData,
  cxwqCirculations:circulationData,
  userVerifys:userVerifysData,
  infoCollectHistory:infoCollectHistoryData,
  landData,
  loanApplicationOutLand:loanApplicationOutLandData,
} = require('../dataSvc/dataUtil');
// const aliOss = require('../aliOssSvc');
// const agent = require('superagent');
// const aliyunAuthSvc = require('../aliyunAuth');
// const overwriteConfig = ['600fe47561f0e675643c5fa1'];

const {assert} = require('../../utils/general');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      debug(method, '[Enter]', `context:`, JSON.stringify(this.context));

      const { input:{ id: outlandId , addList=[] , takeAwayList=[],overwriteList }, opts: { uId:operator, saveWithLand , tId , circulationType } } = this.context;
      assert(outlandId,'E_BIND_CIRCULATION_AND_OUTLAND_001','id is required');
      let outland = await loanApplicationOutLandData.getById(outlandId);
      assert(outland,'E_BIND_CIRCULATION_AND_OUTLAND_002','id is error');
      // overwriteList 如果非空 会覆盖 addList 与 takeAwayList
      overwriteList && addList && ( addList.length = 0 );
      overwriteList && takeAwayList && ( takeAwayList.length = 0 );
      const circulationByOverwrite = await Promise.all( ( overwriteList || [] ).map( async _id=>({ _id , data:await circulationData.getById( _id ) } ) ) );
      const errorOverwriteId = circulationByOverwrite.filter(v=>!v.data).map(v=>v._id) ;
      assert( errorOverwriteId.length === 0 , 'E_BIND_CIRCULATION_AND_OUTLAND_003' , `there are some _id is wrong in overwriteList: ${errorOverwriteId.join(',')}` )
      const bindedList = circulationByOverwrite.map(v=>v.data).filter(v=>v.outlandId && v.outlandId !== outlandId ).concat( addList );
      assert( bindedList.length === 0 , 'E_BIND_CIRCULATION_AND_OUTLAND_004' , `there are some binded circulations in addList or overwriteList: ${bindedList.map(v=>v._id).join(',')}` )
      const addCirculations = addList.length && await circulationData.getByCondition( { _id:{ $in:addList } , limit:'unlimited' } ) || [];
      const takeAwayCirculations = takeAwayList.length && await circulationData.getByCondition( { _id:{ $in:takeAwayList } , limit:'unlimited' } ) || [];
      await Promise.all( addCirculations.map( it=>circulationData.putById( it._id , { outlandId } ) ) );
      await Promise.all( takeAwayCirculations.map( it=>circulationData.putById( it._id , { outlandId:null } ) ) );

      let all = await circulationData.getByCondition( { outlandId , limit:'unlimited'  } ) , circulationCount = all.length  ;
      // 覆盖模式，先清掉之前绑定的
      overwriteList && ( circulationCount = overwriteList.length , await Promise.all( all.map( it=>circulationData.putById( it._id , { outlandId:null } ) ) ) );// 如果指定了 overwriteList
      // 再绑定现在传过来的
      await Promise.all( overwriteList.map( _id=>circulationData.putById( _id , { outlandId } ) ) );

      outland.circulationCount = circulationCount;
      outland = await loanApplicationOutLandData.putById(outlandId,{ circulationCount });

      this.context.result = { success:'ok' , circulationCount , outland , addList , takeAwayList,overwriteList };
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler


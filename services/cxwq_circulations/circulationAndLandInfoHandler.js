'use strict';
const HANDLER_NAME = 'circulationAndLandInfo';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const createShortUrlHandler = require('./createShortUrlHandler');
const formatAreaCode = require('../../persistence/formatAreaCode');
const {
  loanApplication:loanApplicationData,
  cxwqCirculations:circulationData,
  loanLand:landOriginData,
  userVerifys:userVerifysData,
  landData,
} = require('../dataSvc/dataUtil');
const {assert,isLandOccupy} = require('../../utils/general');
const env = require('../env');
const UpdateCirculations = require('./updateCirculations');
const circulationAndLandInfoUtil = require('./circulationAndLandInfoUtil');
class CirculationAndLandInfo extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      debug(method, '[Enter]', `context:`, JSON.stringify(this.context));
      // 可以根据_id 获取 circulation ，也可以根据 aId 或者 idCard 获取
      const { input:{ _id , aId , idCard  }, opts: {  } } = this.context;
      !_id && assert( aId && aId.trim() , 'LAND_LIST_BY_CIRCULATIONS_ERROR_001' , 'aId is required' );
      !_id && assert( idCard && idCard.trim() , 'LAND_LIST_BY_CIRCULATIONS_ERROR_002' , 'idCard is required' );
      const circulationById = _id && await circulationData.getById(_id)
      const circulation = circulationById || await circulationData.getOneByCondition( { aId , idCard , archived:false } );
      const query = circulation || { aId , idCard  };
      // debug('debug233',_id,aId,idCard,query,circulation);
      // assert( circulation , 'Error' , 'circulation not found' );
      const lands = ( await new circulationAndLandInfoUtil().getList( query ) ).map(v=>v.lands).reduce( (r,v)=>r.concat(v) , []) || [];
      const { myFamilies , myFamilyCardNos , userInfo , myContracts } = await new UpdateCirculations().familiesInfo( null , aId  );
      const isCoOwner = myFamilyCardNos[idCard] , isRequester = userInfo.IDCard === idCard;

      this.context.result = { circulation , lands , isCoOwner , isRequester };
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }


  undoAsync(done) {
    done()
  }
}

module.exports = CirculationAndLandInfo


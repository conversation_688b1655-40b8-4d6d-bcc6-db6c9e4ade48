'use strict';
const HANDLER_NAME = 'UPDATE_LAND_ORIGIN';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const createShortUrlHandler = require('./createShortUrlHandler');
const formatAreaCode = require('../../persistence/formatAreaCode');
const {
  loanApplication:loanApplicationData,
  cxwqCirculations:circulationData,
  loanLand:landOriginData,
  userVerifys:userVerifysData,
  contractOwnersV2: contractOwnersV2Data,
  contractSignersV2: contractSignersV2Data,
  contracts: contractsData,
  landData,
} = require('../dataSvc/dataUtil');
const Decimal = require('decimal.js');
const {assert,isLandOccupy} = require('../../utils/general');
const env = require('../env');

const config = require('config');
const { shortHost,longHost,m,scope } = config.get("cxwqShortUrl");
const { host:cmsHost } = config.get('rongxin_loan_cms_api_service');
const url = `http://${cmsHost}/api/v1.0/origin/contracts`;
const superagent = require('superagent');
const redisData = require('../../persistence/dataStore');
const XNTWEBKEY = 'XNTWEBKEY';

const circulationAndLandInfoUtil = require('./circulationAndLandInfoUtil');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      debug(method, '[Enter]', `context:`, JSON.stringify(this.context));
      const { input:{ _id , landCodeList=[] }, opts, opts: { saveWithLand , tId , noHandleOrigin } } = this.context;
      if( noHandleOrigin )return done();
      // circulation 可能由上一步传过来，也可能需要兼容单独出现处理的情况
      const circulation = opts.circulation || ( _id && await circulationData.getById( _id ) );
      assert( circulation , 'E_LAND_SVC_0401' , 'circulation is required' );
      const app = circulation.aId && await loanApplicationData.getById( circulation.aId )
      assert( app , 'E_LAND_SVC_0402' , 'app is required' );
      const olds = circulation._id && await landOriginData.getByCondition({ cxwqId: circulation._id, limit:'unlimited',archived:false}) || [];
      const landOrigins = await this.handle( circulation , landCodeList , app );
      //理论上说不会出现这种情况
      assert( landOrigins.length , 'E_LAND_SVC_0502' , '没有任何有效的选中地块' );
      circulation.orgInfo = app.orgInfo;
      debug(`${HANDLER_NAME}orgInfo`,circulation._id,saveWithLand, app.orgInfo,circulation.orgInfo);

      // 执行更新出让方（如果需要），这行需要先执行，以便拿到cxwqId
      saveWithLand && !circulation._id && Object.assign( circulation , await circulationData.post( circulation ) );
      saveWithLand && circulation._id && Object.assign( circulation , await circulationData.putById( circulation._id , circulation ) );

      // 删除旧的
      await Promise.all( olds.map(({_id})=>landOriginData.putById(_id,{archived:true}) ) );
      // 执行添加新的
      await Promise.all( landOrigins.map( async it=>{
        it.cxwqId = circulation._id ;
        Object.assign( it , await landOriginData.post(it) ) ;
      } ) );

      const shortUrl = await this.getShortUrl( circulation );
      circulation.shortUrl = shortUrl;

      const eSignInfo = await this.eSignGenerate( circulation , landOrigins );
      const {signUrl} = eSignInfo || {};

      circulation.signUrl = signUrl;
      const allLands = landOrigins.map(v=>v.lands).reduce( (r,v)=>r.concat(v),[]);
      circulation.area = circulation.area || Number( allLands.reduce((r,v)=>r.add(new Decimal(v.area)),new Decimal(0)) );
      await circulationData.putById( circulation._id , circulation );
      this.context.result = { circulation , landOrigins , shortUrl , eSignInfo , signUrl  , success:'ok' };
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async getShortUrl({_id,status}) {
    const conf = {
      development:{ scope:'dev-t' , serverMode:'dev-m' },
      test:{ scope:'test-t' , serverMode:'test-m' },
      stage:{ scope:'stage-t' , serverMode:'stage-m' },
      production:{ scope:'t' , serverMode:'m' },
    }
    // const { scope , serverMode } = conf[ env.getServerMode() ];
    const longUrl = `https://${m}${longHost}/#/cxwq/user-certified-v2?id=${_id}&status=${status}`;
    debug(`${HANDLER_NAME}longUrl`, '[Enter](获取短链接 longUrl)', longUrl);

    const shortId = await new createShortUrlHandler().exec(longUrl);
    debug(`${HANDLER_NAME}shortId`, '[Enter](获取短链接 shortId)', shortId);
    //获取短链接
    const shortUrl = `http://${scope}${shortHost}/${shortId}`;
    debug(`${HANDLER_NAME}shortUrl`, '[Exit](获取短链接 shortid)', shortUrl);

    return shortUrl;
  }

  async handle(circulations,landCodeList , app) {
    app = app || ( circulations.aId && await loanApplicationData.getById( circulations.aId ) );
    assert( app , 'ELAND_SVC_038b','aId异常' )
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]');
    const occupyLands = landCodeList && landCodeList.length && await isLandOccupy(landCodeList,app.tId) || [];
    assert( occupyLands.length === 0 , 'E_LAND_SVC_050' , `以下地块已被占用：${occupyLands.join(',')}` );
    const allLands = await new circulationAndLandInfoUtil().getList( circulations );

    assert( landCodeList && landCodeList.length , 'E_LAND_SVC_0501','至少要选择一个地块' );
    // const cardID = circulations.cardNo ;
    const mobile = circulations.mobile ;
    const username = circulations.username;
    // const options = { landHeader: { key: "rongxin-land-area", value: "23" } };

    // const {content:contractList} = await landData.getByUrl("/api/v1.0/contract/list", { cardNo: cardID }, options);
    // debug(method, '[landData](resluts)', contractList);
    assert( allLands.length , 'E_LAND_SVC_038','未查找到经营权证数据' );
    // debug('debug233 handle1',contractList);
    // debug('debug233 handle2',allLands);
    const result = ( await Promise.all( allLands.map( async ({code,grantNo,contractor,contract,lands:landsOrigins})=> {
      debug(`${HANDLER_NAME}landsOrigins a  item`,contract,contract && contract.area,contractor,code,grantNo);
      debug(`${HANDLER_NAME}landsOrigins b  item`,landsOrigins);
      const lands = landsOrigins.filter(v=>!v.occupy && landCodeList.some(landCode=> landCode === v.landCode) );//不能被占用，且要在选择列表之中
      debug(method, '[landData](lands)', lands);
      if( lands.length === 0 )return null;
      const area = parseFloat((contract.area * 0.0015).toFixed(2));
      const now = new Date(),year = now.getMonth() > 9 ? now.getFullYear() + 1 : now.getFullYear();
      const region = await formatAreaCode.getFormatAreaCode(contract.code.substring(0, 6));
      const landOriginItem = {
        aId: circulations.aId,
        IDCard: circulations.idCard ,//contract.cardNo,
        // cxwqId: circulations._id,
        contractorNo: contract.grantNo,
        contractorCode: contract.code,
        area,
        circulationStartDate: moment().format("YYYY-MM-DD"),
        circulationEndDate: `${year}-10-31`,
        enjoySubsidy: '',
        enjoyPlant: '',
        lands,
        username: username,
        // IDCard: cardID,
        mobile: mobile,
        areaCode: contract.code.substring(0, 12),
        areaName: `${region.area}${contractor.address}`
      };
      // debug('debug233 handle item:',landOriginItem);
      return landOriginItem;
      // debug(method, '[loanLandData](post)', landOriginItem);
      // return _=>landOriginData.post(landOriginItem)
    }))).filter(v=>v);

    return result;
  }

  async eSignGenerate(circulation,orgin,loanInfo){
    // (!req.body.id || !req.body.realname || !req.body.IDCard || !req.body.mobile || !req.body.isAgent) {
    //
    // {
    //   aId: req.body.id,
    //       realname: req.body.realname,
    //     IDCard: req.body.IDCard,
    //     mobile: req.body.mobile,
    //     orgin: req.body.orgin,
    //     areaName: req.body.areaName,
    //     isAgent: req.body.isAgent,
    //     cxwqId: req.body.cxwqId
    // };

    const postData = {
      "id": circulation.aId,
      "cxwqId":circulation._id,
      areaName: ( orgin[0] || {} ).areaName,
      orgin,
      "isAgent": 1,
      realname: circulation.signatoryName,
      IDCard: circulation.signatoryIdCard,
      mobile: circulation.signatoryMobile,
      clearOld:true,
    };
    // for (let land of loanLands) {
    //   // let data = await formatAreaCode.getFormatAreaCode(land.areaCode);
    //   // debug(method,'[GETFORAMATAREA](code)',data);
    //
    //   // postData.areaName = data.area || '';
    //   // postData.realname = land.username;
    //   postData.areaName = land.areaName;
    //   // postData.IDCard = land.IDCard;
    //   // postData.mobile = land.mobile;
    // }
    // postData.orgin = loanLands;

    await redisData.set(`${XNTWEBKEY}:${circulation.mobile}`, '');

    debug(`${HANDLER_NAME}eSignGenerate query`,url,postData);
    const {body:result}  = await superagent
        .post(url)
        .send(postData) // sends a JSON post body
        .set('Content-Type', 'application/json');

    debug(`${HANDLER_NAME}eSignGenerate result`,url,result);
    return result;
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler


'use strict';
const HANDLER_NAME = 'UPDATE_CIRCULATIONS';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  loanApplication:loanApplicationData,
  cxwqCirculations:circulationData,
  userVerifys:userVerifysData,
  infoCollectHistory:infoCollectHistoryData,
  landData,
} = require('../dataSvc/dataUtil');
// const aliOss = require('../aliOssSvc');
// const agent = require('superagent');
const aliyunAuthSvc = require('../aliyunAuth');
// const overwriteConfig = ['600fe47561f0e675643c5fa1'];
const statusCondition = {
  '600fe47561f0e675643c5fa1': ['wait_investigation_verify_2', 'wait_investigation_verify_review_2', 'wait_investigation_verify_5', 'pre_expected_value', 'wait_investigation_verify_6', 'loan_verify_4'],
}
const {assert} = require('../../utils/general')
const cropTypeDic = {
  '玉米': 'corn',
  '水稻': 'rice',
  '大豆': 'soybean',
  '其它': 'other',
  '其他': 'other',

}
const UpdateLandOrigin = require('./updateLandOrigin');
const assertDic = {};
assertDic[ 1 ] = async function (circulation){
  const { aId , name , idCard }  = circulation;
  const old2 = await circulationData.getOneByCondition( { aId , idCard , archived:false } );
  old2 && assert( old2.status !== 4 , 'UPDATE_CIRCULATIONS_ERROR_020' , '该出让方已经完成签约' );
  old2 && ( circulation._id = old2._id );
}
assertDic[ 2 ] = async function (circulation){
  const { aId , name , idCard , principalUniqueId , appendixInfo }  = circulation;
  assert( circulation.circulationUserType , 'UPDATE_CIRCULATIONS_WITHOUT_LAND_ERROR_004b' , '出让方类型不能为空' );
  circulation.circulationUserType === 3 && assert( principalUniqueId && principalUniqueId.trim() , 'UPDATE_CIRCULATIONS_WITHOUT_LAND_ERROR_004c' , '社会信用代码不能为空' );

  assert(  circulation.abode && circulation.abode.trim()  , 'UPDATE_CIRCULATIONS_WITHOUT_LAND_ERROR_016' , '住址不能为空' );
  const oldByIdcard = !principalUniqueId && idCard && await circulationData.getOneByCondition( { aId , idCard , circulationUserType:{$ne:3} , archived:false } );
  const oldByPrincipal = principalUniqueId && await circulationData.getOneByCondition( { aId , principalUniqueId , circulationUserType:3 , archived:false } );
  const old2 = oldByIdcard || oldByPrincipal , hintName = [ oldByIdcard && idCard , oldByPrincipal && principalUniqueId ].find(v=>v);
  debug(`${HANDLER_NAME}oldInfo`,circulation._id , idCard,oldByIdcard && oldByIdcard._id , principalUniqueId , oldByPrincipal && oldByPrincipal._id , '|old2._id:',old2 && old2._id )
  old2 && assert( old2.status !== 4 , 'UPDATE_CIRCULATIONS_ERROR_020' , `该出让方【${hintName}】已被导入或添加过，请到删除列表恢复！` );
  old2 && ( circulation._id = old2._id );
}

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      debug(method, '[Enter]', `context:`, JSON.stringify(this.context));

      const { input:{ circulation }, opts , opts: { tId,uId:operator } } = this.context;
      assert( circulation , 'UPDATE_CIRCULATIONS_ERROR_000' , 'circulation cant be empty' );
      !circulation._id && delete circulation._id;
      circulation.circulationType = circulation.circulationType || 1;
      circulation.circulationUserType = circulation.circulationUserType || 1;
      ( circulation.circulationUserType === 3 ) && ( circulation.legalPersonIdCard = circulation.idCard , circulation.legalPersonName = circulation.name )
      const { aId , idCard , name , appendixInfo , circulationType }  = circulation;
      const loanApplication = aId && await loanApplicationData.getById(aId) ;
      assert( loanApplication,'UPDATE_CIRCULATIONS_ERROR_001' , 'application not exists' );
      assert( loanApplication.addons && loanApplication.addons.info && loanApplication.addons.info.mId,'UPDATE_CIRCULATIONS_ERROR_001b' , '信息未采集' );
      const {area:areaCode} = loanApplication;
      const info = await infoCollectHistoryData.getById( loanApplication.addons.info.mId );
      assert( info ,'UPDATE_CIRCULATIONS_ERROR_001b' , '信息未采集关联数据异常' );
      
      const options = { landHeader: { key: "rongxin-land-area", value: loanApplication.area.substr(0, 2) } };
      const { myFamilies , myFamilyCardNos , userInfo , myContracts } = await this.familiesInfo( loanApplication , null , options );
      // assert( userInfo,'UPDATE_CIRCULATIONS_ERROR_002' , 'userverify not exists' );
      // const userInfo = await userVerifysData.getOneByCondition({
      //   uId: loanApplication.uId,archived:false,
      //   IDCardStatus: 'approved',
      // });
      // const options = { landHeader: { key: "rongxin-land-area", value: loanApplication.area.substr(0, 2) } };
      // const { content: myContractsOrigin = [] } = process.env.NODE_ENV !=='local-dev' && (
      //     await landData.getByUrl("/api/v1.0/contract/list", { cardNo: userInfo.IDCard }, options)
      // ) || { content: [] };
      // const myContracts = ( await Promise.all( myContractsOrigin.map( async contract=>{
      //   const families = await landData.getByUrl("/api/v1.0/contractor/families", { contractor: contract.code }, options);
      //   const isCoOwner = families.some( _faimly=>_faimly.cardNo === userInfo.IDCard && _faimly.coOwner === '1' )
      //   return isCoOwner ? contract : null;
      // } ) ) ).filter(v=>v);
      // debug(`${HANDLER_NAME}ContractList`,myContracts);
      // const myFamilies = process.env.NODE_ENV !=='local-dev'
      //     && ( await Promise.all( myContracts.map( contract => // 查出所有的家庭成员，并且摊平数组
      //           landData.getByUrl("/api/v1.0/contractor/families", { contractor: contract.code }, options) )) )
      //         .reduce((r,v)=>r.concat(v||[]),[])
      //     || [];
      // const myFamilyCardNos = myFamilies.reduce((r,v)=>(r[v.cardNo] = true,r),{});
      // delete myFamilyCardNos[userInfo.IDCard];
      // debug(`${HANDLER_NAME}MyFamilyCardNos`,userInfo.IDCard,'|||cards|||',Object.keys(myFamilyCardNos).join(','));

      const statusConfig = statusCondition[loanApplication.tId];
      const belongAll = ['甲方','乙方'];
      assert( statusConfig && statusConfig.includes(loanApplication.status) , 'UPDATE_CIRCULATIONS_ERROR_003' , '当前订单阶段不可以再导入合同了！' );
      assert( idCard && idCard.trim() , 'UPDATE_CIRCULATIONS_ERROR_004' , '出让方身份证号码不能为空' )
      assert( circulation.mobile && circulation.mobile.trim() , 'UPDATE_CIRCULATIONS_ERROR_005' , '出让方手机号不能为空' )
      assert( name && name.trim() , 'UPDATE_CIRCULATIONS_ERROR_006' , '出让方姓名不能为空' )
      assert( [1,2,3].includes( circulation.type ) , 'UPDATE_CIRCULATIONS_ERROR_007' , '支付方式枚举值有误' );
      assert( [ circulation.rentalFees , circulation.payFees , circulation.arrearageFees ].every(v=>typeof v === 'number' || ( v && !isNaN(v)) )  , 'UPDATE_CIRCULATIONS_ERROR_008a' , '流转费用相关值必须是数字' );
      ['rentalFees','payFees','arrearageFees'].forEach(k=>circulation[k] = Number( circulation[k] ));
      assert( [ circulation.rentalFees , circulation.payFees , circulation.arrearageFees ].every(v=>v>=0)  , 'UPDATE_CIRCULATIONS_ERROR_008b' , '流转费用相关值不能为负值' );
      assert( circulation.rentalFees === circulation.payFees + circulation.arrearageFees , 'UPDATE_CIRCULATIONS_ERROR_008c' , '错误：已支付与未支付之和不等于流转费用' )
      circulationType === 2 && circulation.type !== 1 && assert( circulation.receiptAccount && circulation.receiptAccount.trim() , 'UPDATE_CIRCULATIONS_ERROR_009' , '收款账户不能为空' )
      circulationType === 2 && circulation.type !== 1 && assert( circulation.depositBank && circulation.depositBank.trim() , 'UPDATE_CIRCULATIONS_ERROR_010' , '开户行不能为空' )
      assert(  belongAll.includes( circulation.subsidyBelong )  , 'UPDATE_CIRCULATIONS_ERROR_011' , '土地补贴归属枚举值有误' );
      assert(  belongAll.includes( circulation.plantationBelong )  , 'UPDATE_CIRCULATIONS_ERROR_012' , '种植补贴归属枚举值有误' );
      const allCropTypeValues = Object.values( cropTypeDic ) , multipleCropInfo  = Object.entries( circulation.multipleCropType || {} ) , totalArea = multipleCropInfo.reduce( (r,[type,area])=>r+area , 0 ) ;
      const croptTypeValidate1 = allCropTypeValues.includes( circulation.cropType ) ;
      const croptTypeValidate2 = multipleCropInfo.every( ([cropType])=>allCropTypeValues.includes( cropType ) );
      assert( croptTypeValidate1 || croptTypeValidate2  , 'UPDATE_CIRCULATIONS_ERROR_013' , '种植品种枚举值有误' );
      multipleCropInfo.length && ( circulation.area =  totalArea );
      circulationType !== 1 && assert( circulation.area > 0 , 'UPDATE_CIRCULATIONS_ERROR_013' , '种植面积总数必须大于0' );

      const old1 = circulation._id && await circulationData.getById( circulation._id );
      old1 && assert( old1.idCard === idCard , 'UPDATE_CIRCULATIONS_ERROR_021' , '编辑时不可以更改idCard' );

      const authRes = await aliyunAuthSvc.authIdentityByIDcard(name, idCard );
      const authSuccess = authRes && authRes.body && authRes.body.code === '0' && authRes.body.result && authRes.body.result.res === '1';
      debug( `${HANDLER_NAME}authRes` , name, idCard , authSuccess , authRes && authRes.body.code , authRes && authRes.body.result && authRes.body.result.res );
      assert( authSuccess , 'UPDATE_CIRCULATIONS_ERROR_022' , '出让方实名认证未通过' );


      // 定制类型的校验 与 预处理
      assertDic[ circulationType ] && await assertDic[ circulationType ].call( this , circulation );

      circulation.contractorNoList = ( circulation.contractorNoValue  || '' ).split(',').map(v=>v.trim()).filter(v=>v);
      circulation.appendix = circulation.appendix || [];
      const now = new Date(),year = now.getMonth() > 9 ? now.getFullYear() + 1 : now.getFullYear();
      const circulationStartDate = moment().format("YYYY-MM-DD") , circulationEndDate = `${year}-10-31`;

      const dv =  {
        areaCode,
        hasAgent: false,
        status: 3 ,
        sourceType:  '2',
        transfereeName: loanApplication.username,
        transfereeMobile: loanApplication.userMobile,
        transfereeIdCard: userInfo.IDCard,
        familyIdCard: [ circulation.idCard ],
        orderSn: loanApplication.sn,
        username : loanApplication.username,
        signatoryName: circulation.name,
        signatoryIdCard: circulation.idCard,
        signatoryMobile: circulation.mobile,
        circulationStartDate,circulationEndDate,
        outlandId: null,isRevoked:false,
        requestUniqueId: info.uniqueId,
        requestType : ( loanApplication.addons && loanApplication.addons.info || {} ).type,
        requestName : ( loanApplication.addons && loanApplication.addons.info || {} ).requestName,
        operator,
      };
      circulation.payType = circulation.payType || 3;
      circulation.circulationType = circulation.circulationType || 1;
      circulation.circulationUserType = circulation.circulationUserType || 1;
      circulation.simpleLandInfo = circulation.simpleLandInfo || [];
      const appendixList = appendixInfo && Object.entries( appendixInfo ).map( ([group,infos])=>( infos.map(v=>Object.assign( v , {group} )) ) ).reduce((r,v)=>r.concat(v),[]);
      circulation.appendix = appendixList || circulation.appendix || [];

      Object.assign( circulation , dv );
      // debug( `${HANDLER_NAME}Paras`,  circulation.circulationType , circulation.circulationUserType , userInfo.IDCard , circulation.idCard , JSON.stringify( myFamilyCardNos ) );
      // debug(method, `[colerror])`, obj.idCard,myFamilyCardNos[obj.idCard] && true || false);
      !( circulation.circulationType === 3 && [3,4].includes( circulation.circulationUserType ) ) && assert( !myFamilyCardNos[circulation.idCard] , 'UPDATE_CIRCULATIONS_WITHOUT_LAND_ERROR_014' , '不能与共有人签约' );
      !( circulation.circulationType === 3 && [3,4].includes( circulation.circulationUserType ) ) && assert( userInfo.IDCard !== circulation.idCard , 'UPDATE_CIRCULATIONS_WITHOUT_LAND_ERROR_015' , '不能与自己签约' );

      const { content:contractList } = await landData.getByUrl("/api/v1.0/contract/list", { cardNo: circulation.idCard }, options);
      debug(`${HANDLER_NAME}circulationcontractList`,circulation.aId , circulation._id || 'null',{ cardNo: circulation.idCard },contractList);
      const family = ( await Promise.all( contractList.map( async contract=>{
        const families = await landData.getByUrl("/api/v1.0/contractor/families", { contractor: contract.code }, options);
        return ( families || [] ).filter(_faimly => _faimly.coOwner == '1' && _faimly.cardNo)
      } ) ) ).reduce( (r,v)=>r.concat(v),[] )
      //完善家庭成员身份证
      circulation.familyIdCard = family.map(item => item.cardNo);
      debug(`${HANDLER_NAME}familyIdCard`, circulation.aId , circulation._id || 'null' , circulation.familyIdCard.length , circulation.familyIdCard,family)
      circulation.familyIdCard.length < 1 && (  circulation.familyIdCard = [circulation.idCard] );
      this.context.opts.circulation = circulation;
      const saveWithLand = circulation.circulationType === 1 && opts.saveWithLand;
      debug(`${HANDLER_NAME}orgInfo`,circulation._id,saveWithLand, loanApplication.orgInfo);
      if( saveWithLand ){
        debug(method, '[Exit](success)', circulation);
        return done();
      }
      circulation.orgInfo = loanApplication.orgInfo;
      circulation.lastModTime = new Date();
      !circulation._id && Object.assign( circulation , await circulationData.post( circulation ) );
      circulation._id && Object.assign( circulation , await circulationData.putById( circulation._id , circulation ) );
      // debug('debug233 ccc',circulation);

      circulation.circulationType !== 1 && ( opts.noHandleOrigin = true );// 非选地流转不需要
      if( circulation.circulationType !== 1 ){ // 测绘线上
        const {signUrl} = await new UpdateLandOrigin().eSignGenerate( circulation , [] ) || {};// 重新生成合同
        circulation.signUrl = signUrl;
        const shortUrl = await new UpdateLandOrigin().getShortUrl( circulation );
        circulation.shortUrl = shortUrl;
        await circulationData.putById( circulation._id , {signUrl,shortUrl} )
      }
      this.context.result = {circulation};
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async familiesInfo(loanApplication,aId,options){
    loanApplication = loanApplication || await loanApplicationData.getById( aId );
    const userInfo = await userVerifysData.getOneByCondition({
      uId: loanApplication.uId,archived:false,
      IDCardStatus: 'approved',
    });
    assert( userInfo,'UPDATE_CIRCULATIONS_ERROR_002' , 'userverify not exists' );
    options = options || { landHeader: { key: "rongxin-land-area", value: loanApplication.area.substr(0, 2) } };
    const { content: myContractsOrigin = [] } = process.env.NODE_ENV !=='local-dev' && (
        await landData.getByUrl("/api/v1.0/contract/list", { cardNo: userInfo.IDCard }, options)
    ) || { content: [] };
    const myContracts = ( await Promise.all( myContractsOrigin.map( async contract=>{
      const families = await landData.getByUrl("/api/v1.0/contractor/families", { contractor: contract.code }, options);
      const isCoOwner = families.some( _faimly=>_faimly.cardNo === userInfo.IDCard && _faimly.coOwner === '1' )
      return isCoOwner ? contract : null;
    } ) ) ).filter(v=>v);
    debug(`${HANDLER_NAME}ContractList`,myContracts);
    const myFamilies = process.env.NODE_ENV !=='local-dev'
        && ( await Promise.all( myContracts.map( contract => // 查出所有的家庭成员，并且摊平数组
              landData.getByUrl("/api/v1.0/contractor/families", { contractor: contract.code }, options) )) )
            .reduce((r,v)=>r.concat(v||[]),[])
        || [];
    const myFamilyCardNos = myFamilies.reduce((r,v)=>(r[v.cardNo] = true,r),{});
    delete myFamilyCardNos[userInfo.IDCard];
    debug(`${HANDLER_NAME}MyFamilyCardNos`,userInfo.IDCard,'|||cards|||',Object.keys(myFamilyCardNos).join(','));
    return { myFamilies , myFamilyCardNos , userInfo , myContracts }
  }


  undoAsync(done) {
    done()
  }
}

module.exports = Handler


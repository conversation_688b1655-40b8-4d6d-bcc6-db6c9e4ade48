'use strict';
const HANDLER_NAME = 'circulationAndLandInfoUtils';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const moment = require('moment');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const createShortUrlHandler = require('./createShortUrlHandler');
const formatAreaCode = require('../../persistence/formatAreaCode');
const {
  loanApplication:loanApplicationData,
  cxwqCirculations:circulationData,
  loanLand:landOriginData,
  userVerifys:userVerifysData,
  landData,
} = require('../dataSvc/dataUtil');
const {assert,isLandOccupy} = require('../../utils/general');
const env = require('../env');
const UpdateCirculations = require('./updateCirculations');
class Handler extends BaseHandler {

  async getList({ aId , idCard , idCard:cardNo }) {
    const method = `${HANDLER_NAME}.doAsync`
    debug(method, '[Enter]')
    const app = aId && await loanApplicationData.getById( aId );
    assert( app , 'ELAND_SVC_038b','aId异常' )
    // assert( landCodeList && landCodeList.length , 'E_LAND_SVC_0501','至少要选择一个地块' );
    // const occupyLands = landCodeList && landCodeList.length && await isLandOccupy(landCodeList,app.tId) || [];
    // assert( occupyLands.length === 0 , 'E_LAND_SVC_050' , `以下地块已被占用：${occupyLands.join(',')}` )
    const options = { landHeader: { key: "rongxin-land-area", value: "23" } };

    const {content:contractList=[]} = await landData.getByUrl("/api/v1.0/contract/list", { cardNo }, options);
    debug(method,'[landData](resluts)', contractList);
    // assert( contractList && contractList.length , 'E_LAND_SVC_038','未查找到土地数据' );

    const result = ( await Promise.all( contractList.map( async contract=> {
      const {code,grantNo,cardNo} = contract;
      const families = await landData.getByUrl("/api/v1.0/contractor/families", {contractor: contract.code}, options);
      if( !families.some( v=>v.cardNo === idCard && v.coOwner === '1' ) )return null;//不是共有人，不可以有这个本的地
      const contractor = await landData.getByUrl("/api/v1.0/contractor/info", { contractor: code }, options); //承包人信息
      debug(`${HANDLER_NAME}contractor`,contract.code,contractor);
      const lands = await landData.getByUrl("/api/v1.0/contract/land/list", { contract: grantNo }, options); //地块信息
      debug(`${HANDLER_NAME}landsOrigins`,contract.grantNo,lands);
      // const lands = landsOrigins.filter( ({land})=>landCodeList.includes(land) )
      // debug(method,'debug233b', '[landData](lands)', lands);
      if( lands.length === 0 )return null;
      const now = new Date(),year = now.getMonth() > 9 ? now.getFullYear() + 1 : now.getFullYear();

      await Promise.all( lands.map( async land=>{
        const oldQuery = {archived: false,aId,"lands.landCode":land.land};
        const old = await landOriginData.getOneByCondition(oldQuery);
        debug(`${HANDLER_NAME}Select`,JSON.stringify(oldQuery) , old && old._id || 'null' );
        land.area = parseFloat((land.contractArea * 0.0015).toFixed(2));
        land.landCode = land.land;
        land.area = parseFloat(((land.contractArea || 0 ) * 0.0015).toFixed(2));
        land.contractCode = land.contract;
        land.contractorName = contractor.name;
        land.contractorCardNo = contractor.cardNo;
        land.selected = old && true || false;
        land.occupy1 = ( await isLandOccupy([land.land],app.tId) ).length > 0;
        // const tempInfoQuery = {
        //   contractorNo: contract.grantNo,aId, archived: false,signFinish:true,
        // }
        // const tmpInfo = await landOriginData.getOneByCondition(tempInfoQuery);
        // land.occupy2 = tmpInfo && ( tmpInfo.aId !== aId || tmpInfo.IDCard !== idCard ) || false;// 以前为啥有 aId 不等于 aId的判断？罢了，不要改之前的逻辑
        // land.occupy2 && debug('debug233 occupy2',land.land,tmpInfo.aId,aId,tmpInfo.IDCard , idCard,tempInfoQuery,tmpInfo);
        land.occupy = land.occupy1 ;//|| land.occupy2;
      } ) );
      return {code,grantNo,contractor,contract,lands};
    }))).filter(v=>v);

    return result;
  }

}

module.exports = Handler


'use strict';
const HANDLER_NAME = 'createCommissionConfigHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const CommissionWithdrawData = require('../dataSvc/dataUtil').commissionWithdraw;
const CommissionEmployeeData = require('../dataSvc/dataUtil').commissionEmployee;
const EmployeesData = require('../dataSvc/dataUtil').employees;
const assistantersData = require('../dataSvc/dataUtil').assistanters;
const { WithdrawStatusConst } = require('./constant')
const moment = require('moment')
const commissionLimit = require('../../persistence/commissionLimit');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { employeeId, withdrawPrice } = self.context.input;
      const isLock = await commissionLimit.isLock(employeeId);
      if (isLock) {
        throw {
          errorCode: 'E_LOAN_API_LOCK_032',
          httpCode: 429,
          reason: '当前已存在正在提现的请求，请稍后操作'
        };
      }
      await commissionLimit.lock(employeeId);

      const employeeInfo = await assistantersData.getOneByCondition({ eId: employeeId, archived: false });
      if (!employeeInfo || !employeeInfo._id || !employeeInfo.bankCard || !employeeInfo.bankName || !employeeInfo.bankDeposit || !employeeInfo.frontIDCardImage || !employeeInfo.backIDCardImage) {
        throw {
          errorCode: 'E_WITHDRAW_INFO_NOT_ENOUGH_128',
          httpCode: 406,
          reason: '您好，请先去“我的-银行卡及身份证照片信息”维护银行卡'
        }
      }

      const data = {
        employeeId,
        status: WithdrawStatusConst.WaitProvide,
        applyWithdrawTime: new Date(),
      }

      const commissionEmployeeInfo = await CommissionEmployeeData.getOneByCondition({ employeeId, archived: false });
      let allowWithdrawPrice = 0;
      if (commissionEmployeeInfo) {
        allowWithdrawPrice = commissionEmployeeInfo.surplusCommission;
      }
      // 如果传入提现金额，按体现金额来，不得大于剩余金额
      if (withdrawPrice && withdrawPrice > allowWithdrawPrice) {
        throw {
          errorCode: 'E_WITHDRAW_PRICE_NOT_ENOUGH_128',
          httpCode: 406,
          reason: '提现金额不得大于剩余金额'
        }
      }
      // 文案
      if (allowWithdrawPrice < 30) {
        throw {
          errorCode: 'E_WITHDRAW_PRICE_NOT_ENOUGH_128',
          httpCode: 406,
          reason: '您的额度低于30元,暂无法提现'
        }
      }

      data.withdrawPrice = withdrawPrice || allowWithdrawPrice;

      // 查看本月是否已经发起过提现
      const monthStart = moment().startOf('months').toDate();
      const monthEnd = moment().endOf('months').toDate();
      const condition = { employeeId, archived: false, applyWithdrawTime: { $gte: monthStart, $lte: monthEnd } }
      const oldData = await CommissionWithdrawData.getOneByCondition(condition)
      // 文案
      if (oldData && oldData._id) {
        throw {
          errorCode: 'E_WITHDRAW_CREATE_REPEAT_128',
          httpCode: 406,
          reason: '您本月的提现次数已经用完，请期待下个月'
        }
      }

      const res = await CommissionWithdrawData.post(data)

      self.context.result = res;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
'use strict';
const HANDLER_NAME = 'listCommissionEmployeeHandler';
const moment = require('moment');
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const CommissionTrackingData = require('../dataSvc/dataUtil').commissionTracking
const LoanProductsData = require('../dataSvc/dataUtil').loanProducts
const EmployeesData = require('../dataSvc/dataUtil').employees
const LoanApplicationData = require('../dataSvc/dataUtil').loanApplication
const {CommissionTypeMap} = require('./constant')


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const data = self.context.input;

      const res = await CommissionTrackingData.getListAndCountByCondition(data);

      const promises = [];
      for (const it of res.result) {
        const commissionType = it.commissionType;
        it.commissionTypeStr = CommissionTypeMap.get(commissionType);
        it.createdTime = moment(it.createdTime).format('YYYY-MM-DD HH:mm');
        if(it.pId) {
          promises.push(LoanProductsData.getById(it.pId).then(data => {
            it.productName = data && data.name || '';
          }));
        }
        if(it.sn) {
          promises.push(LoanApplicationData.getById(it.relationId).then(data => {
            it.applyUsername = data && data.username || '';
            const len = it.applyUsername.length;
            if(it.applyUsername && len == 2) {
              it.applyUsername = it.applyUsername[0] + '*';
            } else if(it.applyUsername && len > 2) {
              it.applyUsername = it.applyUsername[0] + '*' + it.applyUsername[len - 1];
            }
          }))
          // promises.push(EmployeesData.getById(it.employeeId).then(data => {
          //   it.employeeName = data && data.username || '';
          //   const len = it.employeeName.length;
          //   if(it.employeeName && len == 2) {
          //     it.employeeName = it.employeeName[0] + '*';
          //   } else if(it.employeeName && len > 2) {
          //     it.employeeName = it.employeeName[0] + '*' + it.employeeName[len - 1];
          //   }
          // }));
        }
      }
      await Promise.all(promises);
      self.context.result = res;
      debug(method, '[Exit](success)',self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }
  

}
module.exports = Handler;

/** 一级分类 */
const Type1Const = {
  Finance: 'finance',
}

/** 结算方式 */
const SettlementMethodConst = {
  Price: 'price',
  Count: 'count',
}

/** 佣金类型 */
const CommissionTypeConst = {
  Finance: 'finance',
  Withdraw: 'withdraw',
}

/** 提现状态 */
const WithdrawStatusConst = {
  WaitProvide: 'waitProvide',
  AlreadyProvide: 'alreadyProvide'
}

/** 变动类型 */
const AlterationTypeConst = {
  Add: 'add',
  Sub: 'sub',
}

/** 修正原因类型 */
const AdjustmentTypeConst = {
  FinanceFail: 'financeFail',
}

/** 操作人员类型 */
const OperatorTypeConst = {
  Employee: 'employee',
  System: 'system',
}

/** 提现信息认证状态 */
const WithdrawInfoVerifyStatusConst = {
  WaitVerify: 'waitVerify',
  AlreadyVerify: 'alreadyVerify',
}

const OperatorTypeMap = new Map([
  [OperatorTypeConst.Employee, '员工'],
  [OperatorTypeConst.System, '系统'],
])

const AdjustmentTypeMap = new Map([
  [AdjustmentTypeConst.FinanceFail, '该业务办理失败'],
])

const AlterationTypeMap = new Map([
  [AlterationTypeConst.Add, '增加'],
  [AlterationTypeConst.Sub, '减少'],
])

const WithdrawStatusMap = new Map([
  [WithdrawStatusConst.WaitProvide, '待发放'],
  [WithdrawStatusConst.AlreadyProvide, '已发放']
])

const RoleTypeMap = new Map([
  ['5eb8fee1c6ecfe44d4ecaed1', '客户经理'],
  ['5eb8ff2ec6ecfe44d4ecaed9', '协理员']
])

/** 员工佣金排序方式 */
const CommissionEmployeeSortMap = new Map([
  [JSON.stringify({ createdTime: -1 }), '创建时间由近及远;'],
  [JSON.stringify({ totalCommission: -1 }), '佣金总额由高到低;'],
  [JSON.stringify({ totalCommission: 1 }), '佣金总额由低到高;'],
  [JSON.stringify({ surplusCommission: -1 }), '佣金余额由高到低;'],
  [JSON.stringify({ surplusCommission: 1 }), '佣金余额由低到高;'],
  [JSON.stringify({ totalCommissionWithdraw: -1 }), '已提现金额由高到低;'],
  [JSON.stringify({ totalCommissionWithdraw: 1 }), '已提现金额由低到高;'],
])

const Type1Map = new Map([
  [Type1Const.Finance, '金融业务']
])

const SettlementMethodMap = new Map([
  [SettlementMethodConst.Price, '按金额'],
  [SettlementMethodConst.Count, '按笔数'],
])

const CommissionTypeMap = new Map([
  [CommissionTypeConst.Finance, '金融'],
  [CommissionTypeConst.Withdraw, '提现'],
])

const WithdrawInfoVerifyStatusMap = new Map([
  [WithdrawInfoVerifyStatusConst.WaitVerify, '待认证'],
  [WithdrawInfoVerifyStatusConst.AlreadyVerify, '已认证']
])

module.exports = {
  Type1Map, SettlementMethodMap, CommissionTypeMap,
  RoleTypeMap, CommissionEmployeeSortMap, WithdrawStatusMap,
  AlterationTypeMap, AdjustmentTypeMap, OperatorTypeMap,
  Type1Const, SettlementMethodConst, CommissionTypeConst,
  WithdrawStatusConst, AlterationTypeConst, AdjustmentTypeConst,
  OperatorTypeConst, WithdrawInfoVerifyStatusConst, WithdrawInfoVerifyStatusMap
}
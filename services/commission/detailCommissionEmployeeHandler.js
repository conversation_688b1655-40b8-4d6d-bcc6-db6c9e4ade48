'use strict';
const HANDLER_NAME = 'detailCommissionEmployeeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const CommissionEmployeeData = require('../dataSvc/dataUtil').commissionEmployee;
const EmployeeData =  require('../dataSvc/dataUtil').employees;
const EmployeeGroupsData =  require('../dataSvc/dataUtil').employeeGroups;
const ConfigSealData = require('../dataSvc/dataUtil').configSeals
const dictAreasData = require('../dataSvc/dataUtil').dictAreas
const ComissionWithdrawData = require('../dataSvc/dataUtil').commissionWithdraw
const CommissionTrackingData = require('../dataSvc/dataUtil').commissionTracking
const ProductData = require('../dataSvc/dataUtil').loanProducts
const FundData = require('../dataSvc/dataUtil').funds
const {RoleTypeMap, Type1Map} = require('./constant')
const formatAreaCode = require('../../persistence/formatAreaCode');
const moment = require('moment');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {userid} = self.context.input;

      const commissionEmployee = await CommissionEmployeeData.getOneByCondition({ employeeId: userid, archived: false });

      let data = {
        allowWithdrawCount: 1
      };
      if(commissionEmployee) {
        data.totalCommission = commissionEmployee.totalCommission;
        data.totalCommissionWithdraw = commissionEmployee.totalCommissionWithdraw;
        data.surplusCommission = commissionEmployee.surplusCommission;

        const monthStart = moment().startOf('month').toDate();
        const monthEnd = moment().endOf('month').toDate();
        const withdrawInfo = await ComissionWithdrawData.getOneByCondition({
          employeeId: userid,
          archived: false,
          applyWithdrawTime: { $gte: monthStart, $lte: monthEnd }
        });
        if(withdrawInfo) data.allowWithdrawCount = 0
      }

      self.context.result = data;
      debug(method, '[Exit](success)',self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }
  

}
module.exports = Handler;

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loanApplicationInsureOrder:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetLoanApplicationInsureOrderListHandler = require('./getLoanApplicationInsureOrderListHandler');
const GetLoanApplicationInsureOrderDetailHandler = require('./getLoanApplicationInsureOrderDetailHandler');

class Service {
  constructor() {

  }

  async getLoanApplicationInsureOrderList(input, _opts) {
    const method = 'getLoanApplicationInsureOrderList';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetLoanApplicationInsureOrderListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLoanApplicationInsureOrderDetail(input, _opts) {
    const method = 'getLoanApplicationInsureOrderDetail';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetLoanApplicationInsureOrderDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

}

module.exports = new Service();

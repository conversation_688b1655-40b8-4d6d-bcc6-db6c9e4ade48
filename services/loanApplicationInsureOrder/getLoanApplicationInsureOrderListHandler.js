'use strict';

const HANDLER_NAME = 'GetLoanApplicationInsureOrderListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loanApplicationInsureOrder:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationInsureOrderData = require('../dataSvc/dataUtil').loanApplicationInsureOrder;
const moment = require('moment');
const loanApplicationInsureProduct = require('../dataSvc/dataUtil').loanApplicationInsureProduct;
const { STATUS_MAP, INS_COMPANY_MAP } = require('./const');

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    const self = this;
    const method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      const condition = self.context.input;
      const data = await loanApplicationInsureOrderData.getListAndCountByCondition(condition);
      data.result = await Promise.all((data.result || []).map(async (item) => {
        item.statusDesc = STATUS_MAP.get(item.status);
        const product = await loanApplicationInsureProduct.getById(item.pId, {cache : true , expire: 24 * 60 * 60 });
        item.insCompany = INS_COMPANY_MAP.get(product.insCompanyCode);
        item.name = product.name;
        item.insStartTime = moment(item.insStartTime).format('YYYY-MM-DD');
        item.insEndTime = moment(item.insEndTime).format('YYYY-MM-DD');
        item.createdTime = moment(item.createdTime).format('YYYY/MM/DD HH:mm');
        if (item.payDate) {
          item.payDate = moment(item.payDate).format('YYYY-MM-DD HH:mm:ss');
        }
        return item;
      }));
      self.context.result = data;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;

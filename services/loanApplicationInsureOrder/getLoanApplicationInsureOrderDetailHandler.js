'use strict';

const HANDLER_NAME = 'GetLoanApplicationInsureOrderDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loanApplicationInsureOrder:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const moment = require("moment");
const loanApplicationInsureOrderData = require('../dataSvc/dataUtil').loanApplicationInsureOrder;
const loanApplicationInsureProduct = require('../dataSvc/dataUtil').loanApplicationInsureProduct;
const employeeData = require('../dataSvc/dataUtil').employees;
const formatAreaCode = require('../../persistence/formatAreaCode');
const { STATUS_MAP, INS_COMPANY_MAP } = require('./const');

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    const self = this;
    const method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      const condition = self.context.input;
      const result = await loanApplicationInsureOrderData.getById(condition.id);
      result.statusDesc = STATUS_MAP.get(result.status);
      const product = await loanApplicationInsureProduct.getById(result.pId);
      result.insCompany = INS_COMPANY_MAP.get(product.insCompanyCode);
      result.name = product.name;
      result.insureTerm = `${parseInt((result.insureTerm || 12) / 12)}年`

      result.customerTypeDesc = "个人";
      result.docTypeDesc = "身份证/户口本";
      if (result.IDCard) {
        result.gender = result.IDCard.charAt(result.IDCard.length - 2) % 2 == 1 ? "男" : "女";
        const date = result.IDCard.substring(6, 14);
        result.birthDate = date ? moment(date, "YYYYMMDD").format('YYYY-MM-DD') : "";
      }

      result.region = (await formatAreaCode.getFormatAreaCode(result.areaCode)).area;

      result.insured = {
        customerRelationsDesc: "本人",
        userName: result.userName,
        docTypeDesc: result.docTypeDesc,
        IDCard: result.IDCard,
        idValidStart: result.idValidStart,
        idValidEnd: result.idValidEnd,
        gender: result.gender,
        birthDate: result.birthDate,
        mobile: result.mobile,
        email: result.email,
        region: result.region,
        address: result.address,
      };

      result.firstBeneficiary = "吉林省物权融资农业发展有限公司";
      result.beneficiary = "法定受益人";

      if (result.operator) {
        const employee = await employeeData.getById(result.operator, {cache : true , expire: 24 * 60 * 60 });
        result.pengPaiBaoAgentNo = employee.pengPaiBaoAgentNo;
        result.agentNo = employee.agentNo;
      }

      result.insStartTime = moment(result.insStartTime).format('YYYY-MM-DD');
      result.insEndTime = moment(result.insEndTime).format('YYYY-MM-DD');
      result.createdTime = moment(result.createdTime).format('YYYY-MM-DD HH:mm:ss');
      result.lastModTime = moment(result.lastModTime).format('YYYY-MM-DD HH:mm:ss');

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;

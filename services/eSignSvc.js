/**
 * zhongcheng auth svc
 * <AUTHOR>
 */

'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan:services:eSignSvc');
const config = require('config');
const superagent = require('superagent');
const uuid = require('uid-safe');
const qs = require('qs');
const crypto = require('crypto');
const ESIGN_ERRORCODE = require('../utils/eSignErrorCode').ESIGN_ERRORCODE;

const REQ_DEFAULT_TIMEOUT = 2 * 60 * 1000;

//创建用户
const CREATE_USER = '/account/create/person';
//创建合同签署流程
const CREATE_CONTRACT_FLOW = '/sign/contract/addProcess';
//发起用户自动签署，此针对的是物权公司自动签署
const AUTO_SIGN_CONTRACT_FLOW_TASK = '/sign/contract/userSignTask';
//发起个人手动签署
const MANUAL_SIGN_CONTRACT_FLOW_TASK = '/sign/contract/handPersonSignTask';
//发起个人手动签署
const MANUAL_MORE_SIGN_CONTRACT_FLOW_TASK = '/sign/contracts/handPersonSignTask';
// 发起企业多文档签署
const MANUAL_MORE_SIGN_CONTRACT_FLOW_TASK_ORG = '/sign/contracts/handOrgSignTask';
//查询合同签署详情
const GET_CONTRACT_FLOW_INFO = '/sign/contracts/detail';
//归档流程
const ARCHIVED_CONTRACT_FLOW = '/sign/contract/archiveProcess';
//获取签署任务查看地址
const GET_FLOW_VIEWURL = '/flow/get/viewurl';
//签署文件下载
const SIGN_DOWNLOAD = '/sign/download';
// 创建合同多签流程
const CREATE_MORE_CONTRACT_FLOW = "/sign/contracts/addProcess";
//合同模板创建合同
const CTEATE_DOC_BY_TEMPLATE = '/doc/createbytemplate';
// 创建企业账户
const ORGANIZE_COMMON = '/account/create/organize/common';
// 设置静默签署
const SETSILENTSIGN = '/account/platform/silentsign';
// 创建企业印章
const ORGANIZE_SEAL = '/seal/create/organize';
//发起用户自动签署多份，此针对的是物权公司自动签署
const AUTO_SIGN_MORE_CONTRACT_FLOW_TASK = '/sign/contracts/userSignTask';

// 下载合同原文
const DOWNLOADURL = "/doc/downloadurl"

// 本地文件方式创建合同
const DOC_CREATEBYFILEKEY = "/doc/createbyfilekey"
//获取上传文件地址
const GET_FILE_UPLOADURL = "/file/uploadurl"


class ESignSvc {
  constructor() {
    this.appId = null;
    this.appSecret = null;
    this.host = null;
  }

  //创建用户
  async createUser(payload, league) {
    let self = this;
    let method = 'createUser';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(CREATE_USER, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //创建企业用户
  async createOrganizeCommon(payload, league) {
    let self = this;
    let method = 'createOrganizeCommon';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(ORGANIZE_COMMON, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  // 设置静默签署
  async setSilentsign(payload, league) {
    let self = this;
    let method = 'setSilentsign';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(SETSILENTSIGN, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //创建企业印章
  async createOrganize(payload, league) {
    let self = this;
    let method = 'createOrganize';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(ORGANIZE_SEAL, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //创建合同签署流程
  async createContractFlow(payload, league) {
    let self = this;
    let method = 'createContractFlow';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(CREATE_CONTRACT_FLOW, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //创建合同多签署流程
  async createMoreContractFlow(payload, league) {
    let self = this;
    let method = 'createMoreContractFlow';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(CREATE_MORE_CONTRACT_FLOW, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  //发起用户自动签署，此针对的是物权公司自动签署
  async autoSignContractFlowTask(payload, league) {
    let self = this;
    let method = 'autoSignContractFlowTask';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(AUTO_SIGN_CONTRACT_FLOW_TASK, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //发起用户自动签署多合同，此针对的是物权公司自动签署
  async autoSignMoreContractFlowTask(payload, league) {
    let self = this;
    let method = 'autoSignMoreContractFlowTask';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(AUTO_SIGN_MORE_CONTRACT_FLOW_TASK, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //发起个人手动签署
  async manualSignContractFlowTask(payload, league) {
    let self = this;
    let method = 'manualSignContractFlowTask';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(MANUAL_SIGN_CONTRACT_FLOW_TASK, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //发起个人手动多签署
  async manualMoreSignContractFlowTask(payload, league) {
    let self = this;
    let method = 'manualSignContractFlowTask';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(MANUAL_MORE_SIGN_CONTRACT_FLOW_TASK, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //发起企业多文档手动多签署
  async manualMoreSignContractFlowTaskOrg(payload, league) {
    let self = this;
    let method = 'manualSignContractFlowTask';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(MANUAL_MORE_SIGN_CONTRACT_FLOW_TASK_ORG, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  //查询合同签署详情
  async getContractFlowInfo(payload, league) {
    let self = this;
    let method = 'getContractFlowInfo';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(GET_CONTRACT_FLOW_INFO, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //归档流程
  async archiveContractFlow(payload, league) {
    let self = this;
    let method = 'archiveContractFlow';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(ARCHIVED_CONTRACT_FLOW, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //获取签署任务查看地址
  async getFlowViewUrl(payload, league) {
    let self = this;
    let method = 'getFlowViewUrl';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(GET_FLOW_VIEWURL, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //签署文件下载
  async getSignDownload(payload, league) {
    let self = this;
    let method = 'getSignDownload';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(SIGN_DOWNLOAD, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  //合同模板创建合同
  async createDocByTemplate(payload, league) {
    let self = this;
    let method = 'createDocByTemplate';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(CTEATE_DOC_BY_TEMPLATE, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //获取合同原文下载
  async getDocDownloadurl(payload, league) {
    let self = this;
    let method = 'getDocDownloadurl';
    try {
      debug(method, '[Enter]');
      if (!payload || !payload.docId) {
        throw {
          httpCode: 406,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(DOWNLOADURL, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  // 本地文件创建合同
  async createLoaclContract(payload, league) {
    let self = this;
    let method = 'createLoaclContract';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(DOC_CREATEBYFILEKEY, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  // 获取文件上传url
  async getEsignUploadUrl(payload, league) {
    let self = this;
    let method = 'getEsignUploadUrl';
    try {
      debug(method, '[Enter]');
      if (!payload) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.execute(GET_FILE_UPLOADURL, payload, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  // 上传文件
  async postEsignUpload(url, payload, head, league) {
    let self = this;
    let method = 'postEsignUpload';
    try {
      debug(method, '[Enter]');
      if (!payload || !url) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_038',
          reason: 'invalid input param'
        };
      }
      let result = await self.executeUploanFile(url, payload, head, league);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async execute(url, payload, league) {
    let self = this;
    let method = 'execute';
    try {
      debug(method, '[Enter]');
      if (!payload || !url) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_034',
          reason: 'invalid input param'
        };
      }
      let eSignAuthConfig = config.get('eSignAuth');

      let appId = eSignAuthConfig.appId;
      let appSecret = eSignAuthConfig.appSecret;
      if (league == "YYN") {
        appId = eSignAuthConfig.JFappId;
        appSecret = eSignAuthConfig.JFappSecret;
      }

      let _url = `${self.host}${url}`;
      debug(method, '[RPC-Start] url: ', _url);
      debug(method, '[RPC-Start] appId: ', appId);
      debug(method, '[RPC-Start] payload: ', JSON.stringify(payload));

      const res = await superagent
        .post(_url)
        .send(payload) // sends a JSON post body
        .set('Content-Type', 'application/json')
        .set('X-Tsign-Open-App-Id', appId)
        .set('X-Tsign-Open-App-Secret', appSecret)
        .timeout({
          deadline: REQ_DEFAULT_TIMEOUT
        });

      if (!res || !res.text) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_062',
          reason: 'response body is null'
        }
      }
      let result = self.getResponseBody(res.body);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  // 上传文件到e签宝
  async executeUploanFile(url, payload, head, league) {
    let self = this;
    let method = 'executeUploanFile';
    try {
      debug(method, '[Enter]');
      if (!payload || !url) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_034',
          reason: 'invalid input param'
        };
      }
      let eSignAuthConfig = config.get('eSignAuth');

      let appId = eSignAuthConfig.appId;
      let appSecret = eSignAuthConfig.appSecret;
      if (league == "YYN") {
        appId = eSignAuthConfig.JFappId;
        appSecret = eSignAuthConfig.JFappSecret;
      }

      debug(method, '[RPC-Start] url: ', url);
      debug(method, '[RPC-Start] payload: ', payload);

      const res = await superagent
        .put(url)
        .send(payload) // sends a JSON post body
        .set('Content-Type', 'application/pdf')
        .set('Content-MD5', head)
        .set('X-Tsign-Open-App-Id', appId)
        .set('X-Tsign-Open-App-Secret', appSecret)
        .timeout({
          deadline: REQ_DEFAULT_TIMEOUT
        });

      if (!res || !res.text) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_062',
          reason: 'response body is null'
        }
      }
      let result = self.getResponseBody(res.body);
      debug(method, '[Exit](success)', result);
      return result;

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  getResponseBody(body) {
    let method = 'getResponseBody';
    debug(method, '[Enter]', body);
    try {
      if (!body) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_092',
          reason: 'body is null'
        }
      }
      if (body.errCode) {
        throw {
          httpCode: 400,
          errorCode: 'E_ESIGNSVC_' + body.errCode,
          reason: body.msg || ESIGN_ERRORCODE.get(body.errCode)
        }
      }
      if ((body && body.data) || !body.errCode) {
        return body.data || {
          result: 'success'
        };
      }
      throw {
        httpCode: 400,
        errorCode: 'E_ESIGNSVC_110',
        reason: 'unkonwn error'
      }
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  getSignature(condition) {
    let str = '';
    let self = this;
    for (const key in condition) {
      if (condition.hasOwnProperty(key)) {
        const element = condition[key];
        str += `${key}${element}`;
      }
    }

    str += self.privateKey;
    let hasher = crypto.createHash('md5');
    hasher.update(str);
    let res = hasher.digest('hex').toUpperCase();
    return res;
  }

  init() {
    debug('init [Enter]');
    let eSignAuthConfig = config.get('eSignAuth');
    this.appSecret = eSignAuthConfig.appSecret;
    this.appId = eSignAuthConfig.appId;
    this.host = eSignAuthConfig.host;

    debug('init [Exit](success) appId: ', this.appId, ' appSecret: ', this.appSecret, ' host: ', this.host);
  }
}


let eSignSvc = new ESignSvc();
eSignSvc.init();
module.exports = eSignSvc;
/*
 * @Author: qays
 * @Date: 2023-06-29 10:08:51
 * @LastEditTime: 2023-07-20 10:44:36
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.mgr.app.api\services\info_check\reviewLandCheckInfoHandler.js
 */
'use strict';

const HANDLER_NAME = 'reviewLandCheckInfoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app:api:services:info_check:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeesData = require('../dataSvc/dataUtil').employees
const { reviewLandInfo } = require('./common/request')

class <PERSON><PERSON> extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const body = self.context.input;

      const employee = await employeesData.getById(body.uid, {cache : true , expire: 24 * 60 * 60 });
      if(!employee) {
        throw {
          errorCode: 'E_Info_Check_100',
          httpCode: 406,
          reason: 'user not found'
        }
      }

      body.userName = employee.username;
      const auth = body.auth;
      const appUser = body.appUser
      delete body.auth;

      const result = await reviewLandInfo(body, auth, appUser);

      self.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}


module.exports = Handler;
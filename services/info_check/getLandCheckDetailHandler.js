'use strict';

const HANDLER_NAME = 'getLandCheckDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app:api:services:info_check:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const moment = require('moment')
const { getLandAndFamilyDetail } = require('./common/request');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let {id, auth} = self.context.input;

      // auth = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzY29wZSI6InRlc3Q6cm9uZ3hpbjpsb2FuOm1ncjphcHAiLCJpYXQiOjE2NzUxMzYyNDUsIm93dCI6InVzZXIiLCJhcHAiOiI2MWQ3YTQ5YzAxMWE5NWNmZWZjODA1OTkiLCJleHAiOjE2ODU1MDQyNDUsImF1ZCI6InJvbmd4aW46bG9hbjpqd3Q6YWNjZXNzIiwiaXNzIjoicm9uZ3hpbjpsb2FuOnBhc3Nwb3J0Iiwic3ViIjoiNjNkODhjOThmZGUzMTM2MGUyODJkYzU4IiwianRpIjoiTjcxNGd2QjdaSFVxQTRMRkNUc2RTSnFJN193In0.imzBIJB4rYTS0AcFEo8wb_Ws2iOl6vvkxJ3O160t2-Q'
      const result = await getLandAndFamilyDetail(id, auth)
      if(!result && result.code != 0) {
        throw {
          errorCode: 'E_INFO_CHECK_679',
          httpCode: 406,
          reason: result && result.reason || '获取土地信息处理详情失败'
        }
      }

      const res = result.data;

      self.context.result = res;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}


module.exports = Handler;
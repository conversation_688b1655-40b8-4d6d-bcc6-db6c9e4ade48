const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.app.api:services:info_check:request');
const config = require('config');
const agent = require('superagent');
const env = require('../../env').getServerMode();


/**
 * @description 查询土地检测列表
 * @param {Object} data - 查询条件
 *  - {Number} submitTime [required]: 提交时间排序, 不能为空, 默认倒序-0, 升序-1
 *  - {Number} status: 检测状态, 默认为待处理 10 待检测 20 待处理 21 退回再补充 30 已处理
 *  - {String} areacode : 地区编码
 *  - {Array<String>} areaList [required]: 地区编码列表
 *  - {Number} pageNum [required]: 页码, 不能为空
 *  - {Number} pageSize [required]: 每页条数, 不能为空
 * @param {String} auth [required] - 管理人员token
 * @returns {Object} - 返回结果
 *  {
    "submitTime": 0,
    "status": 0,
    "areacode": "string",
    "areaList": [
        "string"
    ],
    "pageNum": 0,
    "pageSize": 0
  }
 */
async function getLandCheckList(data, auth) {
  let method = 'getLandCheckList';
  debug(method, '[Enter]');

  const path = '/api/v1.0/credit/land/review/list/page';
  const params = data;
  try {
    debug(method, '[Middle](param): ', params);

    const protocol = `${env == 'local-dev' ? 'http' : 'https'}`;
    const host = `${protocol}://${config.get('rongxin_api_usercredit.host')}:${config.get('rongxin_api_usercredit.port')}`
    const url = `${host}${path}`;
    const result = await agent.post(url).set('AuthorizationJinFu', 'Bearer ' + auth).send(params);
    debug(method, '[Middle](result): ', result);

    const body = result && result.body || {};
    debug(method, '[Exit](success)');
    return body;
  } catch (error) {
    debug.error(method, '[Exit](failed)', error);
    formatError(error);
    throw error;
  }
}

/**
 * @description 根据id查询土地信息和家庭成员信息
 * @param {String} id [required] - 查询条件
 * @param {String} auth [required] - 管理人员token
 * @returns {Object} - 返回结果
 * {
    "data": {
        "id": 123,
        "familyCode": "test_3089a8630781",
        "reviewOpinion": 21,
        "remark": "退回再补充, 图片不清晰",
        "descriptions": [
            "承包方编码或经营权证代码错误",
            "其他- 配偶去世，非共有人"
        ],
        "applicantForm": {
            "idCard": "test_8b9e6ca8c1f7",
            "name": "test_a6b4aeb965c7",
            "mobile": "test_ea09836eeab8",
            "areacode": "220422101203",
            "contactAddress": "联系地址"
        },
        "landContractForm": {
            "operationWarrantCode": "test_1873ef7836d9",
            "contractorCode": "test_5c78d79367e6",
            "tdqqhtzmj": "test_8ecc96e34163",
            "contractorName": "test_f73155e2ea77",
            "contractorIdCard": "test_71ad9a870b45",
            "landTotal": "test_c508c1af04e1",
            "firstPartyName": "test_ef969b1adfaf",
            "address": "地址",
            "contractorDateStart": "test_c62016432245",
            "contractorDateEnd": "test_0f1109e1a5b4",
            "contractMode": "test_259e7dea37f7",
            "landImages": {
                "illustratePage": "https://12556723",
                "infoPage": "https://12366367",
                "landPage": "https://12777673",
                "diagramPage": [
                    "https://68768768",
                    "https://687687565668"
                ]
            }
        },
        "familyMemberForms": [
            {
                "memberTmpId": "test_a49f3b41349c",
                "name": "test_4cbabb63d140",
                "idCard": "test_bb6e31693b05",
                "mobile": "test_057413a33d71",
                "relationship": "test_88d981e3d2f8",
                "isCoowner": "test_e01a35577d62",
                "idCardImages": [
                    "https://123123",
                    "https://68768768"
                ],
                "familyCode": "test_3089a8630781"
            }
        ]
    },
    "code": "0",
    "timestamp": 1679537116297,
    "reason": null
  }
 */
async function getLandAndFamilyDetail(id, auth) {
  let method = 'getLandAndFamilyDetail';
  debug(method, '[Enter]');

  const path = '/api/v1.0/credit/land/review/getLandAndFamilyMember';
  const params = id;
  try {
    debug(method, '[Middle](param): ', params);

    const protocol = `${env == 'local-dev' ? 'http' : 'https'}`;
    const host = `${protocol}://${config.get('rongxin_api_usercredit.host')}:${config.get('rongxin_api_usercredit.port')}`
    const url = `${host}${path}/${params}`;
    const result = await agent.get(url).set('AuthorizationJinFu', 'Bearer ' + auth);
    debug(method, '[Middle](result): ', result && result.body || {});

    const body = result && result.body || {};
    debug(method, '[Exit](success)');
    return body;
  } catch (error) {
    debug.error(method, '[Exit](failed)', error);
    formatError(error);
    throw error;
  }
}

/**
 * @description 处理信息
 * @param {Object} data [required] - 处理信息
 * @param {String} auth [required] - 管理人员token
 * @returns {Object} - 返回结果
 * {
    "id": 0,
    "familyCode": "string",
    "applicantForm": {
        "idCard": "string",
        "name": "string",
        "mobile": "string",
        "areacode": "string"
    },
    "landContractForm": {
        "operationWarrantCode": "string",
        "contractorCode": "string",
        "tdqqhtzmj": "string",
        "contractorName": "string",
        "contractorIdCard": "string",
        "landTotal": "string",
        "firstPartyName": "string",
        "address": "string",
        "contractorDateStart": "string",
        "contractorDateEnd": "string",
        "contractMode": "string",
        "landImages": {
            "illustratePage": "string",
            "infoPage": "string",
            "landPage": "string",
            "diagramPage": [
                "string"
            ]
        }
    },
    "familyMemberForms": [
        {
            "memberTmpId": "string",
            "name": "string",
            "idCard": "string",
            "mobile": "string",
            "relationship": "string",
            "isCoowner": "string",
            "idCardImages": [
                {}
            ],
            "familyCode": "string"
        }
    ],
    "descriptions": [
        {}
    ],
    "reviewOpinion": "string",
    "remark": "string"
  }
 */
async function reviewLandInfo(data, auth, appUser) {
  let method = 'reviewLandInfo';
  debug(method, '[Enter]');

  const path = '/api/v1.0/credit/land/review/dispose/info';
  const params = data;
  try {
    debug(method, '[Middle](param): ', params);

    const protocol = `${env == 'local-dev' ? 'http' : 'https'}`;
    const host = `${protocol}://${config.get('rongxin_api_usercredit.host')}:${config.get('rongxin_api_usercredit.port')}`
    const url = `${host}${path}`;
    const result = await agent.post(url)
    .set('AuthorizationJinFu', 'Bearer ' + auth)
    .set('AppUser', appUser)
    .send(params);
    debug(method, '[Middle](result): ', result && result.body || {});

    const body = result && result.body || {};
    debug(method, '[Exit](success)');
    return body;
  } catch (error) {
    debug.error(method, '[Exit](failed)', error);
    formatError(error);
    throw error;
  }
}

/**
 * @description 根据字典编码获取字典集合
 */
async function getDictList(dictId) {
  let method = 'getDictList';
  debug(method, '[Enter]');

  const path = '/api/v1.0/credit/dict/getDictList';
  const params = dictId;
  try {
    debug(method, '[Middle](param): ', params);

    const protocol = `${env == 'local-dev' ? 'http' : 'https'}`;
    const host = `${protocol}://${config.get('rongxin_api_usercredit.host')}:${config.get('rongxin_api_usercredit.port')}`
    const url = `${host}${path}/${params}`;
    const result = await agent.get(url);
    debug(method, '[Middle](result): ', result && result.body || {});

    const body = result && result.body || {};
    debug(method, '[Exit](success)');
    return body;
  } catch (error) {
    debug.error(method, '[Exit](failed)', error);
    formatError(error);
    throw error;
  }
}

/**
 * @description 根据字典编码数组获取字典集合
 */
async function getDictArrList(dictList) {
  let method = 'getDictArrList';
  debug(method, '[Enter]');

  const path = '/api/v1.0/credit/dict/getDictList';
  const params = dictList;
  try {
    debug(method, '[Middle](param): ', params);

    const protocol = `${env == 'local-dev' ? 'http' : 'https'}`;
    const host = `${protocol}://${config.get('rongxin_api_usercredit.host')}:${config.get('rongxin_api_usercredit.port')}`
    const url = `${host}${path}`;
    const result = await agent.post(url).send({dictList});
    debug(method, '[Middle](result): ', result && result.body || {});

    const body = result && result.body || {};
    debug(method, '[Exit](success)');
    return body;
  } catch (error) {
    debug.error(method, '[Exit](failed)', error);
    formatError(error);
    throw error;
  }
}

/**
 * @description 
 * @param {String} areacode [required] - 当前角色所拥有的区域权限(areacode=22,2203,2201)
 */
async function getPendingNum(query) {
  let method = 'getPendingNum';
  debug(method, '[Enter]');

  const path = '/api/v1.0/credit/land/review/getPendingNum';
  const params = query;
  try {
    debug(method, '[Middle](param): ', params);

    const protocol = `${env == 'local-dev' ? 'http' : 'https'}`;
    const host = `${protocol}://${config.get('rongxin_api_usercredit.host')}:${config.get('rongxin_api_usercredit.port')}`
    const url = `${host}${path}`;
    const result = await agent.get(url).query(query);
    debug(method, '[Middle](result): ', result && result.body || {});

    const body = result && result.body || {};
    debug(method, '[Exit](success)');
    return body;
  } catch (error) {
    debug.error(method, '[Exit](failed)', error);
    formatError(error);
    throw error;
  }
}

function formatError(error) {
  if(error && error.response && error.response.body && error.response.body.reason) {
    throw {
      errorCode: error.response.body.code,
      httpCode: 406,
      reason: error.response.body.reason
    }
  }
}

module.exports = {
  getLandCheckList,
  getLandAndFamilyDetail,
  reviewLandInfo,
  getDictList,
  getDictArrList,
  getPendingNum,
}
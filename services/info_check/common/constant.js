// "no_check","checking","finished", "failed"

const CheckStatusConst = {
  NO_CHECK: 'no_check',
  CHECKING: 'checking',
  FINISHED: 'finished',
  FAILED: 'failed'
};

const CheckStatusEnum = new Map([
  [CheckStatusConst.NO_CHECK, '未检测'],
  [CheckStatusConst.CHECKING, '检测中'],
  [CheckStatusConst.FINISHED, '已完成'],
  [CheckStatusConst.FAILED, '不符合贷款资质']
])

const LandCheckStatusConst = {
  NO_CHECK: 10,
  WAIT_HANDLE: 20,
  RETURN_ADD: 21,
  FINISHED: 30
};

const LandCheckStatusEnum = new Map([
  [LandCheckStatusConst.NO_CHECK, '未检测'],
  [LandCheckStatusConst.WAIT_HANDLE, '待处理'],
  [LandCheckStatusConst.RETURN_ADD, '退回再补充'],
  [LandCheckStatusConst.FINISHED, '已完成']
])

const LandCheckMsgStatusConst = {
  NO_READ: 0,
  ALREADY_READ: 1,
}

module.exports = {
  Constant: {
    CheckStatusConst,
    LandCheckStatusConst,
    LandCheckMsgStatusConst
  },
  Enum: {
    CheckStatusEnum,
    LandCheckStatusEnum
  }
}
'use strict';

const HANDLER_NAME = 'getPendingNumHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app:api:services:info_check:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { getPendingNum } = require('./common/request');
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {tId, uId, roleId} = self.context.input;

      const employeeGroup = await employeeGroupsData.getOneByCondition({ tId, employee: uId, group: roleId, archived: false });
      const areaList = employeeGroup && employeeGroup.areaList || [];

      const areacode = areaList && areaList.length != 0 && areaList.join(',') || '';

      const detail = await getPendingNum({areacode: areacode});
      if(!detail || detail.code != 0) {
        throw {
          errorCode: 'E_Info_Check_129',
          httpCode: 406,
          reason: 'get pending num failed'
        }
      }

      self.context.result = { data: detail.data };
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}


module.exports = Handler;
'use strict';

const HANDLER_NAME = 'getDictListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app:api:services:info_check:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { getDictList } = require('./common/request');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {dictId} = self.context.input;

      const detail = await getDictList(dictId);
      if(!detail || detail.code != 0) {
        throw {
          errorCode: 'E_Info_Check_109',
          httpCode: 406,
          reason: 'get dict list failed'
        }
      }

      self.context.result = detail.data;
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}


module.exports = Handler;
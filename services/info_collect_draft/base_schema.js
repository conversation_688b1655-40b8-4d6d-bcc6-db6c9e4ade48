const Joi = require('joi');

let base_schema = {
  name_schema: Joi.string().required().description("姓名"),
  age_schema: Joi.number().integer().required().description("姓名"),
  idCard_schema: Joi.string().required().description("身份证"),
  gender_schema: Joi.number().integer().min(1).max(2).required().description(' 性别 1 代表男 2 代表女'),
  marital_schema: Joi.number().integer().min(1).max(4).required().description('婚姻状况 1 未婚、2 已婚、3 丧偶、4 离异'),
  education_schema: Joi.number().integer().min(1).max(8).description('文化程度 1 研究生以上、2 本科、3 大专、4 中专, 高中、 5 初中、6 小学, 7 文盲或半文盲、8 不了解'),
  mobile_schema: Joi.string().pattern(new RegExp('^1\\d{10}$')).required().description('联系电话'),
  plantingAreas_schema: Joi.array().items(Joi.object({
    areaCode: Joi.string().required().description('地址code'),
    address: Joi.string().required().description('地址描述')
  })).required().description('种植区域,支持多个区域选择，精确到区县 {areaCode:地址code address:地址描述}'),
  address_schema: Joi.object({
    areaCode: Joi.string().description('地址code'),
    cityAddress: Joi.string().description('城市地址描述'),
    villageAddress: Joi.string().description('镇地址描述')
  }),
  workingLife_schema: Joi.number().integer().min(1).max(5).required().description(" 工作年限 1: 1-3年、2: 3-5年、3: 5-10年、4: 10-15年、5: 15年以上"),

  companyName_schema: Joi.string().required().description('企业名称'),
  companyID_schema: Joi.string().required().description("统一社会信用代码"),
  companyCode_schema: Joi.string().required().description("注册号"),
  registeredDate_schema: Joi.string().required().description("注册时间"),
  registeredAddress_schema: Joi.string().required().description("注册地址"),
  registeredCapital: Joi.number().required().description("注册资本（万元）"),

  //上一年度土地种植状况
  previousPlantingSituation_schema: Joi.object({
    plantingSituations: Joi.array().items(Joi.object({
      plantingTime: Joi.string().required().description("土地种植年度"),
      plantingtype: Joi.number().integer().min(1).max(4).required().description("土地种植类型  1 玉米、2 土豆、3 水稻、4 其它"),
      plantingAreas: Joi.number().required().description("种植面积（亩）"),
      plantingOutput: Joi.number().required().description("种植平均单产（斤"),
    })),
    totalPlantingArea: Joi.number().required().description("种植面积合计（亩）"),
  }),
  //*本年度土地种植情况预计
  plantingSituation_schema: Joi.object({
    plantingSituations: Joi.array().items(Joi.object({
      exchangeLandTime: Joi.string().required().description("流转土地年度"),
      plantingType: Joi.number().integer().min(1).max(4).required().description("流转土地种植类型  1 玉米、2 土豆、3 水稻、4 其它"),
      exchangeLandAreas: Joi.number().required().description("流转土地面积合计（亩）"),
      exchangeLandPrice: Joi.number().required().description("流转土地价格（元/亩）"),
    })),
    totalExchangeLandArea: Joi.number().required().description("流转土地面积合计（亩）"),
  }),

  //*农机耕作能力
  machineCapacity_schema: Joi.object({
    machineNum: Joi.number().integer().required().description("农机数量（台/套）"),
    farmingAreas: Joi.number().required().description("满负荷耕作面积（亩）"),
    machinePrice: Joi.number().required().description("农机价值（万元）"),
    machineOperator: Joi.number().integer().required().description("农机手（人）"),
  }),

  //资产清单
  asset_schema: Joi.array().items(Joi.object({
    assetType: Joi.number().integer().min(1).max(11).required().description("资产类型: 1住宅、2办公场所、3厂房、4厂区、5非粮食库房、6粮食储存库房、7烘干塔、8车辆、9设备、10农机、11其它"),
    areas: Joi.number().description("面积（平方米）"),
    warehouseStorage: Joi.number().description("库房存储(万吨) "),
    handlingCapacity: Joi.number().description("日处理量（ 吨）"),
    marketValuation: Joi.number().description("市场估值（万元）"),
    assetPhotos: Joi.array().items(Joi.object({
      image: Joi.object({ url: Joi.string().description("资产相片") }),
      thumbnail: Joi.object({ url: Joi.string().description("资产相片缩略图"), height: Joi.number(), width: Joi.number() }),
    }))
  })),

  //其它经营项目
  project_schema: Joi.array().items(Joi.object({
    projectType: Joi.string().required().description("项目类型"),
    projectIncome: Joi.string().required().description("项目年收入（万元）"),
    projectProfit: Joi.string().required().description("项目年利润（万元）"),
  })),

  //农业补贴
  subsidy_schema: Joi.array().items(Joi.object({
    subsidyType: Joi.number().integer().min(1).max(3).required().description("补贴类型 1地补、2粮补、3其它补贴"),
    subsidyAmount: Joi.number().required().description("补贴金额（元/亩）"),
  })),

  //债务情况
  debt_schema: Joi.array().items(Joi.object({
    debtType: Joi.number().integer().min(1).max(6).required().description("债务类型 1银行贷款、2民间借贷、3网络借贷、4小额贷款公司、5担保、6其它"),
    debtAmount: Joi.number().required().description("债务金额（万元）"),
    repaymentAmount: Joi.number().required().description("每月还款额（万元）"),
    repaymentTime: Joi.string().required().description("债务到期时间"),
  })),
  //*贷款需求及偿还情况
  loanDemand_schema: Joi.object({
    landTransferFee: Joi.number().required().description("土地流转费用（万元）"),
    plantingExpenses: Joi.number().required().description("预计种植费用（万元）"),
    totalPlantingExpenses: Joi.number().required().description("种植总费用（万元）"),
    ownFunds: Joi.number().required().description("自有资金（万元）"),
    valueOfLoan: Joi.number().required().description("贷款金额（万元）"),
  }),


}

base_schema = Object.assign(base_schema, {
  //经营者基本情况
  family_farm_basic_schema: Joi.object({
    name: base_schema.name_schema,
    idCard: base_schema.idCard_schema,
    gender: base_schema.gender_schema,
    marital: base_schema.marital_schema,
    education: base_schema.education_schema,
    mobile: base_schema.mobile_schema,
    residence: base_schema.address_schema,
    detailedAddress: Joi.string().description('家庭住址（详细地址）'),
    plantingAreas: base_schema.plantingAreas_schema,
    emergencyContactPerson: Joi.string().required().description('紧急联系人'),
    emergencyContactNumber: base_schema.mobile_schema.description('紧急联系电话')
  }),
})

module.exports = base_schema;
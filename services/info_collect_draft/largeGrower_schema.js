const Joi = require('joi');
const base_schema = require('./base_schema');
const schema = Jo<PERSON>.object({
  //农户基本情况
  basic: Joi.object({
    name: base_schema.name_schema,
    idCard: base_schema.idCard_schema,
    gender: base_schema.gender_schema,
    marital: base_schema.marital_schema,
    education: base_schema.education_schema,
    mobile: base_schema.mobile_schema,
    residence: base_schema.address_schema.required(),
    detailedAddress: Joi.string().description('家庭住址（详细地址）'),
    plantingAreas: base_schema.plantingAreas_schema,
    emergencyContactPerson: Joi.string().required().description('紧急联系人'),
    emergencyContactNumber: base_schema.mobile_schema.description('紧急联系电话')
  }),
  //社会评价
  evaluation: Joi.object({
    plantingLife: Joi.number().integer().min(1).max(4).required().description("是否种植土地多年  1 种植1年、2 种植2-3年、3 种植3-5年、4 种植5年以上"),
    illegality: Joi.number().integer().min(1).max(3).required().description("有无违法违规  1 无违法违规、2 有过一次、3 有过多次"),
    whoredom: Joi.number().integer().min(1).max(2).required().description("有无嫖娼记录 1 无嫖娼记录、2 有嫖娼记录"),
    excessiveDrinking: Joi.number().integer().min(1).max(2).required().description("有无酗酒史 1 无酗酒嗜好、2 长期酗酒"),
    drugs: Joi.number().integer().min(1).max(2).required().description("有无吸毒史  1 无吸毒史、2 有吸毒史"),
    gambling: Joi.number().integer().min(1).max(2).required().description("有无赌博史  1 无赌博嗜好、2 有赌博嗜好"),
  }),
  //借款人家庭成员情况（数组）
  familyMembers: Joi.array().items(Joi.object({
    name: base_schema.name_schema,
    relationship: Joi.number().integer().min(1).max(6).required().description("与借款人关系 1 父亲、2 母亲、3 配偶、4 儿子、5 女儿、6 其他"),
    idCard: base_schema.idCard_schema,
    mobile: base_schema.mobile_schema,
  }).description('借款人家庭成员情况（数组）')),
  //"*主要种植技术负责人情况（数组）
  plantingTechnologyLeaders: Joi.array().items(Joi.object({
    name: base_schema.name_schema,
    gender: base_schema.gender_schema,
    age: base_schema.age_schema,
    idCard: base_schema.idCard_schema,
    education: base_schema.education_schema,
    professionalTitle: Joi.string().description('专业职称'),
    job: Joi.string().description('职务'),
    workingLife: base_schema.workingLife_schema,
    mobile: base_schema.mobile_schema
  })),
  //上一年度土地种植状况
  previousPlantingSituation: base_schema.previousPlantingSituation_schema,
  //*本年度土地种植情况预计
  plantingSituation: base_schema.plantingSituation_schema,
  //*农机耕作能力
  machineCapacity: base_schema.machineCapacity_schema,
  //资产清单
  asset: base_schema.asset_schema,
  //其它经营项目
  project: base_schema.project_schema,
  //农业补贴
  subsidy: base_schema.subsidy_schema,
  //债务情况
  debt: base_schema.debt_schema,
  //*贷款需求及偿还情况
  loanDemand: base_schema.loanDemand_schema,
});
// const item_schema = schema.or('basic', 'evaluation', 'familyMembers', 'plantingTechnologyLeaders', 'previousPlantingSituation', 'plantingSituation', 'machineCapacity', 'loanDemand');
// const all_schema = schema.with('basic');
module.exports = {
  schema: schema,
  // item_schema: item_schema,
  // all_schema: all_schema
}
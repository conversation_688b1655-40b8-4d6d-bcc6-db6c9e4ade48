'use strict';

const HANDLER_NAME = 'saveHandler';
const Joi = require('joi');
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:info_collect_draft:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  infoCollectDraft: infoCollectDraftData,
  loanApplication: loanApplicationData,
} = require('../dataSvc/dataUtil');

const largeGrower_schema = require('./largeGrower_schema');
const familyFarm_schema = require('./familyFarm_schema');
const plantingCooperative_schema = require('./plantingCooperative_schema');
const machineryCooperative_schema = require('./machineryCooperative_schema');
const economicCooperative_schema = require('./economicCooperative_schema');
const stockCompany_schema = require('./stockCompany_schema');
const soleProprietorship_schema = require('./soleProprietorship_schema');
const limitedCompany_schema = require('./limitedCompany_schema');
const zh_personal_schema = require('./zh_personal_schema');
const zh_company_schema = require('./zh_company_schema');

const authValidateConfigDic = require('./authValidateConfig');
const assert = (success, errorCode, reason, httpCode = 406) => { if (!success) throw { errorCode, reason, httpCode } };
const aliyunAuthSvc = require('../aliyunAuth');


// '采集类型：2 个人 largeGrower ，3、家庭农场  familyFarm 4、种植合作社 plantingCooperative 5、农机合作社 machineryCooperative 6、集体经济股份合作社 economicCooperative 7、有限责任公司和股份公司 stockCompany 8、个人（自然人）独资企业 soleProprietorship  9、一人有限公司 ;limitedCompany'
const content_schema_enums = {
  "01": largeGrower_schema,
  "03": familyFarm_schema,
  "04": plantingCooperative_schema,
  "05": machineryCooperative_schema,
  "06": economicCooperative_schema,
  "07": stockCompany_schema,
  "08": soleProprietorship_schema,
  "09": soleProprietorship_schema,
  101: zh_personal_schema,
  102: zh_company_schema,
}
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]', JSON.stringify(self.context.input));
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let content = input.content;
      let schema = content_schema_enums[input.type];
      if (!schema) {
        throw {
          errorCode: 'E_VALID_ERROR',
          httpCode: 401,
          reason: 'collect type 错误'
        }
      }
      let draftId = input.id;
      const { error, value } = schema.schema.validate(content);
      if (error) {
        debug(method, '[valid error]', error.toString());
        throw {
          errorCode: 'E_VALID_ERROR',
          httpCode: 401,
          reason: error.toString()
        }
      }

      let draft = await infoCollectDraftData.getOneByCondition({ _id: draftId });
      if (!draft) {
        throw {
          errorCode: 'E_VALID_ERROR',
          httpCode: 401,
          reason: 'draft not exists'
        }
      }
      draft.content = Object.assign(draft.content, content);
      if (input.type != '101' && input.type != '102') {
        const app = await loanApplicationData.getById(draft.aId);
        debug('validateAuth0',draft._id,draft.aId,app);
        assert(app,'E_VALID_ERROR_102','该草稿对应的订单不存在')
        await this.validateAuth(app, input.type, content,opts.userid);
      }
      await infoCollectDraftData.putById(draftId, draft);

      self.context.result = draft;
      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async validateAuth(app, type, content, employId) {
    const authRecordHistory = app.verifyInfo && app.verifyInfo.authRecordHistory || [];
    const authValidateConfig = authValidateConfigDic[type];
    if (!authValidateConfig) return; //此类型无需验证
    debug(HANDLER_NAME, 'validateAuth1', app._id,content,app);
    const persons = authValidateConfig(content).filter(v => v)
      .filter(([username, idCard]) => !authRecordHistory.some(v => username === v.username && v.idCard === idCard)); //过滤掉已经验证成功的
    assert(persons.every(([username, idCard]) => username && idCard), 'E_VALID_ERROR_100', '姓名或身份证不能为空');
    debug(HANDLER_NAME, 'validateAuth2', persons.length, persons);
    if (persons.length === 0) return;
    assert(await aliyunAuthSvc.limitEmployee({ id: employId, domain: '*' }), //domain:'cxwq
      'E_VALID_ERROR_99', '今日调用二要素验证已经超过100次');
    debug(HANDLER_NAME,'authIdentityByIDcard1',persons.map(([username, idCard]) =>({username, idCard})));
    const result = await Promise.all(persons.map(async ([username, idCard]) =>
      ({ paras: { username, idCard }, authRes: await aliyunAuthSvc.authIdentityByIDcard(username, idCard) })));
    debug(HANDLER_NAME,'authIdentityByIDcard2',result);
    const successResult = result.filter(({ authRes }) =>
      authRes && authRes.body && authRes.body.code === '0' && authRes.body.result.res === '1');
    authRecordHistory.push(...successResult);
    assert(result.length === successResult.length,
      'E_VALID_ERROR_101', '相关人员二要素验证失败');
    await loanApplicationData.putById(app._id, { 'app.verifyInfo.authRecordHistory': authRecordHistory });
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler;
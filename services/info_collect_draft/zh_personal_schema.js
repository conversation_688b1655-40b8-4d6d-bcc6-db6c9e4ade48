const Joi = require('joi');
const { join } = require('q');
const base_schema = require('./base_schema');
const schema = Joi.object({
  //中行个人基本情况
  basicInfo: Joi.object({
    userType: Joi.string().required(),
    userSubType: Joi.string().required(),
    name: Joi.string().required(),
    idType: Joi.string().required(),
    idNumber: base_schema.idCard_schema,
    contactNumber: Joi.string().required(),
    gender: Joi.string().required(),
    marriage: Joi.string().required(),
    education: Joi.string().required(),
    povertyElimDate: Joi.string(),
    address: Joi.string().required(),
    areaCode: Joi.string().required(),
    insurances: Joi.array().items(Joi.string()).required(),
    familyMemberNumbers: Joi.number().required(),
    urgentContactName: Joi.string().required(),
    urgentContactPhoneNumber: base_schema.mobile_schema,
  }),
  familyInfo: Joi.array().items(Joi.object({
    memberName: Joi.string().required(),
    memberRelationship: Joi.string().required(),
    memberIdCard: base_schema.idCard_schema,
    memberContactNumber: Joi.string().pattern(new RegExp('^1\\d{10}$')).description('联系电话')
  })),
  incomeInfo: Joi.array().items(Joi.object({
    incomeYear: Joi.string().required(),
    plantingIncome: Joi.number(),
    breedingIncome: Joi.number(),
    servicesIncome: Joi.number(),
    landIncome: Joi.number(),
    machineryIncome: Joi.number(),
    subsidyIncome: Joi.number(),
    dividendIncome: Joi.number(),
    otherIncome: Joi.number(),
    totalIncome: Joi.number()
  })),
  plantingInfo: Joi.array().items(Joi.object({
    plantingYear: Joi.string().required(),
    plantingPredicted: Joi.bool().required(),
    plantingType: Joi.string().required(),
    plantingArea: Joi.number().required(),
    plantingUnitOutput: Joi.number().required(),
  })),
  plantingTotalOutput: Joi.number(),
  breedinginfo: Joi.array().items(Joi.object({
    breedingYear: Joi.string().required(),
    breedingPredicted: Joi.bool().required(),
    breedingType: Joi.string().required(),
    breedingNumber: Joi.number().required(),
    breedingPrice: Joi.number().required(),
  })),
  loanDemand: {
    landTransferFee: Joi.number().required(),
    plantingExpenses: Joi.number().required(),
    totalPlantingExpenses: Joi.number().required(),
    ownFunds: Joi.number().required(),
    loanAmount: Joi.number().required(),
  },
  subsidyInfo: Joi.array().items(Joi.object({
    subsidyYear: Joi.string().required(),
    subsidyType: Joi.string().required(),
    subsidyAmount: Joi.number().required(),
  })),
  insuranceInfo: Joi.array().items(Joi.object({
    insuranceType: Joi.string().required(),
    insuranceTime: Joi.string().required(),
    insuranceObject: Joi.string().required(),
    insuranceAmount: Joi.number().required(),
  })),
  assetInfo: Joi.array().items(Joi.object({
    assetType: Joi.string().required(),
    assetNumber: Joi.string(),
    assetAmount: Joi.number(),
    assetValue: Joi.number().required(),
  })),
  totalAssetValue: Joi.number(),
  debtInfo: Joi.array().items(Joi.object({
    debtType: Joi.string().required(),
    debtAmount: Joi.number().required(),
    debtMaturityTime: Joi.string(),
    totalDebt: Joi.number(),
  })),
  totalDebt: Joi.number(),
  customerScreeningInfo: {
    ratingRatingCard: Joi.string(),
    antiFraudResult: Joi.bool().required(),
    antiFraudHitList: Joi.string(),
  }
});

const schema_enumes = {
  "basicInfo": {
    "userType": {
      "1": "农户",
      "2": "企业"
    },
    "userSubType": {
      "01": "普通农户",
      "02": "大户",
      "03": "兵团职工",
      "04": "兵团非职工",
      "05": "连队职工",
      "06": "连队两委",
      "07": "脱贫户",
      "99": "其他",
    },
    "idType": {
      "01": "身份证",
      "02": "军人证",
      "03": "护照",
      "04": "户口本",
    },
    "gender": {
      "1": "男",
      "2": "女",
      "8": "未知的性别",
      "9": "未说明的性别",
    },
    "marriage": {
      "0": "未婚",
      "1": "已婚",
      "5": "丧偶",
      "6": "离婚",
      "7": "其它",
    },
    "education": {
      "1": "研究生教育",
      "2": "大学本科教育",
      "3": "大学专科教育",
      "4": "中等专科和职业高中教育",
      "5": "技工学校教育",
      "6": "普通高级中学教育",
      "7": "初级中学教育",
      "8": "小学教育",
      "9": "其他",
    },
    "insurances": {
      "01": "养老",
      "02": "医疗",
      "03": "新农合",
      "04": "意外",
      "05": "重疾险",
    }
  },
  "familyInfo": {
    "memberRelationship": {
      "1": "父亲",
      "2": "母亲",
      "3": "配偶",
      "4": "儿子",
      "5": "女儿",
      "6": "其他",
    }
  },
  "plantingInfo": {
    "plantingType": {
      "01": "玉米",
      "02": "大豆",
      "03": "水稻",
      "04": "花生",
      "05": "小麦",
      "06": "薯类",
      "07": "棉花",
      "08": "葵花籽",
      "09": "高粱",
      "10": "西瓜",
      "11": "葵花",
      "12": "土豆",
      "13": "水果",
      "14": "中草药",
      "99": "其它",
    }
  },
  "breedinginfo": {
    breedingType: {
      "01": "牛",
      "02": "羊",
      "03": "猪",
      "04": "禽",
      "05": "马",
      "06": "驴、骡",
      "07": "鹿",
      "08": "犬",
      "09": "兔",
      "99": "其他",
    }
  },
  "subsidies": {
    "subsidyType": {
      "01": "耕地地力保护补贴",
      "02": "农民专业合作社补贴",
      "03": "新型职业农民培育补贴",
      "04": "适度规模经营补贴",
      "05": "农机购置补贴",
      "06": "农业保险保费补贴",
      "07": "玉米、大豆和稻谷生产者补贴",
      "99": "其他补贴",
    }
  },
  "insuranceInfo": {
    "insuranceType": {
      "01": "种植补贴保险",
      "02": "养殖补贴保险",
      "03": "种植商业险",
      "04": "养殖商业险",
      "99": "其他",
    },
    "insuranceObject": {
      "0101": "玉米",
      "0102": "大豆",
      "0103": "水稻",
      "0104": "花生",
      "0105": "小麦",
      "0106": "薯类",
      "0107": "棉花",
      "0108": "葵花籽",
      "0109": "高粱",
      "0110": "西瓜",
      "0111": "葵花",
      "0112": "土豆",
      "0113": "水果",
      "0114": "中草药",
      "0199": "其它",
      "0201": "牛",
      "0202": "羊",
      "0203": "猪",
      "0204": "禽",
      "0205": "马",
      "0206": "驴、骡",
      "0207": "鹿",
      "0208": "犬",
      "0209": "兔",
      "0299": "其他",
    }
  },
  "assetInfo": {
    "assetType": [{
        key: "1",
        title: "实物类",
        childs: [{
            key: "11010100",
            title: "耕地",
            childs: [{
                key: "11010101",
                title: "水田",
                unit: "亩",
              },
              {
                key: "11010102",
                title: "水浇地",
                unit: "亩",
              },
              {
                key: "11010103",
                title: "旱地",
                unit: "亩",
              },
              {
                key: "11010199",
                title: "其他耕地",
                unit: "亩",
              },
            ]
          },
          {
            key: "11010200",
            title: "园地",
            childs: [{
                key: "11010201",
                title: "果园",
                unit: "亩",
              },
              {
                key: "11010202",
                title: "茶园",
                unit: "亩",
              },
              {
                key: "11010299",
                title: "其他园地",
                unit: "亩",
              }
            ]
          },
          {
            key: "11010300",
            title: "林地",
            unit: "亩",
            childs: [{
                key: "11010301",
                title: "有林地",
                unit: "亩",
              },
              {
                key: "11010302",
                title: "灌木林地",
                unit: "亩",
              },
              {
                key: "11010399",
                title: "其他林地",
                unit: "亩",
              }
            ]
          },
          {
            key: "11010400",
            title: "草地",
            unit: "亩",
            childs: [{
                key: "11010401",
                title: "天然牧草地",
                unit: "亩",
              },
              {
                key: "11010402",
                title: "人工牧草地",
                unit: "亩",
              },
              {
                key: "11010499",
                title: "其他草地",
                unit: "亩",
              },
            ]
          },
          {
            key: "11010500",
            title: "商业服务业用地",
            unit: "平方米",
            childs: [{
                key: "11010501",
                title: "批发零售用地",
                unit: "平方米",
              },
              {
                key: "11010502",
                title: "住宿餐饮用地",
                unit: "平方米",
              },
              {
                key: "11010503",
                title: "商务金融用地",
                unit: "平方米",
              },
              {
                key: "11010599",
                title: "其他商业服务业用地",
                unit: "平方米",
              }
            ]
          },
          {
            key: "11010600",
            title: "工矿仓储用地",
            unit: "平方米",
            childs: [{
                key: "11010601",
                title: "工业用地",
                unit: "平方米",
              },
              {
                key: "11010602",
                title: "采矿用地",
                unit: "平方米",
              },
              {
                key: "11010603",
                title: "仓储用地",
                unit: "平方米",
              },
            ]
          },
          {
            key: "11010700",
            title: "住宅用地",
            unit: "平方米",
            childs: [{
                key: "11010701",
                title: "城镇住宅用地",
                unit: "平方米",
              },
              {
                key: "11010702",
                title: "农村宅基地",
                unit: "平方米",
              },
              {
                key: "11010799",
                title: "其他宅基地",
                unit: "平方米",
              },
            ]
          },
          {
            key: "11011500",
            title: "特殊用地",
            unit: "亩",
            childs: [{
                key: "11011501",
                title: "军事设施用地",
                unit: "亩",
              },
              {
                key: "11011502",
                title: "外事用地",
                unit: "亩",
              },
              {
                key: "11011503",
                title: "监教场所用地",
                unit: "亩",
              },
              {
                key: "11011504",
                title: "宗教用地",
                unit: "亩",
              },
              {
                key: "11011505",
                title: "殡葬用地",
                unit: "亩",
              },
              {
                key: "11011599",
                title: "其他特殊用地",
                unit: "亩",
              },
            ]
          },
          {
            key: "11011600",
            title: "交通运输用地",
            unit: "亩",
            childs: [{
                key: "11011601",
                title: "铁路用地",
                unit: "亩",
              },
              {
                key: "11011602",
                title: "公路用地",
                unit: "亩",
              },
              {
                key: "11011603",
                title: "街巷用地",
                unit: "亩",
              },
              {
                key: "11011604",
                title: "农村道路用地",
                unit: "亩",
              },
              {
                key: "11011605",
                title: "机场用地",
                unit: "亩",
              },
              {
                key: "11011606",
                title: "港口码头用地",
                unit: "亩",
              },
              {
                key: "11011607",
                title: "管道运输用地",
                unit: "亩",
              },
              {
                key: "11011699",
                title: "其他交通运输用地",
                unit: "亩",
              },
            ]
          },
          {
            key: "11011700",
            title: "水域及水利设施用地",
            unit: "亩",
            childs: [{
                key: "11011701",
                title: "水面用地",
                unit: "亩",
              },
              {
                key: "11011702",
                title: "滩涂用地",
                unit: "亩",
              },
              {
                key: "11011703",
                title: "沟渠用地",
                unit: "亩",
              },
              {
                key: "11011704",
                title: "水利设施用地",
                unit: "亩",
              },
              {
                key: "11011799",
                title: "其他水域及水利设施用地",
                unit: "亩",
              },
            ]

          }
        ]
      },
      {
        key: "2",
        title: "虚拟类",
        childs: [{
            key: "21010100",
            title: "涉农股权",
            unit: "股",
            childs: [{
                key: "21010101",
                title: "企业股权",
                unit: "股",
              },
              {
                key: "21010102",
                title: "村集体股权",
                unit: "股",
              },
            ]
          },
          {
            key: "21020100",
            title: "知识产权",
            unit: "件",
            childs: [{
                key: "21020101",
                title: "发明专利",
                unit: "件",
              },
              {
                key: "21020102",
                title: "实用新型专利",
                unit: "件",
              },
              {
                key: "21020103",
                title: "外观设计专利",
                unit: "件",
              },
              {
                key: "21020104",
                title: "商标权",
                unit: "件",
              },
            ]
          },
        ]
      }
    ]
  },
  "debtInfo": {
    "debtType": {
      "01": "银行贷款",
      "02": "民间借贷",
      "03": "网络借贷",
      "04": "小额贷款公司",
      "05": "担保",
      "06": "其它",
    }
  },
  "customerScreeningInfo": {
    "ratingRatingCard": ["AAA",
      "AA+",
      "AA",
      "AA-",
      "A+",
      "A",
      "A-",
      "BBB",
      "BB",
      "B",
    ]
  },
  "other": {
    "truefalse": {
      "0": "否",
      "1": "是",
    }
  },
};

module.exports = {
  schema: schema,
  schema_enumes: schema_enumes,
}
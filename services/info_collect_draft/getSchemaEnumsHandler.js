/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'getSchemaEnums';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');

const moment = require('moment');
const largeGrower_schema = require('./largeGrower_schema');
const familyFarm_schema = require('./familyFarm_schema');
const plantingCooperative_schema = require('./plantingCooperative_schema');
const machineryCooperative_schema = require('./machineryCooperative_schema');
const economicCooperative_schema = require('./economicCooperative_schema');
const stockCompany_schema = require('./stockCompany_schema');
const soleProprietorship_schema = require('./soleProprietorship_schema');
const limitedCompany_schema = require('./limitedCompany_schema');
const zh_personal_schema = require('./zh_personal_schema');
const zh_company_schema = require('./zh_company_schema');



// '采集类型：1 个人 largeGrower ，2、家庭农场  familyFarm 3、种植合作社 plantingCooperative 4、农机合作社 machineryCooperative 5、集体经济股份合作社 economicCooperative 6、有限责任公司和股份公司 stockCompany 7、个人（自然人）独资企业 soleProprietorship  8、一人有限公司 ;limitedCompany'
const content_schema_enums = {
  "01": largeGrower_schema,
  "03": familyFarm_schema,
  "04": plantingCooperative_schema,
  "05": machineryCooperative_schema,
  "06": economicCooperative_schema,
  "07": stockCompany_schema,
  "08": soleProprietorship_schema,
  "09": soleProprietorship_schema,
  101: zh_personal_schema,
  102: zh_company_schema,
}

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input } = this.context;
      let type = input.type;
      let path = input.path;

      let result;
      if (path) {

        result = getObjectByPath(content_schema_enums[type].schema_enumes, path);
      } else {
        let obj = {};
        Object.keys(content_schema_enums[type].schema_enumes).map(deep1Key => {
          let deep1Obj = content_schema_enums[type].schema_enumes[deep1Key];
          Object.keys(deep1Obj).map(key => {
            obj[key] = deep1Obj[key]
          })
        });
        result = obj;
      }

      debug(method, '[Exit](success)');
      this.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

function getObjectByPath(obj, path) {
  let keys = path.split('.');

  return keys.reduce((pre, cur) => {
    return pre && pre[cur];
  }, obj);
}

module.exports = Handler;
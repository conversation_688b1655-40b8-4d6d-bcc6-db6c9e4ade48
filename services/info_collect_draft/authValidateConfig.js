const generalBasicPersonConfig = data=>data && [data.name,data.idCard] || null;
const generalBasicConfig = data=>[generalBasicPersonConfig(data.basic)];
const generalFamilyPersonConfig = element=>element && [element.name,element.idCard] || null;
const generalFamilyConfig = data=>( data.familyMembers || [] ).map(generalFamilyPersonConfig);

const config = {
    "01":data=>([...generalBasicConfig(data),...generalFamilyConfig(data)]),//不分类型，所有都验
    "03":data=>([...generalBasicConfig(data),...generalFamilyConfig(data)]),//不分类型，所有都验
    "04":data=>([...generalBasicConfig(data),...generalFamilyConfig(data)]),//filter(it=>[1,2].includes(it.personType))  // 1、财务人员2、种植技术人员
    "05":data=>([...generalBasicConfig(data),...generalFamilyConfig(data)]),//filter(it=>[1,2].includes(it.personType)) //1、财务人员2、种植技术人员
    "06":data=>([...generalBasicConfig(data),...generalFamilyConfig(data)]),//filter(it=>[1,2,3].includes(it.personType)) //1、理事会成员 2、监事 3、财务人员
    "07":data=>([...generalBasicConfig(data),...generalFamilyConfig(data)]),//filter(it=>[1,2,3,4].includes(it.personType)) //1、股东 2、董事 3、经理 4、财务人员
    "08":data=>([...generalBasicConfig(data),...generalFamilyConfig(data)]),//filter(it=>[1,2].includes(it.personType)) //1、管理人员2、财务人员
    "09":data=>([...generalBasicConfig(data),...generalFamilyConfig(data)]),//filter(it=>[1,2].includes(it.personType)) //1、管理人员2、财务人员
}

module.exports = config;
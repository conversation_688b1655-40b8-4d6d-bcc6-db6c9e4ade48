const Joi = require('joi');
const { join } = require('q');
const base_schema = require('./base_schema');
const schema = Joi.object({
  //中行企业基本情况
  basicInfo: Joi.object({
    userType: Joi.string().required(),
    companyType: Joi.string().required(),
    companyLevel: Joi.string(),
    companyName: Joi.string().required(),
    companyId: Joi.string().required(),
    registeredDate: Joi.string().required(),
    registeredAddress: Joi.string().required(),
    registeredFullAddress: Joi.string().description('详细地址'),
    registeredCapital: Joi.number().required(),
    businessStartTerm: Joi.string().required(),
    businessEndTerm: Joi.string().required(),
    postalAddress: Joi.string().required(),
    address: Joi.string().required(),
    fullAddress: Joi.string(),
    areaCode: Joi.string().required(),
    councilNumber: Joi.number().required(),
    memberNumber: Joi.number().required(),
    bizSiteType: Joi.string().required(),
    bizSiteAddress: Joi.string().required(),
    bizSiteFullAddress: Joi.string(),
  }),
  operatorInfo: Joi.object({
    villageCadre: Joi.array().items(Joi.string().required()),
    operatorType: Joi.string().required(),
    operatorName: Joi.string().required(),
    opGender: Joi.string().required(),
    opMarriage: Joi.string().required(),
    opIdType: Joi.string().required(),
    opIdNumber: Joi.string().required(),
    opEducation: Joi.string(),
    opWorkingDuration: Joi.string().required(),
    opContactNumber: Joi.string().required(),
    familyAddress: Joi.string().required(),
    familyFullAddress: Joi.string(),
  }),
  technicalDirectorInfo: Joi.object({
    technicalPersonName: Joi.string().required(),
    tpIdType: Joi.string().required(),
    tpIdNumber: Joi.string().required(),
    tpEducation: Joi.string(),
    professionalTitle: Joi.string(),
    workingDuration: Joi.string().required(),
  }),
  shareholderInfo: Joi.array().items(Joi.object({
    shareholderType: Joi.string().required(),
    shareholderName: Joi.string().required(),
    shareholderIdType: Joi.string().required(),
    shareholderrIdNumber: Joi.string(),
    shareRatio: Joi.number(),
  })),
  applicationInfo: Joi.object({
    orderNumber: Joi.string().required(),
    creditAmount: Joi.number().required(),
    creditApplicationAmount: Joi.number().required(),
    creditPeriod: Joi.number().integer().required(),
    loanPurposes: Joi.string().required(),
    repaymentMethod: Joi.string().required(),
    interestRate: Joi.number().required(),
  }),
  plantingInfo: Joi.array().items(Joi.object({
    plantingYear: Joi.string().required(),
    plantingPredicted: Joi.bool().required(),
    plantingType: Joi.string().required(),
    plantingArea: Joi.number().required(),
    plantingOutput: Joi.number().required(),
    marketOutput: Joi.number().required(),
  })),
  breedingInfo: Joi.array().items(Joi.object({
    breedingYear: Joi.string().required(),
    breedingPredicted: Joi.bool().required(),
    breedingType: Joi.string().required(),
    outQuantity: Joi.number().required(),
    breedingProfits: Joi.number().required(),
  })),
  machineryInfo: Joi.array().items(Joi.object({
    machineryYear: Joi.string().required(),
    machineryPredicted: Joi.bool().required(),
    machineryProfits: Joi.number().required(),
    machineryNum: Joi.number().required(),
    fullloadFarmingArea: Joi.number(),
    machineryValue: Joi.number().required(),
    machineryOperatorNum: Joi.number().required(),
  })),
  subsidyInfo: Joi.array().items(Joi.object({
    subsidyYear: Joi.string().required(),
    subsidyType: Joi.string().required(),
    subsidyAmount: Joi.number().required(),
  })),
  expendInfo: Joi.array().items(Joi.object({
    expendYear: Joi.string().required(),
    expendAmount: Joi.number().required(),
  })),
  incomeInfo: Joi.array().items(Joi.object({
    incomeYear: Joi.string().required(),
    plantingIncome: Joi.number(),
    breedingIncome: Joi.number(),
    otherOperatingIncome: Joi.number(),
    landIncome: Joi.number(),
    machineryIncome: Joi.number(),
    subsidyIncome: Joi.number(),
    dividendIncome: Joi.number(),
    otherIncome: Joi.number(),
    totalIncome: Joi.number()
  })),
  insuranceInfo: Joi.array().items(Joi.object({
    insuranceType: Joi.string().required(),
    insuranceTime: Joi.string().required(),
    insuranceObject: Joi.string().required(),
    insuranceAmount: Joi.number().required(),
  })),
  propertyInfo: Joi.array().items(Joi.object({
    assetType: Joi.string().required(),
    assetNumber: Joi.string(),
    assetAmount: Joi.number().required(),
    assetValue: Joi.number().required(),
  })),
  totalAssetValue: Joi.number(),
  debtInfo: Joi.array().items(Joi.object({
    debtType: Joi.string().required(),
    debtAmount: Joi.number().required(),
    debtMaturityTime: Joi.string().required(),
  })),
  totalDebt: Joi.number(),
  businessScale: Joi.array().items(Joi.object({
    businessYear: Joi.string().required(),
    totalCurrentAssets: Joi.number().required(),
    totalAssets: Joi.number().required(),
    totalCurrentLiabilities: Joi.number().required(),
    totalLiabilities: Joi.number().required(),
    operatingIncome: Joi.number().required(),
    operatingCost: Joi.number(),
    retainedProfits: Joi.number(),
  })),
  upstreamCustomerInfo: Joi.array().items(Joi.object({
    upstreamCustomerYear: Joi.string().required(),
    upstreamCustomerEstimate: Joi.bool().required().description('是否为预计'),
    upstreamCustomerType: Joi.string().required(),
    upstreamCustomerName: Joi.string().required(),
    upstreamContractAmount: Joi.number().required(),
  })),
  downstreamCustomerInfo: Joi.array().items(Joi.object({
    downstreamSalesYear: Joi.string().required(),
    downstreamSalesEstimate: Joi.bool().required().description('是否为预计'),
    downstreamSalesType: Joi.string().required(),
    downstreamCustomerType: Joi.string().required(),
    downstreamContractAmount: Joi.number().required(),
    downstreamCooperationPeriod: Joi.string().required(),
  })),
  customerScreeningInfo: Joi.object({
    ratingRatingCard: Joi.string(),
    antiFraudResult: Joi.bool().required(),
    antiFraudHitList: Joi.string(),
  })
});

const schema_enumes = {
  basicInfo: {
    "userType": {
      "1": "农户",
      "2": "企业"
    },
    "companyType": {
      "01": "专业合作社",
      "0101": "大田种植",
      "0102": "设施农业种植",
      "0103": "农机",
      "0104": "飞防",
      "0105": "养殖",
      "02": "集体股份经济合作社",
      "03": "家庭农场",
      "0301": "大田种植",
      "0302": "设施农业种植",
      "0303": "农机",
      "0304": "飞防",
      "0305": "养殖",
      "0306": "休闲农业",
      "04": "涉农企业",
      "0401": "加工企业",
      "0402": "粮贸公司",
      "0403": "农资经销商",
      "0404": "农机经销商",
      "99": "其他",
    },
    "companyLevel": {
      "1": "国家级",
      "2": "省级",
      "3": "市级",
      "4": "县级",
      "9": "其他适用于合作社、家庭农场示范评定",
    },
    "bizSiteType": {
      "01": "自置有产权证书",
      "02": "自置无产权证书 ",
      "03": " 租用",
      "99": " 其他",
    },


  },
  "operatorInfo": {
    "operatorType": {
      "01": "合作社理事长",
      "02": "家庭农场农场主",
      "03": "企业法定代表人",
      "99": "其他",
    },
    "villageCadre": {
      "01": "党支部书记",
      "02": "村委会主任",
      "03": "治保主任",
      "04": "民兵连长",
      "05": "妇女主任",
      "06": "团支部书记",
      "07": "村会计",
      "99": "其他",
    },
    "opGender": {
      "1": "男",
      "2": "女",
      "8": "未知的性别",
      "9": "未说明的性别",
    },
    "opMarriage": {
      "0": "未婚",
      "1": "已婚",
      "5": "丧偶",
      "6": "离婚",
      "7": "其它",
    },
    "opIdType": {
      "01": "身份证",
      "02": "军人证",
      "03": "护照",
      "04": "户口本",
    },
    "opEducation": {
      "1": "研究生教育",
      "2": "大学本科教育",
      "3": "大学专科教育",
      "4": "中等专科和职业高中教育",
      "5": "技工学校教育",
      "6": "普通高级中学教育",
      "7": "初级中学教育",
      "8": "小学教育",
      "9": "其他",
    },
    "opWorkingDuration": {
      "1": "1-3年",
      "2": "3-5年",
      "3": "5-10年",
      "4": "10-15年",
      "5": "15年以上",
    }
  },
  "technicalDirectorInfo": {
    "tpIdType": {
      "01": "身份证",
      "02": "军人证",
      "03": "护照",
      "04": "户口本",
    },
    "tpEducation": {
      "1": "研究生教育",
      "2": "大学本科教育",
      "3": "大学专科教育",
      "4": "中等专科和职业高中教育",
      "5": "技工学校教育",
      "6": "普通高级中学教育",
      "7": "初级中学教育",
      "8": "小学教育",
      "9": "其他",
    },
    "workingDuration": {
      "1": "1-3年",
      "2": "3-5年",
      "3": "5-10年",
      "4": "10-15年",
      "5": "15年以上",
    },
  },
  "shareholderInfo": {
    "shareholderType": {
      "1": "个人",
      "2": "企业",
    },
    "shareholderIdType": {
      "01": "身份证",
      "02": "军人证",
      "03": "护照",
      "04": "户口本",
      "05": "注册号",
      "06": "统一社会信用代码",
    }
  },
  "plantingInfo": {
    "plantingType": {
      "01": "玉米",
      "02": "大豆",
      "03": "水稻",
      "04": "花生",
      "05": "小麦",
      "06": "薯类",
      "07": "棉花",
      "08": "葵花籽",
      "09": "高粱",
      "10": "西瓜",
      "11": "葵花",
      "12": "土豆",
      "13": "水果",
      "14": "中草药",
      "99": "其它",
    }
  },
  "breedingInfo": {
    "breedingType": {
      "01": "牛",
      "02": "羊",
      "03": "猪",
      "04": "禽",
      "05": "马",
      "06": "驴、骡",
      "07": "鹿",
      "08": "犬",
      "09": "兔",
      "99": "其他",
    }
  },
  "subsidyInfo": {
    "subsidyType": {
      "01": "耕地地力保护补贴",
      "02": "农民专业合作社补贴",
      "03": "新型职业农民培育补贴",
      "04": "适度规模经营补贴",
      "05": "农机购置补贴",
      "06": "农业保险保费补贴",
      "07": "玉米、大豆和稻谷生产者补贴",
      "99": "其他补贴",
    }
  },
  "insuranceInfo": {
    "insuranceType": {
      "01": "种植补贴保险",
      "02": "养殖补贴保险",
      "03": "种植商业险",
      "04": "养殖商业险",
      "99": "其他",
    },
    "insuranceObject": {
      "0101": "玉米",
      "0102": "大豆",
      "0103": "水稻",
      "0104": "花生",
      "0105": "小麦",
      "0106": "薯类",
      "0107": "棉花",
      "0108": "葵花籽",
      "0109": "高粱",
      "0110": "西瓜",
      "0111": "葵花",
      "0112": "土豆",
      "0113": "水果",
      "0114": "中草药",
      "0199": "其它",
      "0201": "牛",
      "0202": "羊",
      "0203": "猪",
      "0204": "禽",
      "0205": "马",
      "0206": "驴、骡",
      "0207": "鹿",
      "0208": "犬",
      "0209": "兔",
      "0299": "其他",
    }
  },
  "debt": {
    "debtType": {
      "01": "银行贷款",
      "02": "民间借贷",
      "03": "网络借贷",
      "04": "小额贷款公司",
      "05": "担保",
      "06": "其它",
    }
  },
  "upstreamCustomerInfo": {
    "upstreamCustomerType": {
      "01": "良种提供商",
      "02": "化肥提供商",
      "03": "农业作业外包服务商",
      "04": "其他",
    }
  },
  "downstreamCustomerInfo": {
    "downstreamSalesType": {
      "01": "田间潮粮销售",
      "02": "交割库烘干待售",
    },
    "downstreamCustomerType": {
      "01": "仓储提供商",
      "02": "粮食销售商",
      "09": "其它"
    }
  },
  "customerScreeningInfo": {
    "ratingRatingCard": ["AAA",
      "AA+",
      "AA",
      "AA-",
      "A+",
      "A",
      "A-",
      "BBB",
      "BB",
      "B",
    ]
  },
  "other": {
    "truefalse": {
      "0": "否",
      "1": "是",
    }
  },
  "propertyInfo": {
    "assetType": [{
        key: "1",
        title: "实物类",
        childs: [{
            key: "11010100",
            title: "耕地",
            childs: [{
                key: "11010101",
                title: "水田",
                unit: "亩",
              },
              {
                key: "11010102",
                title: "水浇地",
                unit: "亩",
              },
              {
                key: "11010103",
                title: "旱地",
                unit: "亩",
              },
              {
                key: "11010199",
                title: "其他耕地",
                unit: "亩",
              },
            ]
          },
          {
            key: "11010200",
            title: "园地",
            childs: [{
                key: "11010201",
                title: "果园",
                unit: "亩",
              },
              {
                key: "11010202",
                title: "茶园",
                unit: "亩",
              },
              {
                key: "11010299",
                title: "其他园地",
                unit: "亩",
              }
            ]
          },
          {
            key: "11010300",
            title: "林地",
            unit: "亩",
            childs: [{
                key: "11010301",
                title: "有林地",
                unit: "亩",
              },
              {
                key: "11010302",
                title: "灌木林地",
                unit: "亩",
              },
              {
                key: "11010399",
                title: "其他林地",
                unit: "亩",
              }
            ]
          },
          {
            key: "11010400",
            title: "草地",
            unit: "亩",
            childs: [{
                key: "11010401",
                title: "天然牧草地",
                unit: "亩",
              },
              {
                key: "11010402",
                title: "人工牧草地",
                unit: "亩",
              },
              {
                key: "11010499",
                title: "其他草地",
                unit: "亩",
              },
            ]
          },
          {
            key: "11010500",
            title: "商业服务业用地",
            unit: "平方米",
            childs: [{
                key: "11010501",
                title: "批发零售用地",
                unit: "平方米",
              },
              {
                key: "11010502",
                title: "住宿餐饮用地",
                unit: "平方米",
              },
              {
                key: "11010503",
                title: "商务金融用地",
                unit: "平方米",
              },
              {
                key: "11010599",
                title: "其他商业服务业用地",
                unit: "平方米",
              }
            ]
          },
          {
            key: "11010600",
            title: "工矿仓储用地",
            unit: "平方米",
            childs: [{
                key: "11010601",
                title: "工业用地",
                unit: "平方米",
              },
              {
                key: "11010602",
                title: "采矿用地",
                unit: "平方米",
              },
              {
                key: "11010603",
                title: "仓储用地",
                unit: "平方米",
              },
            ]
          },
          {
            key: "11010700",
            title: "住宅用地",
            unit: "平方米",
            childs: [{
                key: "11010701",
                title: "城镇住宅用地",
                unit: "平方米",
              },
              {
                key: "11010702",
                title: "农村宅基地",
                unit: "平方米",
              },
              {
                key: "11010799",
                title: "其他宅基地",
                unit: "平方米",
              },
            ]
          },
          {
            key: "11011500",
            title: "特殊用地",
            unit: "亩",
            childs: [{
                key: "11011501",
                title: "军事设施用地",
                unit: "亩",
              },
              {
                key: "11011502",
                title: "外事用地",
                unit: "亩",
              },
              {
                key: "11011503",
                title: "监教场所用地",
                unit: "亩",
              },
              {
                key: "11011504",
                title: "宗教用地",
                unit: "亩",
              },
              {
                key: "11011505",
                title: "殡葬用地",
                unit: "亩",
              },
              {
                key: "11011599",
                title: "其他特殊用地",
                unit: "亩",
              },
            ]
          },
          {
            key: "11011600",
            title: "交通运输用地",
            unit: "亩",
            childs: [{
                key: "11011601",
                title: "铁路用地",
                unit: "亩",
              },
              {
                key: "11011602",
                title: "公路用地",
                unit: "亩",
              },
              {
                key: "11011603",
                title: "街巷用地",
                unit: "亩",
              },
              {
                key: "11011604",
                title: "农村道路用地",
                unit: "亩",
              },
              {
                key: "11011605",
                title: "机场用地",
                unit: "亩",
              },
              {
                key: "11011606",
                title: "港口码头用地",
                unit: "亩",
              },
              {
                key: "11011607",
                title: "管道运输用地",
                unit: "亩",
              },
              {
                key: "11011699",
                title: "其他交通运输用地",
                unit: "亩",
              },
            ]
          },
          {
            key: "11011700",
            title: "水域及水利设施用地",
            unit: "亩",
            childs: [{
                key: "11011701",
                title: "水面用地",
                unit: "亩",
              },
              {
                key: "11011702",
                title: "滩涂用地",
                unit: "亩",
              },
              {
                key: "11011703",
                title: "沟渠用地",
                unit: "亩",
              },
              {
                key: "11011704",
                title: "水利设施用地",
                unit: "亩",
              },
              {
                key: "11011799",
                title: "其他水域及水利设施用地",
                unit: "亩",
              },
            ]

          }
        ]
      },
      {
        key: "2",
        title: "虚拟类",
        childs: [{
            key: "21010100",
            title: "涉农股权",
            unit: "股",
            childs: [{
                key: "21010101",
                title: "企业股权",
                unit: "股",
              },
              {
                key: "21010102",
                title: "村集体股权",
                unit: "股",
              },
            ]
          },
          {
            key: "21020100",
            title: "知识产权",
            unit: "件",
            childs: [{
                key: "21020101",
                title: "发明专利",
                unit: "件",
              },
              {
                key: "21020102",
                title: "实用新型专利",
                unit: "件",
              },
              {
                key: "21020103",
                title: "外观设计专利",
                unit: "件",
              },
              {
                key: "21020104",
                title: "商标权",
                unit: "件",
              },
            ]
          },
        ]
      }
    ]
  }
};

module.exports = {
  schema: schema,
  schema_enumes: schema_enumes,
}
const HANDLER_NAME = 'zhpushDicesion';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:cms:api:services:collection:zhpushDicesion');
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const loanApplicationTrackingData = require('../dataSvc/dataUtil').loanApplicationTracking;
const userVerifysData = require('../dataSvc/dataUtil').userVerifys;
const loanDistributeData = require('../dataSvc/dataUtil').loanDistribute;
const infoPersonalFamilyMemberData = require('../dataSvc/dataUtil').infoPersonalFamilyMember;
const config = require('config');
const agent = require('superagent');
const decisionUrl = `http://${config.get('decision_Service.host')}/api/v1.0/order/callDecisionRuleEngine`;
const decisionCallbackUrl = `http://${config.get('decision_Service.callback_host')}/api/v1.0/loan/application/decision/notify`;

async function pushDicesion(info_collect_history) {
  let method = "zhonghangPushDicesionHandler";
  let promises = [];
  try {
    debug(method, '[input](info_collect_history) ', JSON.stringify(info_collect_history));

    //中行企业
    if (info_collect_history.type == 102) {
      let companyId = info_collect_history.content.basicInfo.companyId;
      let companyName = info_collect_history.content.basicInfo.companyName;
      let legalPerson = info_collect_history.content.operatorInfo.operatorName;
      let corporateIDCard = info_collect_history.content.operatorInfo.opIdNumber;
      let corporateMobile = info_collect_history.content.operatorInfo.opContactNumber;
      let applicationCondition = {
        socialCode: companyId,
        // enterpriseName: companyName,
        status: "collection", //待采集资料
        consumer_t: "02",
        archived: false
      };
      let loanApplication = await loanApplicationData.getByUrl('/v1.0/loan/applications/list', applicationCondition);

      debug(method, '[pushDicesion findApplication](result Json string) ', `applicationCondition:${JSON.stringify(applicationCondition)}`, JSON.stringify(loanApplication));
      for (let loanInfo of loanApplication.result) {
        let dicisionInfo = {
          bizModel: {},
          callbackUrl: decisionCallbackUrl,
        }
        let dicisionPersonInfo = {
          bizModel: {},
          callbackUrl: decisionCallbackUrl,
        }
        dicisionInfo.appCode = config.get('decision_Service.zh_enterprise_apiCode');
        dicisionInfo.bizModel = {
          enterpriseName: companyName, //企业名
          socialCreditCode: companyId, //企业统一码
          orderTradeNo: loanInfo._id, //订单号
          enterpriseId: loanInfo.addons && loanInfo.addons.tyEnterpriseId //天眼查企业ID
        };
        debug(method, '[pushDicesionEnterpriseParams]', `url:${decisionUrl}`, `reqbody:${JSON.stringify(dicisionInfo)}`);
        promises.push(agent.post(decisionUrl).send(dicisionInfo).then(data => {
          debug(method, '[pushDicesionEnterpriseResponse]', `url:${decisionUrl}`, `reqbody:${JSON.stringify(dicisionInfo)}`, `resbody:${JSON.stringify(data.body)}`);
        }));
        //法人
        let user = await userVerifysData.getOneByCondition({
          uId: loanInfo.uId,
          IDCardStatus: "approved",
          "archived": false
        });
        let status = "";
        if (user.realname === legalPerson && user.IDCard.toUpperCase() === corporateIDCard.toUpperCase() && loanInfo.userMobile === corporateMobile) {

          dicisionPersonInfo.appCode = config.get('decision_Service.zh_person_apiCode');
          dicisionPersonInfo.bizModel = {
            customerName: user.realname, //实名
            prePhonetel: loanInfo.userMobile,
            cerdId: user.IDCard,
            orderTradeNo: loanInfo._id
          };
          debug(method, '[pushDicesionPersonParams]', `url:${decisionUrl}`, `reqbody:${JSON.stringify(dicisionPersonInfo)}`);
          promises.push(agent.post(decisionUrl).send(dicisionPersonInfo).then(data => {
            debug(method, '[pushDicesionPersonResponse]', `url:${decisionUrl}`, `reqbody:${JSON.stringify(dicisionInfo)}`, `resbody:${JSON.stringify(data.body)}`);
          }));
          status = 'pre_censor';
        }

        if (status != 'pre_censor') {
          continue;
        }
        let collectInfo = {
          mId: info_collect_history._id,
          version: info_collect_history.version,
          type: info_collect_history.type.toString()
        }
        debug(method, '[loanApplication](put) ', loanInfo._id, {
          'addons.info': collectInfo,
          status: status,
          enterpriseType: info_collect_history.content && info_collect_history.content.basicInfo && info_collect_history.content.basicInfo.companyType

        });

        promises.push(loanApplicationData.putById(loanInfo._id, {
          'addons.info': collectInfo,
          status: status,
          enterpriseType: info_collect_history.content && info_collect_history.content.basicInfo && info_collect_history.content.basicInfo.companyType
        }))
        let distributeData = {
          distribute: info_collect_history.operator,
          aId: loanInfo._id,
          handleStatus: true
        };
        promises.push(loanDistributeData.post(distributeData))

        let trackData = {
          "src_t": "staff",
          "target_t": 1,
          "target": loanInfo._id,
          "action": 'pre_censor',
          "parameters": {
            'addons.info': collectInfo,
            status: status
          },
          "source": info_collect_history.operator,
          "comments": status == 'pre_censor' ? "用户补充资料完毕，进入初审" : "credit_access"
        };
        promises.push(loanApplicationTrackingData.post(trackData));
      }
    } else { //中行个人
      let idCards = [];
      let idCard = info_collect_history.content.basicInfo.idNumber;
      idCards.push(idCard);
      if (idCards.length == 0) {
        return;
      }
      let userVerifys = await userVerifysData.getByCondition({
        IDCard: {
          $in: idCards
        },
        IDCardStatus: "approved",
        "archived": false
      });
      let uIds = [];
      for (let userInfo of userVerifys) {
        uIds.push(userInfo.uId);
      }
      if (uIds.length == 0) {
        return;
      }
      let loanApplication = await loanApplicationData.getByUrl('/v1.0/loan/applications/list', {
        uId: {
          $in: uIds
        },
        status: "collection",
        consumer_t: "01",
        archived: false
      });

      for (let loanInfo of loanApplication.result) {
        let dicisionInfo = {
          bizModel: {},
          callbackUrl: decisionCallbackUrl,
        }
        dicisionInfo.appCode = config.get('decision_Service.zh_person_apiCode');
        let laonUserVerifys = await userVerifysData.getOneByCondition({
          uId: loanInfo.uId,
          IDCardStatus: "approved",
          "archived": false
        });

        dicisionInfo.bizModel = {
          customerName: laonUserVerifys.realname, //实名
          prePhonetel: loanInfo.userMobile, //用户手机号
          cerdId: laonUserVerifys.IDCard, //id
          orderTradeNo: loanInfo._id //订单号
        };

        debug(method, '[pushDicesionPersonParams]', `url:${decisionUrl}`, `reqbody:${JSON.stringify(dicisionInfo)}`);
        promises.push(agent.post(decisionUrl).send(dicisionInfo).then(data => {
          debug(method, '[pushDicesionPersonResponse]', `url:${decisionUrl}`, `reqbody:${JSON.stringify(dicisionInfo)}`, `resbody:${JSON.stringify(data.body)}`);
        }));
        let collectInfo = {
          mId: info_collect_history._id,
          version: info_collect_history.version,
          type: info_collect_history.type.toString()
        }
        promises.push(loanApplicationData.putById(loanInfo._id, {
          'addons.info': collectInfo,
          status: "pre_censor"
        }))
        let distributeData = {
          distribute: info_collect_history.operator,
          aId: loanInfo._id,
          handleStatus: true
        };
        promises.push(loanDistributeData.post(distributeData))
        let trackData = {
          "src_t": "staff",
          "target_t": 1,
          "target": loanInfo._id,
          "action": 'pre_censor',
          "parameters": {
            'addons.info': collectInfo,
            status: "pre_censor"
          },
          "source": info_collect_history.operator,
          "comments": "用户补充资料完毕，进入初审"
        };
        promises.push(loanApplicationTrackingData.post(trackData))
      }
    }
    Promise.all(promises).then(data => {
      debug(method, '[Exit](success)', data);
    }).catch(err => {
      debug(method, '[Exit](failed)', err);
    });
  } catch (ex) {
    debug(method, '[Exit](exception)', ex && ex.message);
  }
}

module.exports = {
  pushDicesion: pushDicesion,
}
const Joi = require('joi');
const base_schema = require('./base_schema');
const schema = Joi.object({

  //企业基本情况
  companyBasic: Joi.object({
    companyName: base_schema.companyName_schema,
    companyID: base_schema.companyID_schema,
    registeredAddress: base_schema.registeredAddress_schema,
    registeredCapital: base_schema.registeredCapital,
    registeredDate: base_schema.registeredDate_schema,
    businessTerm: Joi.string().required().description("营业期限"),
    boardOfDirectorNumber: Joi.number().integer().required().description("理事会人数"),
    number: Joi.number().integer().required().description("成员人数"),
    plantingAreas: base_schema.plantingAreas_schema,
  }),
  //*理事长基本情况
  basic: Joi.object({

    name: base_schema.name_schema,
    gender: base_schema.gender_schema,
    administrativePost: Joi.number().integer().min(1).max(2).required().description('行政职务 1、村书记  2、其它'),
    marital: base_schema.marital_schema,
    idCard: base_schema.idCard_schema,
    education: base_schema.education_schema,
    mobile: base_schema.mobile_schema,
    residence: base_schema.address_schema.required(),
    detailedAddress: Joi.string().description('家庭住址（详细地址）'),
    emergencyContactPerson: Joi.string().required().description('紧急联系人'),
    emergencyContactNumber: base_schema.mobile_schema.description('紧急联系电话'),
  }),

  //**理事会主要成员及监事、财务负责人基本情况（数组）
  familyMembers: Joi.array().items(Joi.object({
    personType: Joi.number().integer().min(1).max(3).required().description("1、理事会成员 2、监事 3、财务人员"),
    name: base_schema.name_schema,
    idCard: base_schema.idCard_schema,
    gender: base_schema.gender_schema,
    age: base_schema.age_schema,
    education: base_schema.education_schema,//.required(),
    professionalTitle: Joi.string().description("专业职称"),
    job: Joi.string().description('职务'),
    workingLife: base_schema.workingLife_schema,
    mobile: base_schema.mobile_schema,
  })),

  //上一年度土地种植状况
  previousPlantingSituation: base_schema.previousPlantingSituation_schema,
  //*本年度土地种植情况预计
  plantingSituation: base_schema.plantingSituation_schema,
  //*农机耕作能力
  machineCapacity: base_schema.machineCapacity_schema,
  //资产清单
  asset: base_schema.asset_schema,
  //其它经营项目
  project: base_schema.project_schema,
  //农业补贴
  subsidy: base_schema.subsidy_schema,
  //债务情况
  debt: base_schema.debt_schema,
  //*贷款需求及偿还情况
  loanDemand: base_schema.loanDemand_schema,
});

module.exports = {
  schema: schema,
}
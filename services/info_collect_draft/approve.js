'use strict';

const HANDLER_NAME = 'approveHandler';
const Joi = require('joi');
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:info_collect_draft:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { assert } = require('../../utils/general');

const {
  loanApplicationOutLand: loanApplicationOutLandData,
  loanApplicationLandType: loanApplicationLandTypeData,
  loanApplication: loanApplicationData,
  LoanSvrLand: LoanSvrLandData,
  employeeGroups: employeeGroupsData,
  groups: groupsData,
  infoCollectHistory: infoCollectHistoryData,
  loanApplicationVerify: loanApplicationVerifyData,
  loanSupplement: loanSupplementData,
  userAddons: userAddonsData,
  userCxwqSupplement: userCxwqSupplementData,
  infoCollectDraft: infoCollectDraftData,
  loanData,
} = require('../dataSvc/dataUtil');

const pushDicesion = require('./pushDicesion');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]', JSON.stringify(this.context.input));
    try {

      let input = this.context.input;
      let opts = this.context.opts;
      let draftId = input.id;
      let type = `${input.type}`;
      let source = input.source; //来源
      let historyId = input.historyId;
      let comment = input.comment;
      let status = input.status; //采集状态 1 完成 2 未完成
      let firstComplete = false; //是否首次采集完成
      let history;
      let draft = await infoCollectDraftData.getOneByCondition({ _id: draftId });
      if (!draft || !draft.content) {
        throw {
          errorCode: 'E_VALID_ERROR',
          httpCode: 401,
          reason: 'draft not exists'
        }
      }
      if (parseInt(status) == 1) {
        firstComplete = true;
      }

      //如果有历史版本，需要合并历史版本的内容和草稿
      if (historyId) {
        history = await infoCollectHistoryData.getById(historyId);
        if (history.status == 1) {
          firstComplete = false;
        }
        // console.log(`before content:${JSON.stringify(draft.content)}`);
        draft.content = Object.assign(history.content, draft.content);

        // console.log(`after content:${JSON.stringify(draft.content)}`);
      }
      //'申请主体id, 个人的时候代表身份证，其余的代表 注册码 / 统一社会信用代码',
      let uniqueId;
      let name;
      let mobile;
      let legalPersonName; //经营者/理事长/法人代表
      const update = { type };
      const { basic, companyBasic } = draft.content || {};
      update.enterpriseName = (basic || {}).companyName || (companyBasic || {}).companyName;
      //创新物权
      if (source.toString() == '1') {
        mobile = draft.content.basic.mobile;
        if (type.toString() == '01') {
          uniqueId = draft.content.basic.idCard;
          name = draft.content.basic.name;
          legalPersonName = name;
          const [plantingAreas] = draft.content.basic.plantingAreas || [];
          assert(plantingAreas, 'E_APPROVE_VALID_ERROR_001', '种植区域不能为空');
          update.area = plantingAreas.areaCode;
          debug(`${HANDLER_NAME}AreaCodeReplace 01`, update.area);
        } else if (type.toString() == '03') {
          uniqueId = draft.content.companyBasic.companyCode;
          name = draft.content.companyBasic.companyName;
          legalPersonName = draft.content.basic.name;
          const [plantingAreas] = draft.content.companyBasic.plantingAreas || [];
          assert(plantingAreas, 'E_APPROVE_VALID_ERROR_002', '种植区域不能为空');
          update.area = plantingAreas.areaCode;
          debug(`${HANDLER_NAME}AreaCodeReplace 03`, update.area);
        } else {
          uniqueId = draft.content.companyBasic.companyID;
          name = draft.content.companyBasic.companyName;
          legalPersonName = draft.content.basic.name;
          const [plantingAreas] = draft.content.companyBasic.plantingAreas || [];
          assert(plantingAreas, 'E_APPROVE_VALID_ERROR_002', '种植区域不能为空');
          update.area = plantingAreas.areaCode;
          debug(`${HANDLER_NAME}AreaCodeReplace other ${type}`, update.area);
        }
      } else if (source.toString() == '2') { //中行
        if (type.toString() == '101') { //个人
          uniqueId = draft.content.basicInfo && draft.content.basicInfo.idNumber;
          name = draft.content.basicInfo && draft.content.basicInfo.name;
          mobile = draft.content.basicInfo && draft.content.basicInfo.contactNumber;
          legalPersonName = name;
        } else if (type.toString() == '102') { //企业
          uniqueId = draft.content.basicInfo && draft.content.basicInfo.companyId;
          name = draft.content.basicInfo && draft.content.basicInfo.companyName;
          legalPersonName = draft.content.operatorInfo && draft.content.operatorInfo.operatorName;
          mobile = draft.content.operatorInfo && draft.content.operatorInfo.opContactNumber;
        }
      }

      let data = {
        "draftId": draftId,
        "type": type,
        "source": source,
        'content': draft.content,
        "uniqueId": uniqueId,
        "name": name,
        "legalPersonName": legalPersonName,
        "mobile": mobile,
        'comment': comment,
        "position": input.position,
        "status": status,
        "operator": opts.userid,
        "lastModTime": new Date(),
      }
      const app = draft.aId && await loanApplicationData.getById(draft.aId);
      draft.aId && app && (data.aId = draft.aId, data.uId = app.uId);

      let result;
      if (source.toString() == '1') {
        const oldList = await infoCollectHistoryData.getByCondition({ aId: draft.aId, isLast: true, limit: 'unlimited' });
        await Promise.all(oldList.map(({ _id }) => infoCollectHistoryData.putById(_id, { isLast: false })));
        data.isLast = true;
        result = await infoCollectHistoryData.post(data);
        // description = input.comment,group = input.roleId,employee = opts.userid,verifyInfo = null;
        input.verifyResult = await this.verify(draft.aId, parseInt(input.status), input.comment, opts.userid);
      } else if (source.toString() == '2') { //中行
        //设置最后一次保存的标志位
        if (uniqueId) {
          const oldList = await infoCollectHistoryData.getByCondition({ uniqueId: uniqueId, isLast: true, limit: 'unlimited' });
          await Promise.all(oldList.map(({ _id }) => infoCollectHistoryData.putById(_id, { isLast: false })));
          data.isLast = true;
        }
        if (parseInt(status) == 2 && historyId) { //未完成并且有历史版本，修改 否则添加
          result = await infoCollectHistoryData.putById(historyId, data);
        } else {
          result = await infoCollectHistoryData.post(data);

        }
      }

      //中行首次采集完成 ，生成风控报告
      if (source.toString() == '2') {
        if (parseInt(status) == 1) {
          pushDicesion.pushDicesion(result);
        }
      }

      //给订单表设置 采集id
      if (draft.aId) {
        Object.assign(update, { "addons.info": { source, type, mId: result._id, version: result.version } });
        debug(method, 'collection_mid', `${HANDLER_NAME}AreaCodeReplace save`, draft.aId, JSON.stringify(update));
        await loanApplicationData.putById(draft.aId, update);
      }

      this.context.result = result;

      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async verify(aId, isAgree, description, employee) {
    const cf = { current: 'collection', next: 'wait_fund', refuse: 'rejected_by_info_collection', back: null, employee: ['RegionalManager'] }; //区域审核员
    const action = isAgree === 1 && 'agree' || 'refuse';

    const app = await loanApplicationData.getById(aId);
    const { status } = app;
    assert(status === cf.current, 'E_APPLICATION_VERIFY_21', '当前状态不可以审核');
    debug(HANDLER_NAME, 'QueryParaApprove', aId, status, action, employee);
    // const employeeGroup = employee && group && await employeeGroupsData.getOneByCondition({employee,group}) || null;
    assert(app, 'E_APPLICATION_VERIFY_22', '订单不存在');
    // assert(employeeGroup,'E_APPLICATION_VERIFY_03','用户未登录或未拥有此管理员角色');
    // const role = await groupsData.getById(group);
    // assert(role,'E_APPLICATION_VERIFY_04','不存在的管理员权限');
    // assert(!Array.isArray(cf.employee) || cf.employee.length === 0 || cf.employee.includes(role.name),
    //     'E_APPLICATION_VERIFY_05','该角色当前阶段无权审核');
    const again = app.verifyInfo && app.verifyInfo.fund && true || false;
    action === 'agree' && (again ? app.status = 'credit_access' : app.status = cf.next); //如果是驳回之后再采集，然后再来审核，直接跳到待签约状态。跳过确认贷款银行和完善资料
    action === 'refuse' && (app.status = cf.refuse);
    app.lastModTime = new Date();
    debug(HANDLER_NAME, 'verifyUpdate', aId, again, action, app.status);
    await loanApplicationData.putById(app._id, app);

    status.match(/^rejected_/) && debug(`${HANDLER_NAME}VerifyStatusSaveError2`, status, action, app._id);
    assert(!status.match(/^rejected_/), 'E_APPLICATION_VERIFY_23', '操作日志的记录状态有误');
    let verify = { aId: app._id, status, action, description, operator: employee, createdTime: new Date(), lastModTime: new Date(), archived: false }
    verify = await loanApplicationVerifyData.post(verify);
    return { aId, again, isAgree: action === 'agree' };
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler;

const merge = (target, source) => {
  // Iterate through `source` properties and if an `Object` set property to merge of `target` and `source` properties
  for (const key of Object.keys(source)) {
    if (source[key] instanceof Object) Object.assign(source[key], merge(target[key], source[key]))
  }

  // Join `target` and modified `source`
  Object.assign(target || {}, source)
  return target
}
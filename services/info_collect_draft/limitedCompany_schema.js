const Joi = require('joi');
const base_schema = require('./base_schema');
const schema = Joi.object({

  //企业基本情况
  companyBasic: Joi.object({
    companyName: base_schema.companyName_schema,
    companyID: base_schema.companyID_schema,
    registeredAddress: base_schema.registeredAddress_schema,
    registeredCapital: base_schema.registeredCapital,
    registeredDate: base_schema.registeredDate_schema,
    businessTerm: Joi.string().required().description("营业期限"),
    plantingAreas: base_schema.plantingAreas_schema,
  }),
  //**法定代表人基本情况
  basic: base_schema.family_farm_basic_schema,

  //**高层管理及财务负责人基本情况（支持添加多条信息）
  familyMembers: Joi.array().items(Joi.object({
    personType: Joi.number().integer().min(1).max(2).required().description("1、管理人员2、财务人员"),
    name: base_schema.name_schema,
    gender: base_schema.gender_schema,
    age: base_schema.age_schema,
    education: base_schema.education_schema,
    professionalTitle: Joi.string().description("专业职称"),
    job: Joi.string().description('职务'),
    workingLife: base_schema.workingLife_schema,
    mobile: base_schema.mobile_schema,
  })),

  //*上一年度农机经营情况
  machineryOperatingSituation: Joi.object({
    machineryOperatingYear: Joi.number().integer().required().description("农机经营年度"),
    machineryOperatingIncome: Joi.number().required().description("农机作业收入（万元）"),
    machineryOperatingExpenditure: Joi.number().required().description("农机作业支出（万元）"),
    machineryOperatingProfit: Joi.number().required().description("农机作业利润（万元）"),
  }),
  //上一年度土地种植状况
  previousPlantingSituation: base_schema.previousPlantingSituation_schema,
  //*本年度土地种植情况预计
  plantingSituation: base_schema.plantingSituation_schema,
  //*农机耕作能力
  machineCapacity: base_schema.machineCapacity_schema,
  //资产清单
  asset: base_schema.asset_schema,
  //其它经营项目
  project: base_schema.project_schema,
  //农业补贴
  subsidy: base_schema.subsidy_schema,
  //债务情况
  debt: base_schema.debt_schema,
  //*贷款需求及偿还情况
  loanDemand: base_schema.loanDemand_schema,
});

module.exports = {
  schema: schema,
}
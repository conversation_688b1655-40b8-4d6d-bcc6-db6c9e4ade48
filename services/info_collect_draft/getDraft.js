'use strict';

const HANDLER_NAME = 'getDraftHandler';
const Joi = require('joi');
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanProduct:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const infoCollectDraftData = require('../dataSvc/dataUtil').infoCollectDraft;
const aliOssSvc = require('../aliOssSvc');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]', JSON.stringify(self.context.input));
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let draft;
      if (input.aId || input._id) {
        draft = await infoCollectDraftData.getOneByCondition(input);
        if (!draft) {
          let newDraft = { aId: input.aId, content: { lastModTime: new Date() } };
          draft = await infoCollectDraftData.post(newDraft);
        }
      } else {
        draft = await infoCollectDraftData.post({ content: { lastModTime: new Date() } });
      }

      debug(method,'result',JSON.stringify(draft));
      await Promise.all( ( draft.content.asset || [] )
          .map( v=>v.assetPhotos || [] )
          .reduce((res,it)=>res.concat(it),[])
          .map(formatImg) );
      self.context.result = draft;
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {
  if ( item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
  }
  if ( item && item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    item.image.url =  await aliOssSvc.getFile({ fileName: item.image.url });
  }
}

module.exports = Handler
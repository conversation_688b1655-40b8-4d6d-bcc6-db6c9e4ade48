const Joi = require('joi');
const { join } = require('q');
const { gender_schema } = require('./base_schema');
const base_schema = require('./base_schema');
const schema = Joi.object({
  //企业基本情况
  companyBasic: Joi.object({
    companyName: base_schema.companyName_schema,
    companyCode: base_schema.companyCode_schema,
    registeredDate: base_schema.registeredDate_schema,
    registeredAddress: base_schema.registeredAddress_schema,
    registeredDetailedAddress: Joi.string().description("经营场所（详细地址）"),
    plantingAreas: base_schema.plantingAreas_schema,
    registeredAddressVillage: Joi.string().required().description('地址乡镇村文字描述'),// base_schema.address_schema,
  }),
  //经营者基本情况
  basic: Joi.object({
    name: base_schema.name_schema,
    idCard: base_schema.idCard_schema,
    gender: base_schema.gender_schema,
    marital: base_schema.marital_schema,
    education: base_schema.education_schema,
    mobile: base_schema.mobile_schema,
    residence: base_schema.address_schema,
    detailedAddress: Joi.string().description('家庭住址（详细地址）'),
    // plantingAreas: base_schema.plantingAreas_schema,
    emergencyContactPerson: Joi.string().required().description('紧急联系人'),
    emergencyContactNumber: base_schema.mobile_schema.description('紧急联系电话')
  }),
  //家庭农场主要成员情况（数组）
  familyMembers: Joi.array().items(Joi.object({
    name: base_schema.name_schema,
    idCard: base_schema.idCard_schema,
    gender: base_schema.gender_schema,
    age: base_schema.age_schema,
    relationship: Joi.number().integer().min(1).max(6).required().description("与经营者关系 1 父亲、2 母亲、3 配偶、4 儿子、5 女儿、6 其他"),
    job: Joi.string().description('职务'),
    workingLife: base_schema.workingLife_schema,
    mobile: base_schema.mobile_schema,
  }).description('家庭农场主要成员情况（数组）')),

  //上一年度土地种植状况
  previousPlantingSituation: base_schema.previousPlantingSituation_schema,
  //*本年度土地种植情况预计
  plantingSituation: base_schema.plantingSituation_schema,
  //*农机耕作能力
  machineCapacity: base_schema.machineCapacity_schema,
  //资产清单
  asset: base_schema.asset_schema,
  //其它经营项目
  project: base_schema.project_schema,
  //农业补贴
  subsidy: base_schema.subsidy_schema,
  //债务情况
  debt: base_schema.debt_schema,
  //*贷款需求及偿还情况
  loanDemand: base_schema.loanDemand_schema,
});

module.exports = {
  schema: schema,
}
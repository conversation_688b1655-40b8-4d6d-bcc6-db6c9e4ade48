/*
 * @Description: 粮食预期收益抵押贷合同创建签署
 * @Author: zhu xue song
 * @Date: 2021-08-19 00:32:54
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-08-25 20:17:57
 * @FilePath: \rongxin.loan.user.app.api\services\contract_v2\cxwqGrainExpectIncomeHandler.js
 */

 'use strict';

 const HANDLER_NAME = 'cxwqGrainExpectIncomeHandler';
 const logFactory = require('../../utils/logFactory');
 const logUtil = require('../../utils/logUtil');
 const debug = logFactory(logUtil())('rongxin:app.api:services:contract:' + HANDLER_NAME);
 const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
 const { 
   contractSignersV2: contractSignersV2Data,
   contractFlowsV2: contractFlowsV2Data,
   contractFlowSignTasksV2: contractFlowSignTasksV2Data,
   contractOwnersV2: contractOwnersV2Data,
   contracts: contractsData,
   contractTemplates: contractTemplatesData,
   loanApplication: loanApplicationData,
   infoCollectHistory: infoCollectHistoryData,
   userVerifys: userVerifysData,
   meeting: meetingData
  } = require('../dataSvc/dataUtil');
 const CALLBACK_URL = '/api/v2.0/eSign/cxwq/agent/callback';
 const eSignSvc = require('../eSignSvc');
 const config = require('config');
 const uuid = require('uid-safe');
 const moment = require('moment');

const SIGN_STATUS_SUCCESS = 2; //签署完成: 所有签署人完成签署

 
//  22 粮食预期收益权质押贷款授权委托书 23 粮食预期收益权质押贷款股东会决议 24 粮食预期收益权质押贷款成员大会决议 25 粮食预期收益权质押贷款授权委托书+
const relationshipInfoEnum = {
  Father: 1,
  Mother: 2,
  Couple: 3, // 配偶
  Son: 4,
  Daughter: 5,
  Other: 6
}
// 当前共有人与申请人的关系
const relationshipCownerEnum = {
  UnKnow: 0, // 未知关系
  Huzhu: 1, // 当前共有人是申请人的户主
  Couple: 2, // 当前共有人是申请人的配偶
  CoupleAndHuzhu: 12, // 当前共有人是申请人的户主和配偶
}

const memberTypeEnum = {
  Person: 1, // 个人
  Enterprise: 2, // 企业
}

const RelationshipMap = new Map([
  [1, '10036'],
  [2, '10036'],
  [3, '10033'],
  [4, '10035'],
  [5, '10035'],
  [6, '10032'],
  [7, '10039'],
  [8, '10040']
])

 class Handler extends BaseHandler {
   /**
    * 创新物权：粮食8大类型区分后得4种类型授权合同
    */
   constructor(context) {
     super(context)
   }
 
   getName() {
     return HANDLER_NAME
   }
 
   async doAsync(done) {
     let self = this;
     let method = `${self.getName()}.doAsync`
     debug(method, '[Enter]')
    try {
      const {aId, again,isAgree} = self.context.input.verifyResult || {};
      if( !aId || !again || !isAgree )return done();
       // 订单信息
      const loanInfo = await loanApplicationData.getById(aId);
      // todo: 判断订单状态，如果不是待签约，直接报错
      let {uId, amount} = loanInfo;
      amount = +amount / 1000000;
      const userInfo = await userVerifysData.getOneByCondition({ uId, archived: false });
      // 从采集信息种获得此次得采集类型
      let { version: newVersion, type } = loanInfo.addons.info;

      let cownerCountLimit = 0;
      let businessScene = '';
      let templateType = 0;
      let prefix = '';
      let contractName = '';
      let keyStrPrefix = ''
      if(type) {
        if(type === '01') {
          cownerCountLimit = 1;
          businessScene = '粮食预期收益权质押贷款授权委托书';
          contractName = '粮食预期收益权质押贷款授权委托书';
          templateType = 22
        } else if(type === '03') {
          cownerCountLimit = 10;
          businessScene = '粮食预期收益权质押贷款授权委托书_家庭农场';
          contractName = '粮食预期收益权质押贷款授权委托书';
          templateType = 23
        } else if(type === '04' || type === '05' || type === '06') {
          keyStrPrefix = '成员';
          businessScene = '粮食预期收益权质押贷款成员会决议';
          contractName = '粮食预期收益权质押贷款成员会决议';
          cownerCountLimit = 15;
          templateType = 24;
          prefix = 'cydh';
        } else if(type === '07' || type === '08' || type === '09') {
          keyStrPrefix = '股东';
          businessScene = '粮食预期收益权质押贷款股东会决议';
          contractName = '粮食预期收益权质押贷款股东会决议';
          cownerCountLimit = 15;
          prefix = 'gs';
          templateType = 25;
        }
      }
      if(type === '01' || type === '03') { // 只有一个签约人
        /** ------   处理两次签约人增加修改删除得情况，得到最新需要签约得人，并删除无需签约的人   ------- */
        const newCollectInfo = await infoCollectHistoryData.getOneByCondition({ version: newVersion, aId, type });
        debug(method, newCollectInfo)
        // 查找上一次所有得签约人
        let oldCowners = await contractOwnersV2Data.getByCondition({aId});
        let oldCownersIdObj = {};
        for (const it of oldCowners) {
          const IDCard = upperCase(it.IDCard);
          oldCownersIdObj[IDCard] = it;
        }
        let familyMembers = []; // 家庭成员信息
        let members = []; // 本次符合条件得签约人信息
        if(newCollectInfo && newCollectInfo.content && newCollectInfo.content.familyMembers) {
          familyMembers = newCollectInfo.content.familyMembers;
        }
        if(familyMembers.length !== 0) {
          for (const it of familyMembers) {
            const relationship = it.relationship;
            let cowner = {
              aId,
              IDCard: upperCase(it.idCard),
              realname: it.name,
              mobile: it.mobile,
              contractorRelation: RelationshipMap.get(relationship),
              type: "cowner",
              applicant: false,
              sign: 1,
              archived: false
            }
            if(relationship === relationshipInfoEnum.Couple) {
              members.push(cowner)
            }
            if(type === '03'){
              if(relationship === relationshipInfoEnum.Daughter || relationship === relationshipInfoEnum.Son) {
                let age = it.age || 0;
                if(age >= 18) {
                  cowner.relationship = relationshipCownerEnum.UnKnow;
                  members.push(cowner);
                }
              } else if(relationship === relationshipInfoEnum.Father || relationship === relationshipInfoEnum.Mother) {
                cowner.relationship = relationshipCownerEnum.UnKnow;
                members.push(cowner);
              }
            }
          }
        }

        // 如果是驳回后的第二次签约
        let insertCowners = []; // 本次相对上次新增得签约人
        // 判断这次和上次实际需要签约的人，并新增到签约人种
        for (const it of members) {
          const IDCard = it.IDCard
          let cownerObj = oldCownersIdObj[IDCard];
          // 代表本次这个签约人和上次得签约人不同，此次需要新增[判断签约人是否大于上限后再新增，防止创建完签约人合同却创建失败]
          if(!cownerObj) {
            insertCowners.push(it);
            continue;
          }
          delete oldCownersIdObj[IDCard]; // 删除掉两次相同得签约人
        }

        // 因为在合同上已经签约的人不能撤销掉签字，所以新增的签字只能向后排，所以签字数量总共只有预设的20个，也就是签约人最多有20个
        let len = oldCowners.length;
        if(len !== 0) { // 如果是首次签合同（签约人为空），代表被委托人已被自动生成，需要去掉
          len -= 1;
        }
        if(len + insertCowners.length > cownerCountLimit) {
          throw {
            errorCode: 'ERR_CGEI_56',
            httpCode: 406,
            reason: '合同签约人已超过签约人数，创建失败',
          }
        }

        // 此时剩下得是以前需要签约，但本次不需要签约得, 将其删除, 并删除三者对应关系
        for (const k in oldCownersIdObj) {
          if (Object.hasOwnProperty.call(oldCownersIdObj, k)) {
            const v = oldCownersIdObj[k];
            await contractOwnersV2Data.putById(v._id, { archived: true }); // 删除掉签约人
          }
        }

        // 合同不需要签约人，此时 合同未创建|流未创建
        if(insertCowners.length === 0) {
          if(oldCowners.length === 0) {
            await loanApplicationData.putById(aId, { status: 'wait_investigation_verify_1', lastModTime: new Date() })
            return done();
          }
        }

        // 新增未添加过的本次需要签约的签约人
        for (const it of insertCowners) {
          const type = it.type;
          // 创建或获得e签宝账户id，没有就新增，有就获取（唯一键：IDCard）
          let eSignRes = await eSignSvc.createUser({
            "name": it.realname,
            "idNo": it.IDCard,
            "idType": "19",
          })
          it.eSignAccountId = eSignRes.accountId;
          if(type !== 'cowner') { // 企业
            const organInfo = await eSignSvc.createOrganizeCommon({
              creatorId: eSignRes.accountId,
              name: it.extras.name,
              organCode: it.extras.organCode,
              organType: 11, // 默认是 11统一社会信用代码,
              legalName: it.realname
            })
            it.eSignOrgId = organInfo.accountId;
          }
          it.IDCard = upperCase(it.IDCard);
          const cownerObj = await contractOwnersV2Data.post(it);
          it._id = cownerObj._id;
        }

        /** -----   判断合同是否已经创建过   ----- */
        let year = new Date().getFullYear();
        if (new Date().getMonth() + 1 > 8) {
          year++;
        }
        let contractTmp = await contractTemplatesData.getOneByCondition({ type: templateType, archived: false });
        if (!contractTmp || !contractTmp.eTempId) {
          throw {
            errorCode: 'E_FOR_ENTRUST_CTRC_043',
            httpCode: 406,
            reason: 'invalid contract template'
          }
        }
        let contract = await contractsData.getOneByCondition({ 
          tId: contractTmp._id,
          loanId: aId,
          type: templateType
        });
        if(!contract) {
          let simpleFormFields = {};
          if(type === '01') {
            if(insertCowners.length === 0) {
              insertCowners = oldCowners;
            }
            simpleFormFields = {
              "zzdh_1": insertCowners[0].realname,
              "zzdh_2": insertCowners[0].IDCard,
              "zzdh_3": userInfo.realname,
              "zzdh_4": userInfo.IDCard,
              "zzdh_5": insertCowners[0].realname,
              "zzdh_6": userInfo.realname,
              "zzdh_7": new Date().getFullYear(),
              "zzdh_8": new Date().getMonth() + 1,
              "zzdh_9": new Date().getDate(),
              "zzdh_10": year,
            }
          } else {
            simpleFormFields = {
              jtnc_21: userInfo.realname,
              jtnc_22: userInfo.IDCard,
              jtnc_1_2: userInfo.realname,
              jtnc_2_2: new Date().getFullYear(),
              jtnc_3_2: new Date().getMonth() + 1,
              jtnc_4_2: new Date().getDate(),
              jtnc_100: year
            }
            let num1 = 1;
            for (const it of insertCowners) {
              const realname = it.realname;
              const IDCard = it.IDCard;
              simpleFormFields[`jtnc_${num1++}`] = realname;
              simpleFormFields[`jtnc_${num1++}`] = IDCard;
            }
          }
          let payload = {
            templateId: contractTmp.eTempId,
            name: contractName,
            simpleFormFields,
          }
          const sSignContract = await eSignSvc.createDocByTemplate(payload);
          if (!sSignContract) {
            throw {
              errorCode: 'C_CREDIT_ACCESS_56',
              httpCode: 406,
              reason: 'CA contract is Create fail',
            }
          }
          // 创建好合同
          contract = await contractsData.post({
            tId: contractTmp._id,
            loanId: aId,
            uId,
            type: templateType,
            name: contractName,
            signatory:  2,
            eDocId: sSignContract.docId,
            eDocUrl: sSignContract.docUrl,
          })
        }

        // 如果此次新增签约人为0，原签约人都已签约（相当于签约人没有变化）
        // 考虑失败情况：如果签约任务数量和所有以前签约人数不相等，不予通过【证明之前签约任务逻辑失败，没有为签约人成功创建签约任务】
        let isTaskMatch = true;
        let isSignerMatch = true;

        // 判断singer之前是否有创建失败的
        let oldSigners = [];
        if(insertCowners.length === 0) {
          oldSigners = await contractSignersV2Data.getByCondition({ cId: contract._id, aId: aId })
          if(!oldSigners || !oldCowners || oldSigners.length !== oldCowners.length) {
            isSignerMatch = false;
          }
        }

        // 删除signers
        for (const it of oldSigners) {
          const _id = it._id;
          const cownerId = it.cownerId;
          for (const k in oldCownersIdObj) {
            if (Object.hasOwnProperty.call(oldCownersIdObj, k)) {
              const v = oldCownersIdObj[k];
              if(v._id === cownerId) {
                delete oldCownersIdObj[k];
                await contractSignersV2Data.putById(_id, { archived: true })
              }
            }
          }
        }

        /** ----   创建签署流   ----- */
        let flow = await contractFlowsV2Data.getOneByCondition({
          cId: contract._id,
          loanId: aId,
          archived: false
        });
        if(!flow) {
          let flowRes = await eSignSvc.createMoreContractFlow({
            businessScene,
            signPlatform: '1',
            docList: [{
              docId: contract.eDocId,
              docName: contract.name
            }],
            noticeUrl: `${config.get('cxwqBaseUrl')}${CALLBACK_URL}`
          });
          if (!flowRes) {
            throw {
              errorCode: 'E_CREATE_FLOW_042',
              httpCode: 406,
              reason: 'eSignFlow is fail'
            }
          }
          flow = await contractFlowsV2Data.post({
            loanId: aId,
            cId: [contract._id],
            type: 6, // 喜农淘授权委托书
            eFlowId: flowRes.flowId,
            eBuzScene: '喜农淘授权委托'
          })
          if (!flow) {
            throw {
              errorCode: 'E_CREATE_FLOW_042',
              httpCode: 406,
              reason: 'contractFlow is fail'
            }
          }
        }

        // 判断tasks之前是否有创建失败的
        let oldTasks = [];
        let flowNew = 0;
        if(insertCowners.length === 0) {
          oldTasks = await contractFlowSignTasksV2Data.getByCondition({eFlowId: flow.eFlowId, $sort: { createdTime: 1 }});
          if(oldTasks && oldCowners && oldTasks.length === oldCowners.length) {
            let allFinished = true;
            for (const it of oldTasks) {
              const status = it.status;
              if(status !== SIGN_STATUS_SUCCESS) {
                allFinished = false;
              }
            }
            if(allFinished) {
              let flow2 = await contractFlowsV2Data.putById(flow._id, { status: 2, lastModTime: new Date() })
              flowNew = flow2.status;
              await loanApplicationData.putById(aId, { status: 'wait_investigation_verify_1', lastModTime: new Date() })
              return done();
            }
          } else{
            isTaskMatch = false;
          }
        }
        // 之前状态为已完成，并且这次判定为未完成，修改签署流状态
        if(flow.status === 2 && !flowNew) {
          await contractFlowsV2Data.putById(flow._id, { status: 1, lastModTime: new Date() })
        }

        /** -----   创建关联订单 | 合同 | 签约人关系, 并处理增删改得关系   ----- */
        for (const it of insertCowners) {
          await contractSignersV2Data.post({
            cownerId: it._id,
            aId: aId,
            cId: contract._id
          })
        }
        // 如果不匹配，补充以前未创建得signer
        if(!isSignerMatch) {
          const oldSigners = await contractSignersV2Data.getByCondition({
            cId: contract._id,
            aId
          })
          for (const cowner of oldCowners) {
            let exist = false;
            for (const signer of oldSigners) {
              if(cowner._id === signer.cownerId) {
                exist = true;
              }
            }
            if(!exist) {
              await contractSignersV2Data.post({
                cownerId: cowner._id,
                aId: aId,
                cId: contract._id
              })
            }
          }
        }

        /** -----   创建签约任务   ----- */
        if(!isTaskMatch) {
          for (const cowner of oldCowners) {
            let exist = false;
            for (const task of oldTasks) {
              if(cowner.eSignAccountId === task.accountId) {
                exist = true;
                break;
              }
            }
            if(!exist) {
              insertCowners.push(cowner)
            }
          }
        }

        let arr = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"];
        /** -----   创建签约任务   ----- */
        let num2 = len;
        for (const it of insertCowners) {
          let keyStr = '委托人' + arr[num2++] + '（电子签名）';
          let task = uuid.sync(24);
          let posX = type === '01' ? 200 : 150;
          // 远程创建
          let eSignCond = {
            flowId: flow.eFlowId,
            thirdOrderNo: task,
            accountId: it.eSignAccountId,
            signDocList: [{
              docId: contract.eDocId,
              posList: [{
                signType: 1,
                key: keyStr,
                posX
              }]
            }]
          };
          let flowTaskRes = await eSignSvc.manualMoreSignContractFlowTask(eSignCond);
          debug(method, '[createContractFlowESign]', flowTaskRes);
          // 本地创建
          let contractFlowTask = {
            fId: flow._id,
            cownerId: it._id,
            eFlowId: flow.eFlowId,
            taskId: task,
            accountId: it.eSignAccountId,
            status: 1,
            signUrl: flowTaskRes.signUrl,
            signShortUrl: flowTaskRes.signShortUrl
          };
          await contractFlowSignTasksV2Data.post(contractFlowTask);
        }
        self.context.result = { data: 'success' };
        debug(method, '[Exit](success)', self.context.result);
        return done();
      } else {
        /** ------   处理两次签约人增加修改删除得情况，得到最新需要签约得人，并删除无需签约的人   ------- */
        const newCollectInfo = await infoCollectHistoryData.getOneByCondition({ version: newVersion, aId, type,archived: false });
        // 查找上一次所有得签约人
        let oldCowners = await contractOwnersV2Data.getByCondition({aId, $sort: { createdTime: 1 }});
        let {meetingTime, meetingPlace, emcee, requireArrivePeople, realArrivePeople, members: meetMembers} = await meetingData.getOneByCondition({ loanId: aId });
        let meetingUsers = meetMembers ? meetMembers : [];
        
        // 原有签约人
        let oldCownersIdObj = {};
        for (const it of oldCowners) {
          const IDCard = upperCase(it.IDCard);
          oldCownersIdObj[IDCard] = it;
        }
        let members = []; // 本次符合条件得签约人信息
        for (const it of meetingUsers) {
          let contractorRelation = '10040';
          if(type === '04' || type === '05' || type === '06') {
            contractorRelation = '10039';
          }
          const type2 = it.type;
          let cowner = {
            aId,
            IDCard: upperCase(it.IDCard),
            mobile: it.mobile,
            realname: it.name,
            type: 'cowner',
            contractorRelation,
            applicant: false,
            sign: 1,
            archived: false,
            extras: {}
          }
          if(type2 === memberTypeEnum.Enterprise) {
            const name = it.name;
            const organCode = it.companyCode;
            cowner.type = 'stockholder';
            cowner.realname = it.legal;
            cowner.extras = {name, organCode}
          }
          members.push(cowner);
        }

        // 如果是驳回后的第二次签约
        let insertCowners = []; // 本次相对上次新增得签约人
        // 判断这次和上次实际需要签约的人，并新增到签约人种
        for (const it of members) {
          const IDCard = it.IDCard
          let cownerObj = oldCownersIdObj[IDCard];
          // 代表本次这个签约人和上次得签约人不同，此次需要新增[判断签约人是否大于上限后再新增，防止创建完签约人合同却创建失败]
          if(!cownerObj) {
            insertCowners.push(it);
            continue;
          }
          delete oldCownersIdObj[IDCard]; // 删除掉两次相同得签约人
        }
        // 因为在合同上已经签约的人不能撤销掉签字，所以新增的签字只能向后排，所以签字数量总共只有预设的20个，也就是签约人最多有20个
        let len = oldCowners.length;
        if(len + insertCowners.length > cownerCountLimit) { // 合同模板只有20个股东（再多格式就乱了）
          throw {
            errorCode: 'ERR_CGEI_56',
            httpCode: 406,
            reason: '合同签约人已超过签约人数，创建失败',
          }
        }

        // 此时剩下得是以前需要签约，但本次不需要签约得, 将其删除, 并删除三者对应关系
        for (const k in oldCownersIdObj) {
          if (Object.hasOwnProperty.call(oldCownersIdObj, k)) {
            const v = oldCownersIdObj[k];
            await contractOwnersV2Data.putById(v._id, { archived: true }); // 删除掉签约人
          }
        }

        // 合同不需要签约人，此时 合同未创建|流未创建
        if(insertCowners.length === 0) {
          if(oldCowners.length === 0) {
            await loanApplicationData.putById(aId, { status: 'wait_investigation_verify_1', lastModTime: new Date() })
            return done();
          }
        }

        // 新增未添加过的本次需要签约的签约人
        for (const it of insertCowners) {
          const type = it.type;
          // 创建或获得e签宝账户id，没有就新增，有就获取（唯一键：IDCard）
          let eSignRes = await eSignSvc.createUser({
            "name": it.realname,
            "idNo": it.IDCard,
            "idType": "19",
          })
          it.eSignAccountId = eSignRes.accountId;
          if(type !== 'cowner') { // 企业
            const organInfo = await eSignSvc.createOrganizeCommon({
              creatorId: eSignRes.accountId,
              name: it.extras.name,
              organCode: it.extras.organCode,
              organType: 11, // 默认是 11统一社会信用代码,
              legalName: it.realname
            })
            it.eSignOrgId = organInfo.accountId;
          }
          it.IDCard = upperCase(it.IDCard);
          const cownerObj = await contractOwnersV2Data.post(it);
          it._id = cownerObj._id;
        }
        
        /** -----   判断合同是否已经创建过   ----- */
        let contractTmp = await contractTemplatesData.getOneByCondition({ type: templateType, archived: false });
        if (!contractTmp || !contractTmp.eTempId) {
          throw {
            errorCode: 'E_FOR_ENTRUST_CTRC_043',
            httpCode: 406,
            reason: 'invalid contract template'
          }
        }
        let contract = await contractsData.getOneByCondition({ 
          tId: contractTmp._id,
          loanId: aId,
          type: templateType
        });
        if(!contract) {
          let simpleFormFields = {
              [`${prefix}_1`]: newCollectInfo.content.companyBasic.companyName,
              [`${prefix}_2`]: moment(meetingTime).get('year'),
              [`${prefix}_3`]: moment(meetingTime).get('month'),
              [`${prefix}_4`]: moment(meetingTime).get('date'),
              [`${prefix}_5`]: meetingPlace,
              [`${prefix}_6`]: '', // 参会人员
              [`${prefix}_7`]: emcee,
              [`${prefix}_8`]: requireArrivePeople,
              [`${prefix}_9`]: realArrivePeople,
              [`${prefix}_10`]: loanInfo.verifyInfo.fund.text, // 资金方（fund）
              [`${prefix}_100`]: amount,
              [`${prefix}_31`]: new Date().getFullYear(),
              [`${prefix}_32`]: new Date().getMonth() + 1,
              [`${prefix}_33`]: new Date().getDate()
            };
          let nameStr = '';
          for (const it of meetMembers) {
            if(it.type === memberTypeEnum.Person) {
              nameStr += it.name;
            } else {
              nameStr += it.legal
            }
          }
          simpleFormFields[`${prefix}_6`] = nameStr;
          let payload = {
            "templateId": contractTmp.eTempId,
            "name": contractName,
            "simpleFormFields": simpleFormFields
          }
          const sSignContract = await eSignSvc.createDocByTemplate(payload);
          if (!sSignContract) {
            throw {
              errorCode: 'C_CREDIT_ACCESS_56',
              httpCode: 406,
              reason: 'CA contract is Create fail',
            }
          }
          // 创建好合同
          contract = await contractsData.post({
            tId: contractTmp._id,
            loanId: aId,
            uId,
            type: templateType,
            name: contractName,
            signatory:  2,
            eDocId: sSignContract.docId,
            eDocUrl: sSignContract.docUrl,
          })
        }

        // 如果此次新增签约人为0，原签约人都已签约（相当于签约人没有变化）
        // 考虑失败情况：如果签约任务数量和所有以前签约人数不相等，不予通过【证明之前签约任务逻辑失败，没有为签约人成功创建签约任务】
        let isTaskMatch = true;
        let isSignerMatch = true;

        // 判断singer之前是否有创建失败的
        let oldSigners = [];
        if(insertCowners.length === 0) {
          oldSigners = await contractSignersV2Data.getByCondition({ cId: contract._id, aId: aId })
          if(!oldSigners || !oldCowners || oldSigners.length !== oldCowners.length) {
            isSignerMatch = false;
          }
        }
        
        for (const it of oldSigners) {
          const _id = it._id;
          const cownerId = it.cownerId;
          for (const k in oldCownersIdObj) {
            if (Object.hasOwnProperty.call(oldCownersIdObj, k)) {
              const v = oldCownersIdObj[k];
              if(v._id === cownerId && it.archived === false) {
                delete oldCownersIdObj[k];
                await contractSignersV2Data.putById(_id, { archived: true })
              }
            }
          }
        }

        /** ----   创建签署流   ----- */
        let flow = await contractFlowsV2Data.getOneByCondition({
          cId: contract._id,
          loanId: aId,
          archived: false
        });
        if(!flow) {
          let flowRes = await eSignSvc.createMoreContractFlow({
            businessScene,
            signPlatform: '1',
            docList: [{
              docId: contract.eDocId,
              docName: contract.name
            }],
            // https://nat.natapp4.cc  ${config.get('baseUrl')}
            noticeUrl: `${config.get('cxwqBaseUrl')}${CALLBACK_URL}`
          });
          if (!flowRes) {
            throw {
              errorCode: 'E_CREATE_FLOW_042',
              httpCode: 406,
              reason: 'eSignFlow is fail'
            }
          }
          flow = await contractFlowsV2Data.post({
            loanId: aId,
            cId: [contract._id],
            type: 6, // 喜农淘授权委托书
            eFlowId: flowRes.flowId,
            eBuzScene: '喜农淘股东会议决议'
          })
          if (!flow) {
            throw {
              errorCode: 'E_CREATE_FLOW_042',
              httpCode: 406,
              reason: 'contractFlow is fail'
            }
          }
        }

        // 判断tasks之前是否有创建失败的
        let oldTasks = [];
        let flowNew = 0;
        if(insertCowners.length === 0) {
          oldTasks = await contractFlowSignTasksV2Data.getByCondition({eFlowId: flow.eFlowId, $sort: { createdTime: 1 }});
          if(oldTasks && oldCowners && oldTasks.length === oldCowners.length) {
            let allFinished = true;
            for (const it of oldTasks) {
              const status = it.status;
              if(status !== SIGN_STATUS_SUCCESS) {
                allFinished = false;
              }
            }
            if(allFinished) {
              let flow2 = await contractFlowsV2Data.putById(flow._id, { status: 2, lastModTime: new Date() })
              flowNew = flow2.status;
              await loanApplicationData.putById(aId, { status: 'wait_investigation_verify_1', lastModTime: new Date() })
              return done();
            }
          } else{
            isTaskMatch = false;
          }
        }
        // 之前状态为已完成，并且这次判定为未完成，修改签署流状态
        if(flow.status === 2 && !flowNew) {
          await contractFlowsV2Data.putById(flow._id, { status: 1, lastModTime: new Date() })
        }

        /** -----   创建关联订单 | 合同 | 签约人关系, 并处理增删改得关系   ----- */
        for (const it of insertCowners) {
          await contractSignersV2Data.post({
            cownerId: it._id,
            aId: aId,
            cId: contract._id
          })
        }

        // 如果不匹配，补充以前未创建得signer
        if(!isSignerMatch) {
          const oldSigners = await contractSignersV2Data.getByCondition({
            cId: contract._id,
            aId
          })
          for (const cowner of oldCowners) {
            let exist = false;
            for (const signer of oldSigners) {
              if(cowner._id === signer.cownerId) {
                exist = true;
              }
            }
            if(!exist) {
              await contractSignersV2Data.post({
                cownerId: cowner._id,
                aId: aId,
                cId: contract._id
              })
            }
          }
        }

        let arr = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十"];
        /** -----   创建签约任务   ----- */
        if(!isTaskMatch) {
          for (const cowner of oldCowners) {
            let exist = false;
            for (const task of oldTasks) {
              if(cowner.eSignAccountId === task.accountId) {
                exist = true;
                break;
              }
            }
            if(!exist) {
              insertCowners.push(cowner)
            }
          }
        }
        let num2 = len;
        for (const it of insertCowners) {
          const type = it.type;
          let keyStr = keyStrPrefix + arr[num2++] + '（电子签名';
          let task = uuid.sync(24);
          // 远程创建
          let eSignCond = {
            flowId: flow.eFlowId,
            thirdOrderNo: task,
            accountId: it.eSignAccountId,
            signDocList: [{
              docId: contract.eDocId,
              posList: [{
                signType: 1,
                key: keyStr,
                posX: 150
              }]
            }]
          };
          let flowTaskRes = {};
          if(type !== 'cowner') {
            eSignCond.authorizationOrgId = it.eSignOrgId;
            flowTaskRes = await eSignSvc.manualMoreSignContractFlowTaskOrg(eSignCond);
          } else {
            flowTaskRes = await eSignSvc.manualMoreSignContractFlowTask(eSignCond);
          }
          debug(method, '[createContractFlowESign]', flowTaskRes);
          // 本地创建
          let contractFlowTask = {
            fId: flow._id,
            cownerId: it._id,
            eFlowId: flow.eFlowId,
            taskId: task,
            accountId: it.eSignAccountId,
            status: 1,
            signUrl: flowTaskRes.signUrl,
            signShortUrl: flowTaskRes.signShortUrl
          };
          await contractFlowSignTasksV2Data.post(contractFlowTask);
        }

        self.context.result = { data: 'success' };
        debug(method, '[Exit](success)', self.context.result);
        return done();
      }
     } catch (error) {
       debug.error(method, '[Exit](failed)', error);
       return done(error);
     }
   }
 
   undoAsync(done) {
     done();
   }
 }

  // 将字符串的字符全部转换为大写字符
  function upperCase(str) {
    let arr = str.split("");
    let newStr = "";
    // 通过数组的forEach方法来遍历数组
    arr.forEach(function (value) {
        if (value >= 'a' && value <= 'z')
            newStr += value.toUpperCase();
        else
            newStr += value;
    });
    return newStr;
  }
   
 
 module.exports = Handler;
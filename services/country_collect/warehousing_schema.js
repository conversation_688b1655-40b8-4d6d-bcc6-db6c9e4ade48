const Joi = require('joi');
const debug = require('redis/lib/debug');

//仓储企业基本情况
const schema = Joi.object({
  //乡镇基本信息
  basic: Joi.object({
    year: Joi.string().required().description('采集年度'),
    city: Joi.object({ code: Joi.string().required().description('市地区编码'), detail: Joi.string().required().description('文本地址'), }),
    county: Joi.object({ code: Joi.string().required().description('县地区编码'), detail: Joi.string().required().description('文本地址'), }),
    village: Joi.object({ code: Joi.string().required().description('镇地区编码'), detail: Joi.string().required().description('文本地址'), }),
    areacode: Joi.string().required().description('地区编码'),
    cooperativeName: Joi.string().required().description('企业名称'),
    companyId: Joi.string().required().description('统一社会信用代码'),
    warehousingNature: Joi.string().required().description('仓库属性 1:自有 2:租赁'),
    address: Joi.object({
      areaCode: Joi.string().description('地址code'),
      address: Joi.string().description('地址文字描述'),
    }),
    detailAddress: Joi.string().description('详细地址')
  }).required().description('乡镇基本信息'),
  //库容
  capacity: Joi.object({
    strandard: Joi.number().required().description('标准库'),
    normal: Joi.number().required().description('一般库'),
  }).required(),
  //年末仓储量
  yearEndStorage: Joi.object({
    year: Joi.number().required().description('仓储年度'),
    self: Joi.number().required().description('自储'),
    country: Joi.number().required().description('国储'),
    other: Joi.number().required().description('其它代储'),
    dailyDryingCapacity: Joi.number().required().description('日烘干能力'),
    isSelected: Joi.boolean().required().description('是否精选'),
    hasSpecialLine: Joi.boolean().required().description('有无专用线'),
    trafficCondition: Joi.string().required().description('交通状况: 1 好  2 中  3 差'),
    legalPersonName: Joi.string().required().description("实控人"),
    legalPersonPhone: Joi.string().pattern(new RegExp('^1\\d{10}$')).required().description("实控人联系电话"),
    comment: Joi.string().description('备注')
  }).required(),
  storageVarieties: Joi.array().items(Joi.object({
    type: Joi.string().description('仓储品种: 1 玉米 2 水稻 3 大豆'),
    capacity: Joi.number().required().description('容量')
  })).required(),
  comment: Joi.string().description('备注')
})


function getRepeatKey(id, collect_info) {
  let repeatKeys = [];
  if (collect_info.basic) {
    repeatKeys.push({ "type": "16", "content.basic.areacode": collect_info.basic.areacode, "content.basic.year": collect_info.basic.year, "content.basic.companyId": collect_info.basic.companyId });
  }
  if (id) {
    repeatKeys.forEach(item => {
      item._id = { $ne: id };
    })
  }
  console.log(`getRepeatKey:`, JSON.stringify(repeatKeys));
  return repeatKeys
}

module.exports = {
  schema: schema,
  getRepeatKey: getRepeatKey
}
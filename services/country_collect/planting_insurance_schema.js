const Joi = require('joi');
const debug = require('redis/lib/debug');

const insurance_schema = Joi.object({
  //大灾险
  catastrophe: Joi.object({
    premium: Joi.number().required().description('保费'),
    insuranceRate: Joi.number().required().description('保险比例'),
    content: Joi.string().required().description('保费内容'),
    CompensationRate: Joi.number().required().description('赔偿率'),
  }).required(),
  //完全成本险
  fullCost: Joi.object({
    premium: Joi.number().required().description('保费'),
    insuranceRate: Joi.number().required().description('保险比例'),
    content: Joi.string().required().description('保费内容'),
    CompensationRate: Joi.number().required().description('赔偿率'),
  }).required(),
  //收入险
  income: Joi.object({
    premium: Joi.number().required().description('保费'),
    insuranceRate: Joi.number().required().description('保险比例'),
    content: Joi.string().required().description('保费内容'),
    CompensationRate: Joi.number().required().description('赔偿率'),
  }).required(),
  //完全成本险2
  fullCost2: Joi.object({
    premium: Joi.number().required().description('保费'),
    insuranceRate: Joi.number().required().description('保险比例'),
    content: Joi.string().required().description('保费内容'),
    CompensationRate: Joi.number().required().description('赔偿率'),
  }).required(),
  comment: Joi.string().description('备注')
})

//种植成本采集
const schema = Joi.object({
  //乡镇基本信息
  basic: Joi.object({
    year: Joi.string().required().description('采集年度'),
    city: Joi.object({ code: Joi.string().required().description('市地区编码'), detail: Joi.string().required().description('文本地址'), }),
    county: Joi.object({ code: Joi.string().required().description('县地区编码'), detail: Joi.string().required().description('文本地址'), }),
    village: Joi.object({ code: Joi.string().required().description('镇地区编码'), detail: Joi.string().required().description('文本地址'), }),
    areacode: Joi.string().required().description('地区编码'),
    // temperateZone: Joi.string().required().description('积温带: 1:第一积温带 2:一下二上积温带 3:第二积温带 4:二上三下积温带 5:第三积温带 6:三上四下积温带 7:第四积温带 8:四上五下积温带 9:第五积温带 10:五上六下积温带 11:第六积温带')
  }).description('乡镇基本信息'),
  //玉米种植保险
  corn: insurance_schema,
  //大豆种植保险
  bean: insurance_schema,
  //水稻种植保险
  rice: insurance_schema,
  //其他种植保险
  grain: insurance_schema,
})


function getRepeatKey(id, collect_info) {
  let repeatKeys = [];
  if (collect_info.basic) {
    repeatKeys.push({ "type": "13", "content.basic.areacode": collect_info.basic.areacode, "content.basic.year": collect_info.basic.year });
  }
  if (id) {
    repeatKeys.forEach(item => {
      item._id = { $ne: id };
    })
  }
  console.log(`getRepeatKey:`, JSON.stringify(repeatKeys));
  return repeatKeys
}

module.exports = {
  schema: schema,
  getRepeatKey: getRepeatKey
}
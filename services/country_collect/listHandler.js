/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'listHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const {
  employees: employeeData,
  countryCollect: countryCollect,
} = require('../dataSvc/dataUtil');
const moment = require('moment');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input, opts } = this.context;
      if (opts.roleId == "5eb8ff2ec6ecfe44d4ecaed9") {
        input.operator = opts.userid;
      } else {
        input.$or = [
          { operator: opts.userid },
          { status: 1 }
        ];
      }
      let result = await countryCollect.getListAndCountByCondition(input);
      for (const item of result.result) {
        item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss');
        item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
        if (item.operator) {
          item.operatorUser = await getUserName(item.operator);
          item.operatorName = item.operatorUser && item.operatorUser.username || '';

        }
      }
      debug(method, '[Exit](success)');
      this.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}


async function formatImg(item) {
  item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0 &&
    (item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url }));
  item && item.image && item.image.url && item.image.url.indexOf('http') !== 0 &&
    (item.image.url = await aliOssSvc.getFile({ fileName: item.image.url }));
}

async function getUserName(uId) {
  try {
    let user = await employeeData.getOneByCondition({
      _id: uId,
      "archived": false
    });
    return user;
  } catch (ex) {}
}


module.exports = Handler;
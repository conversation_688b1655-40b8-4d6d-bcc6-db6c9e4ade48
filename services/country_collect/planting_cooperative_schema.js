const Joi = require('joi');
const debug = require('redis/lib/debug');

//种植合作社基本情况
const schema = Joi.object({
  //乡镇基本信息
  basic: Joi.object({
    year: Joi.string().required().description('采集年度'),
    city: Joi.object({ code: Joi.string().required().description('市地区编码'), detail: Joi.string().required().description('文本地址'), }),
    county: Joi.object({ code: Joi.string().required().description('县地区编码'), detail: Joi.string().required().description('文本地址'), }),
    village: Joi.object({ code: Joi.string().required().description('镇地区编码'), detail: Joi.string().required().description('文本地址'), }),
    areacode: Joi.string().required().description('地区编码'),
    cooperativeName: Joi.string().required().description('合作社名称'),
    companyId: Joi.string().required().description('统一社会信用代码'),
    cooperativeNature: Joi.string().required().description('合作社性质 1:农民合作社 2:村集体合作社'),
    address: Joi.object({
      areaCode: Joi.string().description('地址code'),
      address: Joi.string().description('地址文字描述'),
    }),
    detailAddress: Joi.string().description('详细地址'),
    businessTerm: Joi.string().required().description("营业期限"),
    registeredCapital: Joi.number().required().description("注册资本（元）"),
    legalPersonName: Joi.string().required().description("实控人"),
    legalPersonPhone: Joi.string().pattern(new RegExp('^1\\d{10}$')).required().description("实控人联系电话"),
  }).required().description('乡镇基本信息'),
  //种植面积
  plantingArea: Joi.object({
    self: Joi.number().required().description('自耕'),
    entrust: Joi.number().required().description('委托代耕'),
  }).required(),
  //资金来源
  sourcesOfFunds: Joi.object({
    bank: Joi.number().required().description('银行融资'),
    popular: Joi.number().required().description('民间借贷'),
    self: Joi.number().required().description('自筹资金')
  }).required(),
  //政策补贴
  subsidy_schema: Joi.object({
    list: Joi.array().items(Joi.object({
      subsidyType: Joi.string().required().description('补贴类型'),
      subsidyAmount: Joi.number().required().description("补贴金额（元）"),
    })).required(),
    sum: Joi.number().required().description("补贴总金额"),
  }).required(),
  comment: Joi.string().description('备注')
})


function getRepeatKey(id, collect_info) {
  let repeatKeys = [];
  if (collect_info.basic) {
    repeatKeys.push({ "type": "15", "content.basic.areacode": collect_info.basic.areacode, "content.basic.year": collect_info.basic.year, "content.basic.companyId": collect_info.basic.companyId });
  }
  if (id) {
    repeatKeys.forEach(item => {
      item._id = { $ne: id };
    })
  }
  console.log(`getRepeatKey:`, JSON.stringify(repeatKeys));
  return repeatKeys
}

module.exports = {
  schema: schema,
  getRepeatKey: getRepeatKey
}
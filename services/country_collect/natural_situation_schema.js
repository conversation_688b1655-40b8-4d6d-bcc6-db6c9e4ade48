const Joi = require('joi');
const debug = require('redis/lib/debug');

//乡镇土壤自然情况采集
const schema = Joi.object({
  //乡镇基本信息
  basic: Joi.object({
    year: Joi.string().required().description('采集年度'),
    city: Joi.object({ code: Joi.string().required().description('市地区编码'), detail: Joi.string().required().description('文本地址'), }),
    county: Joi.object({ code: Joi.string().required().description('县地区编码'), detail: Joi.string().required().description('文本地址'), }),
    village: Joi.object({ code: Joi.string().required().description('镇地区编码'), detail: Joi.string().required().description('文本地址'), }),
    areacode: Joi.string().required().description('地区编码'),
    // temperateZone: Joi.string().required().description('积温带')
  }).required().description('乡镇基本信息'),
  //测土配方
  soil: Joi.object({
    N: Joi.number().required().description('*N'),
    P: Joi.number().required().description('*P'),
    K: Joi.number().required().description('*K'),
    threeYearPrecipitation: Joi.number().required().description('近三年年均降水量')
  }).required(),
  //自然灾害类型
  disasterType: Joi.object({
    hail: Joi.number().required().description('冰雹'),
    drought: Joi.number().required().description('旱灾'),
    pestInfestation: Joi.number().required().description('虫灾'),
    flood: Joi.number().required().description('洪涝'),
    lodging: Joi.number().required().description('倒伏'),
  }).required(),
})


function getRepeatKey(id, collect_info) {
  let repeatKeys = [];
  if (collect_info.basic) {
    repeatKeys.push({ "type": "14", "content.basic.areacode": collect_info.basic.areacode, "content.basic.year": collect_info.basic.year });
  }
  if (id) {
    repeatKeys.forEach(item => {
      item._id = { $ne: id };
    })
  }
  console.log(`getRepeatKey:`, JSON.stringify(repeatKeys));
  return repeatKeys
}

module.exports = {
  schema: schema,
  getRepeatKey: getRepeatKey
}
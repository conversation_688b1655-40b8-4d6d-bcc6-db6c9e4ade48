const Joi = require('joi');
const debug = require('redis/lib/debug');
const {
  countryCollect: countryCollect,
} = require('../dataSvc/dataUtil');

const year_value_schema = Joi.object({
  value: Joi.number().required().description('数值'),
  // list: Joi.array().items(Joi.object({
  //   year: Joi.string().required().description('年度'),
  //   value: Joi.number().required().description('数值')
  // })).required(),
  // average: Joi.number().required().description('平均值'),
  // sum: Joi.number().required().description('总和'),
});

const planting_schema = Joi.object({
  //土地流转成本
  landTransfer: year_value_schema,
  //生资成本
  planting: Joi.object({
    "seed": Joi.number().required().description('种子'),
    "fertilizer": Joi.number().required().description('化肥'),
    "pesticide": Joi.number().required().description('农药'),
    "machine": Joi.number().required().description('机耕成本'),
    "insurance": Joi.number().required().description('保险成本'),
    "other": Joi.number().required().description('其他费用'),
  }).required(),
  comment: Joi.string().description('备注')
})

//种植成本采集
const schema = Joi.object({
  //乡镇基本信息
  basic: Joi.object({
    year: Joi.string().required().description('采集年度'),
    city: Joi.object({ code: Joi.string().required().description('市地区编码'), detail: Joi.string().required().description('文本地址'), }),
    county: Joi.object({ code: Joi.string().required().description('县地区编码'), detail: Joi.string().required().description('文本地址'), }),
    village: Joi.object({ code: Joi.string().required().description('镇地区编码'), detail: Joi.string().required().description('文本地址'), }),
    areacode: Joi.string().required().description('地区编码'),
    temperateZone: Joi.string().required().description('积温带: 1:第一积温带 2:一下二上积温带 3:第二积温带 4:二上三下积温带 5:第三积温带 6:三上四下积温带 7:第四积温带 8:四上五下积温带 9:第五积温带 10:五上六下积温带 11:第六积温带')
  }).description('乡镇基本信息'),
  //玉米种植成本
  corn: planting_schema,
  //大豆种植成本
  bean: planting_schema,
  //水稻种植成本
  rice: planting_schema,
  //杂粮种植成本
  grain: planting_schema,
  //经济作物种植成本
  cashCrops: planting_schema,
  //中草药种植成本
  medicine: planting_schema
})


function getRepeatKey(id, collect_info) {
  let repeatKeys = [];
  if (collect_info.basic) {
    repeatKeys.push({ "type": "18", "content.basic.areacode": collect_info.basic.areacode, "content.basic.year": collect_info.basic.year });
  }
  // ['corn', 'bean', 'rice', 'grain', 'cashCrops', 'medicine'].forEach(item => {
  //   if (collect_info[item]) {
  //     ['landTransfer'].forEach(item1 => {
  //       if (collect_info[item][item1]) {
  //         collect_info[item][item1].list.forEach(item3 => {
  //           let repeatKey = {};
  //           repeatKey.type = "12";
  //           repeatKey[`content.basic.areacode`] = collect_info.basic.areacode;
  //           repeatKey[`content.${item}.${item1}.list`] = { $elemMatch: { year: item3.year } };
  //           repeatKeys.push(repeatKey);
  //         });
  //       }
  //     });
  //   }
  // });
  if (id) {
    repeatKeys.forEach(item => {
      item._id = { $ne: id };
    })
  }
  console.log(`getRepeatKey:`, JSON.stringify(repeatKeys));
  return repeatKeys
}

module.exports = {
  schema: schema,
  getRepeatKey: getRepeatKey
}
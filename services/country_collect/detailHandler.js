/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'listHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const {
  countryCollect: countryCollect,
} = require('../dataSvc/dataUtil');
const moment = require('moment');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input } = this.context;
      let item = await countryCollect.getById(input.id);
      item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss');
      item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
      item.operatorInfo = await getUserName(item.operator);
      debug(method, '[Exit](success)');
      this.context.result = item;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

async function getUserName(uId) {
  try {
    let user = await employeeData.getOneByCondition({
      _id: uId,
      "archived": false
    });
    return { username: user.username, mobile: user.mobile };
  } catch (ex) {

  }
}


module.exports = Handler;
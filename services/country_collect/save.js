'use strict';

const HANDLER_NAME = 'saveCountryCollectHandler';
const Joi = require('joi');
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:info_collect_draft:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  countryCollect: countryCollect, employees,
} = require('../dataSvc/dataUtil');

const planting_status_schema = require('./planting_status_schema');
const planting_cost_schema = require('./planting_cost_schema');
const planting_insurance_schema = require('./planting_insurance_schema');
const natural_situation_schema = require('./natural_situation_schema');
const planting_cooperative_schema = require('./planting_cooperative_schema');
const warehousing_schema = require('./warehousing_schema');
const planting_status_schema_v2 = require('./planting_status_schema_v2');
const planting_cost_schema_v2 = require('./planting_cost_schema_v2');

//采集类型： 11 种植情况 12 种植成本 13 种植保险 14 土壤 15 县域种植合作社 16 县域仓储企业 17 种植情况v2版本 18 种植成本v3
const content_schema_enums = {
  "11": planting_status_schema,
  "12": planting_cost_schema,
  "13": planting_insurance_schema,
  "14": natural_situation_schema,
  "15": planting_cooperative_schema,
  "16": warehousing_schema,
  "17": planting_status_schema_v2,
  "18": planting_cost_schema_v2,
}
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]', JSON.stringify(self.context.input));
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let content = input.content;
      delete input.status;
      delete input.approveStatus;
      delete input.comment;
      input.approveStatus = 3;
      input.status = 2;
      let id = input.id;
      let collectInfo;
      let schema = content_schema_enums[input.type];
      if (!schema) {
        throw {
          errorCode: 'E_VALID_ERROR',
          httpCode: 401,
          reason: 'collect type 错误'
        }
      }
      const { error, value } = schema.schema.validate(content);
      if (error) {
        debug(method, '[valid error]', error.toString());
        throw {
          errorCode: 'E_VALID_ERROR',
          httpCode: 401,
          reason: error.toString()
        }
      }
      //验证重复的数据
      if (schema.getRepeatKey) {
        console.log('getRepeatKey1111')
        await validateRepeat(schema.getRepeatKey(id, content));
      }
      delete input.id;
      input.operator = opts.userid;
      input.lastModTime = new Date();

      if (id) {
        collectInfo = await countryCollect.putById(id, input);
      } else {
        input.createdTime = new Date();
        input.archived = false;
        collectInfo = await countryCollect.post(input);
      }
      self.context.result = collectInfo;
      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }


  undoAsync(done) {
    done()
  }
}


//验证 地区-年份-种类 是否已经采集
async function validateRepeat(repeatKeys) {
  await Promise.all(repeatKeys.map(async repeatkey => {
    let exist = await countryCollect.getOneByCondition(repeatkey);
    const {username} = exist && exist.operator && await employees.getById( exist.operator ) || {}
    if (exist) {
      throw {
        errorCode: 'E_VALID_ERROR',
        httpCode: 402,
        reason: [username && `当前${username}正在采集信息，保存失败！`,'存在重复的采集记录', ].find(v=>v)
      }
    }
  }))
}

module.exports = Handler;
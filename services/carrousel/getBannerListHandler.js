/*
 * @Author: wcy 
 * @Date: 2018-11-23 15:29:21 
 * @Last Modified by: wcy
 * @Last Modified time: 2018-12-10 11:37:57
 */


'use strict';

const HANDLER_NAME = 'getBannerListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:carrousel:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const carrouselsData = require('../dataSvc/dataUtil').carrousels;
const config = require('config')
const IMAGE_BASE_URL = config.get("repoServer").host;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let result = await carrouselsData.getListAndCountByCondition(condition);
      for (const item of result.result) {
        if (item.image && item.image.indexOf('http') !== 0) {
          item.image = IMAGE_BASE_URL.concat(item.image);
        }
      }

      self.context.result = result
      debug(method, '[Exit](success)', result)
      return done();

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
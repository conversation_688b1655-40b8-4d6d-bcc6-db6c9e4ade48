/*
 * @Author: wcy 
 * @Date: 2018-11-23 15:26:46 
 * @Last Modified by: wcy
 * @Last Modified time: 2018-12-07 12:53:55
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:routes:carrousel:services:carrousel:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;

const GetBannerListHandler = require('./getBannerListHandler');

class Service {
  constructor() {

  }

  async getBannerList(input, _opts) {
    let method = 'getBannerList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetBannerListHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();
/**
 * <AUTHOR>
 * 2019-05-05  
 */

'use strict';

const HANDLER_NAME = 'getOrganizationHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:loan_charts:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeeData = require('../dataSvc/dataUtil').employees;
const groupV2Data = require('../dataSvc/dataUtil').groupsV2;
const employeeGroupData = require('../dataSvc/dataUtil').employeeGroups;
const groupData = require('../dataSvc/dataUtil').groups;
const formatAreaCode = require('../../persistence/formatAreaCode');
const tenantsData = require('../dataSvc/dataUtil').tenants;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let employeeInfo = await employeeData.getById(opts.uId, {cache : true , expire: 24 * 60 * 60 });
      if (!employeeInfo || !employeeInfo.tId) {
        throw {
          errorCode: 'E_organization_List_36',
          httpCode: 406,
          reason: '用户信息不全'
        }
      }
      let condition = {
        tId: employeeInfo.tId,
        archived: false
      }
      const tenants = await tenantsData.getById(employeeInfo.tId, {cache : true , expire: 24 * 60 * 60 });
      if (input.code === "root") {
        condition.code = `/^.{3}$/`
      } else {
        condition.code = `/^${input.code}\\d{3}$/`
      }
      let organization = await groupV2Data.getByCondition(condition);
      let employee = null;
      if (input.code !== "root") {
        // 查到当前的机构
        let orgInfo = await groupV2Data.getOneByCondition({
          code: input.code,
          tId: employeeInfo.tId,
          archived: false
        });
        employee = await employeeData.getByUrl('/v1.0/employee/tId/group/list', {
          tId: employeeInfo.tId,
          groupV2: orgInfo._id,
          limit: "unlimited"
        });
        let promises = [];
        for (const item of employee.result) {
          for (const group of item.groups) {
            promises.push(formatAreaCode.getFormatAreaCode(group.areaList[0]).then(data => {
              group.areaName = data.area
            }))
          }
        }
        await Promise.all(promises);
      }

      let result = {
        tenants: tenants,
        organization: organization,
        employee: employee && employee.result || []
      }
      self.context.result = result
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
/*
 * @Description: 测产详情
 * @Author: zhu xue song
 * @Date: 2021-07-27 15:24:48
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-07-29 09:21:10
 * @FilePath: \rongxin.loan.user.app.api\services\estimatedOutput\getEstimatedOutput.js
 */
'use strict';

const HANDLER_NAME = 'getEstimatedOutput';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.user.app.api:services:estimatedOutput:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const estimatedOutputData = require('../dataSvc/dataUtil').estimatedOutput;
const moment = require('moment');
const constant = require('./constant');

const LandTypeEnum = {
  Identity: 1, // 身份地
  Exchange: 2, // 流转地
  Manage: 3 // 经营地
}

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;

      const govList = constant.govList;

      const result = await estimatedOutputData.getById(condition._id);
      const gov_sn = result.gov_sn;
      if(gov_sn) {
        for (const gov of govList) {
          if(gov_sn === gov.gov_sn) {
            result.gov_sn_str = gov.name
          }
        }
      }
      result.is_staff_str = result.is_staff === 0 ? '非职工' : '职工';
      result.landTypeStr = getLandTypeStr(result.landType);
      result.createdTime = moment(result.createdTime).format('YYYY-MM-DD HH:mm:ss');
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

function getLandTypeStr(landType) {
  let landTypeStr = '未确权地';
  if(landType === LandTypeEnum.Identity) {
    landTypeStr = '身份地';
  } else if(landType === LandTypeEnum.Exchange) {
    landTypeStr = '流转地';
  } else if(landType === LandTypeEnum.Manage) {
    landTypeStr = '经营地';
  }
  return landTypeStr;
}

module.exports = Handler
/*
 * @Description: 测产列表
 * @Author: zhu xue song
 * @Date: 2021-07-27 15:24:48
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-07-29 22:34:42
 * @FilePath: \rongxin.loan.user.app.api\services\estimatedOutput\listEstimatedOutput.js
 */
'use strict';

const HANDLER_NAME = 'listEstimatedOutput';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.user.app.api:services:estimatedOutput:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  estimatedOutput: estimatedOutputData,
  employeeGroups: employeeGroupData,
} = require('../dataSvc/dataUtil');
const moment = require('moment');
const {getEmployeeLimit} = require('../../utils/getUserFromReq');
const { StatusEnum, CheckStageEnum, StatusStrEnum, tenant } = require('./constant');

const LandTypeEnum = {
  Identity: 1, // 身份地
  Exchange: 2, // 流转地
  Manage: 3, // 经营地
};

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      const opts = self.context.opts;
      
      // 判断当前人是否为连队审核员，如果是就审核所有2阶段的未审核的单子
      const { userid, roleId } = opts;
      // 如果是属于60861f1ca1b6d7938920f310, 新疆服务商-建设兵团的租户权限（tenant）
      const info = await getEmployeeLimit(employeeGroupData, tenant.XinjiangServiceProvider, userid, 'areaCode', roleId);
      if(info.length === 0) {
        self.context.result = [];
        debug(method, '[Exit](success)')
        return done();
      }
      

      let condition = {
        archived: input.archived,
        limit: input.limit,
        skip: input.skip,
        $sort: input.$sort,
        $or: []
      };
      for (const it of info) {
        condition.$or.push({ gov_sn: it.areaCode })
      }
      let {gov_sn, createYear, status} = input;

      createYear = createYear ? createYear : createYear = moment().format('YYYY');
      if(gov_sn) {
        condition.gov_sn = gov_sn;
      }
      // if(createTimeStart) {
      //   createTimeStart = new Date(moment(createTimeStart).unix() * 1000);
      //   condition.createdTime = { ...condition.createdTime, $gte: createTimeStart };
      // }
      // if(createTimeEnd) {
      //   createTimeEnd = new Date(moment(createTimeEnd).unix() * 1000);
      //   condition.createdTime = { ...condition.createdTime, $lte: createTimeEnd };
      // }
      if(createYear) {
        condition.createYear = createYear;
      }
      if(status) {
        if(status === 'under_review') {
          condition.status = StatusEnum.Wait;
          condition.checkStage = CheckStageEnum.Center;
        } else if(status === StatusEnum.Wait) {
          condition.checkStage = CheckStageEnum.Gov;
          condition.status = status;
        } else {
          condition.status = status;
        }
      }
      if(gov_sn) {
        condition.gov_sn = gov_sn;
      }

      let res = [];
      let result = await estimatedOutputData.getListAndCountByCondition(condition);
      for (const it of result.result) {
        const status = it.status;
        const checkStage = it.checkStage;
        const statusStr = getStatusStr(checkStage, status);
        res.push({
          _id: it._id,
          name: it.name,
          lands: it.lands,
          areOutput: it.areOutput,
          landOutput: it.landOutput,
          landTypeStr: getLandTypeStr(it.landType),
          createUser: it.createUser,
          createdTime: moment(it.createdTime).format('YYYY-MM-DD HH:mm:ss'),
          checkStage,
          status,
          statusStr
        })
      }
      result.result = res;
      
      self.context.result = result;
      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

/**
 * 获得当前审核阶段审核情况，连队角色的列表对应的状态字符串
 * 
 * @param {number} checkStage 审核阶段
 * @param {string} status 审核情况
 * @returns 
 */
 function getStatusStr(checkStage, status) {
  let statusStr = '';
  if(checkStage === CheckStageEnum.Gov) {
    if(status === StatusEnum.Wait) {
      statusStr = StatusStrEnum.WaitAudit;
    } else if(status === StatusEnum.Reject) {
      statusStr = StatusStrEnum.NotPass;
    }
  } else if(checkStage === CheckStageEnum.Center) {
    if(status === StatusEnum.Wait) {
      statusStr = StatusStrEnum.Auditing;
    } else if(status === StatusEnum.Reject) {
      statusStr = StatusStrEnum.NotPass;
    } else if(status === StatusEnum.Pass) {
      statusStr = StatusStrEnum.Pass;
    }
  }
  return statusStr;
}

function getLandTypeStr(landType) {
  let landTypeStr = '';
  if (landType === LandTypeEnum.Identity) {
    landTypeStr = '身份地';
  } else if (landType === LandTypeEnum.Exchange) {
    landTypeStr = '流转地';
  } else if (landType === LandTypeEnum.Manage) {
    landTypeStr = '经营地';
  }
  return landTypeStr;
}

module.exports = Handler
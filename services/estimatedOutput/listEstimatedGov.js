/*
 * @Description: 连队列表
 * @Author: zhu xue song
 * @Date: 2021-07-27 15:24:48
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-07-29 21:28:44
 * @FilePath: \rongxin.loan.mgr.app.api\services\estimatedOutput\listEstimatedGov.js
 */
'use strict';

const HANDLER_NAME = 'listEstimatedGov';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.user.app.api:services:estimatedOutput:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { tenant, govList } = require('./constant');
const {
  employeeGroups: employeeGroupData,
} = require('../dataSvc/dataUtil');
const {getEmployeeLimit} = require('../../utils/getUserFromReq');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let {roleId} = self.context.input;
      let {userid} = self.context.opts;

      const info = await getEmployeeLimit(employeeGroupData, tenant.XinjiangServiceProvider, userid, 'areaCode', roleId);
      
      let gov_obj = {};
      for (const it of info) {
        const regexStr = it.areaCode.$regex;
        const regex = new RegExp(regexStr);
        for (const it2 of govList) {
          if(regex.test(it2.gov_sn)) {
            gov_obj[it2.gov_sn] = it2;
          }
        }
      }
      let gov_res = [];
      for (const k in gov_obj) {
        if (Object.hasOwnProperty.call(gov_obj, k)) {
          const v = gov_obj[k];
          gov_res.push(v);
        }
      }
      let result = gov_res;
      self.context.result = result;
      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
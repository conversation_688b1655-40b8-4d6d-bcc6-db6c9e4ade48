/*
 * @Description: 审核测产
 * @Author: zhu xue song
 * @Date: 2021-07-27 15:24:48
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-07-28 19:29:00
 * @FilePath: \rongxin.loan.user.app.api\services\estimatedOutput\auditEstimatedOutput.js
 */
'use strict';

const HANDLER_NAME = 'auditEstimatedOutput';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.user.app.api:services:estimatedOutput:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { 
  estimatedOutput: estimatedOutputData,
  employees: employeeData,
  employeeGroups: employeeGroupData,
} = require('../dataSvc/dataUtil');
const moment = require('moment');
const {getEmployeeLimit} = require('../../utils/getUserFromReq');
const { tenant, StatusEnum } = require('./constant');
class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      let status = self.context.input.status;
      const { userid, id } = self.context.opts;
      const user = await employeeData.getOneByCondition({_id: userid});
      let operatorUser = user ? user.username : '';

      // 如果是属于60861f1ca1b6d7938920f310, 新疆服务商-建设兵团的租户权限（tenant）
      const info = await getEmployeeLimit(employeeGroupData, tenant.XinjiangServiceProvider, userid);
      if(info.length === 0) {
        self.context.result = [];
        debug(method, '[Exit](success)');
        return done();
      }
      let selectCondition = {
        _id: id,
        archived: false,
        $or: [],
      };
      for (const it of info) {
        selectCondition.$or.push({ gov_sn: it.areaCode });
      }
      const res = await estimatedOutputData.getOneByCondition(selectCondition);
      if(!res) {
        throw {
          errorCode: 'E_EO_AUDIT_60',
          httpCode: 406,
          reason: '您没有审核权限',
        };
      }

      if (res.checkStage !== 2 || res.status !== StatusEnum.Wait) {
        throw {
          errorCode: 'E_EO_AUDIT_69',
          httpCode: 406,
          reason: '该条记录已审核',
        };
      }

      const update = {
        status,
        lastModTime: new Date(),
        checkStage: 2,
        $push: {},
      };
      let auditStr = '拒绝';
      if(status === StatusEnum.Pass) {
        update.checkStage = 3;
        update.status = StatusEnum.Wait;
        auditStr = '通过';
      }
      update.$push = {
        operatorLogs: {
          $each: [{ operator: operatorUser, operatorId: userid, actions: auditStr, time: moment().format('YYYY-MM-DD HH:mm:ss'), comment: '' }],
          $position: 0,
        },
      };
      const result = await estimatedOutputData.putById(id, update);
      if (!result) {
        throw {
          errorCode: 'E_EO_AUDIT_101',
          httpCode: 406,
          reason: '审核失败',
        };
      }
      const _result = {
        audit: 1,
        status: 'SUCCESS',
      };
      self.context.result = _result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
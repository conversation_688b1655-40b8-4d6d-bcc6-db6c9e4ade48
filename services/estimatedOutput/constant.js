/*
 * @Description: 静态变量
 * @Author: zhu xue song
 * @Date: 2021-07-29 10:53:56
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-07-29 21:28:34
 * @FilePath: \rongxin.loan.user.app.api\services\estimatedOutput\constant.js
 */
module.exports ={
  govList: [
    { gov_sn: "652823502501",	typeCode: "220",	name: "一连" },
    { gov_sn: "652823502502",	typeCode: "123",	name: "二连" },
    { gov_sn: "652823502503",	typeCode: "220",	name: "三连" },
    { gov_sn: "652823502518",	typeCode: "220",	name: "四连" },
    { gov_sn: "652823502504",	typeCode: "220",	name: "五连" },
    { gov_sn: "652823502505",	typeCode: "220",	name: "六连" },
    { gov_sn: "652823502520",	typeCode: "220",	name: "七连" },
    { gov_sn: "652823502506",	typeCode: "220",	name: "八连" },
    { gov_sn: "652823502507",	typeCode: "220",	name: "九连" },
    { gov_sn: "652823502509",	typeCode: "220",	name: "十一连" },
    { gov_sn: "652823502519",	typeCode: "220",	name: "十五连" },
    { gov_sn: "652823502517",	typeCode: "220",	name: "十六连" },
    { gov_sn: "652823502524",	typeCode: "220",	name: "十七连" },
    { gov_sn: "652823502522",	typeCode: "220",	name: "十九连" },
    { gov_sn: "652823502523",	typeCode: "220",	name: "二十连" },
    { gov_sn: "652823502513",	typeCode: "220",	name: "林园连" },
  ],

  StatusEnum: {
    Wait: 'wait', // 待审核
    Reject: 'reject', // 已拒绝
    Pass: 'pass' // 已通过
  },
  
  CheckStageEnum: {
    Gov: 2, // 连队审核
    Center: 3, // 经发办审核
  },
  
  StatusStrEnum: {
    WaitAudit: '待审核',
    Auditing: '审核中',
    NotPass: '未通过',
    Pass: '已通过'
  },

  tenant: {
    XinjiangServiceProvider: '60861f1ca1b6d7938920f310', //新疆服务商-建设兵团
  }
}
  
/**
 * @description: getArea<PERSON>ist<PERSON><PERSON><PERSON>
 * @author: hexu
 */
'use strict'

const HANDLER_NAME = 'getAreaInfoHandler'
const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:dashboard.api:services:area:' + HANDLER_NAME)
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const areasData = require('../dataSvc/dataUtil').dictAreas
const formatAreaCode = require('../../persistence/formatAreaCode');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  } 

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {input:{areaCodes=[]}} = this.context;

      const result = await Promise.all( areaCodes.map(v=> formatAreaCode.getFormatAreaCode(v)) )
      this.context.result = result.map(v=>v.area);
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler

/**
 * @description: area service index
 * @author: hexu
 */

'use strict'

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:dashboard.api:services:area:index')
const Q = require('q')
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetAreaListHandler = require('./getAreaListHandler')
const GetAreaInfoHandler = require('./getAreaInfoHandler')

class Service {
  constructor() {

  }

  getAreaList(input, _opts) {
    let method = 'getAreaList'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    let defer = Q.defer()

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()

      svcHandlerMgrt.addHandler(new getAreaInfoHandler(context))
      svcHandlerMgrt.processAsync(context).then(() => {
        debug(method, '[Exit](success): ', context.result)
        defer.resolve(context.result)
      }).fail((error) => {
        debug.error(method, '[Exit](failed)', error)
        defer.reject(error)
      })
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      defer.reject(error)
    }

    return defer.promise
  }

  getAreaInfo(input, _opts) {
    let method = 'getAreaList'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    let defer = Q.defer()

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt()

      svcHandlerMgrt.addHandler(new GetAreaInfoHandler(context))
      svcHandlerMgrt.processAsync(context).then(() => {
        debug(method, '[Exit](success): ', context.result)
        defer.resolve(context.result)
      }).fail((error) => {
        debug.error(method, '[Exit](failed)', error)
        defer.reject(error)
      })
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      defer.reject(error)
    }

    return defer.promise
  }


}

module.exports = new Service()

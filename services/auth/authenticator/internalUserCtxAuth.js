/**
 * internal user ctx authenticator
 * <AUTHOR>
 */

'use strict';
const CLASS_NAME = 'INTERNAL_USER_CTX_AUTH';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:services:auth:' + CLASS_NAME);
const BaseAuth = require('../baseAuth');

const env = require('../../env');
const SCOPE = require('../../../utils/credentials').jwtScope;

const INTERNAL_USER_CTX_HEADER = 'rongxin-internal-user-ctx';

class Auth extends BaseAuth {
  constructor() {
    super();
  }

  getName() {
    return CLASS_NAME;
  }

  canProcess(req) {
    if (req && req.header(INTERNAL_USER_CTX_HEADER)) {
      return true;
    }

    return false;
  }

  async invoke(req, res) {
    let method = `invoke`;
    debug(method, '[Enter]');

    try {
      let userCtx = req.header(INTERNAL_USER_CTX_HEADER);
      userCtx = JSON.parse(userCtx);

      let scope = env.getServerMode() === 'production' ? SCOPE : `${env.getServerMode()}:${SCOPE}`;
      debug(method, 'SCOPE: ', scope, 'userCtx', userCtx.scope, 'ENV', env.getServerMode());

      if (userCtx.scope !== scope || userCtx.owt !== 'user')
        throw {
          errorCode: 'EAUTH_TOKEN_ILLEGAL_ENV',
          httpCode: 401,
          reason: 'Illegal Token Environment'
        };

      let profile = {
        userid: userCtx.sub,
        token: userCtx.token,
        client: userCtx.app
      }

      req.user = profile;
      debug(method, '[Exit](success)', profile);
      return profile;
    } catch (error) {
      debug.error(`current req can't authenticated by ${CLASS_NAME}, due to `, error);
      if (error.httpCode && error.httpCode !== 500)
        res.set('Warning', `199 - ${error.errorCode || 'EAUTH_TOKEN_ILLEGAL'} ${new Date()}`)
      throw error;
    }
  }
}

module.exports = Auth;
/**
 * jwt authenticator
 * <AUTHOR>
 * 2018-03-22
 */

const BaseAuth = require('../baseAuth')
const CLASS_NAME = 'JWTAuth'
const logFactory = require('../../../utils/logFactory')
const logUtil = require('../../../utils/logUtil')
const debug = logFactory(logUtil())(`rongxin:loan:mgr:app:apiservices:auth:${CLASS_NAME}`)
const jwt = require('jsonwebtoken')
const credentials = require('../../../utils/credentials')

const env = require('../../env');
const tokenCache = require('../../../persistence/token');

const SCOPE = 'rongxin:loan:mgr:app';

class Auth extends BaseAuth {
  constructor() {
    super()
  }

  getName() {
    return CLASS_NAME
  }

  canProcess(req) {
    try {
      if (!req.headers || !req.headers.authorization)
        throw {
          errorCode: 'EJWTAUTHPRE044',
          httpCode: 401,
          reason: `Invalid Authentication Info`
        }

      let authStrArray = req.headers.authorization.split(' ')

      if (authStrArray.length !== 2 || authStrArray[0] !== 'Bearer')
        throw {
          errorCode: 'EJWTAUTHPRE046',
          httpCode: 401,
          reason: `Invalid Authentication Info`
        }

      return true
    } catch (error) {
      debug(`current req can't process by ${CLASS_NAME}, due to `, error.reason)
      return false
    }
  }

  async invoke(req, res) {
    let method = `${CLASS_NAME}.invoke`
    debug(method, '[Enter]')
    try {
      let authStrArray = req.headers.authorization.split(' ')
      let access_token = authStrArray[1]

      debug(method, `Token:${access_token}`)

      let decoded
      try {
        decoded = jwt.verify(access_token, credentials.jwtSecret, {
          audience: 'rongxin:loan:jwt:access'
        })
      } catch (error) {
        debug(method, 'Token verify filed, due to', error)
        throw {
          errorCode: error.name === 'TokenExpiredError' ? 'EAUTH_TOKEN_EXPIRED' : 'EAUTH_TOKEN_ILLEGAL',
          httpCode: 401,
          reason: error.name === 'TokenExpiredError' ? 'Token Exipired' : `failed to auth via access_token, due to: ${error.message || 'internal error'}`
        }
      }

      let cache = await tokenCache.getAccessToken(decoded.sub, access_token);

      if (!cache || cache.sub !== decoded.sub) {
        throw {
          errorCode: 'EAUTH_TOKEN_ILLEGAL',
          httpCode: 401,
          reason: 'Illegal Token'
        };
      }

      // strict env token scope
      let scope = env.getServerMode() === 'production' ? SCOPE : `${env.getServerMode()}:${SCOPE}`;
      debug(method, 'SCOPE: ', scope, 'DECODED', decoded.scope, 'ENV', env.getServerMode());

      if (decoded.scope !== scope)
        throw {
          errorCode: 'EAUTH_TOKEN_ILLEGAL_ENV',
          httpCode: 401,
          reason: 'Illegal Token Environment'
        };

      let profile = {
        userid: decoded.sub,
        token: access_token,
        client: decoded.app
      }

      req.user = profile
      debug(method, '[Exit](success)', profile)
      return profile

    } catch (error) {
      debug.error(`current req can't authenticated by ${CLASS_NAME}, due to `, error)

      if (error.httpCode && error.httpCode !== 500)
        res.set('Warning', `199 - ${error.errorCode || 'EAUTH_TOKEN_ILLEGAL'} ${new Date()}`)
      throw error
    }
  }
}

module.exports = Auth
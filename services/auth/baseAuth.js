/**
 * base authenticator class
 * <AUTHOR>
 * 2018-03-22
 */

class BaseAuth {
  constructor() {
  }

  /**
   * return authenticator's class name
   * @abstract
   * @memberof BaseAuth
   * @return name
   */
  getName() {
    throw {
      errorCode: 'ABSTRACT019',
      reason: 'abstract func should be impelemented'
    }
  }

  /**
   * determine whether defined auth way is applicable for current req
   * @abstract
   * @param req Request
   * @return {Boolean} true => can be processed
   */
  canProcess(req) {
    throw {
      errorCode: 'ABSTRACT032',
      reason: 'abstract func should be impelemented'
    }
  }

  /**
   * invoke auth func by using this._ctx req info (e.g token) and return authenticated user/client info
   * support async & sync processing
   * @abstract
   * @param req Request
   * @param res Response
   * @return {Object} user / client
   */
  async invoke(req, res) {
    throw {
      errorCode: 'ABSTRACT047',
      reason: 'abstract func should be impelemented'
    }
  }
}

module.exports = BaseAuth
/**
 * auth service entry
 * <AUTHOR>
 */

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:services:authInvoker')

class AuthInvoker {
  constructor() {
    this._pipeline = []
  }

  /**
   * register authenticator => invoke pipeline
   * authenticator should be extended from BaseAuth
   * @param {Auth} authenticator 
   * @memberof AuthInvoker
   */
  register(authenticator) {
    this._pipeline.push(authenticator)
  }

  /**
   * invoke authenticator pipeline synchronously
   * 
   * @param {any} req 
   * @param {any} res 
   * @memberof AuthInvoker
   */
  async auth(req, res) {
    let self = this
    let method = 'auth'
    debug(method, '[Enter]')
    try {
      if (!req || !res)
        throw {
          httpCode: 500,
          errorCode: 'EAUTHSVC034',
          reason: 'invalid req || res'
        }

      for (let item of self._pipeline) {
        if (req.user)
          break

        if (!item.canProcess(req))
          continue

        let user = await item.invoke(req, res)
        if (user) {
          debug(method, `authenticated by ${item.getName()}`)
          req.user = user
          break
        }
      }

      debug(method, '[Exit](success)', req.user || {})
      return
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }
}

let authInvoker = new AuthInvoker()

module.exports = authInvoker

let InternalAuth = require('./authenticator/internalUserCtxAuth');
authInvoker.register(new InternalAuth());

let JWTAuth = require('./authenticator/jwtAuth')
authInvoker.register(new JWTAuth())

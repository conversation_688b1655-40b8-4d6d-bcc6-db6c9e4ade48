'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan:services:postGisSvc');
const config = require('config');

const { Pool } = require('pg');

class PostGisSvc {
  constructor() {
    this.host = "";
    this.port = "";
    this.pool = {};
  }

  //查询
  async query(sql, values) {
    let method = 'query';
    try {
      debug(method, '[Enter]');

      debug(method, '[values]', values);
      let client = await this.pool.connect();
      let res = { rows: [] };
      try {
        if (values && Array.isArray(values)) {
          res = await client.query(sql, values)
        } else {
          res = await client.query(sql)
        }
      } finally {
        client.release();
      }

      let result = res.rows;
      debug(method, '[Exit](success)', result);
      return result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //单纯查询(无事务)
  async singleQuery(sql, values) {
    let method = 'singleQuery';
    try {
      debug(method, '[Enter]');


      debug(method, '[sql]', sql);
      debug(method, '[values]', values);
      let res = { rows: [] };
      if (values && Array.isArray(values)) {
        res = await this.pool.query(sql, values);
      } else {
        res = await this.pool.query(sql, values);
      }
      let result = res.rows;
      debug(method, '[Exit](success)', result);
      return result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  init() {
    debug('init [Enter]');
    let pgSvcConfig = config.get('pgService');
    this.host = pgSvcConfig.host;
    this.port = pgSvcConfig.port;

    this.pool = new Pool({
      host: this.host,
      port: this.port,
      database: pgSvcConfig.database,
      user: pgSvcConfig.user,
      password: pgSvcConfig.password,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    })
    this.pool.on('error', (err, client) => {
      debug.error('postGisSvc [connect failed]', err);
    })
  }
}


let postGisSvc = new PostGisSvc();
postGisSvc.init();
module.exports = postGisSvc;
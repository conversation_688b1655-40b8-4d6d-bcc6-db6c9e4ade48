/**
 * @description: getAreaListByCodesHandler
 * @author: q
 */
'use strict'

const HANDLER_NAME = 'getAreaListByCodesHandler'
const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:services:area:' + HANDLER_NAME)
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const areasData = require('../dataSvc/dataUtil').dictAreas;
const redisData = require('../../persistence/dataStore');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  } 

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let data = await redisData.get('areaCode_'+condition.areaCode);
      if(!data){
        let areaList = condition.areaCode.split(',');
        let reg = '';
        for(let i=0;i<areaList.length;i++){
          reg += '^';
          reg += areaList[i].length>6 ? areaList[i] : areaList[i]+'[0-9]{0,3}';
          reg += '$';
          if(i<areaList.length-1){
            reg += '|';
          }
        }
        // for(let areaItem of areaList){
        //   areaItem = new RegExp(`^${areaItem}`);
        // }
        reg = new RegExp(`${reg}`);
        data = await areasData.getByCondition({code:reg+'',archived:false,limit:condition.limit,skip:condition.skip});
        await redisData.set('areaCode_'+condition.areaCode,JSON.stringify(data),{expire:60 * 60 * 24 * 30});
      }else{
        data = JSON.parse(data);
      }
      self.context.result = data;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler

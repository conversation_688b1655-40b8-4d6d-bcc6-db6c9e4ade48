/**
 * @description: getArea<PERSON>ist<PERSON><PERSON><PERSON>
 * @author: hexu
 */
'use strict'

const HANDLER_NAME = 'getAreaListHandler'
const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:services:area:' + HANDLER_NAME)
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const areasData = require('../dataSvc/dataUtil').dictAreas;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  } 

  getName() {
    return HANDLER_NAME
  }

  doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let url = '/v1.0/area/code/list';
      let condition = self.context.input;

      areasData.getByUrl(url, condition).then(data => {
        debug(method, '[Exit](success)', data)
        self.context.result = data || [];
        return done()
      }).fail(error => {
        debug.error(method, '[Exit](failed)', error)
        error.httpCode = error.httpCode || error.errorCode;
        return done(error)
      });
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler

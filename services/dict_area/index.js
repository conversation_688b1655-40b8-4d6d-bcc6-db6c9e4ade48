/**
 * @description: area service index
 * @author: hexu
 */

'use strict'

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:services:area:index')
const Q = require('q')
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetAreaListHandler = require('./getAreaListHandler')
const GetAreaListByCodesHandler = require('./getAreaListByCodesHandler')

class Service {
  constructor() {

  }

  getAreaList(input, _opts) {
    let method = 'getAreaList'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    let defer = Q.defer()

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()

      svcHandlerMgrt.addHandler(new GetAreaListHandler(context))
      svcHandlerMgrt.processAsync(context).then(() => {
        debug(method, '[Exit](success): ', context.result)
        defer.resolve(context.result)
      }).fail((error) => {
        debug.error(method, '[Exit](failed)', error)
        defer.reject(error)
      })
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      defer.reject(error)
    }

    return defer.promise
  }

  async areaListByCodes(input, _opts) {
    let method = 'areaListByCodes';
    debug(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetAreaListByCodesHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

}

module.exports = new Service()

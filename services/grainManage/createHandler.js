/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'createHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const {assert} = require('../../utils/general');
const { 
  loanApplication:loanApplicationData,
  grainManage:grainManageData,
  infoCollectHistory:infoCollectHistoryData,
} = require('../dataSvc/dataUtil');
const moment = require('moment');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input, opts } = this.context;
      let result;

      const app = await loanApplicationData.getById(input.aId);
      assert(app ,'E_LAND_APP_GRAIN_MANAGER_ADD_003','aId is error');
      const infoId = app && app.addons && app.addons.info && app.addons.info.mId;
      const info = infoId && await infoCollectHistoryData.getById( infoId );
      assert(info ,'E_LAND_APP_GRAIN_MANAGER_ADD_004','mId is error');

      input.username = app.username
      input.requestName = info && info.name;
      input.requestMobile = app.userMobile;
      input.requestAreaCode = app.area;
      input.requestCompanyName = app.enterpriseName || ""
      input.requestType = info && info.type || app.consumer_t;

      if (input._id) {
        result = await grainManageData.putById(input._id, input);
      } else {
        result = await grainManageData.post(input);
      }
      debug(method, '[Exit](success)');
      this.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

async function formatImg(item) {
  item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0 &&
    (item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url }));
  item && item.image && item.image.url && item.image.url.indexOf('http') !== 0 &&
    (item.image.url = await aliOssSvc.getFile({ fileName: item.image.url }));
}

async function getUserName(uId) {
  try {
    let user = await employeeData.getOneByCondition({
      _id: uId,
      "archived": false
    });
    return user && user.username || '';
  } catch (ex) {

  }
}


module.exports = Handler;
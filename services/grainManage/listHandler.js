/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'listHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const grainManageData = require('../dataSvc/dataUtil').grainManage;
const employeeData = require('../dataSvc/dataUtil').employees;
const employeeGroupData = require('../dataSvc/dataUtil').employeeGroups;
const { getEmployeeLimit } = require('../../utils/getUserFromReq')
const moment = require('moment');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input , input: condition , opts } = this.context;

      debug(`${HANDLER_NAME}findAreaLimitAreaPara`,opts.uId || 'null', opts.tId  || null,opts.roleId||'null');
      const areaCodeLimitOr = opts.uId && opts.tId && await getEmployeeLimit(employeeGroupData, opts.tId, opts.uId, 'areaCode', opts.roleId);
      opts.uId && (condition['$or'] = areaCodeLimitOr);
      debug(`${HANDLER_NAME}findAreaLimitAreaCode`,condition['$or'] || 'null');

      let result = await grainManageData.getListAndCountByCondition(input);
      for (const item of result.result) {
        item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss');
        item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
        item.operateTime && (item.operateTime = moment(item.operateTime).format('YYYY-MM-DD HH:mm:ss'));
        item.operatorInfo = await getUserName(item.operator);
        await Promise.all(item.meidas.map(formatImg).reduce((r, v) => r.concat(v), []));
        if (item.areaCode) {
          item.areaInfo = await formatAreaCode.getFormatAreaCode(item.areaCode)
        }
        item.app = item.aId && await loanApplicationData.getById( item.aId );
      };

      debug(method, '[Exit](success)');
      this.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

async function formatImg(item) {
  item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0 &&
    (item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url }));
  item && item.image && item.image.url && item.image.url.indexOf('http') !== 0 &&
    (item.image.url = await aliOssSvc.getFile({ fileName: item.image.url }));
}

async function getUserName(uId) {
  try {
    let user = await employeeData.getOneByCondition({
      _id: uId,
      "archived": false
    });
    console.log('user:::', user);
    return user && user.username || '';
  } catch (ex) {}
}


module.exports = Handler;
'use strict';

const HANDLER_NAME = 'CreateAssistanceFaqHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin.loan.mgr.app.api:services:assistanceFaq:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistanceRequestData = require('../dataSvc/dataUtil').assistanceRequest;
const assistanceFaqData = require('../dataSvc/dataUtil').assistanceFaq;
const assistanceSubscribeData = require('../dataSvc/dataUtil').assistanceSubscribe;

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    const self = this;
    const method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      const input = self.context.input;
      // const opts = self.context.opts;
      const request = await assistanceRequestData.getOneByCondition({
        tId: input.tId,
        sn: input.requestSn,
        archived: false,
      });

      if (!request || !request._id) {
        throw {
          errorCode: 'E_CREATE_ASSISTANCE_FAQ_HANDLER_036',
          httpCode: 406,
          reason: 'Assistance request not found'
        }
      }

      input.userId = request.userId;
      const data = await assistanceFaqData.post(input);
      if (input.userType === 0) { // 重点关注用户
        const _subscribe = await assistanceSubscribeData.getOneByCondition({
          tId: input.tId,
          userId: input.userId,
          agentId: input.agentId,
          archived: false,
        });

        if (!_subscribe || !_subscribe._id) {
          await assistanceSubscribeData.post({
            tId: input.tId,
            userId: input.userId,
            agentId: input.agentId
          });
        }
      }
      
      self.context.result = data;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;

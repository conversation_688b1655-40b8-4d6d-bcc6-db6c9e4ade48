'use strict';

const HANDLER_NAME = 'GetAssistanceFaqDetailHandler';
const moment = require('moment');
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin.loan.mgr.app.api:services:assistanceFaq:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistanceFaqData = require('../dataSvc/dataUtil').assistanceFaq;
const assistanceFaqTrackingData = require('../dataSvc/dataUtil').assistanceFaqTracking;
const employeeData = require('../dataSvc/dataUtil').employees;
const { STATUS_MAP } = require('./const');

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    const self = this;
    const method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      const condition = self.context.input;
      const data = await assistanceFaqData.getById(condition.id);
      if (data) {
        const trackings = await assistanceFaqTrackingData.getByCondition({
          source: data._id, archived: false, $sort: { createdTime: -1 },
        });
        data.trackings = await Promise.all(trackings.map(async (tracking) => {
          if (tracking.action === 'new') {
            const employee = await employeeData.getById(tracking.target, {cache : true , expire: 24 * 60 * 60 });
            tracking.actionDesc = `创建并指派给${employee.username}`;
          } else {
            tracking.actionDesc = STATUS_MAP.get(tracking.action);
          }

          const operator = await employeeData.getById(tracking.uId, {cache : true , expire: 24 * 60 * 60 });
          tracking.operatorName = operator ? operator.username : '';

          tracking.createdTime = moment(tracking.createdTime).format('YYYY-MM-DD HH:mm:ss');
          tracking.durationDesc = formatDuration(tracking.duration);

          return tracking;
        }));
      }

      self.context.result = data;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

function formatDuration(duration) {
  const minutes = Math.floor(moment.duration(duration).asMinutes());
  if (minutes === 0) {
    return 0;
  } else if (minutes < 120) {
    return `${minutes}分钟`;
  } else if (minutes < 1440) {
    return moment.duration(duration).hours() + '小时' + (moment.duration(duration).minutes() === 0 ? '' : `${Math.floor(moment.duration(duration).minutes())}分钟`);
  } else {
    return moment.duration(duration).days() + '天' + (moment.duration(duration).hours() === 0 ? '' : `${Math.floor(moment.duration(duration).hours())}小时`);
  }
}

module.exports = Handler;

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin.loan.mgr.app.api:services:assistanceFaq:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetAssistanceFaqListHandler = require('./getAssistanceFaqListHandler');
const GetAssistanceFaqDetailHandler = require('./getAssistanceFaqDetailHandler');
const FormatAssistanceFaqHandler = require('./formatAssistanceFaqHandler');
const UpdateAssistanceFaqHandler = require('./updateAssistanceFaqHandler');
const RemoveAssistanceFaqHandler = require('./removeAssistanceFaqHandler');

class Service {
  constructor() {

  }

  async getAssistanceFaqList(input, _opts) {
    const method = 'getAssistanceFaqList';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetAssistanceFaqListHandler(context));
      svcHandlerMgrt.addHandler(new FormatAssistanceFaqHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getAssistanceFaqDetail(input, _opts) {
    const method = 'getAssistanceFaqDetail';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetAssistanceFaqDetailHandler(context));
      svcHandlerMgrt.addHandler(new FormatAssistanceFaqHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async updateAssistanceFaq(input, _opts) {
    const method = 'updateAssistanceFaq';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new UpdateAssistanceFaqHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async removeAssistanceFaq(input, _opts) {
    const method = 'removeAssistanceFaq';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    };

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new RemoveAssistanceFaqHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();

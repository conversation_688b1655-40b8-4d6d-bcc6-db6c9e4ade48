'use strict';

const HANDLER_NAME = 'FormatAssistanceFaqHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:assistanceFaq:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const moment = require('moment');
const assistanceFaqCategoryData = require('../dataSvc/dataUtil').assistanceFaqCategory;
const { BUSINESS_TYPE_MAP, STATUS_MAP, PRIORITY_MAP } = require('./const');

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    const self = this;
    const method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      const _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)');
        return done();
      }

      for (const item of _result) {
        item.businessTypeDesc = BUSINESS_TYPE_MAP.get(item.businessType);
        item.statusDesc = STATUS_MAP.get(item.status);
        item.priorityDesc = PRIORITY_MAP.get(item.priority);
        item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
        if (item.category) {
          const category = await assistanceFaqCategoryData.getById(item.category);
          item.categoryDesc = category ? category.name : '';
        }

        if (item.subCategory) {
          const subCategory = await assistanceFaqCategoryData.getById(item.subCategory);
          item.subCategoryDesc = subCategory ? subCategory.name : '';
        }
      }

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }

  getEnumValue(list, key) {
    for (const item of list) {
      if (item._id === key) return item.name;
    }
    return '';
  }
}

module.exports = Handler;

/**
 * <AUTHOR>
 * 2019-06-03 
 */

'use strict';
const HANDLER_NAME = 'getLoanStatisSummaryHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationStatisData = require('../dataSvc/dataUtil').loanApplicationStatis;
const tenantsData = require('../dataSvc/dataUtil').tenants;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const dictAreasData = require('../dataSvc/dataUtil').dictAreas;
const moment = require('moment');
const { async } = require('q');
const fundsData = require('../dataSvc/dataUtil').funds;
const LOAN_APP_STATUS_MAP = require('../../utils/const/applicationConst').LOAN_APP_STATUS_MAP

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let totalLoan = await loanApplicationData.getListAndCountByCondition({
        tId: "638870ab3c4ae7a6977a59a1",
        archived: false,
        limit: 'unlimited'
      });
      if (!totalLoan || totalLoan.total <= 0) {
        throw {
          errorCode: 'E_STATIS_36',
          httpCode: 406,
          reason: '没有订单相关数据'
        }
      }
      // 申请统计
      let amountTotal = 0;
      // 放款订单
      let loanedTotal = 0;
      // 放款金额
      let actualLoanTotal = 0;

      // 订单各种情况统计
      let loanDetails = {
        "new": 0,
        "certified": 0,
        "biopsy_approved": 0,
        "pre_censor": 0,
        "final_review": 0,
        "apply_credit": 0,
        "loaned": 0,
        "wait_operator_collect": 0,
        "rejected_operator_collect": 0,
        "rejected_censor": 0,
        "rejected_loan": 0,

      }

      // 乡镇统计
      let village = {};
      // 银行纬度统计
      let fundInfo = {};

      for (const item of totalLoan.result) {
        amountTotal += item.amount || 0;
        if (!fundInfo[item.fund]) {
          fundInfo[item.fund] = {
            loanCount: 1,
            amount: item.amount || 0,
            actualLoan: 0,
            loanedCount: 0,
          }
        } else {
          fundInfo[item.fund].loanCount += 1;
          fundInfo[item.fund].amount = fundInfo[item.fund].amount + item.amount
        }

        // 放款情况
        if (item.status == "waitLoan" || item.status == "loaned") {
          loanedTotal += 1;
          actualLoanTotal += item.actualLoan || 0;
          if (fundInfo[item.fund]) {
            fundInfo[item.fund].actualLoan = (fundInfo[item.fund].actualLoan || 0) + item.actualLoan || 0;
            fundInfo[item.fund].loanedCount += 1;
          }
        }

        loanDetails[item.status] = loanDetails[item.status] >= 0 ? loanDetails[item.status] + 1 : 0;
        let villageCode = item.area.substr(0, 9);
        if (!village[villageCode]) {
          village[villageCode] = 1
        } else {
          village[villageCode] = village[villageCode] + 1;
        }

      }
      let today = new Date();
      let startDate = new Date(moment().add(-1, 'y').format('YYYY-MM-DD'));
      let start = new Date(`${startDate.getFullYear()}-${startDate.getMonth() +1}-01`);

      let loanStatis = await loanApplicationStatisData.getByCondition({
        limit: 'unlimited',
        tId: "638870ab3c4ae7a6977a59a1",
        createdTime: {
          $gte: start,
          $lte: today
        }
      });
      let monthlyStatistics = {}
      for (const item of loanStatis) {
        let month = new Date(item.createdTime).getMonth() + 1
        let year = new Date(item.createdTime).getFullYear()
        let day = `${year}-${month}`
        if (!monthlyStatistics[day]) {
          monthlyStatistics[day] = {
            newUser: item.newUser,
            loanOrders: item.loanOrders,
            loanAmount: item.loanAmount / 100,
            loanedOrders: item.loanedOrders,
            loanRejected: item.loanRejected,
            loanFinished: item.loanFinished,
            amount: item.amount / 100
          }
        } else {
          monthlyStatistics[day].newUser += item.newUser;
          monthlyStatistics[day].loanOrders += item.loanOrders;
          monthlyStatistics[day].loanAmount += item.loanAmount / 100;
          monthlyStatistics[day].loanedOrders += item.loanedOrders;
          monthlyStatistics[day].loanRejected += item.loanRejected;
          monthlyStatistics[day].loanFinished += item.loanFinished;
          monthlyStatistics[day].amount += item.amount / 100;
        }
      }
      let monthlyInfo = [];
      Object.keys(monthlyStatistics).forEach(item => {
        monthlyInfo.push(Object.assign(monthlyStatistics[item], { month: item }));
      })

      let villageInfo = [];
      let promises = [];
      Object.keys(village).forEach(item => {
        promises.push(dictAreasData.getOneByCondition({ code: item, archived: false }).then(data => {
          if (data) {
            villageInfo.push({
              code: item,
              name: data.name,
              loanCount: village[item]
            });
          }
        }));
      })

      await Promise.all(promises);

      let fundStatis = [];
      promises = [];

      Object.keys(fundInfo).forEach(item => {
        promises.push(fundsData.getById(item, {cache : true , expire: 24 * 60 * 60 }).then(data => {
          fundStatis.push({
            fundName: data.name,
            fundId: item,
            loanCount: fundInfo[item].loanCount,
            amount: fundInfo[item].amount / 100,
            actualLoan: fundInfo[item].actualLoan,
            loanedCount: fundInfo[item].loanedCount
          })
        }));
      })

      await Promise.all(promises);
      let loanStatusStatis = [];
      Object.keys(loanDetails).forEach(item => {
        let status = LOAN_APP_STATUS_MAP.get(item);
        if (status) {
          loanStatusStatis.push({
            name: status,
            status: item,
            count: loanDetails[item]
          })
        }

      })



      let result = {
        total: {
          count: totalLoan.total,
          amount: amountTotal / 100,
          loaned: loanedTotal,
          actualLoan: actualLoanTotal
        },
        loanDetails: loanStatusStatis,
        villageStatis: villageInfo,
        fundStatis: fundStatis,
        monthlyStatistics: monthlyInfo

      }


      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }


}
module.exports = Handler;
/**
 * <AUTHOR>
 * 2019-05-05  
 */

'use strict';

const HANDLER_NAME = 'getClientVersionHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:client:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const clientVersionData = require('../dataSvc/dataUtil').clientVersion;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let result = await clientVersionData.getOneByCondition({
        client: input.clientId,
        platform: input.platform,
        update: true,
        status: true,
        archived: false,
        $sort: {
          version: -1
        }
      });

      result && result.file && result.file.path
        && (result.file.path = result.file.path.replace(/^https:\/\//gi,''));
      self.context.result = result || {};
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
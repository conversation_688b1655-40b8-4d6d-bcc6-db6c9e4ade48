'use strict';

const HANDLER_NAME = 'getLoanDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loan_assistant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication
const LoanProductsData = require('../dataSvc/dataUtil').loanProducts
const moment = require('moment');
const APP_STATUS_MAP = require('../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const WDPRODUCTS = require('../../data/products').WDPRODUCTS;
const userIdentityData = require('../dataSvc/dataUtil').userIdentity;
const loanDistributeData = require('../dataSvc/dataUtil').loanDistribute;
const aliOssSvc = require('../aliOssSvc');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {

      let condition = self.context.input;

      let result = await loanApplicationData.getById(condition.id);
      if(result.addons && result.addons.personalInfo  && result.addons.personalInfo.imagesList ){
        result.addons.personalInfo.imagesList = await self.getPath(result.addons.personalInfo.imagesList);
      }
      if(result.addons && result.addons.enterpriseInfo  && result.addons.enterpriseInfo.imagesList ){
        result.addons.enterpriseInfo.imagesList = await self.getPath(result.addons.enterpriseInfo.imagesList);
      }
      const userInfo = await userIdentityData.getOneByCondition({
        uId: result.uId,
        IDCardStatus: "approved",
        "archived": false
      });
      if (userInfo) {
        result.IDCard = userInfo.IDCard
      }
      result.amount = result.amount / 100;
      const productInfo = await LoanProductsData.getById(result.pId, {cache : true , expire: 24 * 60 * 60 });
      let product = WDPRODUCTS.get(result.pId)
      result.product = productInfo;
      if (product) {
        result.productType = product.name
        result.type = product.type
      }
      const distributeInfo = await loanDistributeData.getOneByCondition({
        aId: condition.id,
        "$sort":
        {
          "createdTime":-1
        }
      });
      if (distributeInfo) {
        result.assignStatus = distributeInfo.assignStatus
      }
      result.createdTime = moment(result.createdTime).utc().add(8, 'h').format('YYYY-MM-DD HH:mm');
      result.lastModTime = moment(result.lastModTime).utc().add(8, 'h').format('YYYY-MM-DD HH:mm');
      result.statusChName = APP_STATUS_MAP.get(result.status);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
  async getPath(images={}){
    debug('getPath', '[images]', images);
    const keys = ["businessLicenseImages","frontIDCardImages","backIDCardImages","otherImages"];
    let promise = [];
    for (const val of keys) {
      if ( images[val] && images[val].length) {
        for (let item of images[val]) {
          formatImg(item,promise);
        }
      }
    }
    await Promise.all(promise);
    debug('getPath', '[images](string)', JSON.stringify(images));
    
    return images;
  }
}

function formatImg(item,promise) {
  if (item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    if(item.thumbnail.url.indexOf('oss/') === 0){
      promise.push(aliOssSvc.getFile({fileName:item.thumbnail.url}).then(data => {
        item.thumbnail.url = data
      }));
    }else{
      item.thumbnail.url = IMAGE_BASE_URL.concat(item.thumbnail.url);
    }
  }
  if (item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    if(item.image.url.indexOf('oss/') === 0){
      promise.push(aliOssSvc.getFile({fileName:item.image.url}).then(data => {
        item.image.url = data
      }));
    }else{
      item.image.url = IMAGE_BASE_URL.concat(item.image.url);
    }
  }
}
module.exports = Handler
'use strict';
const HANDLER_NAME = 'preGetLoanAppListHander';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loan_assistant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups
const GROUP = "5eb8ff2ec6ecfe44d4ecaed9";
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _opts = self.context.opts;
      let _input = self.context.input;
      // 过滤身份信息，如果获取到的roll 通过登录的employee._id 和查协理员的group._id 查询
      const role = await employeeGroupsData.getOneByCondition({
        employee: _opts.userInfo.userid,
        group: GROUP,
        archived: false
      });
      if (!role || !role._id) {
        throw {
          errorCode: 'E_assistant_34',
          httpCode: 401,
          reason: '该用户没有协理员角色，请联系管理员'
        }
      }
      // 如果角色信息存在areaList则只取跟areaList相关的订单数据
      if (role.areaList && role.areaList.length > 0) {
        if (!_input.$or) {
          _input.$or = [];
        }
        role.areaList.forEach(item => {
          let area = {
            area: '/^' + item + '/'
          };
          _input.$or.push(area);
        });
      }
      self.context.input = _input;
      debug(method, '[Exit](success)', self.context.input);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'lockSupplementHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loan_assistant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const requestLimit = require('../../persistence/requestLimit');
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const LOAN_APP_STATUS = require('../../utils/const/applicationConst').LOAN_APP_STATUS;
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const opts = self.context.opts;
      const loanInfo = await loanApplicationData.getById(opts.id);
      if (loanInfo.status !== LOAN_APP_STATUS.LOAN_APP_BIOPSY_APPROVED) {
        throw {
          errorCode: 'E_LOAN_APP_BIOPSY_APPROVED_44',
          httpCode: 406,
          reason: `${loanInfo.status} is not supplement`
        }
      }
      const isLock = await requestLimit.isLock(opts.id);
      if (isLock) {
        throw {
          errorCode: 'E_LOAN_API_LOCK_032',
          httpCode: 429,
          reason: '订单处理中'
        };
      }
      await requestLimit.lock(opts.id);


      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
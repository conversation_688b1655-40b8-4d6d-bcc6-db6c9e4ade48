
'use strict';

const HANDLER_NAME = 'GetDecisionHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:report:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const dataUtil = require('../dataSvc/dataUtil');
const agent = require('superagent');
const config = require('config');
const env = require('../env');
const userVerifysData = dataUtil.userIdentity;
const decisionUrl = `http://${config.get('decision_Service.host')}/api/v1.0/order/callDecisionRuleEngine`;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      let loanApplication = self.context.opts.loanApplication;
      if (!loanApplication || !loanApplication._id) {
        debug(method, '[Exit](continue)')
        return done()
      }

      let userVerifyInfo = await userVerifysData.getOneByCondition({
        uId: loanApplication.uId,
        archived: false,
        IDCardStatus: 'approved'
      });

      if (!userVerifyInfo) {
        throw {
          errorCode: 'E_DECISION_045',
          httpCode: 406,
          reason: 'invalid userinfo'
        };
      }

      let server = `http://${config.get('rongxin_loan_cms_api_service.host')}`;
      let payload = {
        appCode: config.get('decision_Service.wd_apiCode'),
        bizModel: {
          customerName: userVerifyInfo.realname,
          prePhonetel: loanApplication.userMobile,
          cerdId: userVerifyInfo.IDCard,
          orderTradeNo: loanApplication._id
        },
        callbackUrl: `${server}/api/v1.0/loan/application/decision/notify`
      }
      debug(method, '[continue] payload', payload);
      let res = await agent.post(decisionUrl).send(payload);
      debug(method, '[Exit](success)', res.body);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done();
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler;
'use strict';
const HANDLER_NAME = 'FormatAreaCodeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loan_assistant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const config = require('config');
const aliOssSvc = require('../aliOssSvc');
const REPO_HOST = config.get('repoServer').host;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }
      let promises = [];

      for (let item of _result) {
        promises.push(formatAreaCode.getFormatAreaCode(item.area).then(data => {
          item.region = data && data.region || {};
          item.location = data && data.area + item.address || '';
        }));
        if (item.addons && item.addons.personalInfo) {
          promises.push(formatAreaCode.getFormatAreaCode(item.addons.personalInfo.censusRegister).then(data => {
            item.addons.personalInfo.censusRegister = data && data.area + item.addons.personalInfo.minuteAddress || ''
          }));
        }

        if (item.addons && item.addons.riskReportFile) {
          if (item.addons.riskReportFile.indexOf('oss/') === 0 || item.addons.riskReportFile.indexOf('decision/') === 0) {
            promises.push(aliOssSvc.getFile({
              fileName: item.addons.riskReportFile
            }).then(data => {
              item.addons.riskReportFile = data
            }));
          } else {
            item.addons.riskReportFile = REPO_HOST + item.addons.riskReportFile;
          }
        }
      }

      await Promise.all(promises);

      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
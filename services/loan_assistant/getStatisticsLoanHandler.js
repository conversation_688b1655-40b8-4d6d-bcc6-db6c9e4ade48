'use strict';

const HANDLER_NAME = 'getStatisticsLoanHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loan_assistant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication
const loanApplicationTrackingData = require('../dataSvc/dataUtil').loanApplicationTracking;
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let opts = self.context.opts;
      let condition = self.context.input;

      condition.status = "biopsy_approved";
      const pending = await loanApplicationData.getCountByCondition(condition);
      const processed = await loanApplicationTrackingData.getCountByCondition({
        src_t: "staff",
        source: opts.userInfo.userid,
        action: "pre_censor",
        archived: false
      });
      self.context.result = {
        pending: pending.count,
        processed: processed.count
      };
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
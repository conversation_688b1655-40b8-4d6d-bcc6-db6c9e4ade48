'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:loan_assistant:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const PreGetLoanAppListHander = require('./preGetLoanAppListHander');
const PendingListHandler = require('./pendingListHandler');
const FromatLoanHandler = require('./formatLoanHandler');
const ProcessedListHandler = require('./processedListHandler');
const SupplementInformationHandler = require('./supplementInformationHandler');
const GetDecisionHandler = require('./getDecisionHandler');
const GetLoanDetailHandler = require("./getLoanDetailHandler");
const GetStatisticsLoanHandler = require('./getStatisticsLoanHandler');
const LockSupplementHandler = require('./lockSupplementHandler');
const FormatAreaCodeHandler = require('./formatAreaCodeHandler');
class Service {
  constructor() {
    this.createApplicationFactory = new Map();
  }

  async getPendingLoanList(input, _opts) {
    let method = 'getPendingLoanList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new PreGetLoanAppListHander(context));
      svcHandlerMgrt.addHandler(new PendingListHandler(context));
      svcHandlerMgrt.addHandler(new FromatLoanHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async getProcessedLoanList(input, _opts) {
    let method = 'getProcessedLoanList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new ProcessedListHandler(context));
      svcHandlerMgrt.addHandler(new FromatLoanHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async getStatisticsLoan(input, _opts) {
    let method = 'getStatisticsLoan';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new PreGetLoanAppListHander(context));
      svcHandlerMgrt.addHandler(new GetStatisticsLoanHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async supplementInformation(input, _opts) {
    let method = 'supplementInformation';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new LockSupplementHandler(context));
      svcHandlerMgrt.addHandler(new SupplementInformationHandler(context));
      svcHandlerMgrt.addHandler(new GetDecisionHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async getLoanDetail(input, _opts) {
    let method = 'getLoanDetail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetLoanDetailHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  init() {
    // register by pid_found
    this.createApplicationFactory.set("1", this.getPendingLoanList); // 待处理
    this.createApplicationFactory.set("2", this.getProcessedLoanList); // 已处理
  }
}

let service = new Service();
service.init();
module.exports = service;
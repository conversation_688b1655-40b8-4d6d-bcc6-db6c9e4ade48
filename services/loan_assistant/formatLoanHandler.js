'use strict';

const HANDLER_NAME = 'fromatLoanHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loan_assistant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const LoanProductsData = require('../dataSvc/dataUtil').loanProducts
const moment = require('moment');
const WDPRODUCTS = require('../../data/products').WDPRODUCTS;
const APP_STATUS_MAP = require('../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _result = self.context.result.result;
      let promise = [];
      for (const item of _result) {
        let product = WDPRODUCTS.get(item.pId)
        item.amount = item.amount / 100;
        promise.push(LoanProductsData.getById(item.pId, {cache : true , expire: 24 * 60 * 60 }).then(data => {
          item.product = data;
          item.productName = data.name
          if (product) {
            item.productType = product.name
            item.type = product.type
          }
        }))
        item.createdTime = moment(item.createdTime).utc().add(8, 'h').format('YYYY-MM-DD HH:mm');
        item.lastModTime = moment(item.lastModTime).utc().add(8, 'h').format('YYYY-MM-DD HH:mm');
        item.statusChName = APP_STATUS_MAP.get(item.status);
      }
      await Promise.all(promise);
      self.context.result.result = _result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
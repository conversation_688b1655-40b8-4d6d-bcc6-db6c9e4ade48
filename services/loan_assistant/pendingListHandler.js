'use strict';

const HANDLER_NAME = 'pendingListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loan_assistant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _opts = self.context.opts;
      let condition = self.context.input;
      if (_opts.start && _opts.end) {
        condition.createdTime = {
          $gte: moment(_opts.start).utc().format(),
          $lte: moment(req.query.end).utc().add(1, 'd').format()
        };
      }
      condition.status = "biopsy_approved";
      const result = await loanApplicationData.getListAndCountByCondition(condition);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
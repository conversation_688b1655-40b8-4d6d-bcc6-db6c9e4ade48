'use strict';

const HANDLER_NAME = 'supplementInformationHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loan_assistant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication
const loanApplicationTrackingData = require('../dataSvc/dataUtil').loanApplicationTracking;
const requestLimit = require('../../persistence/requestLimit');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const condition = self.context.input;
      const opts = self.context.opts;

      let result
      // 处理订单
      if (condition.productType === "1") {
        result = await loanApplicationData.putById(opts.id, {
          "addons.personalInfo": condition.addons,
          status: "pre_censor"
        });
      }
      if (condition.productType === "2") {
        result = await loanApplicationData.putById(opts.id, {
          "addons.enterpriseInfo": condition.addons,
          status: "pre_censor"
        });
      }
      if (!result) {
        throw {
          errorCode: 'E_supplementInformation_44',
          httpCode: 406,
          reason: 'supplementInformation is failed'
        }
      }
      // 记录tracking
      await loanApplicationTrackingData.post({
        src_t: "staff",
        source: opts.userid,
        target_t: 1,
        target: opts.id,
        action: "pre_censor",
        comments: "协理员补充资料完成"
      })
      self.context.result = {
        success: true
      }
      await requestLimit.unlock(opts.id);
      opts.loanApplication = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
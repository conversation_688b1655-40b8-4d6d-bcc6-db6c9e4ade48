/**
 * getInvitation<PERSON>ain<PERSON><PERSON><PERSON>
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'enrollListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:iguopin:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const DataSvc = require('../dataSvc/dataUtil');
const iguopinRecordData = DataSvc.iguopinRecord;
const iguopinOfflineData = DataSvc.iguopinOffline;
const iguopinEnrollData = DataSvc.iguopinEnroll;
const employeeData = DataSvc.employees;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;

      let list = await iguopinEnrollData.getListAndCountByCondition(condition);
      if (list && list.result) {
        await Promise.all(list.result.map(async item => {
          let offline = await iguopinOfflineData.getOneByCondition({ _id: item.offlineInvitationId });
          item.offlineTitle = offline.title;
          let employee = await employeeData.getOneByCondition({ _id: item.employeeId });
          item.employeeName = employee && employee.username || '';

        }));
      }

      debug(method, '[Exit](success)');
      self.context.result = list;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
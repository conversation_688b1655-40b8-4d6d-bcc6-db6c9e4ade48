/*
 * @Author: q
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:iguopin:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetInvitationMainHandler = require('./getInvitationMainHandler');
const GetPositionInfoHandler = require('./getPositionInfoHandler');
const SaveEnrollHandler = require('./saveEnrollHandler');
const shareHandler = require('./shareHandler');
const statisticHandler = require('./statisticHandler');
const enrollListHandler = require('./enrollListHandler');
const MarketingHandler = require('./marketingHandler');
const LeagueListHandler = require('./leagueListHandler')
const LeagueApproveHandler = require('./leagueApproveHandler')

class Service {
  constructor() {

  }

  async getInvitationMain(input, _opts) {
    let method = 'getInvitationMain';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetInvitationMainHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getPositionInfo(input, _opts) {
    let method = 'getPositionInfo';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetPositionInfoHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async saveEnroll(input, _opts) {
    let method = 'saveEnroll';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new SaveEnrollHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


  async share(input, _opts) {
    let method = 'share';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new shareHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async statistic(input, _opts) {
    let method = 'statistic';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new statisticHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async enrollListHandler(input, _opts) {
    let method = 'enrollListHandler';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new enrollListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async marketing(input, _opts) {
    let method = 'marketingHandler';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new MarketingHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async leagueList(input, _opts) {
    let method = 'leagueList';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new LeagueListHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async leagueApprove(input, _opts) {
    let method = 'leagueApprove';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new LeagueApproveHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();
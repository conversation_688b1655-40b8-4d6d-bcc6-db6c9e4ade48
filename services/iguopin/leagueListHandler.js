/**
 * getInvitation<PERSON>ain<PERSON>and<PERSON>
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'leagueListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:iguopin:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const DataSvc = require('../dataSvc/dataUtil');
const guopinEmployeesData = DataSvc.guopinEmployees;
const moment = require('moment');
const STATUS = new Map([
  ["1", "待审批"],
  ["2", "通过"],
  ["3", "拒绝"]

])

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;

      let list = await guopinEmployeesData.getListAndCountByCondition(condition);
      for (const item of list.result) {
        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm");
        item.approveTime = moment(item.approveTime).format("YYYY-MM-DD HH:mm");
        item.statusName = STATUS.get(item.status) || ""
      }

      debug(method, '[Exit](success)');
      self.context.result = list;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
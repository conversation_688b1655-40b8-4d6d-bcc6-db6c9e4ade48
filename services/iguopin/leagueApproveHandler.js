/**
 * getInvitation<PERSON>ain<PERSON><PERSON><PERSON>
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'leagueApproveHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:iguopin:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const DataSvc = require('../dataSvc/dataUtil');
const guopinEmployeesData = DataSvc.guopinEmployees;
const groupV2Data = DataSvc.groupsV2;
const superagent = require('superagent')
const STATUS = new Map([
  ["1", "待审批"],
  ["2", "通过"],
  ["3", "拒绝"]
])
const config = require('config')
const createEmployeeUrl = `http://${config.get('rongxin_admin_api_service.host')}/api/v1.0/system/employee/create`;
let templateId = "SMS_224346518"
const smsALiYunDispatcher = require('../messages/dispatchers/smsALiYunDispatcher')
const MsgSvcRegistry = require('nongfu.merchant.msgfw').MsgSvcRegistry.INSTANCE;
const Froms = require('../messages/messageConfig').MessageFroms;


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;
      let id = opts.id;
      let guopinEmployees = await guopinEmployeesData.putById(id, {
        status: condition.status,
        approveTime: new Date()
      })


      // 通过则创建账号
      if (condition.status == "2" && guopinEmployees) {
        let context = {
          mobile: guopinEmployees.mobile,
          tId: guopinEmployees.tId,
          username: guopinEmployees.username,
          groups: [{
            group: "61418978eef71e9f5de43d86",
            groupV2: guopinEmployees.orgId,
            areaList: [""]
          }]
        }
        let result = await superagent.post(createEmployeeUrl).send(context)
        // 创建成功发送短信通知
        if (result) {
          let groupInfo = await groupV2Data.getById(guopinEmployees.orgId);
          let param = {
            organization: groupInfo.name || "其他"
          }
          let SmsALiYunDispatcher = MsgSvcRegistry.getDisptcher(smsALiYunDispatcher.QNAME);
          let content = {
            templateid: templateId,
            param: JSON.stringify(param),
            signName: "春耕",
            caller: "rongxin_userapp"
          };
          let smsResult = await SmsALiYunDispatcher.send_Sms(guopinEmployees.mobile, content, {});
          debug(method, 'Sms msg sent', smsResult);

          if (!smsResult.msgQueue || !smsResult.msgQueue) {
            throw {
              httpCode: 500,
              errorCode: 'INTERNALGSMS_FAL055',
              reason: 'failed to send internal msg'
            };
          }
        }

      }



      debug(method, '[Exit](success)');
      self.context.result = {
        success: true,
      };
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
/**
 * getPositionInfoHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'getPositionInfoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:iguopin:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const DataSvc = require('../dataSvc/dataUtil');
const iguopinRecordData = DataSvc.iguopinRecord;
const iguopinOfflineData = DataSvc.iguopinOffline;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let data;
      if (input.id.length == 4) {
        data = await iguopinRecordData.getOneByCondition({ id: input.id });
      } else {
        data = await iguopinRecordData.getById(input.id);
      }

      debug(method, '[Exit](success)');
      // await iguopinRecordData.putById(input.id,{$inc:{pv:1}});
      self.context.result = {
        position: data,
        com_info: {
          "id": "72718ffeec9440cd852251ffe798ada0",
          "companyname": "国投人力资源服务有限公司",
          "logo": "http://iguopin-worker.oss-cn-beijing.aliyuncs.com//uploads/20200609/dd09e698eb0256048fa2187adf10bb5a.png",
          "contents": "国投人力资源服务有限公司（简称国投人力）是国投的全资子公司，是国务院所属部门56家人才中介服务机构之一。公司以互联网为基础，服务“人”的价值实现。\n\n国投人力拥有专业化的人力资源服务体系，丰富的市场经验以及完备的业务资质，是国内领先的“互联网+人才”的新型人力资源服务企业。\n业务包括人才服务、人才引进、人才开发、人员派遣、业务外包及咨询业务等，合作客户涵盖国内外知名企业横跨能源、金融、IT、电子、快消、医疗等多个领域",
          "address": "北京市西城区阜成门北大街6号6层613室",
          "telephone": "",
          "email": "",
        }
      };
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
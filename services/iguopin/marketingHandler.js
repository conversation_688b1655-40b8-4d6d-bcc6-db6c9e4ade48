/**
 * MarketingHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'CarrouselListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('picc:eloan:mgr:app:api:services:order:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const qrcode = require('qrcode');
const env = require('../env');
const ossSvc = require('../attachment/aliOssSvc');
class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }


  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      let input = self.context.input;
      let opts = self.context.opts;

      /*QR START*/
      let qrUrl = input.qrcodeUrl;
      let bg_img_path = '/chungeng_guanjia/advertising/banner/share_guopin.png';

      let qr_buffer = Buffer.from((await qrcode.toDataURL(qrUrl, { margin: 0, width: 280, heigth: 280, errorCorrectionLevel: "H" })).replace(/^data:image\/\w+;base64,/, ""), 'base64');

      /*QR Buffer*/
      let qrOssUrl = `znrx/adTmpFile/${input.username}.png`;
      let qrReslut = await ossSvc.put(qrOssUrl, qr_buffer);

      let qrName = Buffer.from(qrReslut.name).toString('base64').replace(/\+/g, "-").replace(/\//g, "_");
      console.log('qrname', qrName);
      /*QR logo merger file name*/
      let userNameText = input.username + '邀您投递，岗位多福利好';
      let userNameText_base64 = Buffer.from(userNameText).toString('base64').replace(/\+/g, "-").replace(/\//g, "_");
      let x = parseInt(750 / 2 - (userNameText.length * 38) / 2);
      let text_watermarker = `/watermark,text_${userNameText_base64},type_ZmFuZ3poZW5naGVpdGk,size_38,shadow_0,g_nw,x_${x},y_1156`;
      let qr_watermarkder = `/watermark,image_${qrName},g_nw,x_235,y_836`;
      console.log(text_watermarker);
      console.log(qr_watermarkder);

      let optProccess = `image${text_watermarker}${qr_watermarkder}`;
      let uOpts = {
        expires: 60 * 60 * 10,
        process: optProccess
      };
      let urlRes = await ossSvc.getFile(bg_img_path, uOpts);

      /**/

      self.context.result = {
        "imageUrl": urlRes
      };
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async undoAsync(done) {
    return done();
  }
}

module.exports = Handler;
/**
 * getPositionInfoHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'getPositionInfoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:iguopin:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const DataSvc = require('../dataSvc/dataUtil');
const iguopinEnrollData = DataSvc.iguopinEnroll;
const aliyunAuthSvc = require('../aliyunAuth');
const iguopinOfflineData = DataSvc.iguopinOffline;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;

      let info = await iguopinEnrollData.getOneByCondition({ uId: input.uId, id: input.id, archived: false });
      if (info) {
        throw {
          errorCode: 'E_POSITION_35',
          httpCode: 406,
          reason: '重复报名'
        }
      }
      let offline = await iguopinOfflineData.getOneByCondition({ _id: input.offlineInvitationId });
      if (!offline) {
        throw {
          errorCode: 'E_POSITION_35',
          httpCode: 406,
          reason: '未找到邀请人'
        }
      }
      input.employeeId = offline.employeeId;

      let authRes = await aliyunAuthSvc.authIdentityByIDcard(input.name, input.idCard);
      if (!authRes || authRes.body.code !== "0") {
        throw {
          errorCode: 'E_USER_IDENTIFY_SVC_035',
          httpCode: 406,
          reason: authRes.body.message || '身份证与姓名信息不一致'
        };
      }
      if (authRes.body.result.res !== "1") {
        throw {
          errorCode: 'E_USER_IDENTIFY_SVC_042',
          httpCode: 406,
          reason: '身份证与姓名信息不一致'
        };
      }
      input.status = '01';
      let data = await iguopinEnrollData.post(input);

      debug(method, '[Exit](success)');
      self.context.result = data;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
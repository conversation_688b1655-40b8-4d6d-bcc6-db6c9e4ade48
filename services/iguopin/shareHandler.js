/**
 * getPositionInfoHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'shareHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:iguopin:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const DataSvc = require('../dataSvc/dataUtil');
const iguopinRecordData = DataSvc.iguopinRecord;
const iguopinOfflineData = DataSvc.iguopinOffline;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let userId = input.userId;
      let offline = await iguopinOfflineData.getOneByCondition({ employeeId: userId });
      if (!offline) {
        offline = await iguopinOfflineData.post({
          employeeId: userId,
          archived: false,
          status: "1",
          shareTimes: 0,
          viewTimes: 0,
          operator: userId
        })
      }
      offline = await iguopinOfflineData.putById(offline._id, {
        $inc: { shareTimes: 1 }
      });
      debug(method, '[Exit](success)');
      self.context.result = offline;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
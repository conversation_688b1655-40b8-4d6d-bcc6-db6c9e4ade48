/**
 * getInvitation<PERSON>ain<PERSON>and<PERSON>
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'getInvitationMainHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:iguopin:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const DataSvc = require('../dataSvc/dataUtil');
const iguopinRecordData = DataSvc.iguopinRecord;
const iguopinOfflineData = DataSvc.iguopinOffline;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;

      let list = await iguopinRecordData.getListAndCountByCondition(condition);
      if (list && list.result) {
        list.result.map(item => item.logo = 'http://iguopin-worker.oss-cn-beijing.aliyuncs.com//uploads/20200609/dd09e698eb0256048fa2187adf10bb5a.png')
      }
      debug(method, '[Exit](success)');
      self.context.result = list;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
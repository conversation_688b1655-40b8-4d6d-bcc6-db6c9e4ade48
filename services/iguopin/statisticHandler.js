/**
 * getPositionInfoHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'statisticHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:iguopin:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const DataSvc = require('../dataSvc/dataUtil');
const groupV2Data = DataSvc.groupsV2;
const iguopinRecordData = DataSvc.iguopinRecord;
const iguopinOfflineData = DataSvc.iguopinOffline;
const iguopinEnrollData = DataSvc.iguopinEnroll;
const employee_groupsData = DataSvc.employeeGroups;
const employeeData = DataSvc.employees;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let userId = input.userId;
      let groupV2Id = input.groupV2Id;
      let statistic = await this.getStaticByEmployeeId(userId);
      let groupStatistic = [];
      let groupEmployes = await this.findEmployeesByGroup(groupV2Id);
      await Promise.all(groupEmployes.map(async employeeId => {
        let employee = await employeeData.getById(employeeId, {cache : true , expire: 24 * 60 * 60 });
        if (employee) {
          let statisticResult = {};
          statisticResult.username = employee.username;
          let statistic1 = await this.getStaticByEmployeeId(employeeId);
          if (statistic1) {
            statisticResult = Object.assign(statisticResult, statistic1);
            groupStatistic.push(statisticResult);
          }
        }

      }));
      debug(method, '[Exit](success)');
      self.context.result = { statistic, groupStatistic };
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }

  async findEmployeesByGroup(groupV2Id) {
    let groupV2 = await groupV2Data.getOneByCondition({ _id: groupV2Id });
    if (!groupV2) {
      throw {
        errorCode: 'E_IGUOPIN_129',
        httpCode: 406,
        reason: 'groupv2 not exists'
      }
    }
    let groups = await groupV2Data.getByCondition({
      tId: groupV2.tId,
      archived: false,
      code: { $regex: `^${groupV2.code}.{3,}` },
      "addons.type": "company",
      // code: { $regex: `^${groupV2.code}.{3,}` },
      limit: "unlimited"
    });
    console.log('groups:', groups);
    let employees = [];
    await Promise.all(groups.map(async group => {
      let employee_group = await employee_groupsData.getByCondition({ 'groupV2': group._id, archived: false, limit: "unlimited" });
      if (employee_group && employee_group.length > 0) {
        employees = employees.concat(employees, employee_group.map(item => item.employee));
      }
    }));
    employees = Array.from(new Set(employees));
    console.log('employees:', employees);
    return employees;
  }

  async getStaticByEmployeeId(employeeId) {
    let offline = await iguopinOfflineData.getOneByCondition({ employeeId: employeeId });
    if (!offline) {
      return null;
    }
    let sharteTimes = offline.shareTimes || 0;
    let viewTimes = offline.viewTimes || 0;
    let enroolTimes = 0;
    let enroolResult = await iguopinEnrollData.getCountByCondition({ employeeId: employeeId, archived: false });
    enroolTimes = enroolResult && enroolResult.count || 0;
    let enroolPeopleTimes = 0;
    let enroolPepleResult = await iguopinRecordData.getByUrl('/v1.0/iguopin/managed/enroll/enroolPeopleCount', { employeeId: employeeId, archived: false });
    enroolPeopleTimes = enroolPepleResult && enroolPepleResult.total || 0;
    return { sharteTimes, viewTimes, enroolTimes, enroolPeopleTimes };
  }
}

module.exports = Handler;
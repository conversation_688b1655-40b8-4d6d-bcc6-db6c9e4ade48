/*
 * @Author: qays
 * @Date: 2021-09-17 18:21:09
 * @LastEditTime: 2021-09-18 14:47:02
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.mgr.app.api\services\gis\getCoordinateHandler.js
 */
/**
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'GetCoordinateHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:gis:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const postGisData = require('../postGisSvc');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      
      let str = "";
      for (let code of input.landCode) {
        str += `'${code}',`
      }
      str = str.substr(0, str.length - 1);

      let sql = `select st_astext(the_geom),"DKBM","DKMC","SCMJ","SCMJM","ZJRXM" from "public"."${input.dbname}" where "DKBM" in (${str})`;
      let result = await postGisData.singleQuery(sql);

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
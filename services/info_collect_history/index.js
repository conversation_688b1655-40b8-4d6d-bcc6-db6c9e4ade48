/**
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:infoCollectHistory:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const listHandler = require('./listHandler');
const detailHandler = require('./detailHandler');
const detailv2Handler = require('./detailv2Handler');


class Service {
  constructor() {

  }
  async list(input, _opts) {
    let method = 'list';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new listHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async detailv2(input, _opts) {
    let method = 'detailv2';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new detailv2Handler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async detail(input, _opts) {
    let method = 'detail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new detailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


}

module.exports = new Service();
/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'listHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const infoCollectDraftData = require('../dataSvc/dataUtil').infoCollectDraft;
const infoCollectHistoryData = require('../dataSvc/dataUtil').infoCollectHistory;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const moment = require('moment');


class Hand<PERSON> extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input, opts } = this.context;
      // if (opts.roleId && opts.userid) {

      //   let employeeGroup = await employeeGroupData.getOneByCondition({ employee: opts.userid, group: opts.roleId });
      //   if (employeeGroup && employeeGroup.groupV2 == 'groupV2') {
      //     input.op
      //   }
      // }
      if (opts.roleId == "5eb8ff2ec6ecfe44d4ecaed9") {
        input.operator = opts.userid;
      }
      let result = await infoCollectHistoryData.getListAndCountByCondition(input);
      result.result.forEach(async item => {
        item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss');
        item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');

      })
      debug(method, '[Exit](success)');
      this.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}


async function formatImg(item) {
  item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0 &&
    (item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url }));
  item && item.image && item.image.url && item.image.url.indexOf('http') !== 0 &&
    (item.image.url = await aliOssSvc.getFile({ fileName: item.image.url }));
}

async function getUserName(uId) {
  try {
    let user = await employeeData.getOneByCondition({
      _id: uId,
      "archived": false
    });
    return user && user.username || '';
  } catch (ex) {

  }
}


module.exports = Handler;
/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'listHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const infoCollectDraftData = require('../dataSvc/dataUtil').infoCollectDraft;
const infoCollectHistoryData = require('../dataSvc/dataUtil').infoCollectHistory;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const moment = require('moment');

const largeGrower_schema = require('../info_collect_draft/largeGrower_schema');
const familyFarm_schema = require('../info_collect_draft/familyFarm_schema');
const plantingCooperative_schema = require('../info_collect_draft/plantingCooperative_schema');
const machineryCooperative_schema = require('../info_collect_draft/machineryCooperative_schema');
const economicCooperative_schema = require('../info_collect_draft/economicCooperative_schema');
const stockCompany_schema = require('../info_collect_draft/stockCompany_schema');
const soleProprietorship_schema = require('../info_collect_draft/soleProprietorship_schema');
const limitedCompany_schema = require('../info_collect_draft/limitedCompany_schema');
const zh_personal_schema = require('../info_collect_draft/zh_personal_schema');
const zh_company_schema = require('../info_collect_draft/zh_company_schema');

const content_schema_enums = {
  1: largeGrower_schema,
  2: familyFarm_schema,
  3: plantingCooperative_schema,
  4: machineryCooperative_schema,
  5: economicCooperative_schema,
  6: stockCompany_schema,
  7: soleProprietorship_schema,
  8: soleProprietorship_schema,
  101: zh_personal_schema,
  102: zh_company_schema,
}

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input } = this.context;
      let item = await infoCollectHistoryData.getById(input.id);
      let type = item.type;
      item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss');
      item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
      item.operatorInfo = await getUserName(item.operator);
      Object.keys(item.content).forEach(key1 => {
        let obj1 = item.content[key1];
        if (isObject(obj1)) {
          Object.keys(obj1).forEach(key2 => {
            let obj2 = obj1[key2];
            if (isArray(obj2)) {
              let path2 = [key1, key2].join('.');
              if (content_schema_enums[type].schema_enumes) {
                let enumObj2 = getObjectByPath(content_schema_enums[type].schema_enumes, path2);
                if (enumObj2) {
                  obj1[key2 + "ShowName"] = obj2.map(key3 => {
                    return enumObj2[key3]
                  })
                }
              }

            } else {
              let path2 = [key1, key2].join('.');
              // console.log('path2:', path2);
              if (content_schema_enums[type].schema_enumes) {
                let enumObj2 = getObjectByPath(content_schema_enums[type].schema_enumes, path2);
                if (enumObj2) {
                  obj1[key2 + "ShowName"] = enumObj2[obj2];
                }
              }
            }

          })
        } else if (isArray(obj1)) {
          let obj1Array = obj1;
          for (const obj1 of obj1Array) {
            Object.keys(obj1).forEach(key2 => {
              let obj2 = obj1[key2];
              let path2 = [key1, key2].join('.');
              if (content_schema_enums[type].schema_enumes) {
                let enumObj2 = getObjectByPath(content_schema_enums[type].schema_enumes, path2);
                if (enumObj2) {
                  obj1[key2 + "ShowName"] = enumObj2[obj2];
                }
              }
            })
          }
        }

      })
      debug(method, '[Exit](success)');
      this.context.result = item;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

function getObjectByPath(obj, path) {
  let keys = path.split('.');

  return keys.reduce((pre, cur) => {
    return pre && pre[cur];
  }, obj);
}


async function getShowname(_key) {
  let obj = {};
  Object.keys(content_schema_enums[type].schema_enumes).map(deep1Key => {
    let deep1Obj = content_schema_enums[type].schema_enumes[deep1Key];
    Object.keys(deep1Obj).map(key => {
      obj[key] = deep1Obj[key]
    })
  });
  return obj[_key];
}

function isObject(a) {
  return (!!a) && (a.constructor === Object);
};

function isArray(a) {
  return (!!a) && (a.constructor === Array);
};
async function formatImg(item) {
  item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0 &&
    (item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url }));
  item && item.image && item.image.url && item.image.url.indexOf('http') !== 0 &&
    (item.image.url = await aliOssSvc.getFile({ fileName: item.image.url }));
}

async function getUserName(uId) {
  try {
    let user = await employeeData.getOneByCondition({
      _id: uId,
      "archived": false
    });
    return { username: user.username, mobile: user.mobile };
  } catch (ex) {

  }
}


module.exports = Handler;
/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 *
 */

'use strict';

const HANDLER_NAME = 'listHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const infoCollectDraftData = require('../dataSvc/dataUtil').infoCollectDraft;
const infoCollectHistoryData = require('../dataSvc/dataUtil').infoCollectHistory;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const employeeData = require('../dataSvc/dataUtil').employees;
const moment = require('moment');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input } = this.context;
      let item = await infoCollectHistoryData.getById(input.id);
      this.context.result = item;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
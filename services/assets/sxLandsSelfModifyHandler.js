/*
 * @Date: 2021-07-21 20:01:54
 * @LastEditTime: 2021-11-10 10:37:10
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.mgr.app.api\services\assets\sxLandsSelfModifyHandler.js
 */

'use strict';

const HANDLER_NAME = 'sxLandsSelfModifyHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.api:services:user:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { assetSvrLand:assetSvrLandData } = require('../dataSvc/dataUtil');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let body =  await assetSvrLandData.putById(input.id,{lastModTime:new Date(), flowType:~~input.flowType});

      debug(method, '[Exit] result:', JSON.stringify(body));
      self.context.result = body;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
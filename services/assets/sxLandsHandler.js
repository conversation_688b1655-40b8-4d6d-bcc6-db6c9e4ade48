/*
 * @Date: 2021-07-21 20:01:54
 * @LastEditTime: 2021-11-09 11:08:31
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.mgr.app.api\services\assets\sxLandsHandler.js
 */

'use strict';

const HANDLER_NAME = 'sxLandsHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr.api:services:user:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { assetSvrOutContractor } = require('../dataSvc/dataUtil');
const formatAreaCode = require('../../persistence/formatAreaCode');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let body =  await assetSvrOutContractor.getByCondition(input) || {};
      for(let item of body){
        item.areaInfo = await formatAreaCode.getFormatAreaCode(item.areaCode)
        item.areaName = item.areaInfo.area;
      }
      debug(method, '[Exit] result:', JSON.stringify(body));
      self.context.result = body;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
'use strict';

const HANDLER_NAME = 'restructuringDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  loanApplication: loanApplicationData,
  transferWhitelist: transferWhitelistData,
  employees: employeeData
} = require('../dataSvc/dataUtil');
const {
  APPLICATION_STATUS_LIST: STATUS_LIST,
} = require('../loan_application_v2/baseOperations/Constants');
 const APPLICATION_STATUS_LIST = new Map(STATUS_LIST);
//nhd：红本贷+, ncd：农场贷, nzd：农资贷
const BUSINESS_TYPE_MAP = new Map([
  ['nhd', '红本贷'],
  ['ncd', '农场贷'],
  ['nzd', '农资贷'],
]);
const RESTRUCTURING_STATUS_LIST = {
  1: '锁定',
  2: '可支用',
  3: '授权结束',
  4: '取消'
}
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input: condition, opts } = this.context;

      let transferWhitelist = await transferWhitelistData.getById(condition.id);
      
      transferWhitelist.restructuringStatus = RESTRUCTURING_STATUS_LIST[transferWhitelist.status];
      let loanInfo = await loanApplicationData.getById(transferWhitelist.aId);

      transferWhitelist.loanStatus = APPLICATION_STATUS_LIST.get(transferWhitelist.loan.status);
      transferWhitelist.contractorNo = loanInfo.addons.contractorNo;
      let data = await formatAreaCode.getFormatAreaCode(transferWhitelist.area);
      transferWhitelist.location = data && data.area || "";
      transferWhitelist.type = BUSINESS_TYPE_MAP.get(transferWhitelist.loanType);
      let oaImages = [];
      for(let item of transferWhitelist.oaImages){
        oaImages.push(await aliOssSvc.getFile({ fileName: item }));
      }
      transferWhitelist.oaImages = oaImages;
      for(let item of transferWhitelist.trackings){
        let employee = await employeeData.getById(item.target);
        item.operator = employee.username;
        item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
      }
      
      self.context.result = transferWhitelist;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
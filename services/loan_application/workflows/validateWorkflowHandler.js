/**
* validateWorkflowHandler
* <AUTHOR>
* 2017-09-14
*/

'use strict';

const HANDLER_NAME = 'ValidateWorkflowHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanAppWorkflows = require('./loanAppWorkflows');
const loanAppWorkflow = loanAppWorkflows.loanAppWorkflow;

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  /**
   * Get current handler name
   * @returns {string}
   */
  getName() {
    return HANDLER_NAME;
  }

  /**
  * prepare infos for fin workflow
  */
  doAsync(done) {
    let self = this;
    try {
      let method = self.getName();
      debug(method, '[Enter]');

      if (!self.context.input || !self.context.input.id || !self.context.input.body)
        throw {
          httpCode: 406,
          errorCode: 'EFINPARAM0044',
          reason: 'invalid input'
        }

      if (!self.context.opts || !self.context.opts.user)
        throw {
          httpCode: 401,
          errorCode: 'SE001',
          reason: 'invalid user'
        }

      self.context.opts.loanAppWorkflow = loanAppWorkflow;

      if (!loanAppWorkflow.validateState(self.context.opts.preWorkFlow.currentStatus))
        throw {
          httpCode: 406,
          errorCode: 'EFINPARAM0060',
          reason: '该表单状态不存在于状态列表中: ' + self.context.opts.preWorkFlow.currentStatus
        };

      let workflowConnection = loanAppWorkflow.getWorkflowConnection(self.context.opts.preWorkFlow.currentStatus, self.context.opts.preWorkFlow.targetStatus)

      if (!workflowConnection)
        throw {
          httpCode: 406,
          errorCode: 'EFINPARAM0069',
          //reason: '当前表单状态不允许更改为目标状态: from ' + self.context.opts.preWorkFlow.currentStatus + ' to ' + self.context.opts.preWorkFlow.targetStatus
          reason: "订单状态已变更，请刷新页面。"
        };

      self.context.opts.workflowConnection = workflowConnection;
      debug(self.getName(), '[Exit](success)');
      return done();
    } catch (err) {
      debug(self.getName(), '[Exit](failed)', err);
      return done(err);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
/**
 * @summary preWorkflowHandler
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:39:32 
 * Last modified  : 2018-12-13 17:32:26
 */

'use strict';

const HANDLER_NAME = 'preWorkflowHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  /**
   * Get current handler name
   * @returns {string}
   */
  getName() {
    return HANDLER_NAME;
  }

  /**
   * get insurance info berfor workflow
   * @param {*} done 
   */
  async doAsync(done) {
    let self = this;
    try {
      let method = self.getName();
      debug(method, '[Enter]');

      if (!self.context.input || !self.context.input.id || !self.context.input.body) {
        throw {
          httpCode: 406,
          errorCode: 'EORDERPARAM0001',
          reason: 'invalid input'
        }
      }

      if (!self.context.opts || !self.context.opts.user) {
        throw {
          httpCode: 401,
          errorCode: 'SE001',
          reason: 'invalid user'
        }
      }

      try {
        let preWorkFlow = {
          user: self.context.opts.user,
          targetStatus: self.context.opts.status,
          userRoles: ""
        };
        let promises = [];
        promises.push(loanApplicationData.getById(self.context.input.id).then(data => {
          preWorkFlow.loanAppInfo = data || {};
          preWorkFlow.currentStatus = data.status;
        }));
        await Promise.all(promises);

        self.context.opts.preWorkFlow = preWorkFlow;

        debug(self.getName(), '[Exit](success)');
        return done();
      } catch (error) {
        debug(self.getName(), '[Exit](failed)', error);
        return done(error);
      }

    } catch (error) {
      debug(method, '[Exit](error)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
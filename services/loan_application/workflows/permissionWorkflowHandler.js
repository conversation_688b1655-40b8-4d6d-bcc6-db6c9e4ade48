/**
 * @summary permissionWorkflowHandler
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 17:11:31 
 * Last modified  : 2018-12-13 17:52:53
 */

'use strict';

const HANDLER_NAME = 'permissionWorkflowHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  /**
   * Get current handler name
   * @returns {string}
   */
  getName() {
    return HANDLER_NAME;
  }

  /**
  * prepare infos for order workflow
  */
  doAsync(done) {
    let self = this;
    try {
      let method = self.getName();
      debug(method, '[Enter]');

      if (!self.context.input || !self.context.input.id || !self.context.input.body)
        throw {
          httpCode: 406,
          errorCode: 'EFINPARAM0044',
          reason: 'invalid input'
        }

      if (!self.context.opts || !self.context.opts.user)
        throw {
          httpCode: 401,
          errorCode: 'SE001',
          reason: 'invalid user'
        }

      let formOwner = self.context.opts.preWorkFlow.loanAppInfo.user;

      let callerRole = {};

      let userRoles = self.context.opts.preWorkFlow.userRoles;
      

      debug(self.getName(), '[Exit](success)');
      return done();
    } catch (err) {
      debug(self.getName(), '[Exit](failed)', err);
      return done(err);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
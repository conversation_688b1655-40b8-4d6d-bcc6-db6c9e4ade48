'use strict';

const HANDLER_NAME = 'UpdateDistributeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:services:LoanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanDistributeData = require('../dataSvc/dataUtil').loanDistribute;
const LOAN_APP_STATUS = require('../../utils/const/applicationConst').LOAN_APP_STATUS;
const successStatus = new Set([LOAN_APP_STATUS.LOAN_APP_WAITLOAN, LOAN_APP_STATUS.LOAN_APP_WAIT_INCOME, LOAN_APP_STATUS.LOAN_APP_LOANED]);

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      if (successStatus.has(input.body.status)) {
        debug(method, '[Exit](continue)');
        return done();
      }
      let user = opts.user;
      let tacking = await loanDistributeData.getOneByCondition({ "assigned": user.userid, "aId": input.id, "handleStatus": false });
      if (tacking && tacking._id) {
        let payload = {};
        payload.handleStatus = true;
        payload.handleTime = new Date();
        let data = await loanDistributeData.putById(tacking._id, payload);
        debug(method, '[Exit](success)', data);
      }

      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
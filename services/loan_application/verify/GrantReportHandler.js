/**
 * @summary loanAppWorkflows
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'GrantReportHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const {BaseHandler} = require('nongfu.merchant.svcfw');
const {SvcHandlerMgrt} = require('nongfu.merchant.svcfw');

const config = require('config');
const agent = require('superagent');
const decisionUrl = `http://${config.get('decision_Service.host')}/api/v1.0/order/callDecisionRuleEngine`;
const decisionCallbackUrl = `http://${config.get('decision_Service.callback_host')}/api/v1.0/loan/application/verify/notify`;
// decision_Service.callback_host baseUrl
const {
    loanApplicationOutLand: loanApplicationOutLandData,
    loanApplicationLandType: loanApplicationLandTypeData,
    loanApplication: loanApplicationData,
    LoanSvrLand: LoanSvrLandData,
    employeeGroups:employeeGroupsData,
    groups:groupsData,
    infoCollectHistory:infoCollectHistoryData,
    loanApplicationVerify:loanApplicationVerifyData,
    loanSupplement: loanSupplementData,
    userAddons:userAddonsData,
    userCxwqSupplement: userCxwqSupplementData,
    loanReport:reportData,
    userVerifys:userVerifyData,
    loanData,
} = require('../../dataSvc/dataUtil');

const {assert} = require('../../../utils/general')

async function grantReportHandlerV2(app,source){
    assert(app.addons && app.addons.info && app.addons.info.mId,'E_APPLICATION_VERIFY_100','信息未采集');
    const verify = await userVerifyData.getOneByCondition({archived:false,uId:app.uId,IDCardStatus: 'approved'});
    assert(verify,'E_APPLICATION_VERIFY_101','用户没有实名认证通过');
    const collection = await infoCollectHistoryData.getById(app.addons.info.mId);
    debug(`${HANDLER_NAME}V2Step1`,app._id,app.addons.info.mId,collection.type);
    assert(collection,'E_APPLICATION_VERIFY_101','错误的信息采集id');
    let {person=[],enterprise=[]} = reportHandler[ collection.type ] && reportHandler[ collection.type ].call( this,app,collection ) || {};
    debug(`${HANDLER_NAME}V2Step2`,person,enterprise);
    person = person.filter(v=>v && v.cerdId && v.customerName && v.prePhonetel);
    person.some(({cerdId})=>verify.IDCard === cerdId) //如果在验证名单中没有找到申请人，将申请人加入校验名单
        || person.unshift({cerdId:verify.IDCard,customerName:app.username,prePhonetel:app.mobile})
    person = person.map( v=>( {
        appCode : config.get('decision_Service.cxwq_apiCode'),
        callbackUrl: decisionCallbackUrl,
        bizModel:{...v,orderTradeNo : app._id},
    } ) );

    enterprise = enterprise.filter(v=>v).map( v=>( {
        appCode : config.get('decision_Service.cxwq_enterprise_apiCode'),
        callbackUrl: decisionCallbackUrl,
        bizModel:{...v,orderTradeNo : app._id},
    } ) );
    const reports = app.addons && app.addons.cxwqData && app.addons.cxwqData.reportFiles || [];
    debug(`${HANDLER_NAME}V2Step3`,person,enterprise,reports);
    person.forEach( it=>{
        const existIndex = reports.findIndex(v=>v.idCard===it.bizModel.cerdId);
        existIndex >= 0 && reports.splice( existIndex ,1);
        reports.push( {type:'internal_decision',source,name:it.bizModel.customerName,idCard:it.bizModel.cerdId,mobile:it.bizModel.prePhonetel});
    } ) ;
    enterprise.forEach( it=>{
        const existIndex = reports.findIndex(v=>v.socialCreditCode===it.bizModel.socialCreditCode);
        existIndex >= 0 && reports.splice( existIndex ,1);
        reports.push( {type:'internal_decision_enterprise',source,name:it.bizModel.enterpriseName,socialCreditCode:it.bizModel.socialCreditCode});
    } ) ;
    debug(`${HANDLER_NAME}V2Step4`,reports);
    //清空之前的报告数据
    await loanApplicationData.putById(app._id, {"addons.cxwqData.reportFiles":reports,lastModTime: new Date()});
    const removeList = await reportData.getByCondition({aId:app._id,limit:'unlimited'});
    await Promise.all( removeList.map(it=>reportData.putById(it._id,{archived:false})) );

    await Promise.all( [...person,...enterprise].map(async para=>{
        //看之前的代码是一次性并行发出，可合适？若不妥将来再改
        debug(`${HANDLER_NAME}ReportCall1`,decisionUrl,para);
        const result = await agent.post(decisionUrl).send(para);
        // debug(HANDLER_NAME, '[continue] Info', para);
        debug(`${HANDLER_NAME}ReportCall2`, result.body);
    }) );
    await loanApplicationData.putById(app._id,{reportTime:Date.now()});
}
const reportHandler = {
    "01":(app,{content:info})=>({
        //八大类型之一
        person:[
            info.basic && { customerName: info.basic.name,prePhonetel: info.basic.mobile,cerdId:info.basic.idCard},
            ...(info.familyMembers || [] ).filter(it=>parseInt(it.relationship)===3)//只需配偶，不许多
                .map( it=>({customerName:it.name,prePhonetel:it.mobile,cerdId:it.idCard}) ),
        ],
    }),
    "03":(app,{content:info})=>({
        //八大类型之二
        person:[
            info.basic && { customerName: info.basic.name,prePhonetel: info.basic.mobile,cerdId:info.basic.idCard},
            ...(info.familyMembers || [] ).filter(it=>parseInt(it.relationship)===3)//只需配偶，不许多
                .map( it=>({customerName:it.name,prePhonetel:it.mobile,cerdId:it.idCard}) ),
        ],
        enterprise:[
            info.companyBasic && {enterpriseName:info.companyBasic.companyName,socialCreditCode:info.companyBasic.companyCode},
        ],
    }),
    "04":(app,{content:info})=>({
        //八大类型之三和四
        person:[
            info.basic && { customerName: info.basic.name,prePhonetel: info.basic.mobile,cerdId:info.basic.idCard},
        ],
        enterprise:[
            info.companyBasic && {enterpriseName:info.companyBasic.companyName,socialCreditCode:info.companyBasic.companyID},
        ],
    }),
    // "06":(app,{content:info})=>({
    //     //八大类型之五，以下有重复代码。经过沟通，为了保持机动性，采取复制的方式，未提纯
    //     person:[
    //         info.basic && { customerName: info.basic.name,prePhonetel: info.basic.mobile,cerdId:info.basic.idCard},
    //     ],
    //     enterprise:[
    //         info.companyBasic && {enterpriseName:info.companyBasic.companyName,socialCreditCode:info.companyBasic.companyID},
    //     ],
    // }),
    "06":(app,{content:info})=>({
        //八大类型之五
        person:[
            info.basic && { customerName: info.basic.name,prePhonetel: info.basic.mobile,cerdId:info.basic.idCard},
            ...(info.familyMembers || [] ).filter(it=>[2,3].includes(parseInt(it.relationship)))//只需监事和财务，不许多
                .map( it=>({customerName:it.name,prePhonetel:it.mobile,cerdId:it.idCard}) ),
        ],
        enterprise:[
            info.companyBasic && {enterpriseName:info.companyBasic.companyName,socialCreditCode:info.companyBasic.companyID},
        ],
    }),
    "07":(app,{content:info})=>({
        //八大类型之六
        person:[
            info.basic && { customerName: info.basic.name,prePhonetel: info.basic.mobile,cerdId:info.basic.idCard},
            ...(info.familyMembers || [] ).filter(it=>[1,2].includes(parseInt(it.relationship)))//只需投东和董事，不许多
                .map( it=>({customerName:it.name,prePhonetel:it.mobile,cerdId:it.idCard}) ),
        ],
        enterprise:[
            info.companyBasic && {enterpriseName:info.companyBasic.companyName,socialCreditCode:info.companyBasic.companyID},
        ],
    }),
    "08":(app,{content:info})=>({
        //八大类型之七
        person:[
            info.basic && { customerName: info.basic.name,prePhonetel: info.basic.mobile,cerdId:info.basic.idCard},
        ],
        enterprise:[
            info.companyBasic && {enterpriseName:info.companyBasic.companyName,socialCreditCode:info.companyBasic.companyID},
        ],
    }),
    "09":(app,{content:info})=>({
        //八大类型之八
        person:[
            info.basic && { customerName: info.basic.name,prePhonetel: info.basic.mobile,cerdId:info.basic.idCard},
        ],
        enterprise:[
            info.companyBasic && {enterpriseName:info.companyBasic.companyName,socialCreditCode:info.companyBasic.companyID},
        ],
    }),
};
reportHandler['05'] = reportHandler['04'];//种植合作社与农机合作社一样

class Handler extends BaseHandler {
    constructor(context) {
        super(context)
    }

    getName() {
        return HANDLER_NAME
    }

    async doAsync(done) {
        const method = `${this.getName()}.doAsync`
        debug(method, '[Enter]')
        try {
            const {id,source} = this.context.input;
            const app = await loanApplicationData.getById(id);
            debug(`${method}Paras`,id);

            app.appVersion === '2.0' && await grantReportHandlerV2.call(this,app,source);

            this.context.result = {success:'ok'}
            debug(method, '[Exit](success)');
            return done();
        } catch (error) {
            debug.error(method, '[Exit](failed)', error);
            return done(error);
        }
    }

    undoAsync(done) {
        done()
    }
};

module.exports = Handler;
/**
 * @summary 待尽职调查，登记员记录信息，并向管理员提交审核
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'investigationVerify2ForCommitHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const GrantReportHandler = require('./GrantReportHandler');
const {BaseHandler} = require('nongfu.merchant.svcfw');
const {SvcHandlerMgrt} = require('nongfu.merchant.svcfw');
const eSignSvc = require('../../eSignSvc');
const moment = require('moment');
const Decimal = require('decimal.js');
const ExecuteLoanAppVerifyHandler = require('./ExecuteLoanAppVerifyHandler.js');
const getOrgListHandler = require('./getOrgListHandler');

const {
  loanApplicationOutLand: loanApplicationOutLandData,
  loanApplicationLandType: loanApplicationLandTypeData,
  loanApplication: loanApplicationData,
  LoanSvrLand: LoanSvrLandData,
  employeeGroups:employeeGroupsData,
  employees:employeeData,
  groups:groupsData,
  infoCollectHistory:infoCollectHistoryData,
  loanApplicationVerify:loanApplicationVerifyData,
  loanSupplement: loanSupplementData,
  userAddons:userAddonsData,
  userCxwqSupplement: userCxwqSupplementData,
  contractFlowsV2: contractFlowsV2Data,
  insOrder: insOrderData,
  fundReceive: fundReceiveData,
  cxwqCirculations:cxwqCirculationData,
  applicationSubcontractLand:applicationSubcontractLandData,
  loanApplicationConfirmLand:loanApplicationConfirmLandData,
  loanApplicationGrainSale:loanApplicationGrainSaleData,
  landSupervisoryRecord:landSupervisoryRecordData,
  loanLand:loanLandData,
  loanData,
} = require('../../dataSvc/dataUtil');

const {verifyConfig , supplementKeyConfig } = require('./VerifyUtils');

const {assert,parseEmployee} = require('../../../utils/general')
// const mappingStatusConfig = {
//   wait_investigation_verify_2_pre:['RegistrationOperator'],
//   wait_investigation_verify_3_pre:['Supervisor'],
//   wait_investigation_verify_4_pre:['RegionalApprover'],
// }
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input:{id,action='agree',description,roleId,roleId:group,userId:employee,verifyInfo,enough} , opts , opts:{ investigationVerify } } = this.context;
      const supplements = verifyInfo && verifyInfo.supplements || [];
      const app = id && await loanApplicationData.getById(id);
      assert(app,'E_APPLICATION_VERIFY_104','不存在的金融订单');
      assert( app.status === 'wait_investigation_verify_2' , 'E_APPLICATION_INVESTIGATION_VERIFY_001' , '金融订单未处于复核尽调阶段' )
      assert( group && employee , 'E_APPLICATION_INVESTIGATION_VERIFY_002' , 'permission denied' );
      const mappingStatusConfig = supplementKeyConfig[ app.status ];
      const { roleName , orgId , orgCode } = await parseEmployee({ roleId , tId:app.tId , uId:employee });
      // debug('debug233 ',app.status,roleName,group,app.tId,employee);
      assert( roleName , 'E_APPLICATION_INVESTIGATION_VERIFY_007a' , 'role is error' );
      const cf = verifyConfig.find( v=>v.current === app.status && Array.isArray( v.committer ) && v.committer.includes( roleName ) );
      assert( cf , 'E_APPLICATION_INVESTIGATION_VERIFY_003' , 'permission denied !' );
      const [verifyStatus] = Object.entries( mappingStatusConfig ).find( ([mappingStatus,roles])=>roles.includes( roleName ) ) || []
      assert( verifyStatus , 'E_APPLICATION_INVESTIGATION_VERIFY_007b' , 'role is error！！' );
      app.verifyInfo = app.verifyInfo || {};
      app.verifyInfo.investigationInfo = app.verifyInfo.investigationInfo || {}
      app.verifyInfo.investigationInfo[ cf.sign ] = app.verifyInfo.investigationInfo[ cf.sign ] || {};
      app.verifyInfo.investigationInfo[ cf.sign ].status = app.verifyInfo.investigationInfo[ cf.sign ].status || 0;// 0 是未操作过，1是已提交，2是打回，3是通过
      assert( [0,2].includes( app.verifyInfo.investigationInfo[ cf.sign ].status ) , 'E_APPLICATION_INVESTIGATION_SUBMITE_004' , '已经提交资料' )
      assert( [true,false].includes( enough ) , 'E_APPLICATION_INVESTIGATION_VERIFY_005' , '是否尽调是必填项' )
      const update = {
        [`verifyInfo.investigationInfo.${cf.sign}.status`]: 1,
        [`verifyInfo.investigationInfo.${cf.sign}.committerDescription`]: description,
        [`verifyInfo.investigationInfo.${cf.sign}.committerAction`]: action,
        [`verifyInfo.investigationInfo.${cf.sign}.enough`]: enough,
        // [`verifyInfo.investigationInfo.${cf.sign}.supplements`]: supplements || [],
      }
      const result = await loanApplicationData.putById( id , update );//告知部门领导审核
      const verify = await new ExecuteLoanAppVerifyHandler().addVerifyLog( app._id , employee , roleName , verifyStatus , 'finished' , description );
      // const verify = {aId:app._id ,status:verifyStatus ,action:'finished',description ,roleName ,
      //   operator:employee,createdTime:new Date(),lastModTime:new Date(),archived:false}
      // Object.assign( verify , await loanApplicationVerifyData.post(verify) );
      this.context.result = { success:'ok',result , verify }
      debug(method,'agree', '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};

module.exports = Handler;


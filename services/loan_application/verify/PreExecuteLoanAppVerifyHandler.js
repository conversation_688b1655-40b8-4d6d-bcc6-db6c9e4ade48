/**
 * @summary loanAppWorkflows
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'PreExecuteLoanAppVerifyHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const GrantReportHandler = require('./GrantReportHandler');
const {BaseHandler} = require('nongfu.merchant.svcfw');
const {SvcHandlerMgrt} = require('nongfu.merchant.svcfw');
const eSignSvc = require('../../eSignSvc');
const moment = require('moment');
const Decimal = require('decimal.js');

const {assert} = require('../../../utils/general')

const {
  loanApplicationOutLand: loanApplicationOutLandData,
  loanApplicationLandType: loanApplicationLandTypeData,
  loanApplication: loanApplicationData,
  LoanSvrLand: LoanSvrLandData,
  employeeGroups:employeeGroupsData,
  employees:employeeData,
  groups:groupsData,
  infoCollectHistory:infoCollectHistoryData,
  loanApplicationVerify:loanApplicationVerifyData,
  loanSupplement: loanSupplementData,
  userAddons:userAddonsData,
  // userCxwqSupplement: userCxwqSupplementData,
  contractFlowsV2: contractFlowsV2Data,
  insOrder: insOrderData,
  fundReceive: fundReceiveData,
  // loanData,
} = require('../../dataSvc/dataUtil');

const { supplementKeyConfig:config } = require('./VerifyUtils');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {id,verifyInfo,roleId:group,userId:employee,description,addonVerifyInfo={}} = this.context.input;
      const { saveSupplementsAtPreHandle } = this.context.opts ;
      const admin = employee && await employeeData.getById(employee, {cache : true , expire: 24 * 60 * 60 });
      const role = group && await groupsData.getById(group);
      process.env.DEBUG_NO_NEED_LOGIN || assert(role,'E_APPLICATION_VERIFY_04','不存在的管理员权限');
      process.env.DEBUG_NO_NEED_LOGIN || assert(admin,'E_APPLICATION_VERIFY_004','没有登陆！');

      debug('debug233 0:',verifyInfo)
      if(!verifyInfo)return done();
      const keys = ['supplements','verifyFiles'];
      const supplements = keys.map( key=>{
        debug('debug233 a:',key,verifyInfo[key])
        const arr = [ ...verifyInfo[key] || [] , ...addonVerifyInfo && addonVerifyInfo[key] || [] ];
        debug('debug233 b:',arr)

        arr.forEach(v=> {
          //加上操作员信息
          v.key = key;
          v.createdTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
          v.operator = admin && admin._id;
          v.operatorName = admin && admin.username;
        });
        return arr;
      } ).reduce( (r,v)=>r.concat( v ) , [] );
      // if( !supplements.length )return done();

      const app = await loanApplicationData.getById(id);
      debug(method,'PreQueryPara1',id,app,config[app.status]);
      const cf = config[app.status] || {[app.status]:[role.name]}//如果没有配置，构建默认值。记录supplements时，并行流需要映射一下状态值
      app.verifyInfo = app.verifyInfo || {};
      debug(method,'PreQueryPara2',id,cf,role.name);
      const [key] = Object.entries(cf).find(([key,roleList])=>roleList.includes(role.name)) || [];
      if( !key )return done();// 如果不是当前角色，不让上传但不能报错
      assert(key,'E_APPLICATION_VERIFY_044','错误的角色');
      verifyInfo.supplements = Object.assign( app.verifyInfo.supplements || {} , {[key]:{supplements,description}} )
      saveSupplementsAtPreHandle && await loanApplicationData.putById( app._id , { 'verifyInfo.supplements':verifyInfo.supplements } );
      this.context.opts.supplementsKey = key;
      debug(method, 'result',{[key]:{supplements,description}},verifyInfo.supplements);
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};

module.exports = Handler;

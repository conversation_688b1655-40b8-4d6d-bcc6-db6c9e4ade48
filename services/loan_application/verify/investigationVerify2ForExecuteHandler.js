/**
 * @summary 待尽职调查部门领导审核：批准或拒绝，并记录尽调与否
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'InvestigationVerify2ExecuteHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const GrantReportHandler = require('./GrantReportHandler');
const {BaseHandler} = require('nongfu.merchant.svcfw');
const {SvcHandlerMgrt} = require('nongfu.merchant.svcfw');
const moment = require('moment');
const Decimal = require('decimal.js');
const ExecuteLoanAppVerifyHandler = require('./ExecuteLoanAppVerifyHandler.js');

const {
  loanApplicationOutLand: loanApplicationOutLandData,
  loanApplicationLandType: loanApplicationLandTypeData,
  loanApplication: loanApplicationData,
  LoanSvrLand: LoanSvrLandData,
  employeeGroups:employeeGroupsData,
  employees:employeeData,
  groups:groupsData,
  infoCollectHistory:infoCollectHistoryData,
  loanApplicationVerify:loanApplicationVerifyData,
  loanSupplement: loanSupplementData,
  userAddons:userAddonsData,
  userCxwqSupplement: userCxwqSupplementData,
  contractFlowsV2: contractFlowsV2Data,
  insOrder: insOrderData,
  fundReceive: fundReceiveData,
  cxwqCirculations:cxwqCirculationData,
  applicationSubcontractLand:applicationSubcontractLandData,
  loanApplicationConfirmLand:loanApplicationConfirmLandData,
  loanApplicationGrainSale:loanApplicationGrainSaleData,
  landSupervisoryRecord:landSupervisoryRecordData,
  loanLand:loanLandData,
  loanData,
} = require('../../dataSvc/dataUtil');

const {verifyConfig} = require('./VerifyUtils');

const {assert,parseEmployee} = require('../../../utils/general')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input, input:{id,action='agree',description,enough,roleId,roleId:group,userId:employee,verifyInfo} , opts , opts:{ investigationVerify , supplementsKey } } = this.context;
      if( !investigationVerify )return done();
      const app = id && await loanApplicationData.getById(id);
      assert(app,'E_APPLICATION_VERIFY_104','不存在的金融订单');
      assert( app.status === 'wait_investigation_verify_2' , 'E_APPLICATION_INVESTIGATION_VERIFY_001' , '金融订单未处于复核尽调阶段' )
      assert( group && employee , 'E_APPLICATION_INVESTIGATION_VERIFY_002' , 'permission denied' )
      const { roleName , orgId , orgCode } = await parseEmployee({ roleId , tId:app.tId , uId:employee });
      assert( roleName , 'E_APPLICATION_INVESTIGATION_VERIFY_005' , 'role is error' );
      const cf = verifyConfig.find( v=>v.current === app.status && Array.isArray( v.employee ) && v.employee.includes( roleName ) );
      assert( cf , 'E_APPLICATION_INVESTIGATION_VERIFY_003' , 'permission denied !' );
      const {status} = app.verifyInfo && app.verifyInfo.investigationInfo && app.verifyInfo.investigationInfo[cf.sign] || {}
      const approved = app.verifyInfo && app.verifyInfo.signInfo && app.verifyInfo.signInfo[cf.sign] && true ||false
      // && app.verifyInfo.investigationInfo[ cf.sign ] && app.verifyInfo.investigationInfo[ cf.sign ].status;
      // debug('debug233',cf.sign,roleName,app.verifyInfo.investigationInfo,status,approved);
      assert( !( status === 3 || approved ) , 'E_APPLICATION_INVESTIGATION_VERIFY_004' , '您已经审核过这个订单了' )
      assert( status === 1 , 'E_APPLICATION_INVESTIGATION_VERIFY_004b' , '登记业务员还没有提交' )
      verifyInfo && delete verifyInfo.supplements;
      delete input.description; // 删除附加意见，但保留了 verify 表里的意见
      const supplementsNode = verifyInfo && verifyInfo.supplements && supplementsKey && verifyInfo.supplements && verifyInfo.supplements[ supplementsKey ];
      supplementsNode && delete supplementsNode.description;// 详情中展示审批意见的时候，并行流要特殊处理，使用别的字段
      if( action === 'refuse'  ){
        const update = { 
          [`verifyInfo.investigationInfo.${cf.sign}.status`]: 2,
          [`verifyInfo.investigationInfo.${cf.sign}.leaderDescription`]: description,
          [`verifyInfo.investigationInfo.${cf.sign}.leaderAction`]: action,
        }
        // debug('debug233 a', JSON.stringify( update  ) );
        Object.assign( app , await loanApplicationData.putById( id , update ) );//打回重审
        opts.stopExecute = true;
        const verify = await new ExecuteLoanAppVerifyHandler().addVerifyLog( app._id , employee , roleName , cf.verifyStatus , 'rejectBack' , description );
        // const verify = {aId:app._id ,status:cf.verifyStatus ,action:'rejectBack',description ,roleName ,
        //   operator:employee,createdTime:new Date(),lastModTime:new Date(),archived:false}
        // Object.assign( verify , await loanApplicationVerifyData.post(verify) );
        this.context.result = {success:'ok' , app , verify };
        debug(method,'refuse', '[Exit](success)');
        return done();
      }
      
      const update = { 
        [`verifyInfo.investigationInfo.${cf.sign}.leaderAction`]: action,
        [`verifyInfo.investigationInfo.${cf.sign}.status`]: 3 ,
        [`verifyInfo.investigationInfo.${cf.sign}.leaderDescription`]: description,
      };
      // debug('debug233 b', JSON.stringify( update  ) );
      await loanApplicationData.putById( id , update );//打回重审
      debug(method,'agree', '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};

module.exports = Handler;


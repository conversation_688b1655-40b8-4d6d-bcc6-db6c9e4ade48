'use strict';

const HANDLER_NAME = 'getOrgListHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.admin.api:services:tenant:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { groupV2:groupV2Data , employeeGroups:employeeGroupsData } = require('../../dataSvc/dataUtil');
const {assert} = require('../../../utils/general');
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input:condition,input:{tId,areaCode},opts,opts:{ byAreaCode,withroot } } = this.context;
      assert( tId , 'E_GET_ORG_LIST_001' , 'tId is required' );
      byAreaCode && assert( areaCode , 'E_GET_ORG_LIST_002' , 'areaCode is required' );
      condition.enable = 1;
      const result1 = !areaCode && await groupV2Data.getByCondition( condition  );//如果没有传入areaCode限制，返回所有数据
      const result2 = areaCode && await this.list( tId , areaCode , opts );//如果限制了areaCode，从本级向上级查找第一个匹配的列表
      const result = result1 || result2;
      this.context.result = result;
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async list( tId,areaCode , opts ){
    const formatFunc = (it,orgAreaCode)=>({  orgId:it._id , orgAreaCode , ...it });
    const arr = areaCode.split('') , queries = Array.from( new Set( [2,4,6,9,12].map( len=>arr.slice( 0 , len ).join('') ).filter(v=>v).reverse() ) );
    debug(`${HANDLER_NAME}ListPara`,tId,areaCode,queries);
    const result = ( await Promise.all( queries.map( async orgAreaCode=>{
      const list = await groupV2Data.getByCondition( { archived:false , enable:1 ,areaList:orgAreaCode,tId }  );
      return list.map( it=>formatFunc( it , orgAreaCode ) );
    } ) ) ).filter( v=>v.length ).shift();
    // 根据配置，可变更县域列表会调这个方法。其中的逻辑可能强行加上物权公司总部
    const root = formatFunc( await groupV2Data.getById('60111486fda3812feb51fd00') , '23' );//物权公司特殊
    opts.withroot && result.unshift( root );
    return result;
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
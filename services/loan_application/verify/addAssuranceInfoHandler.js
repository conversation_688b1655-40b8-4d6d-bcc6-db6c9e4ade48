/**
 * @summary 待尽职调查，登记员记录信息，并向管理员提交审核
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'addAssuranceInfoHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const GrantReportHandler = require('./GrantReportHandler');
const {BaseHandler} = require('nongfu.merchant.svcfw');
const {SvcHandlerMgrt} = require('nongfu.merchant.svcfw');
const eSignSvc = require('../../eSignSvc');
const moment = require('moment');
const Decimal = require('decimal.js');
const ExecuteLoanAppVerifyHandler = require('./ExecuteLoanAppVerifyHandler.js');

const {
  loanApplicationOutLand: loanApplicationOutLandData,
  loanApplicationLandType: loanApplicationLandTypeData,
  loanApplication: loanApplicationData,
  LoanSvrLand: LoanSvrLandData,
  employeeGroups:employeeGroupsData,
  employees:employeeData,
  groups:groupsData,
  infoCollectHistory:infoCollectHistoryData,
  loanApplicationVerify:loanApplicationVerifyData,
  loanSupplement: loanSupplementData,
  userAddons:userAddonsData,
  userCxwqSupplement: userCxwqSupplementData,
  contractFlowsV2: contractFlowsV2Data,
  insOrder: insOrderData,
  fundReceive: fundReceiveData,
  cxwqCirculations:cxwqCirculationsData,
  applicationSubcontractLand:loanApplicationSubcontractLandData,
  loanApplicationConfirmLand:loanApplicationConfirmLandData,
  loanApplicationGrainSale:loanApplicationGrainSaleData,
  loanLand:loanLandData,
  loanData,
} = require('../../dataSvc/dataUtil');

const {verifyConfig , supplementKeyConfig } = require('./VerifyUtils');

const {assert,parseEmployee} = require('../../../utils/general');
const FormatApplicatVerifyInfoHandler = require('../../loan_application_v2/baseOperations/formatApplicatVerifyInfoHandler')
const getAllLandListWithTypeHandler = require('../../loanApplicationLandType/getAllLandListWithTypeHandler');

const {cloneDeep} = require('lodash');
const cropTypeNameDic = {
  'corn': '玉米' ,
  'rice': '水稻' ,
  'soybean': '大豆' ,
  'other': '其它' ,
}

// const mappingStatusConfig = {
//   wait_investigation_verify_2_pre:['RegistrationOperator'],
//   wait_investigation_verify_3_pre:['Supervisor'],
//   wait_investigation_verify_4_pre:['RegionalApprover'],
// }
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input, input:{id,action='agree',description,roleId,roleId:group,userId:employee,verifyInfo,record:data} , opts , opts:{ addAssuranceInfo } } = this.context;
      if( !addAssuranceInfo )return done();
      const executeLoanAppVerifyHandler = new ExecuteLoanAppVerifyHandler();
      const app = id && await loanApplicationData.getById(id);
      assert(app,'E_APPLICATION_VERIFY_104','不存在的金融订单');
      assert( app.status === 'loan_verify_4' , 'E_APPLICATION_ADD_ASSURANCE_INFO_ERROR_001' , '金融订单未处于待生成鉴证报告阶段' );
      debug(`${HANDLER_NAME}Para`,app._id);
      assert( app.verifyInfo && app.verifyInfo.assuranceInfo , 'E_APPLICATION_ADD_ASSURANCE_INFO_ERROR_010' , '数据有误！' );
      const {lands:allMyLands=[] } = app.verifyInfo && app.verifyInfo.occupyInfo && app.verifyInfo.occupyInfo || {};
      const assuranceInfoList = app.verifyInfo && app.verifyInfo.assuranceInfoList || [];
      await new FormatApplicatVerifyInfoHandler().formatAssuranceInfoList( app , '' , { outlandAssuranceIncludedIn:true } );
      assert( Object.values( app.verifyInfo.assuranceInfo.data ).some( v=>Number( v.surplusArea ) > 0 ) , 'E_APPLICATION_ADD_ASSURANCE_INFO_ERROR_002' , '没有未鉴证过的土地' );
      // 本批鉴证报告：面积=之前的剩余，剩余鉴证面积=0（因为全部鉴证完了），已鉴证面积=之前的剩余
      const info = { data:cloneDeep( app.verifyInfo.assuranceInfo.data )  };

      Object.values( info.data ).forEach(v=>( v.area = v.surplusArea , v.surplusArea = '0.00', v.hadAssuranceArea = v.area ));
      await executeLoanAppVerifyHandler.formatAssuranceData( app , info.data );
      await executeLoanAppVerifyHandler.formatAssuranceInfo( app , info );

      const { personStatistics } = await new getAllLandListWithTypeHandler().handle( app._id , '' , { assuranceNotEmpty: true , outlandAssuranceIncludedIn:true } )
      info.personStatistics = personStatistics;

      // 面积不为0，但结果为0
      const unexpectedZero = Object.entries( info.data ).filter( ([cropType,v])=>Number( v.area ) > 0 && Number( v.except ) === 0 ).map( ([cropType,v])=>cropType );
      const unexpectedZeroHint = `请确保以下品种的【近三年平均产量】和【近三年平均产量】皆不为0： ${unexpectedZero.map(k=>cropTypeNameDic[k]).join(',')}`;
      assert( unexpectedZero.length === 0 , 'E_APPLICATION_VERIFY_105' , unexpectedZeroHint );
      debug(`${HANDLER_NAME}updatePara`,app._id,info.sn,JSON.stringify(info));
      // assuranceInfoList.push( info );
      Object.assign( input.addonVerifyInfo || {} , { assuranceInfoList:[info] } );
      input.action = 'justSave';

      // 将现在的地都打上鉴证报告 sn 的标记
      const assuranceSn = info.sn , aId = app._id , snQuery = { $or:[ { assuranceSn:{$exists:false} } , { assuranceSn:''} ] };
      const myLands = allMyLands.filter( v=>!v.assuranceSn ).map( v=>( v.assuranceSn = assuranceSn , v ) );
      myLands.length && await loanApplicationData.putById( id , { 'verifyInfo.occupyInfo.lands':myLands } );// 标记自有耕地
      debug(`${HANDLER_NAME}updateMyLands`,myLands.map(v=>v.land).join(','));
      const circulationLands = await loanLandData.postByCondition({ aId,signFinish:true, archived: false,...snQuery, limit: "unlimited" });
      await Promise.all( circulationLands.map( ({_id})=>loanLandData.putById( _id , { assuranceSn } ) ) ); // 标记流转地
      debug(`${HANDLER_NAME}updatecirculationLands`,circulationLands.map(v=>v._id).join(','));
      const subcontracLands = await loanApplicationSubcontractLandData.postByCondition({limit:'unlimited',aId,archived:false,signFinish:true,...snQuery,isRevoked:{$ne:true}});
      // debug(`${HANDLER_NAME}debug233`,JSON.stringify( {limit:'unlimited',aId,archived:false,signFinish:true,assuranceSn:{$exists:false},isRevoked:{$ne:true}} ));
      await Promise.all( subcontracLands.map( ({_id})=>loanApplicationSubcontractLandData.putById( _id , { assuranceSn } ) ) );//标记转包地
      debug(`${HANDLER_NAME}updatesubcontracLands`,subcontracLands.map(v=>v._id).join(','));
      const confirmLands = await loanApplicationConfirmLandData.postByCondition({limit:'unlimited',aId,archived:false,...snQuery,isRevoked:{$ne:true}});
      await Promise.all( confirmLands.map( ({_id})=>loanApplicationConfirmLandData.putById( _id , { assuranceSn } ) ) );//标记确权流转地
      debug(`${HANDLER_NAME}updateconfirmLands`,confirmLands.map(v=>v._id).join(','));
      const cxwqWithoutLands = await cxwqCirculationsData.postByCondition({ aId , status:4 ,circulationType:{$in:[2,3]},...snQuery, archived: false,limit: "unlimited" });
      await Promise.all( cxwqWithoutLands.map( ({_id})=>cxwqCirculationsData.putById( _id , { assuranceSn } ) ) );//标记测绘地
      debug(`${HANDLER_NAME}updatecxwqWithoutLands`,cxwqWithoutLands.map(v=>v._id).join(','));
      const outlands = await loanApplicationOutLandData.postByCondition({ aId, ...snQuery , archived: false, disabled: false,limit: "unlimited" });
      await Promise.all( outlands.map( ({_id})=>loanApplicationOutLandData.putById( _id , { assuranceSn } ) ) );//标记测绘地
      debug(`${HANDLER_NAME}updateoutlands`,outlands.map(v=>v._id).join(','));

      this.context.result = { success:'ok' , app , info };
      debug(method,'agree', '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};

module.exports = Handler;


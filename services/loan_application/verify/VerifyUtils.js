
const {
    groups:groupsData,
    fundInvestor: fundInvestorData,
} = require('../../dataSvc/dataUtil');
const { APPLICATION_STATUS_SMALL_LIST:{ cxwq } } = require('../../loan_application_v2/baseOperations/Constants');

const verifyConfig = [
    {current:'collection',next:'wait_investigation_verify_1',refuse:'rejected_by_info_collection',back:null,employee:['AccountManager']},//客户经理（县级业务员）
    // {current:'wait_fund',next:'wait_investigation_verify_1',refuse:'rejected_by_fund',back:null,employee:['RegistrationOperator']},//登记员

    {current:'wait_investigation_verify_1',next:'wait_investigation_verify_2',
        refuse:'rejected_by_wait_investigation_verify_1',back:null,employee:['AccountManager']
    },//客户经理(县级业务员)
    {current:'wait_investigation_verify_2',next:'wait_investigation_verify_review_2',verifyStatus:'wait_investigation_verify_2',
        refuse:'',back:'collection',employee:['RegistrationManager'],committer:['RegistrationOperator'],//登记管理员 和 登记员
        supplementRole:['RegistrationOperator'],committerStatus:'wait_investigation_verify_2_pre',
        sign:'wait_investigation_verify_2_registration_manager',actionAlias:'adopt',circulationAble:true,outlandAble:true,
    },
    {current:'wait_investigation_verify_2',next:'wait_investigation_verify_review_2',verifyStatus:'wait_investigation_verify_3',
        refuse:'',back:null,employee:['SupervisorManager'],committer:['Supervisor'],//监管主管 和 监管业务员
        supplementRole:['Supervisor'],committerStatus:'wait_investigation_verify_3_pre',
        sign:'wait_investigation_verify_3_registration_manager',actionAlias:'adopt',circulationAble:true,outlandAble:true,
    },
    {current:'wait_investigation_verify_2',next:'wait_investigation_verify_review_2',verifyStatus:'wait_investigation_verify_4',
        refuse:'',back:null,employee:['DataCollectManager'],committer:['RegionalApprover'],//县域管理 和 客户经理 (县域业务员)
        supplementRole:['RegionalApprover'],committerStatus:'wait_investigation_verify_4_pre',
        sign:'wait_investigation_verify_4_registration_manager',actionAlias:'adopt',circulationAble:true,outlandAble:true,
    },
    // 意见不一致时决定是否尽调 登记部登记结果
    {current:'wait_investigation_verify_review_2',next:'wait_investigation_verify_5',circulationAble:true,outlandAble:true,
        refuse:'',back:'',employee:['RegistrationOperator'],//登记员 
    },

    {current:'wait_investigation_verify_5',next:'pre_expected_value',circulationAble:true,outlandAble:true,
        refuse:'rejected_by_wait_investigation_verify_5',back:'',employee:['RiskApprover'],//风控审批（风控审核员）
    },//待风控审核
    {current:'pre_expected_value',next:'wait_investigation_verify_6',circulationAble:true,outlandAble:true,
        refuse:'',back:'',employee:['RegistrationOperator'],//登记员
    },//评估粮食预期产值
    {
        current:'wait_investigation_verify_6',next:'loan_verify_4',refuse:'rejected_by_wait_investigation_verify_6',back:null,
        circulationAble:true,outlandAble:true,
        employee:['RegistrationOperator'], //登记员 
        editor:['RegistrationOperator'],
    },// 待风控评审会 

    // {current:'pre_transferor',next:'loan_verify_4',refuse:'',back:null,employee:['AccountManager'],circulationAble:true,},//客户经理（县级业务员）
    // {current:'loan_verify_1',next:'loan_verify_2',refuse:'rejected_by_loan_verify_1',back:null,employee:['RegistrationManager']},//登记管理
    // {current:'loan_verify_2',next:'loan_verify_3',refuse:'rejected_by_loan_verify_2',back:null,employee:['SupervisorManager']},//监管主管
    // {current:'loan_verify_3',next:'loan_verify_4',refuse:'rejected_by_loan_verify_3',back:'rejected_by_loan_verify_1',employee:['RiskApprover']},//风控审批
    {current:'loan_verify_4',next:'waitLoan',refuse:'rejected_by_loan_verify_4',back:null,employee:['RegistrationOperator'],editor:['RegistrationOperator'],circulationAble:true,outlandAble:true,},//登记员
    {current:'waitLoan',next:'waitBankSign',refuse:'rejected_final',back:null,employee:['RegistrationOperator'],editor:['RegistrationOperator'],outlandAble:true,},//登记员
    {current:'waitBankSign',next:'wait_mortgage',refuse:'',back:null,employee:['Supervisor'],outlandAble:true,},//监管业务员
    {current:'wait_mortgage',next:'loaned',refuse:'',back:null,employee:['RegistrationOperator'],outlandAble:true,},//登记员
    {current:'loaned',next:'finished_loan',refuse:'',back:null,employee:['Supervisor'],outlandAble:true,},//监管业务员

];

const circulationAbleRole = ['AccountManager'];
const circulationAbleStatus = Array.from( new Set( verifyConfig.filter(v=>v.circulationAble).map(v=>v.current) ) );
const outlandAbleStatus = Array.from( new Set( verifyConfig.filter(v=>v.outlandAble).map(v=>v.current) ) );
// console.log('debug233 circulationAbleStatus',circulationAbleStatus)
const signConfigList = verifyConfig.filter( v=>v.sign ) ;
const signCurrentValues = Array.from( new Set( signConfigList.map( v=>v.current ) ) ) ;
const signCommitterValues = Array.from( new Set( signConfigList.map( v=>v.committerStatus ) ) ) ;
const signNextValues = Array.from( new Set( signConfigList.map( v=>v.next ) ) ) ;

const statusPreConfig = signNextValues.reduce( ( r , next )=>( r[ next ] = signConfigList.filter(v=>v.next === next ).map( v=>v.sign ) , r ) , {} );
// 格式类似如下
// {
//     wait_investigation_verify_review_2:['wait_investigation_verify_2_registration_manager','wait_investigation_verify_3_registration_manager','wait_investigation_verify_4_registration_manager'],
// };

// verifyStatus 用来记录 操作日志的 status ，同时用来记录 verifyInfo.supplements 中的键值
const supplementNodeFunc = current=>signConfigList.filter( v=>v.current === current )
    .reduce( ( r, cf )=>( r[ cf.committerStatus ] = cf.supplementRole || cf.committer || cf.employee , r ) , {} );
const supplementKeyConfig = signCurrentValues.reduce( ( r , current )=>( r[ current ] = supplementNodeFunc( current ) , r ) , {} );
// console.log( supplementKeyConfig )
// 格式类似如下 并行流用来记录 supplement的伪状态值。该状态可以不存在于数据库枚举中
// {
//     wait_investigation_verify_2:{
//       wait_investigation_verify_2_pre:['RegistrationOperator'],
//       wait_investigation_verify_3_pre:['Supervisor'],
//       wait_investigation_verify_4_pre:['RegionalApprover'],
//     },
// }

async function findSignConditionByRoleId( id , debug ){
    // 得到本角色才可以签的 sign 标记。以便在数据库里查询哪些 sign 为 true的记录需要排除
    const role = await groupsData.getById(id),{name:roleName} = role || {} ,  $or = [] , result = {} ;
    if(!role)return null;
    const otherStatus = verifyConfig.filter(v=>!v.sign && v.employee.includes(roleName) ).map(v=>v.current) ;// 或者订单不处于我可能负责的并行流的状态中
    const circulationAble = circulationAbleRole.includes( roleName );
    circulationAble && otherStatus.push( ...circulationAbleStatus );
    const status = Array.from( new Set( otherStatus ) );
    status.length && $or.push( { status } );
    // 要改为 committer 或 employee 两种了
    // verifyInfo.investigationInfo.${sign}.status ![1,3].includes( status ) committer 可以提交，status === 1 employee可以审核
    const {sign:leaderSign,current:leaderStatus} = verifyConfig.find(v=>v.sign && v.employee.includes(roleName) ) || {};
    const {sign:committerSign,current:committerStatus} = verifyConfig.find(v=>v.sign && v.committer.includes(roleName) ) || {};
    // const foundSign = leaderSign || committerSign;
    // !foundSign && debug( 'findSignConditionByRoleId not found:', id , '|', roleName , '|' , leaderSign , committerSign );
    // if( !foundSign )return null;//没有我负责的并行流，不作此限制
    // const $or = [ {[`verifyInfo.signInfo.${sign}`]:{$ne:true}} ]; // 曾经的做法
    committerSign && $or.push( { $and:[ { status:committerStatus } ,  {[`verifyInfo.investigationInfo.${committerSign}.status`]:{$nin:[1,3]}} ] } );
    leaderSign && $or.push( { $and:[ { status:leaderStatus } ,  {[`verifyInfo.investigationInfo.${leaderSign}.status`]:1} ] } );
    debug( 'findSignConditionByRoleId', id , '|', roleName , '|' , leaderSign , committerSign , leaderStatus , committerStatus , '|' , JSON.stringify($or) , '|' , circulationAble , JSON.stringify(otherStatus) , '|' , JSON.stringify( status ) );
    $or.length && Object.assign( result , {$or} );
    return result;
    // result.map( sign=>({[`verifyInfo.signInfo.${sign}`]:{$ne:true}})
}

async function handleBankAccountManagerQuery(condition,admin,debug){
    // console.log('debug233 a:',JSON.stringify(admin))
    debug && debug( `handleBankAccountManagerQuery a:` , admin && admin._id || null , admin && admin.group || null , admin && admin.fundId || null , JSON.stringify(admin) )
    if( !admin || admin.group !== '5eb8fefdc6ecfe44d4ecaed4' || !admin.fundId )return;
    const funds = ( await fundInvestorData.getByCondition( { limit:'unlimited', parentIdList:admin.fundId} ) ).map( v=>v._id );
    // if( !_id )return;
    delete condition['orgInfo.orgId'];//银行经理不受县域限制
    funds.push( admin.fundId );
    condition[ "verifyInfo.loanAndLawInfos.fund.id" ] = {$in:funds};
    debug && debug( `handleBankAccountManagerQuery b:` , admin && admin._id || null , admin && admin.group || null , admin && admin.fundId || null , JSON.stringify(funds) ,  JSON.stringify(condition) )
    // console.log('debug233 b:',JSON.stringify(condition[ "verifyInfo.loanAndLawInfos.fund.id" ]))
    // "verifyInfo.loanAndLawInfos.fund.id" : {$in:["61934d37b1562f3597479ad7","61935ab34829b50423e87156"]} }
}

const actionConfig = ['agree','refuse','back','justSave'];
const actionAliasConfig = [
    ['retain','agree']
];

const useAliasTid = ['600fe47561f0e675643c5fa1'];
const roleNameAlias = {
  'RegionalManager': '县级管理员',
  'RegionalApprover':  '县域业务员',
  'RegistrationOperator':  '登记业务员',
  'RegistrationManager':  '登记管理员',
  'Supervisor':  '监管业务员',
  'SupervisorManager':  '监管管理员',
  'RiskApprover':  '风控审核员',
  'AccountManager':  '县级业务员',
  'DataCollectManager':  '县域管理员',
};

const statusText = { // 操作日志用到的状态名
  new: {text:'补充资料'},
  collection: {text:'资格审核'},
  wait_fund: {text:'贷款银行确认'},// 老的操作日志有之，要保留
  wait_investigation_verify_1: {text:'初步尽调'},
  wait_investigation_verify_2_pre: {text:'补充调查'},
  wait_investigation_verify_3_pre: {text:'补充调查'},
  wait_investigation_verify_4_pre: {text:'补充调查'},
  wait_investigation_verify_2: {text:'补充调查'},
  wait_investigation_verify_3: {text:'补充调查'},
  wait_investigation_verify_4: {text:'补充调查'},
  wait_investigation_verify_review_2:{text:'尽职调查'},
  wait_investigation_verify_5: {text:'风控审核'},
  pre_expected_value: {text:'评估粮食预期产值'},
  wait_investigation_verify_6: {text:'风控评审会'},
  biopsy_approved:{text:'完善资料'},
  credit_access:{text:'授权签约'},
  pre_transferor:{text:'补充出让方信息'},// 老的操作日志有之，要保留
  loan_verify_1: {text:'登记放款审批'},
  loan_verify_2: {text:'监管放款审批'},
  loan_verify_3: {text:'风控放款审批'},
  loan_verify_4: {text:'待生成鉴证报告'},
  waitLoan: {text:'放款审核'},
  waitBankSign: {text:'签署合同'},
  wait_mortgage: {text:'办理质押登记'},
  loaned: {text:'放款'},
  rejected_loan: {text:''},//由action配置项显示[订单终止]
};
const actionText = {agree:'通过',refuse:'拒绝',back:'驳回',finished:'完成',retain:'保留',rejectBack:'驳回',adopt:'通过',rejected_loan:'订单终止'};

const supplementsNameConfig = [
    ['collection','县域服务中心资格审查',true],
    ['wait_fund','登记部确认贷款银行'],// 老订单的 supplement 有之，要保留
    ['wait_investigation_verify_1','县域服务中心初步尽职调查',true],
    ['wait_investigation_verify_2_pre','登记部审核补充调查',true],
    ['wait_investigation_verify_3_pre','监管部审核补充调查',true],
    ['wait_investigation_verify_4_pre','县域部审核补充调查',true],
    ['wait_investigation_verify_review_2','尽调结果',true],
    ['wait_investigation_verify_5','风控部复核尽职调查',true],
    ['wait_investigation_verify_6','监委会复核尽职调查',true],
    // ['pre_transferor','确认种植面积'],
    // ['loan_verify_4','出具鉴证报告'],
    // ['waitLoan','登记部录入放款数据'],
    // ['had_mortgage','登记部办理粮食抵押登记'],
  
];

const investigationInfoActionDic = { agree:'同意',refuse:'不同意' } , investigationInfoEnoughDic = { [0]:'不需要尽调' , [1]:'需要尽调' };

const rejectedLoanTypeEnum = new Map([
    ["1", '客户明确表示已不需要借款'],
    ["2", '无匹配银行'],
    ["3", '银行放款审批周期过长'],
    ["4", '贷款成本不接受'],
    ["5", '创新物权审批时间过长'],
    ["6", '其他原因'],
]) 

module.exports = {
    actionConfig,actionAliasConfig,
    verifyConfig,
    circulationAbleRole,circulationAbleStatus,outlandAbleStatus,
    signConfigList,
    statusPreConfig,
    supplementKeyConfig,
    actionConfig,useAliasTid,
    roleNameAlias,statusText,actionText,
    supplementsNameConfig,
    investigationInfoActionDic,investigationInfoEnoughDic,
    findSignConditionByRoleId,handleBankAccountManagerQuery,
    rejectedLoanTypeEnum,
}
/**
 * @summary loanAppWorkflows
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'loanAppVerifyHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const GrantReportHandler = require('./GrantReportHandler');
const {BaseHandler} = require('nongfu.merchant.svcfw');
const {SvcHandlerMgrt} = require('nongfu.merchant.svcfw');
const eSignSvc = require('../../eSignSvc');
const moment = require('moment');
const Decimal = require('decimal.js');

const {
  loanApplicationOutLand: loanApplicationOutLandData,
  loanApplicationLandType: loanApplicationLandTypeData,
  loanApplication: loanApplicationData,
  LoanSvrLand: LoanSvrLandData,
  employeeGroups:employeeGroupsData,
  employees:employeeData,
  groups:groupsData,
  infoCollectHistory:infoCollectHistoryData,
  loanApplicationVerify:loanApplicationVerifyData,
  loanSupplement: loanSupplementData,
  userAddons:userAddonsData,
  cxwqCirculations:cxwqCirculationData,
  userCxwqSupplement: userCxwqSupplementData,
  contractFlowsV2: contractFlowsV2Data,
  insOrder: insOrderData,
  fundReceive: fundReceiveData,
  groupsV2:groupV2Data,
  landSupervisoryRecord:landSupervisoryRecordData,
  applicationSubcontractLand:applicationSubcontractLandData,
  loanApplicationConfirmLand:loanApplicationConfirmLandData,
  loanLand:loanLandData,
  loanApplicationGrainSale:loanApplicationGrainSaleData,
  loanData,
} = require('../../dataSvc/dataUtil');

const FormatApplicatVerifyInfoHandler = require('../../loan_application_v2/baseOperations/formatApplicatVerifyInfoHandler')
const {verifyConfig,statusPreConfig,actionConfig,actionAliasConfig,} = require('./VerifyUtils');

const beforeHandler = {
  // async pre_transferor(nextStatus,app){
  //   // const {statistics:{cropArea}} = assuranceData ? //有种植面积参数才处理这个流程，优化效率考虑
  //   //     await new GetAllLandListWithTypeHandler({ input: {}, opts: {}, result: {}, error: {} }).handle(app._id) //取出汇总数据
  //   //     : {statistics:{cropArea:{}}};
  // },
  async loan_verify_4(nextStatus,app,action,verifyInfo,addonVerifyInfo){
    if( action !== 'agree' )return;
    await new FormatApplicatVerifyInfoHandler().formatAssuranceInfoList( app , '' , { outlandAssuranceIncludedIn:true } );
    const assuranceInfoList = app.verifyInfo && app.verifyInfo.assuranceInfoList || [];
    const assuranceInfo = app.verifyInfo && app.verifyInfo.assuranceInfo || {};
    assert( assuranceInfo , 'E_APPLICATION_VERIFY_VERIFY_4_001' , '您还没有评估过粮食预期产量哦！' );
    assert( assuranceInfoList.length , 'E_APPLICATION_VERIFY_VERIFY_4_002' , '您还没有生成过鉴证报告呢！' );
    assert( Number( assuranceInfo.totalSurplusArea ) === 0 , 'E_APPLICATION_VERIFY_VERIFY_4_003' , '您还有一部分地块没有用于生成鉴证报告哦！' );
  },
  async waitLoan(nextStatus,app,action,verifyInfo,addonVerifyInfo){
    if( action !== 'agree' )return;
    // debug('debug233 ', verifyConfig || null, verifyConfig && verifyConfig.loanAndLawInfos)
    const loanAndLawInfos = verifyInfo && verifyInfo.loanAndLawInfos || [];
    assert( loanAndLawInfos.length , 'E_APPLICATION_VERIFY_WAIT_LOAN_001' , 'loanAndLawInfos has no content' );
    assert( loanAndLawInfos.every( it=>it.fund && it.fund.id ) , 'E_APPLICATION_VERIFY_WAIT_LOAN_002' , 'every loanAndLawInfos[i].fund.id is required' );
    assert( loanAndLawInfos.every( it=>it.lawFiles && it.lawFiles.length ) , 'E_APPLICATION_VERIFY_WAIT_LOAN_003' , 'every loanAndLawInfos[i].lawFiles cant be empty' );
    assert( loanAndLawInfos.every( it=>it.loanInfo  ) , 'E_APPLICATION_VERIFY_WAIT_LOAN_004' , 'every loanAndLawInfos[i].loanInfo  is required' );
    const actualLoan = loanAndLawInfos.reduce( (r,v)=>r+(parseInt( v.loanInfo.amountYuan ) || 0 ) , 0 ) ;//amountYuan是整型，不用担心精度问题。同时把 它变成分
    assert( actualLoan > 100 , 'E_APPLICATION_VERIFY_WAIT_LOAN_005' , 'sum of amount muse more than 100 yuan' );

    const amount = app.approveAmount = actualLoan * 100;
    app.actualLoan = actualLoan;
  },
  async loaned(nextStatus,app){
    assert(app.orgInfo && app.orgInfo.orgId,'E_APPLICATION_VERIFY_000','orgId is null');
  },
};
const afterHandler = {
  async wait_investigation_verify_1(preStatus,app,verify){
    await this.occupyLand( app , true );
  },
  /*
  这个节点去掉了
  async wait_fund(preStatus,app,verify){
    //复制上一次的完善资料，如果有
    if( verify.action !== 'agree' )return;
    await this.occupyLand( app , true );
    // const addon = await userAddonsData.getOneByCondition({uId:app.uId,type:'cxwq',archived: false,});
    // assert(app.addons && app.addons.info && app.addons.info.type,'E_APPLICATION_VERIFY_101','信息未采集');
    // const last = await userCxwqSupplementData.getOneByCondition({type:app.addons.info.type,archived: false});
    // const current = await loanSupplementData.getOneByCondition({aId:app._id,archived: false});
    // if( !last || current )return;
    // await loanSupplementData.post({aId:app._id,content:last.cxwqContent});
  },
  */
  async wait_investigation_verify_2(preStatus,app,verify,{action}){
    // debug('debug233 verify2ToNext',action,preStatus,JSON.stringify(app.verifyInfo.investigationInfo))
    if( action !== 'agree' )return;
    const {next} = verifyConfig.find(it=>it.current === preStatus ) || {};//不会为空
    debug(`${HANDLER_NAME}verify2ToNext0:`, app._id , app.status , preStatus , next)
    if( app.status !== next )return; // 并行流还没过关
    const investigationInfo = app.verifyInfo && app.verifyInfo.investigationInfo;
    // if( !investigationInfo )return;//老单子
    // const enoughs = [1]; // 回滚关闭此步骤打开此行，关闭下一行
    // const enoughs = Array.from( new Set( Object.values( investigationInfo ).map( v=>v.enough ) ) ) , [first] = enoughs ;
    const investigationList = Object.values( investigationInfo || {} ) , need = investigationList.some( v=>v.enough ) , allEnough = investigationList.every(v=>v.enough);
    debug(`${HANDLER_NAME}verify2ToNext1:`, app._id , need , JSON.stringify( Object.values( investigationInfo || {} ).map( v=>v.enough ) ) )
    if( need ){
      //三部门有一个认为需要尽调
      const update = { 
        'verifyInfo.investigationResult.needVerify':true,
      };
      allEnough && ( update['verifyInfo.investigationResult.enough'] = true );//三部门一致认为需要调查，默认值为true。否则为unfined，由审核人员来选择
      await loanApplicationData.putById( app._id , update );
      return;
    }
    const cf = verifyConfig.find( v=>v.current === app.status );//不会为空
    debug(`${HANDLER_NAME}verify2ToNext2:`, app._id , cf && cf.next || 'null')
    // 不需要尽调
    const update = { 
      status:cf.next , 
      'verifyInfo.investigationResult.needVerify':false,
      'verifyInfo.investigationResult.enough':false,// 三部门一致为false才会到这里
    };
    debug(`${HANDLER_NAME}verify2ToNext3:`, app._id , JSON.stringify(update))
    await loanApplicationData.putById( app._id , update );// 跳到下个状态，并记录尽调与否信息
    //生成风控报告
    // const context = { input: {id:app._id,source:'cmsVerify'}, opts:  {}, result: {}, error: {} };
    // new GrantReportHandler(context).doAsync();//不作异步执行，失败了不阻塞流程 最新流程，cms里不再生成
  },
  async wait_investigation_verify_5(preStatus,app,verify){
    
  },
  // async pre_transferor(preStatus,app,verify){
  //   if( verify.action !== 'agree' )return;

  //   //发送签约短信
  //   const context = { input: {}, opts: {}, result: {},  error: {} };
  //   const oldList = await cxwqCirculationData.getByCondition({aId:app._id,archived:false,limit:'unlimited'}) || [];
  //   oldList.map(({_id})=>new SendSmsByOneHandler(context).handleOne(_id) );//遇到发短信失败，不卡住此处流程
  //   // await Promise.all( oldList.map(({_id})=>new SendSmsByOneHandler(context).handleOne(_id) ) );
  //   // const context = { input: {id:app._id,source:'cmsVerify'}, opts:  {}, result: {}, error: {} };
  //   // new GrantReportHandler(context).doAsync();//不作异步执行，失败了不阻塞流程 最新流程，cms里不再生成
  // },

  async waitLoan(preStatus,app,verify){
    if( verify.action !== 'agree' )return;
    // assert(app.verifyInfo && app.verifyInfo.loanInfo && app.verifyInfo.loanInfo,'E_APPLICATION_VERIFY_39','没有回填贷款信息');
    // assert(app.verifyInfo && app.verifyInfo.loanInfo && app.verifyInfo.loanInfo.amount,
        // 'E_APPLICATION_VERIFY_09','没有回填贷款金额')
    // await this.document(app);//归档
    const verifyInfo = app.verifyInfo , assuranceInfo = verifyInfo && verifyInfo.assuranceInfo ;
    assert( verifyInfo.loanAndLawInfos && verifyInfo.loanAndLawInfos.length  , 'E_APPLICATION_VERIFY_229' , '数据格式有误，没有填写放款信息' );
    assert( app.orgInfo && app.orgInfo.orgId , 'E_APPLICATION_VERIFY_209' , '数据异常，该订单没有所属县域' )
    assert( verifyInfo , 'E_APPLICATION_VERIFY_210' , '数据异常，该订单没有verifyInfo' )
    assert( assuranceInfo , 'E_APPLICATION_VERIFY_211' , '数据异常，该订单没有verifyInfo.assuranceInfo' )
    const groupV2 = await groupV2Data.getById( app.orgInfo.orgId );
    const {code} = groupV2 , codeArr = code.split('');
    const arr = Array( parseInt( codeArr.length / 3 ) ).fill(1).map( (_,i)=>codeArr.slice( 0 , (i+1)*3 ).join('' ) );
    const groupV2Query = { code:{$in:arr},tId:app.tId, archived:false,limit:'unlimited',$sort:{code:1}};
    const groupV2List = await groupV2Data.getByCondition( groupV2Query );
    app.orgInfo.parentOrgList = groupV2List.map(v=>v._id);
    debug(`${HANDLER_NAME}parentOrgList`,app._id,groupV2._id,code,arr,app.orgInfo.parentOrgList.join(','),JSON.stringify(groupV2Query))
    
    verifyInfo.loanInfo = app.verifyInfo.loanAndLawInfos[0].loanInfo;
    verifyInfo.loanInfo.amount = app.approveAmount;
    // debug('debug233 amount:',app.approveAmount)
    // app.approveAmount =  app.verifyInfo.loanInfo.amount;
    // app.actualLoan = app.approveAmount / 100
    await this.formatAssuranceInfo( app , app.verifyInfo.assuranceInfo );
    app.verifyInfo.superviseExtendInfo = {superviseStatus:'init',entrustCount:0};
    Object.assign( app.verifyInfo,{superviseExtendInfo:{superviseStatus:'none'}});
    app.verifyInfo.assuranceInfo.totalPledgeRate = new Decimal( app.approveAmount )
        .div( new Decimal( 100 ) )
        .div( new Decimal( app.verifyInfo.assuranceInfo.totalExcept ) ).toFixed(2, Decimal.ROUND_DOWN);
    debug(`${HANDLER_NAME}totalPledgeRate`,app.verifyInfo.assuranceInfo.totalExcept,app.approveAmount);
    await loanApplicationData.putById(app._id,app);
    const history = await infoCollectHistoryData.getOneByCondition({archived:false,uId:app.uId,isLast:true})
    const {basic={},companyBasic} = history && history.content || {};

    const loanSupplements = await loanSupplementData.getByCondition({archived: false,aId:app._id,limit:'unlimited'})
    await Promise.all(loanSupplements.map(v=>loanSupplementData.putById(v._id,{isLoaned:1})))
    debug(`${HANDLER_NAME}waitLoanHandle`,app._id,JSON.stringify(app));

    //统计订单数据
    const context = { input: {}, opts:  {}, result: {}, error: {} };
    const svcHandlerMgrt = new SvcHandlerMgrt();
    svcHandlerMgrt.addHandler(new FixDataHandler(context));
    await svcHandlerMgrt.processAsync(context);

  },
  async wait_mortgage(preStatus,app,verify){
    const mId = app.addons && app.addons.info && app.addons.info.mId,lastModTime = new Date();
    const history = mId && await infoCollectHistoryData.getById( mId ) || {content:{}};
        // await infoCollectHistoryData.getOneByCondition({archived:false,uId:app.uId,isLast:true})
    const {basic={},companyBasic} = history && history.content || {},requestUniqueId = history && history.uniqueId ;

    const old = await fundReceiveData.getOneByCondition( {aId:app._id,action:'loaned',archived:false} );
    const update = {aId:app._id,uId:app.uId,tId:app.tId,action:'loaned',
      amount:app.approveAmount,extendInfo:{},appSn:app.sn,
      successful:2,status:'finished',hasPayToken:false,
      createdTime:lastModTime,lastModTime,archived:false,
      requestAreaCode:app.area,
      requestUniqueId,requestName:basic.name,requestMobile:basic.mobile,
      requestType:history.type,requestCompanyName:companyBasic && companyBasic.companyName
    };
    history._id && !old && await fundReceiveData.post( update );
    history._id && old && await fundReceiveData.putById( old._id , update );
    const supervisors = await landSupervisoryRecordData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(supervisors.map(({_id})=>landSupervisoryRecordData.putById(_id,{hadClosed: false,lastModTime})));//
  },
  async loaned(preStatus,app,verify,opts){
    const lastModTime = new Date();
    const subLands = await applicationSubcontractLandData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(subLands.map(({_id})=>applicationSubcontractLandData.putById(_id,{hadReleased: true,lastModTime})));//
    const confirmLands = await loanApplicationConfirmLandData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(confirmLands.map(({_id})=>loanApplicationConfirmLandData.putById(_id,{hadReleased: true,lastModTime})));//
    const funds = await fundReceiveData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(funds.map(({_id})=>fundReceiveData.putById(_id,{status: 'closed',lastModTime})));//
    // const cxwqList = await cxwqCirculationData.getByCondition({aId:app._id,archived:false,limit:'unlimited'}) || [];
    // await Promise.all(cxwqList.map(({_id})=>cxwqCirculationData.putById(_id,{hadReleased: true})));//
    const landList = await loanLandData.getByCondition({aId:app._id,archived:false,limit:'unlimited'}) || [];
    await Promise.all(landList.map(({_id})=>loanLandData.putById(_id,{hadReleased: true,lastModTime})));//
    const sales = await loanApplicationGrainSaleData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(sales.map(({_id})=>loanApplicationGrainSaleData.putById(_id,{appStatus: 2,lastModTime})));//
    const supervisors = await landSupervisoryRecordData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(supervisors.map(({_id})=>landSupervisoryRecordData.putById(_id,{hadClosed: true,lastModTime})));//
    const outlands = await loanApplicationOutLandData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(outlands.map(({_id})=>loanApplicationOutLandData.putById(_id,{"occupyInfo.hadReleased": '1',lastModTime})));//册外地
  }
};
const beforeJustSaveHandler = {};
const afterJustSaveHandler = {
  async loaned(preStatus,app,verify){
    if( verify.action !== 'agree' )return;

  }
};

const {assert,isLandOccupy,getAllMyLands} = require('../../../utils/general')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {id,action:actionParameter='agree',description,verifyDescription,roleId:group,userId:employee,verifyInfo,addonVerifyInfo} = this.context.input;
      const [actionAlias,actionByAlias] = actionAliasConfig.find( ([alias])=>actionParameter === alias ) || [];
      const action = actionByAlias || actionParameter;// 尝试根据传入值找别名，找到则action使用之；否则action使用传入值
      const {stopExecute} = this.context.opts;
      if( stopExecute )return done();
      const app = id && await loanApplicationData.getById(id);
      assert(app,'E_APPLICATION_VERIFY_104','不存在的金融订单');
      debug(method,'QueryPara1',id,app.status,action,group,employee,verifyInfo,addonVerifyInfo);
      const employeeGroup = employee && group && await employeeGroupsData.getOneByCondition({tId:app.tId, employee,group}) || null;
      const groupV2 = employeeGroup && await groupV2Data.getById( employeeGroup.groupV2 );
      const admin = employee && await employeeData.getById(employee, {cache : true , expire: 24 * 60 * 60 });
      const role = group && await groupsData.getById(group);
      assert(role,'E_APPLICATION_VERIFY_04','不存在的管理员权限');
      assert(admin,'E_APPLICATION_VERIFY_004','没有登陆！');
      assert(groupV2,'E_APPLICATION_VERIFY_005','没有登陆！');
      assert(app,'E_APPLICATION_VERIFY_01','订单不存在');
      assert(app.status !== 'finished_loan','E_APPLICATION_VERIFY_01a','订单已封存');
      const {status} = app , roleName = role.name;
      debug(`${HANDLER_NAME}RoleInfo`,status,role.name);
      const cfList = verifyConfig.filter(it=>it.current === status );
      assert(cfList.length,'E_APPLICATION_VERIFY_02','当前状态不可审核');
      const cf = cfList.find(it=>it.employee.includes(roleName) );
      const editable = cfList.some( cf=>[].concat( cf.employee , cf.editor ).filter(v=>v).includes(roleName) );
      action !== 'justSave' && assert( cf , 'E_APPLICATION_VERIFY_02b' , '角色有误，不可审核' ) ;
      action === 'justSave' && assert( editable , 'E_APPLICATION_VERIFY_02c' , '角色有误，不可提交资料' ) ;
      //或者有sign配置，或者不是并行流，
      // assert(cf.sign || !statusPreConfig[cf.next],'E_APPLICATION_VERIFY_11','sign is required');
      assert(employeeGroup,'E_APPLICATION_VERIFY_03','用户未登录或未拥有此管理员角色');
      //todo && role.type === 1，限制金融角色权限
      assert(!Array.isArray(cf.employee) || cf.employee.length === 0 || cf.employee.includes(role.name),
          'E_APPLICATION_VERIFY_05','该角色当前阶段无权审核');
      assert(actionConfig.includes(action),'E_APPLICATION_VERIFY_06','错误的动作');
      assert( action !== 'back' || cf.back  ,'E_APPLICATION_VERIFY_07','此阶段不能驳回');
      assert( action !== 'refuse' || cf.refuse  ,'E_APPLICATION_VERIFY_07a','此阶段不能拒绝');

      await this.formatVerifyInfoPara(verifyInfo,addonVerifyInfo,app,admin,role,action,cf)

      //前置处理
      beforeHandler[ status ] && await beforeHandler[ status ].call( this , cf.next,app,action,verifyInfo,addonVerifyInfo );

      // debug('debug233 a',app.status);
      action === 'agree' && ( await this.handleAgreeStatus(app,cf));
      action === 'refuse' && ( await this.handleRefuseStatus(app,cf));
      app.lastModTime = new Date();
      // debug('debug233 b',app.status);
      // action === 'back' && ( app.status = cf.back );//驳回的逻辑已废弃

      await this.formatAppBeforeSave(app,employee,verifyInfo,addonVerifyInfo);
      // debug('debug233 c',app.status);

      await loanApplicationData.putById(app._id,app);

      if( action === 'justSave' ){
        this.context.result = {success:'ok',app};
        // debug('debug233 d',app.status);

        debug(method, 'justSave','[Exit](success)');
        return done();
      }

      debug(`${HANDLER_NAME}VerifyStatusSave1`,status,action,app._id);
      // status.match(/^rejected_/) && debug(`${HANDLER_NAME}VerifyStatusSaveError1`,status,action,app._id);
      // assert( !status.match(/^rejected_/) , 'E_APPLICATION_VERIFY_08' , '操作日志的记录状态有误' );
      const verify = await this.addVerifyLog( app._id , employee , roleName , cf.verifyStatus || status , actionAlias || cf.actionAlias || action , verifyDescription )
      // let verify = {aId:id,status:cf.verifyStatus || status,
      //   action:actionAlias || cf.actionAlias || action , // 前端可能传入操作别名，某状态可能有自己的通过时的别名，都没有再用默认值
      //   description:verifyDescription,roleName:role.name,
      //   operator:employee,createdTime:new Date(),lastModTime:new Date(),archived:false}
      // verify = await loanApplicationVerifyData.post(verify);

      //后置处理
      afterHandler[ status ] && await afterHandler[ status ].call( this , status,app,verify,{groupV2,action} );

      debug(method,'QueryPara2',id,app.status,action,group,employee);
      this.context.result = {success:'ok',app,verify}
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async handleAgreeStatus(app,cf){
    const {sign} = cf,pre = statusPreConfig[cf.next];
    const {signInfo} = app.verifyInfo = Object.assign( {signInfo:{}} , app.verifyInfo );
    pre && sign && ( signInfo[sign] = true );//如果需要，先标记本类操作。此行代码主要是为了支持并行流
    const canNext = !pre || pre.every(key=>signInfo[key]);//没有并行流，或者并行所有操作类型都标记为true了
    debug(`${HANDLER_NAME}handleAgreeStatus`,app._id,canNext,sign,cf.next,pre,signInfo)
    canNext && (app.status = cf.next);
  }

  async handleRefuseStatus(app,cf){
    cf && ( app.status = cf.refuse );
    const lastModTime = new Date();
    const cxwqList = await cxwqCirculationData.getByCondition({aId:app._id,archived:false,limit:'unlimited'}) || [];
    await Promise.all(cxwqList.map(({_id})=>cxwqCirculationData.putById(_id,{archived: true})));//
    const landList = await loanLandData.getByCondition({aId:app._id,archived:false,limit:'unlimited'}) || [];
    await Promise.all(landList.map(({_id})=>loanLandData.putById(_id,{archived: true})));//
    const subLands = await applicationSubcontractLandData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(subLands.map(({_id})=>applicationSubcontractLandData.putById(_id,{archived: true})));//
    const confirmLands = await loanApplicationConfirmLandData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(confirmLands.map(({_id})=>loanApplicationConfirmLandData.putById(_id,{archived: true,hadReleased: true})));//
    const funds = await fundReceiveData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(funds.map(({_id})=>fundReceiveData.putById(_id,{archived: true})));//
    const sales = await loanApplicationGrainSaleData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(sales.map(({_id})=>loanApplicationGrainSaleData.putById(_id,{appStatus: 1})));//
    const outlands = await loanApplicationOutLandData.getByCondition({aId:app._id,archived:false,limit:'unlimited'});
    await Promise.all(outlands.map(({_id})=>loanApplicationOutLandData.putById(_id,{archived:true, "occupyInfo.hadReleased": '1',lastModTime})));//册外地
    app.verifyInfo = app.verifyInfo || {};
    app.verifyInfo.occupyInfo = app.verifyInfo.occupyInfo || {};
    // app.verifyInfo.occupyInfo.hadOccupy = '0';
    app.verifyInfo.occupyInfo.hadReleased = '1';
    await loanApplicationData.putById(app._id,{'verifyInfo.occupyInfo.hadReleased':'1'}) ;
    // await this.document(app)
  }

  async formatVerifyInfoPara(verifyInfo,addonVerifyInfo,app,admin,role,action,cf){

    //提交鉴证信息确定起止时间
    // const {assuranceInfo} = Object.assign( {} , verifyInfo || {} , addonVerifyInfo || {} );
    // if( assuranceInfo ){
    //   debug(`${HANDLER_NAME}FormatVerifyInfoParaAssuranceInfo1`,assuranceInfo);
    //   const now = new Date();
    //   const next = now.getMonth() > 10 ? moment(`${now.getFullYear()+1}-10-31`) : moment(`${now.getFullYear()}-10-31`);
    //   assuranceInfo && ( assuranceInfo.cxwqAuthenticationData = {
    //     start:{year:moment(now).format('YYYY'),month:moment(now).format('MM'),day:moment(now).format('DD')},
    //     end:{year:next.format('YYYY'),month:next.format('MM'),day:next.format('DD')},
    //   } );
    //   debug(`${HANDLER_NAME}FormatVerifyInfoParaAssuranceInfo2`,assuranceInfo);
    // }
  }


  async formatAppBeforeSave(app,employee,verifyInfo,addonVerifyInfo){

    //已改由前端传入amountYuan
    // [verifyInfo,addonVerifyInfo].forEach(verifyInfo=>{
    //   verifyInfo && verifyInfo.loanInfo && verifyInfo.loanInfo.amount &&
    //     ( verifyInfo.loanInfo.amount = Number(new Decimal(verifyInfo.loanInfo.amount).mul(new Decimal(1000000)) ).toString());
    // })
    debug(`${HANDLER_NAME}formatAppBeforeSave1`,app._id,verifyInfo)

    //保存之前一些节点的特殊处理
    app.verifyInfo = Object.assign(app.verifyInfo || {},verifyInfo || {});
    // debug('addonVerifyInfo',JSON.stringify(addonVerifyInfo),JSON.stringify(app.verifyInfo));
    Object.entries( addonVerifyInfo || {} )//追回型信息，数组追加，对象合并
        .forEach(([k,v])=>Array.isArray(v) ?
            app.verifyInfo[k] = [...(app.verifyInfo[k]||[]),...v]
            : app.verifyInfo[k] = Object.assign(app.verifyInfo[k]||{},v) )
    app.lastModTime = new Date();
    app.operator = employee;
    debug(`${HANDLER_NAME}formatAppBeforeSave2`,app._id, app.verifyInfo);

    app.verifyInfo.assuranceInfo = app.verifyInfo.assuranceInfo || {};
    app.addons = app.addons || {};
    app.addons.xntData = app.addons.xntData || {};
    const {verifyInfo:{assuranceInfo:{sn:assuranceSn,data:assuranceData}}} = app;

    if( !assuranceSn && assuranceData &&
        Object.values(assuranceData).filter(it=>!isNaN( it.avg ) && !isNaN( it.price )).length ){
      const {codeSn} = await insOrderData.getByUrl("/v1.0/ins/counter/inc",
          { id: `${app.area.substr(0,12)}`,prefix:':application:assuranceInfo:',len:4 })
      app.verifyInfo.assuranceInfo.sn = codeSn;
      debug(`${HANDLER_NAME}formatAppBeforeSave3`,app._id, app.verifyInfo);

    }
    // Object.values(assuranceData||{}).forEach(item=>{
    //   isNaN(item.avg) || (item.avg = Number(item.avg) );
    //   isNaN(item.price) || (item.price = Number(item.price) );
    // });

    // new Decimal(master.greenhouseSubsidyAmount || 0)
    //     .add( new Decimal(master.landSubsidyAmount || 0) )
    //     .add( new Decimal(master.breedSubsidyAmount || 0) )
    //     .add( new Decimal(master.protectSubsidyAmount || 0) )
    //     .toFixed(2, Decimal.ROUND_DOWN)

    // 由于需求变更，下面两段代码移至它处。因为相关值需要动态计算出来
    // const {statistics:{cropArea}} = assuranceData ? //有种植面积参数才处理这个流程，优化效率考虑
    //       await new GetAllLandListWithTypeHandler({ input: {}, opts: {}, result: {}, error: {} }).handle(app._id) //取出汇总数据
    //      : {statistics:{cropArea:{}}};
    // Object.entries(cropArea).forEach(([cropType,area])=> //如果assuranceData为空，此段代码实际不会执行
    //         Object.assign(assuranceData[cropType] || {} , {area}));//为assuranceData里的每一项赋area字段

    // debug(`${HANDLER_NAME}assuranceDataExcept`,assuranceData||{},app.addons.xntData);
    // Object.entries(assuranceData||{}) // 粮食预期产值计算，如果为空后面的循环不会走
    //     // .map(arr=>(app.addons.xntData[arr[0]]=app.addons.xntData[arr[0]]||0,arr))
    //     .map(([cropType,item])=>(app.addons.xntData[cropType] = item.area,[cropType,item]))
    //     .filter(([cropType,item])=> !isNaN( item.avg ) && !isNaN( item.price ) )
    //     .forEach( ([cropType,item])=>{
    //       assuranceData[cropType].except =
    //         new Decimal( item.area )
    //             .mul( new Decimal( item.avg ) )
    //             .mul( new Decimal( item.price ) )
    //             .toFixed(2, Decimal.ROUND_DOWN);
    //       assuranceData[cropType].yield =
    //           new Decimal( item.area )
    //               .mul( new Decimal( item.avg ) )
    //               .toFixed(2, Decimal.ROUND_DOWN);
    //     });// 粮食预期产值 = 种植面积  *  三年平均亩产  *  三年平均价格
    //做一些数据验证的工作
    const {verifyInfo:{loanInfo}} = app;
    loanInfo && ( loanInfo.amount = ( Number( loanInfo.amountYuan) || 0  ) * 100 );//计算容错，后面会检验
    // 产品说把这个检验去掉
    // loanInfo && assert( loanInfo.amount <= app.amount , 'E_APPLICATION_VERIFY_024','放款金额不能大于申请金额！' );
  }


  async formatAssuranceInfo( app , assuranceInfo ){
    // 生成鉴证报告编号，起止时间，和统计一些总数
    debug(`${HANDLER_NAME}FormatVerifyInfoParaAssuranceInfo1`,assuranceInfo);
    const now = new Date();
    const next = now.getMonth() > 10 ? moment(`${now.getFullYear()+1}-10-31`) : moment(`${now.getFullYear()}-10-31`);
    assuranceInfo.cxwqAuthenticationData = assuranceInfo.cxwqAuthenticationData || {
      start:{year:moment(now).format('YYYY'),month:moment(now).format('MM'),day:moment(now).format('DD')},
      end:{year:next.format('YYYY'),month:next.format('MM'),day:next.format('DD')},
    } ;
    const {codeSn} = !assuranceInfo.sn && await insOrderData.getByUrl("/v1.0/ins/counter/inc",
          { id: `${app.area.substr(0,12)}`,prefix:':application:assuranceInfo:',len:4 }) || {};
    codeSn && ( assuranceInfo.sn = codeSn );

    // 优化后的代码x
    const totalConfig = {totalExcept:'except',totalAvg:'avg',totalPrice:'price',totalYield:'yield',totalArea:'area',totalSurplusArea:'surplusArea',totalAssuranceArea:'hadAssuranceArea'};
    Object.entries(totalConfig).forEach(([total,key])=>
        assuranceInfo[total] = Object.values(assuranceInfo.data||{})
            // .filter(v=>!isNaN(v[key]))
            .reduce((res,it)=>res.add( new Decimal( it[key] || 0 )),new Decimal( 0 ))
            .toFixed(2, Decimal.ROUND_DOWN)
    );
    // 优化前的代码
    // app.verifyInfo.assuranceInfo.totalExcept = Object.values(assuranceData||{})
    //     .filter(v=>!isNaN(v.except))
    //     .reduce((res,it)=>res.add( new Decimal( it.except)),new Decimal( 0 ))
    //     .toFixed(2, Decimal.ROUND_DOWN);
    // app.verifyInfo.assuranceInfo.totalAvg = Object.values(assuranceData||{})
    //     .filter(v=>!isNaN(v.avg))
    //     .reduce((res,it)=>res.add( new Decimal( it.avg )),new Decimal( 0 ))
    //     .toFixed(2, Decimal.ROUND_DOWN);
    // app.verifyInfo.assuranceInfo.totalPrice = Object.values(assuranceData||{})
    //     .filter(v=>!isNaN(v.price))
    //     .reduce((res,it)=>res.add( new Decimal( it.price)),new Decimal( 0 ))
    //     .toFixed(2, Decimal.ROUND_DOWN);
    // app.verifyInfo.loanInfo.amountYuan && ( app.verifyInfo.loanInfo.amount = Number(app.verifyInfo.loanInfo.amountYuan) * 100 );

    debug(`${HANDLER_NAME}formatAppBeforeSave3`,app._id, app.verifyInfo);
    debug(`${HANDLER_NAME}FormatVerifyInfoParaAssuranceInfo2`,assuranceInfo);
  }

  formatAssuranceData(app,assuranceData){
    const assuranceInfoList = app.verifyInfo && app.verifyInfo.assuranceInfoList || [];
    debug(`${HANDLER_NAME}assuranceDataExcept`,assuranceData||{}, JSON.stringify( assuranceInfoList ) );
    Object.entries(assuranceData).forEach( ([cropType,item])=>{ // 粮食预期产值计算，如果为空后面的循环不会走
      item.avg = item.avg || '0';
      item.price = item.price || '0';
      assuranceData[cropType].except =
        new Decimal( item.area )
            .mul( new Decimal( item.avg || 0 ) )
            .mul( new Decimal( item.price || 0 ) )
            .toFixed(2, Decimal.ROUND_DOWN);
      assuranceData[cropType].yield =
          new Decimal( item.area || 0 )
              .mul( new Decimal( item.avg || 0 ) )
              .toFixed(2, Decimal.ROUND_DOWN);
      const hadAssuranceList = assuranceInfoList.map(v=>v.data[ cropType ] || {area:'0'} );
      const hadAssuranceArea = hadAssuranceList.reduce( (r,{area})=>r.add( new Decimal( area || 0 ) ) , new Decimal(0) ).toFixed(2, Decimal.ROUND_DOWN);
      const surplusArea = new Decimal( item.area ).sub( hadAssuranceArea ).toFixed(2, Decimal.ROUND_DOWN)
      Object.assign( item , { surplusArea , hadAssuranceArea } );
    });// 粮食预期产值 = 种植面积  *  三年平均亩产  *  三年平均价格
  }

  async occupyLand(app,excludeOccupy=true){
    if( app.verifyInfo && app.verifyInfo.occupyInfo && parseInt( app.verifyInfo.occupyInfo.hadOccupy ) )return;//已经占用过了
    const now = new Date(),year = now.getMonth() > 9 ? now.getFullYear() + 1 : now.getFullYear();
    const circulationStartDate = moment().format("YYYY-MM-DD"),circulationEndDate = `${year}-10-31`;
    const { allMyLands , userVerify , allLandCodes } = await getAllMyLands( app , debug , HANDLER_NAME );
    debug(`${HANDLER_NAME}occupyLand step a`,app._id,excludeOccupy,allMyLands.length,userVerify && userVerify.IDCard || 'null',allMyLands.join(','))
    const occupySet = new Set( await isLandOccupy( allLandCodes , app.tId ) );
    debug(`${HANDLER_NAME}occupyLand step a2`,app._id,[...occupySet].join(','))
    const lands = allMyLands.filter( v=>!excludeOccupy || !occupySet.has( v.landCode ) );//不检查占用或没被占用
    debug(`${HANDLER_NAME}occupyLand step b`,app._id,lands.length)
    const contractList = lands
        .map(v=>v.contract)
        .filter((v,i,arr)=>!arr.some((vv,ii)=>ii<i && v.grantNo === vv.grantNo ));
    debug(`${HANDLER_NAME}occupyLand step c`,app._id,contractList.length)
    const occupyInfo = {
      hadOccupy:'1',hadReleased:'0',
      lands,contractList,
      circulationStartDate,circulationEndDate,
    }
    await loanApplicationData.putById(app._id,{'verifyInfo.occupyInfo':occupyInfo});
  }

  async addVerifyLog( aId , operator , roleName , logStatus , logAction , description ){
    debug(`${HANDLER_NAME}addVerifyLog`,aId , operator , roleName , logStatus , logAction , description)
    const verify = {aId,status:logStatus, action:logAction,
      description,roleName,operator,
      createdTime:new Date(),lastModTime:new Date(),archived:false}
    return await loanApplicationVerifyData.post(verify);
  }

  // 归档合同 已弃用
  async document(appObj){
    return;
    try {
      if(appObj.pId === '60111027d9bb2378a799b874') { // 粮食预期收益权质押贷
        const flow = await contractFlowsV2Data.getOneByCondition({ loanId: appObj._id });
        await eSignSvc.archiveContractFlow({ flowId: flow.eFlowId });
      }
      debug(`${HANDLER_NAME}Document`, '[Exit](success)-contract_archiveProcess');
    } catch (err) {
      debug.error(`${HANDLER_NAME}Document`, '[Exit](failed)-contract_archiveProcess', err);
    }
  }

};

module.exports = Handler;

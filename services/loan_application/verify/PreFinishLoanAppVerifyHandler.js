/**
 * @summary loanAppWorkflows
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'PreFinishLoanAppVerifyHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const GrantReportHandler = require('./GrantReportHandler');
const {BaseHandler} = require('nongfu.merchant.svcfw');
const {SvcHandlerMgrt} = require('nongfu.merchant.svcfw');
const eSignSvc = require('../../eSignSvc');
const moment = require('moment');
const Decimal = require('decimal.js');

const {assert} = require('../../../utils/general')

const {
  loanApplicationOutLand: loanApplicationOutLandData,
  loanApplicationLandType: loanApplicationLandTypeData,
  loanApplication: loanApplicationData,
  LoanSvrLand: LoanSvrLandData,
  employeeGroups:employeeGroupsData,
  employees:employeeData,
  groups:groupsData,
  infoCollectHistory:infoCollectHistoryData,
  loanApplicationVerify:loanApplicationVerifyData,
  loanSupplement: loanSupplementData,
  userAddons:userAddonsData,
  userCxwqSupplement: userCxwqSupplementData,
  contractFlowsV2: contractFlowsV2Data,
  insOrder: insOrderData,
  fundReceive: fundReceiveData,
  loanData,
} = require('../../dataSvc/dataUtil');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      // 处理订单的完成，同时进行一些预处理
      const {input , input:{id,supplements=[],description=''},opts:{finished}} = this.context;
      input.verifyDescription = description;
      const app = await loanApplicationData.getById(id),documentCreatedTime = moment().format('YYYY-MM-DD');
      if( !finished || app.status !== 'loaned' )return done();
      const update = {
        verifyInfo:{
          finishInfo:{supplements,description,documentCreatedTime},
          supplements
        },
        description,action : 'agree',
      };
      Object.assign(this.context.input,update);
      debug(`${HANDLER_NAME}Para`,this.context.input);
      delete this.context.input.superviseSupplement;
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};

module.exports = Handler;

/**
 * @summary createLoanAppTrackingHandler
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:29:45 
 * Last modified  : 2018-12-13 17:28:58
 */

'use strict';

const HANDLER_NAME = 'createLoanAppTrackingHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationTrackingData = require('../dataSvc/dataUtil').loanApplicationTracking;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;

      let result = loanApplicationTrackingData.post(condition);

      self.context.result = result;
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler;
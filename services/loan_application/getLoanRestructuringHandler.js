'use strict';

const HANDLER_NAME = 'getLoanRestructuringHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  loanApplication: loanApplicationData,
  employeeGroups: employeeGroupData,
  transferWhitelist: transferWhitelistData
} = require('../dataSvc/dataUtil');
const { getEmployeeLimit } = require('../../utils/getUserFromReq');
const {
  APPLICATION_STATUS_LIST: STATUS_LIST,
} = require('../loan_application_v2/baseOperations/Constants');
 const APPLICATION_STATUS_LIST = new Map(STATUS_LIST);
const BUSINESS_TYPE_MAP = new Map([
  ['nhd', '红本贷'],
  ['ncd', '农场贷'],
  ['nzd', '农资贷'],
]);
const formatAreaCode = require('../../persistence/formatAreaCode');
//'状态 1 锁定 2 可支用 3 授权结束 4 取消
const RESTRUCTURING_STATUS_LIST = {
  1: '锁定',
  2: '可支用',
  3: '授权结束',
  4: '取消'
}
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input: condition, opts } = this.context;

      let promises = [];
      const $and = [];
      let areaCodeLimitOr = opts.uId && await getEmployeeLimit(employeeGroupData, condition.tId, opts.uId, 'area', opts.roleId);
      opts.uId && areaCodeLimitOr && areaCodeLimitOr.length && ($and.push({ $or: areaCodeLimitOr }));
      let result = {};
      if(condition.loanRestructuring != 1){
        condition.$and = $and
        condition.status = { '$in': [ 'loaned', 'overdue' ] };
        debug(method, '[Exit](condition)', condition);

        result = await loanApplicationData.postListAndCountByCondition(condition);
        for(let item of result.result){
          item.status = APPLICATION_STATUS_LIST.get(item.status);
          item.type = BUSINESS_TYPE_MAP.get(item.type);
          item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
          item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss');
          // if(item.loanRestructuring == 1){
          //   let transferWhitelist = await transferWhitelistData.getOneByCondition({
          //     'loan.sn': item.sn
          //   });
          //   if(!transferWhitelist){
          //     throw {
          //       errorCode: 'E_LOAN_RESTRUCTURING_055',
          //       httpCode: 406,
          //       reason: 'not found transfer whitelist'
          //     };
          //   }
          //   item.restructuringStatus = RESTRUCTURING_STATUS_LIST[transferWhitelist.status];
          //   item.restructuringTime = moment(transferWhitelist.createdTime).format('YYYY-MM-DD HH:mm:ss');
          // }
        }
      }else{
        condition.mobile = condition.userMobile;
        delete condition.userMobile;
        delete condition.loanRestructuring;

        areaCodeLimitOr = opts.uId && await getEmployeeLimit(employeeGroupData, condition.tId, opts.uId, 'area', opts.roleId);
        opts.uId && areaCodeLimitOr && areaCodeLimitOr.length && ($and.push({ $or: areaCodeLimitOr }));
        condition.$and = $and
        result = await transferWhitelistData.getListAndCountByCondition(condition);
        for(let item of result.result){
          item.status = APPLICATION_STATUS_LIST.get(item.loan.status);
          item.type = BUSINESS_TYPE_MAP.get(item.loanType);
          item.outstanding = item.loan.outstanding;
          item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
          item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss');
          item.restructuringStatus = RESTRUCTURING_STATUS_LIST[item.status];
          // if(item.loanRestructuring == 1){
          //   let transferWhitelist = await transferWhitelistData.getOneByCondition({
          //     'loan.sn': item.sn
          //   });
          //   if(!transferWhitelist){
          //     throw {
          //       errorCode: 'E_LOAN_RESTRUCTURING_055',
          //       httpCode: 406,
          //       reason: 'not found transfer whitelist'
          //     };
          //   }
          //   item.restructuringStatus = RESTRUCTURING_STATUS_LIST[transferWhitelist.status];
          //   item.restructuringTime = moment(transferWhitelist.createdTime).format('YYYY-MM-DD HH:mm:ss');
          // }
        }

      }


      for(let item of result.result){
        promises.push(formatAreaCode.getFormatAreaCode(item.area).then(data => {
          let region = data && data.region || {};
          let townInfo = region.town || '';
          let villageInfo = region.village || '';

          item.region = region;
          item.location = data && data.area || "";
          item.village = (townInfo + villageInfo) || _result.town || '';
        }));
      }

      await Promise.all(promises);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
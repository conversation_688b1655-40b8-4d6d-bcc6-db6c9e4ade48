/**
 * @summary getDoListHandler
 * <AUTHOR>
 *
 * Created at     : 2020-03-26 16:45:36 
 * Last modified  : 2020-03-26 16:45:36 
 */

'use strict';

const HANDLER_NAME = 'getDoListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const LOAN_APP_STATUS_MAP = require('../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const debug = logFactory(logUtil())('rongxin:loan.mgr:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;  //产品区域相同的待补充资料订单
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;  //产品区域相同的待补充资料订单
const moment = require('moment');
// const WDPRODUCTS = require('../../data/products').WDPRODUCTS
const loanProduct = require('../dataSvc/dataUtil').loanProducts;
const formatAreaCode = require('../../persistence/formatAreaCode');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      // let tId = self.context.input.tId;
      
      let employeeGroupsInfo = await employeeGroupsData.getOneByCondition({employee:opts.userid});
      
      let areaList = employeeGroupsInfo.areaList;
      let areaItem = '^'+ areaList.join('|');
      
      input.area = {
          '$regex': areaItem,
          '$options': 'si'
        }
      // let loanApplicationInfo = await loanApplicationData.getListAndCountByCondition(input);
      
      let loanApplicationInfo = await loanApplicationData.getByUrl('/v1.0/loan/applications/list',input);
      let promise = [];
      for(let item of loanApplicationInfo.result){
        item.status = LOAN_APP_STATUS_MAP.get(item.status);
        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm");
        item.amount = item.amount/100;
        item.approveAmount = item.approveAmount/100;
        
        promise.push(formatAreaCode.getFormatAreaCode(item.area).then(data => {
          item.region = data && data.region || {};
          item.location = data && data.area || "";
          item.location += item.address || "";
        }));

        // let product = WDPRODUCTS.get(item.pId);
        promise.push(loanProduct.getById(item.pId, {cache : true , expire: 24 * 60 * 60 }).then(data => {
          item.productName = data.name;
          item.product = data;
        }))
      }
      await Promise.all(promise);
      
      self.context.result = loanApplicationInfo;

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler;
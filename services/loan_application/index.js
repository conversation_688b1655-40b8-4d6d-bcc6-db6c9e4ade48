'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:loanApplication:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const PreWorkflowHandler = require("./workflows/preWorkflowHandler");
const ValidateWorkflowHandler = require("./workflows/validateWorkflowHandler");
const PermissionWorkflowHandler = require("./workflows/permissionWorkflowHandler");
const CreateLoanAppTrackingHandler = require("./createLoanAppTrackingHandler");
const PutLoanAppHandler = require("./putLoanAppHandler");
const UpdateDistributeHandler = require("./updateDistributeHandler");
const GetJXHomeNumHandler = require("./getJXHomeNumHandler");
const GetClaimNumHandler = require("./getClaimNumHandler");
const GetAlreadListHandler = require("./getAlreadListHandler");
const GetDoListHandler = require("./getDoListHandler");
const GetCollectionMidHandler = require("./getCollectionMidHandler");
const SmsUrgingHandler = require("./smsUrgingHandler");
const GetJxDoAndAlreadListHandler = require("./getJxDoAndAlreadListHandler");

const getLoanApplicationsUserListHandler = require('./getLoanApplicationsUserListHandler');
const GetCrmLoanAppListHandler = require('./getCrmLoanAppListHandler');

const GetLoanApplicationDetailHandler = require('./getLoanApplicationDetailHandler');
const GetLoanApplicationsHandler = require('./getLoanApplicationsHandler');
const FormatAreaCodeHandler = require('./formatAreaCodeHandler');
const FormatApplicatEnumHandler = require('./formatApplicatEnumHandler');

const ExecuteLoanAppVerifyHandler = require('./verify/ExecuteLoanAppVerifyHandler');
const PreFinishLoanAppVerifyHandler = require('./verify/PreFinishLoanAppVerifyHandler');
const PreExecuteLoanAppVerifyHandler = require('./verify/PreExecuteLoanAppVerifyHandler')
const FormatApplicatVerifyInfoHandler = require('../loan_application_v2/baseOperations/formatApplicatVerifyInfoHandler')
const InvestigationVerify2ForExecuteHandler = require('./verify/investigationVerify2ForExecuteHandler');
const InvestigationVerify2ForCommitHandler = require('./verify/investigationVerify2ForCommitHandler');
const AddAssuranceInfoHandler = require('./verify/addAssuranceInfoHandler');
const GetCzdLoanAppListHandler = require('./getCzdLoanAppListHandler');
const PreGetLoanAppListHander = require('./preGetLoanAppListHander');
const PostCzdCallTrackingHandler = require('./postCzdCallTrackingHandler');
const GetLoanRestructuringHandler = require('./getLoanRestructuringHandler');
const RestructuringApplyHandler = require('./restructuringApplyHandler');
const RestructuringOffHandler = require('./restructuringOffHandler');
const RestructuringDetailHandler = require('./restructuringDetailHandler');

class Service {
  constructor() {

  }

  async putLoanApplication(input, _opts) {
    let method = 'putLoanApplication';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new PreWorkflowHandler(context));
      svcHandlerMgrt.addHandler(new ValidateWorkflowHandler(context));
      svcHandlerMgrt.addHandler(new PermissionWorkflowHandler(context));
      svcHandlerMgrt.addHandler(new PutLoanAppHandler(context));
      svcHandlerMgrt.addHandler(new UpdateDistributeHandler(context));
      svcHandlerMgrt.addHandler(new CreateLoanAppTrackingHandler(context));
      await svcHandlerMgrt.processAsync(context);

      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getJXHomeNum(input, _opts) {
    let method = 'getJXHomeNum';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetJXHomeNumHandler(context));
      await svcHandlerMgrt.processAsync(context);

      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getClaimNum(input, _opts) {
    let method = 'getClaimNum';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetClaimNumHandler(context));
      await svcHandlerMgrt.processAsync(context);

      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getAlreadList(input, _opts) {
    let method = 'getAlreadList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetAlreadListHandler(context));
      await svcHandlerMgrt.processAsync(context);

      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getDoList(input, _opts) {
    let method = 'getDoList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetDoListHandler(context));
      await svcHandlerMgrt.processAsync(context);

      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getCollectionMid(input, _opts) {
    let method = 'getCollectionMid';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetCollectionMidHandler(context));
      await svcHandlerMgrt.processAsync(context);

      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async smsUrging(input, _opts) {
    let method = 'smsUrging';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new SmsUrgingHandler(context));
      await svcHandlerMgrt.processAsync(context);

      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getJxDoAndAlreadList(input, _opts) {
    let method = 'getJxDoAndAlreadList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetJxDoAndAlreadListHandler(context));
      await svcHandlerMgrt.processAsync(context);

      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


  async getLoanApplicationList(input, _opts) {
    let method = 'getLoanApplicationList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      // svcHandlerMgrt.addHandler(new CertUserLoanAppHandler(context));
      svcHandlerMgrt.addHandler(new GetLoanApplicationsHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatEnumHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatVerifyInfoHandler(context));
      // svcHandlerMgrt.addHandler(new UpdateLoanAppStatusHandler(context));
      // svcHandlerMgrt.addHandler(new GetPaymentHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


  async getLoanApplicationDetail(input, _opts) {
    let method = 'getLoanApplicationDetail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetLoanApplicationDetailHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatEnumHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatVerifyInfoHandler(context));

      // svcHandlerMgrt.addHandler(new UpdateLoanAppDetailStatusHandler(context));
      // svcHandlerMgrt.addHandler(new FormatLoanAppRejectReasonHandler(context));
      // svcHandlerMgrt.addHandler(new GetPaymentHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


  async executeLoanAppVerify(input, _opts) {
    const method = 'executeLoanAppVerify';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new PreFinishLoanAppVerifyHandler(context));
      svcHandlerMgrt.addHandler(new InvestigationVerify2ForExecuteHandler(context));
      svcHandlerMgrt.addHandler(new AddAssuranceInfoHandler(context));
      svcHandlerMgrt.addHandler(new PreExecuteLoanAppVerifyHandler(context));
      svcHandlerMgrt.addHandler(new ExecuteLoanAppVerifyHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


  async getLoanApplicationUserList(input, _opts) {
    let method = 'getLoanApplicationList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      // svcHandlerMgrt.addHandler(new CertUserLoanAppHandler(context));
      svcHandlerMgrt.addHandler(new getLoanApplicationsUserListHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatEnumHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatVerifyInfoHandler(context));
      // svcHandlerMgrt.addHandler(new UpdateLoanAppStatusHandler(context));
      // svcHandlerMgrt.addHandler(new GetPaymentHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getCrmLoanAppList(input, _opts) {
    let method = 'getCrmLoanAppList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetCrmLoanAppListHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatEnumHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


  async investigationVerify2Submit(input, _opts) {
    const method = 'investigationVerify2Submit';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new PreExecuteLoanAppVerifyHandler(context));
      svcHandlerMgrt.addHandler(new InvestigationVerify2ForCommitHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getCzdLoanAppList(input, _opts) {
    let method = 'getCzdLoanAppList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new PreGetLoanAppListHander(context));
      svcHandlerMgrt.addHandler(new GetCzdLoanAppListHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatEnumHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async postCzdCallTracking(input, _opts) {
    let method = 'postCzdCallTracking';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new PostCzdCallTrackingHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async getLoanRestructuring(input, _opts) {
    let method = 'getLoanRestructuring';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetLoanRestructuringHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async restructuringApply(input, _opts) {
    let method = 'restructuringApply';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new RestructuringApplyHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async restructuringOff(input, _opts) {
    let method = 'restructuringOff';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new RestructuringOffHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async restructuringDetail(input, _opts) {
    let method = 'restructuringDetail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new RestructuringDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

}

module.exports = new Service();
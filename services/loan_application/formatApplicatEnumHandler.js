/**
 * @summary FormatApplicatEnumHandler
 * <AUTHOR>
 *
 * Created at     : 2018-11-26 14:48:43 
 * Last modified  : 2018-12-06 16:14:12
 */

'use strict';

const HANDLER_NAME = 'FormatApplicatEnumHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanAppEnumData = require('../../data/loan_application_enum').LOAN_APP_ENUM_MAP;
const productData = require('../../data/loanProduct').LOAN_PRODUCT_WND;
const moment = require('moment');
const loanProduct = require('../dataSvc/dataUtil').loanProducts;
const loanApplicationTrackingData = require('../dataSvc/dataUtil').loanApplicationTracking;
const userIdentityData = require('../dataSvc/dataUtil').userIdentity;
const { APP_STATUS_COMMENT, APPLICATION_STATUS } = require('../../utils/loanApplicationConst');
const { PRODUCT } = require('../../utils/fundConst');
const WDPRODUCTS = require('../../data/products').WDPRODUCTS;
const fundData = require('../dataSvc/dataUtil').funds;
const employeesData = require('../dataSvc/dataUtil').employees;
const groupsV2Data = require('../dataSvc/dataUtil').groupsV2;
const CONSUMER_MAP = require('../../utils/loanApplicationConst').CONSUMER_MAP;
const CZD_STATUS_MAP = new Map([
  [APPLICATION_STATUS.REJECTED_CREDIT_ACCESS, "授信拒绝"]
])

const FARMLANDTYPE = new Map([
  [
    '1', '自有土地'
  ],
  [
    '2', '流转土地'
  ]
]);
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }

      for (let item of _result) {
        if (item.farmlandType) item.farmlandType = FARMLANDTYPE.get(item.farmlandType.toString());
        //if (item.loanUse) item.loanUse = self.getEnumValue(loanAppEnumData.get("3"), item.loanUse);
        if (item.repayment) item.repayment = self.getEnumValue(loanAppEnumData.get("4"), item.repayment);
        if (item.marital) item.marital = self.getEnumValue(loanAppEnumData.get("5"), item.marital);
        if (item.contactRelationship) item.contactRelationship = self.getEnumValue(loanAppEnumData.get("6"), item.contactRelationship);
        let userIdentity = await userIdentityData.getOneByCondition({
          uId: item.uId,
          archived: false,
          IDCardStatus: 'approved'
        });
        item.IDCard = userIdentity && userIdentity.IDCard;

        const product = await loanProduct.getById(item.pId, { cache: true, expire: 24 * 60 * 60 });
        if (product) {
          if (!item.loanTerm) {
            item.loanTerm = `${product.borrowPeriod}期`;
          }
          item.productName = product.name;
          item.type = product.name; //益易农取该字段作为产品名称
          //WDPRODUCTS 临时方案，表示业务类型，正确应该是consumer_t字段
          let productConst = WDPRODUCTS.get(item.pId);
          if (productConst) {
            item.productType = productConst.name + "贷";
            item.type = productConst.type;
          }
          if (item.consumer_t) {
            item.productType = `${CONSUMER_MAP.get(item.consumer_t)}贷`;
            item.type = item.consumer_t;
          }
        }
        if (item.fund) {
          let fund = await fundData.getById(item.fund, { cache: true, expire: 24 * 60 * 60 });
          item.fundName = fund.name || '';
        }
        if (item.createdTime) {
          item.formatCreatedTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss") || item.createdTime;
        }
        item.statusName = APP_STATUS_COMMENT.get(item.status);
        if (item.pId == PRODUCT.CG_CZD_ID) {
          item.statusName = CZD_STATUS_MAP.get(item.status) || APP_STATUS_COMMENT.get(item.status);
        }
        if (item.ascription) {
          let employees = await employeesData.getById(item.ascription);
          item.ascriptionName = employees && employees.username || ""
        }
        if (item.orgInfo && item.orgInfo.orgId) {
          let groupsV2 = await groupsV2Data.getById(item.orgInfo.orgId);
          item.orgInfo.orgName = groupsV2 && groupsV2.name || ""
        }
      }

      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item.key === key) return item.value;
    }
    return '';
  }
}

module.exports = Handler
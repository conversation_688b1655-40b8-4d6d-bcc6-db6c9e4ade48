/**
 * @summary getCollectionMidHandler
 * <AUTHOR>
 *
 * Created at     : 2020-03-26 16:45:36 
 * Last modified  : 2020-03-26 16:45:36 
 */

'use strict';

const HANDLER_NAME = 'getCollectionMidHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');

const debug = logFactory(logUtil())('rongxin:loan.mgr:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const userIdentityData = require('../dataSvc/dataUtil').userIdentity;  
const infoEnterpriseBasicData = require('../dataSvc/dataUtil').infoEnterpriseBasic;  
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;  

const infoPersonalFamilyMemberData = require('../dataSvc/dataUtil').infoPersonalFamilyMember;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let mId = '';
      let loanApp;
      if(input.type=='01'){ //个人
        let userInfo = await userIdentityData.getOneByCondition({'uId':input.uniqueCode,"IDCardStatus":"approved"});
        let member = await infoPersonalFamilyMemberData.getOneByCondition({'members.idCard':userInfo.IDCard,archived:false});
        mId = member && member.mId || '';
      }else{
        let basic = await infoEnterpriseBasicData.getOneByCondition({'companyID':input.uniqueCode});
        mId = basic && basic.mId || '';
        // if(mId){
          let result = await loanApplicationData.getByUrl('/v1.0/loan/applications/list',{socialCode:input.uniqueCode,archived:false});
          if(result && result.result &&  result.result.length>0){
            loanApp = {
              socialCode : result.result[0].socialCode,
              enterpriseName : result.result[0].enterpriseName
            }
            if(result.result[0].contactRelationship == '1'){
              loanApp.username = result.result[0].username;
              loanApp.userMobile = result.result[0].userMobile;
              let userInfo = await userIdentityData.getOneByCondition({'uId':result.result[0].uId,"IDCardStatus":"approved"});
              loanApp.IDCard = userInfo.IDCard;
            }
         }
        // }
      }
      
      
      
      self.context.result = {mId,loanApp};

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler;
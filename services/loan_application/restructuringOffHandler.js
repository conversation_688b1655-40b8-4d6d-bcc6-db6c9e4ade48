'use strict';

const HANDLER_NAME = 'restructuringOffHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  loanApplication: loanApplicationData,
  transferWhitelist: transferWhitelistData
} = require('../dataSvc/dataUtil');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input: condition, opts } = this.context;

      let transferWhitelist = await transferWhitelistData.getById(condition.id);
      

      let loanInfo = await loanApplicationData.getOneByCondition({
        tId: opts.tId,
        userMobile:transferWhitelist.mobile,
        archived: false,
        status: {
          $regex: '^(?!rejected|finished|loaned)',
          $options: 'i'
        },
      });
      if(loanInfo){
        throw {
          errorCode: 'E_LOAN_RESTRUCTURING_043',
          httpCode: 406,
          reason: '存在在途订单'
        };
      }

      let result = await transferWhitelistData.putById(transferWhitelist._id, {
        status: 4,
        trackings: [{
            action: 'restructuring_off',
            target_t: 1,
            target: opts.uId,
            comments: '取消',
            createdTime: new Date()
        }].concat(transferWhitelist.trackings)
      })
      await loanApplicationData.putById(transferWhitelist.aId, {
        loanRestructuring: 0
      })
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
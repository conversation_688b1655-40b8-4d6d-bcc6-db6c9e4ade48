'use strict';

const HANDLER_NAME = 'GetCrmLoanAppListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const userVerifysData = require('../dataSvc/dataUtil').userVerifys;
const { TENANT_LIST } = require('../../utils/tenantConst');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;

      let userVerify = await userVerifysData.getOneByCondition({
        IDCard: input.idCard,
        IDCardStatus: "approved",
        archived: false
      });
      if (!userVerify) {
        throw {
          errorCode: 'CRM_LIST_035',
          httpCode: 406,
          reason: 'Invalid idCard'
        }
      }

      let payload = {
        tId: TENANT_LIST.TENANT_JF,
        uId: userVerify.uId,
        $sort: input.$sort,
        archived: false
      }
      if (!input.isAll) {
        payload.status = {
          $regex: '^(?!rejected|finished)',
          $options: 'i'
        }
      }
      let loanList = await loanApplicationData.getByCondition(payload);
      self.context.result.result = loanList;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'getLoanApplicationsHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  loanApplication: loanApplicationData,
  employeeGroups: employeeGroupData,
  groupsV2:groupV2Data,
  groups: groupData,
  userAddons: userAddonsData,
} = require('../dataSvc/dataUtil');
const { getEmployeeLimit } = require('../../utils/getUserFromReq');
const {
  APPLICATION_STATUS_LIST,
  APPLICATION_STATUS_DIC,
  APPLICATION_STATUS_SMALL_LIST,APPLICATION_STATUS_SMALL_DIC,
  APPLICATION_STATUS_STEP,
  STATUS_MENU,STATUS_MENU_LIST
} = require('../loan_application_v2/baseOperations/Constants');
const { outlandAbleStatus , findSignConditionByRoleId , handleBankAccountManagerQuery } = require('../loan_application/verify/VerifyUtils');
const landConfirmStatus = require('../../data/land_confirm_status');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input: condition, opts } = this.context;
      const  { onlyMyOrg , getLandData, tId, uId , toBeHandledByMe , statusAfter , statusAfterType , getAllArea , outlandAbleStatus: outlandAbleStatusOpts } = opts;
      const roleId = opts.role || opts.roleId
      // const areaCodeLimitOr = uId && await getEmployeeLimit(employeeGroupData, tId, uId, 'area', roleId);
      // !getAllArea && uId && (condition['$or'] = areaCodeLimitOr);

      // const eg = tId && uId && await employeeGroupData.getOneByCondition( { employee: uId , tId , ...roleId && {group:roleId} || {} ,archived:false } );
      // const v2 = eg && await groupV2Data.getById( eg.groupV2 ) ;
      // const {name:roleName} = roleId && await groupsData.getById( roleId ) || {}
      if (condition.tId === '600fe47561f0e675643c5fa1' && opts.uId) {
        const $and = [];
        const areaCodeLimitOr = opts.uId && await getEmployeeLimit(employeeGroupData, condition.tId, opts.uId, 'area', opts.roleId);
        !getAllArea && opts.uId && areaCodeLimitOr && areaCodeLimitOr.length && ($and.push({ $or: areaCodeLimitOr }));
        // 物权的若干个角色只会有一个groupV2
        const eQuery = { employee: opts.uId , tId: condition.tId, archived: false };
        roleId && ( eQuery.group = roleId );
        const eg = await employeeGroupData.getOneByCondition( eQuery );
        const v2 = await groupV2Data.getById(eg.groupV2);
        opts.groupV2 = v2;
        // const orgQuery = [{ "orgInfo.orgId": v2._id }]; // 这个逻辑已废弃
        // const orgCodeQuery = [].concat(v2 && v2.areaList || []).map(code => ({ "orgInfo.orgAreaCode": { '$regex': `^${code}.+`, '$options': 'si' } }))
        // orgCodeQuery.length && (orgQuery.push({ $or: orgCodeQuery }));
        // $and.push( { $or:orgQuery } );
        const signQuery = roleId && toBeHandledByMe && await findSignConditionByRoleId.call(this, roleId, debug);
        const { name: roleName } = roleId && await groupData.getById(roleId) || {}
        Object.keys( signQuery || {} ).length && $and.push(signQuery);// 如果该角色没有在配置节点中找到可以审核的对象，跳过此处理
        $and.length && (condition.$and = $and);
        const childrenOrgId = !onlyMyOrg && (await groupV2Data.getByCondition({ tId: condition.tId, archived: false, code: { '$regex': `^${v2.code}`, '$options': 'si' }, limit: 'unlimited' })).map(v => v._id) || []; //拿到子孙县域
        childrenOrgId.push( v2._id );//无条件添加自己
        // debug('debug233',childrenOrgId,JSON.stringify({ tId:condition.tId,archived:false, code:{ '$regex': `^${v2.code}`, '$options': 'si' }, limit:'unlimited'}))
        //物权总公司特别，单独在列
        v2._id !== '60111486fda3812feb51fd00' && (condition["orgInfo.orgId"] = { $in: childrenOrgId });
        await handleBankAccountManagerQuery( condition , eg , debug );
        // toBeHandledByMe && roleName ==='AccountManager' && (condition["orgInfo.orgId"] = v2._id);
        debug(`${HANDLER_NAME}AreaLimit`, v2._id, opts.uId, opts.uId, toBeHandledByMe, roleId, roleName, v2.code, JSON.stringify(condition));
        // toBeHandledByMe && roleName ==='AccountManager' && (condition["orgInfo.orgId"] = v2._id);
      }

      const statusList = statusAfter && statusAfterType && APPLICATION_STATUS_STEP[statusAfterType];
      const statusIn = statusList && statusList.steps
          .slice( statusList.steps.findIndex(v=>v.includes(statusAfter)) + 1 )
          .reduce((r,v)=>r.concat(v),[])
          .concat(statusList.rejects);
      statusIn && ( condition.status = {$in:statusIn} );
      outlandAbleStatusOpts && ( condition.status = {$in:outlandAbleStatus} );
      if (outlandAbleStatusOpts) {
        condition['verifyInfo.outLand.status'] = { 
          $nin: [landConfirmStatus.WAIT_CONFIRM, landConfirmStatus.AGREED_CONFIRM, landConfirmStatus.FINISHED] 
        };
      }

      // 移到了上面
      // const childrenOrgId = !onlyMyOrg && ( await groupV2Data.getByCondition({ tId:condition.tId,archived:false, code:{ '$regex': `^${v2.code}+`, '$options': 'si' }, limit:'unlimited'}) ).map(v=>v._id) || [] ;//拿到子孙县域
      //   // debug('debug233',childrenOrgId,JSON.stringify({ tId:condition.tId,archived:false, code:{ '$regex': `^${v2.code}`, '$options': 'si' }, limit:'unlimited'}))
      // childrenOrgId.push( v2._id );//再无条件添加自己
      //   //物权总公司特别，单独在列
      // v2._id !== '60111486fda3812feb51fd00' && ( condition["orgInfo.orgId"] = { $in:childrenOrgId } );
      // noHadSign && roleName ==='AccountManager' && (condition["orgInfo.orgId"] = v2._id);
      // debug( `${HANDLER_NAME}Query`, method,'query', v2 && v2._id  , v2 && v2.code , uId , tId , roleId , roleName , getAllArea,uId,onlyMyOrg,JSON.stringify(condition));
      // condition.tId = self.context.opts.tId;
      this.context.result = await loanApplicationData.postListAndCountByCondition(condition);
      await Promise.all(self.context.result.result.map(async it => {
        const userData = await userAddonsData.getOneByCondition({ uId: it.uId, type: 'cxwq', archived: false });
        (userData && userData.userCatagory) && (it.userCatagory = userData.userCatagory);
      }));
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
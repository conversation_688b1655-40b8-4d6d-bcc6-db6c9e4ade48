/**
 * @summary smsUrgingHandler
 * <AUTHOR>
 *
 * Created at     : 2018-11-29 11:20:52 
 * Last modified  : 2018-11-29 11:33:12
 */

'use strict';

const HANDLER_NAME = 'smsUrgingHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:mgr:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const infoEnterpriseBasicData = require('../dataSvc/dataUtil').infoEnterpriseBasic;
const contractFlowsData = require('../dataSvc/dataUtil').contractFlowsV2;
const contractOwnersV2Data = require('../dataSvc/dataUtil').contractOwnersV2;
const agent = require('superagent');
//const ENTERPRISE_URL = '/api/v1.0/loan/auth/enterprise';
const SENDSMS_URL = '/api/v2.0/loan/contract/enterprise/sendSms';
const moment = require('moment');
const config = require('config');
const redisData = require('../../persistence/dataStore');
const env = require('../env');
let mode = "";
if (env.getServerMode() === "development") {
  mode = 'dev-'
} else if (env.getServerMode() === "production") {
  mode = '';
} else {
  mode = env.getServerMode() + '-'
}
const H5_URL = 'http://' + mode + 'm.loan.cacfintech.com/cg/certification';

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let loanId = self.context.input.loanId;
      
      let remindNum = await redisData.incr(`REMIND_${loanId}_${moment().format('YYYYMMDD')}`);
      if(remindNum>3){
        throw {
          errorCode: 'E_REMIND_043',
          httpCode: 406,
          reason: 'remindNum overflow'
        };
      }
      try{
        let loanInfo = await loanApplicationData.getById(loanId);
        debug(method, '[loanInfo](addons)', loanInfo.addons.info.mId);
        let basic = await infoEnterpriseBasicData.getOneByCondition({ mId: loanInfo.addons.info.mId ,archived:false});
        let userAppApiConfig = config.get("rongxin_loan_user_app_api_service");

        // let getSignUrl = `${userAppApiConfig.host}${ENTERPRISE_URL}`
        // let payload = {
        //   loanId: loanId,
        //   enterpriseName: basic.legalPerson,
        //   enterpriseCardId: basic.corporateIDCard,
        //   mobile: basic.corporateMobile
        // }
        // debug(method, '[loanInfo](getSignUrl)', getSignUrl,payload);
        // let execData = await agent.get(getSignUrl).query(payload);
        debug(method, '[loanInfo](basic.legalPerson)', basic.legalPerson);
        let conFlowRes = await contractFlowsData.getOneByCondition({
          uId: loanInfo.uId,
          loanId: loanId,
          archived: false
        });
        debug(method, '[loanInfo](conFlowRes._id)', conFlowRes._id);
        let owners = await contractOwnersV2Data.getOneByCondition({
          realname: basic.legalPerson,
          IDCard: basic.corporateIDCard,
          aId: loanId,
          sign: 1,
          archived: false
        });
        let signUrl = `${H5_URL}?flowId=${conFlowRes._id}&uId=${owners._id}`;
        debug(method, '[loanInfo](signUrl)', signUrl);
        let sendSmsUrl = `${userAppApiConfig.host}${SENDSMS_URL}`
        let body = {
          loanId: loanId,
          signUrl:signUrl
        }
        let result = await agent.post(sendSmsUrl).send(body)
        debug(method, '[loanInfo](sendSmsUrl)', sendSmsUrl,body);
        self.context.result = result.body;
      }catch(error){
        throw {
          httpCode: 500,
          errorCode: 'E_SMS_URGING_058',
          reason: error.message
        }
      }

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
/**
 * @summary getJXHomeNumHandler
 * <AUTHOR>
 *
 * Created at     : 2020-03-26 15:20:37
 * Last modified  : 2020-03-26 15:20:37
 */

'use strict';

const HANDLER_NAME = 'getJXHomeNumHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanDistributeData = require('../dataSvc/dataUtil').loanDistribute; //已办
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;  //产品区域相同的待补充资料订单
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;  //产品区域相同的待补充资料订单

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let userid = self.context.input.userid;
      let tId = self.context.input.tId;
      let loanDistributeInfo = await loanDistributeData.getCountByCondition({distribute:userid,handleStatus:true});
      let alreadyNum = loanDistributeInfo.count;
      let employeeGroupsInfo = await employeeGroupsData.getOneByCondition({employee:userid});
      let doNum = 0;
      if(employeeGroupsInfo){
        let areaList = employeeGroupsInfo.areaList;
        
        let areaItem = '^'+ areaList.join('|');
        
        let loanApplicationInfo = await loanApplicationData.getByUrl('/v1.0/loan/applications/list',{area:{
          '$regex': areaItem,
          '$options': 'si'
        },status:"biopsy_approved",archived:false});
        doNum = loanApplicationInfo && loanApplicationInfo.total || 0;
      }
      // let assignedNum = 0;
      let loanAssignInfo = await loanDistributeData.getCountByCondition({assigned:userid,handleStatus:false});
      let assignedNum = loanAssignInfo.count;
      let result = {alreadyNum,doNum,assignedNum}
      self.context.result = result;

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler;
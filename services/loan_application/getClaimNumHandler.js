/**
 * @summary getClaimNumHandler
 * <AUTHOR>
 *
 * Created at     : 2020-03-26 15:20:37
 * Last modified  : 2020-03-26 15:20:37
 */

'use strict';

const HANDLER_NAME = 'getClaimNumHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanDistributeData = require('../dataSvc/dataUtil').loanDistribute; //已办
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;  //产品区域相同的待补充资料订单
const employeeGroupsData = require('../dataSvc/dataUtil').employeeGroups;  //产品区域相同的待补充资料订单
const GROUP_ROLE = require('../permission').GROUP_ROLE;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let userid = self.context.input.userid;
      let tId = self.context.input.tId;
      let opts = self.context.opts;
      let _role = opts.role;
      let assignStatus;
      // 初审由业务管理认领
      if (_role === GROUP_ROLE.BUSINESSMANAGEMENT) {
        assignStatus = "pre_censor_destined";
      }
      // 客户经理认领
      if (_role === GROUP_ROLE.ACCOUNTMANAGER) {
        assignStatus = "destined";
      }
      let loanDistributeInfo = await loanDistributeData.getCountByCondition({assigned:userid,handleStatus:true,assignStatus:assignStatus,archived:false});
      let alreadyNum = loanDistributeInfo.count || 0;

      // let assignedNum = 0;
      let loanAssignInfo = await loanDistributeData.getCountByCondition({assigned:userid,handleStatus:false,assignStatus:assignStatus,archived:false});
      let assignedNum = loanAssignInfo.count || 0;
      let result = {alreadyNum,assignedNum}
      self.context.result = result;

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler;
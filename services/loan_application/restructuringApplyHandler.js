'use strict';

const HANDLER_NAME = 'restructuringApplyHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  loanApplication: loanApplicationData,
  transferWhitelist: transferWhitelistData,
  employees: employeeData
} = require('../dataSvc/dataUtil');

const formatAreaCode = require('../../persistence/formatAreaCode');
const aLiYunMsg = require('../messages/sendSmsALiYunMessage');
const Templateid = 'SMS_491520570';

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input: condition, opts } = this.context;

      let info = await transferWhitelistData.getOneByCondition({
        aId: condition.aId,
        status:{$in: [1,2]}
      });
      if(info){
        throw {
          errorCode: 'E_LOAN_RESTRUCTURING_33',
          httpCode: 406,
          reason: '订单重复申请'
        }
      }

      const employeeInfo = await employeeData.getById(condition.operator);
      //employeeInfo.username


      const loanInfo = await loanApplicationData.getById(condition.aId);

      condition.area = loanInfo.area;
      condition.amount = (loanInfo.outstanding - condition.lowerAmount) || 0;
      condition.loan = {
        sn: loanInfo.sn,
        status: loanInfo.status,
        outstanding: loanInfo.outstanding,
        //根据身份证号判断当天年龄
        age: getAge(condition.IDCard.substring(6, 14))
      };
      condition.loanType = loanInfo.type;
      const result = await transferWhitelistData.post(condition);
      await loanApplicationData.putById(loanInfo._id,{
        loanRestructuring: 1
      })

      let areaData = await formatAreaCode.getFormatAreaCode(condition.area);

      let param = {
        area: `${areaData.region.city}${areaData.region.district}`,
        account_manager: employeeInfo.username,
        customer: condition.name,
        time: formatDateToChinese()
      };

      let signName = "益易农";
      let content = {
        payload: {
          PhoneNumbers: '***********',
          // PhoneNumbers: '***********',
          TemplateCode: Templateid,
          SignName: signName,
          TemplateParam: JSON.stringify(param),
        },

        caller: "rongxin_userapp"
      };

      let sendRes = await aLiYunMsg.sendSms(content);
      debug(method, 'Sms msg sent', sendRes.body);

      //${areaData.region.city}${areaData.region.district}的${employeeInfo.username}客户经理的客户${input.name}，已经添加至业务重组名单，时间${formatDateToChinese（）}。

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler

//对比年月日判断年龄
function getAge(strBirthday) {
  var returnAge;
  //19900101
  var birthYear = strBirthday.substring(0, 4);
  var birthMonth = strBirthday.substring(4, 6);
  var birthDay = strBirthday.substring(6, 8);

  var d = new Date();
  var nowYear = d.getFullYear();
  var nowMonth = d.getMonth() + 1;
  var nowDay = d.getDate();

  if (nowYear == birthYear) {
    returnAge = 0; //同年 则为0岁
  }else {
    var ageDiff = nowYear - birthYear; //年之差
    if (ageDiff > 0) {
      if (nowMonth == birthMonth) {
        var dayDiff = nowDay - birthDay; //日之差
        if (dayDiff < 0) {
          returnAge = ageDiff - 1;
        }
        else {
          returnAge = ageDiff;
        }
      }
      else {
        var monthDiff = nowMonth - birthMonth; //月之差
        if (monthDiff < 0) {
          returnAge = ageDiff - 1;
        }
        else {
          returnAge = ageDiff;
        }
      }
    }
  }
  return returnAge;
}

function formatDateToChinese(date = new Date()) {
  // 获取日期各部分
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // 月份从0开始，需+1
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();
  
  // 补零函数：确保个位数前面加0
  const padZero = (num) => num.toString().padStart(2, '0');
  
  // 拼接成中文格式
  return `${year}年${padZero(month)}月${padZero(day)}日${padZero(hours)}分${padZero(seconds)}秒`;
}
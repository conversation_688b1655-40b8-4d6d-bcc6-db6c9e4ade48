'use strict';

const HANDLER_NAME = 'getLoanApplicationsUserListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  loanApplication: loanApplicationData,
  employeeGroups: employeeGroupData,
  userAddons: userAddonsData,
} = require('../dataSvc/dataUtil');
const { getEmployeeLimit } = require('../../utils/getUserFromReq');
const {
  APPLICATION_STATUS_LIST,
  APPLICATION_STATUS_DIC,
  APPLICATION_STATUS_SMALL_LIST,APPLICATION_STATUS_SMALL_DIC,
  APPLICATION_STATUS_STEP,
  STATUS_MENU,STATUS_MENU_LIST
} = require('../loan_application_v2/baseOperations/Constants');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input: condition, opts: { getLandData, tId, uId, roleId , statusAfter , statusAfterType } } = this.context;
      const areaCodeLimitOr = uId && await getEmployeeLimit(employeeGroupData, tId, uId, 'area', roleId);
      // uId && (condition['$or'] = areaCodeLimitOr);
      
      debug(method, 1, JSON.stringify(condition));
      // condition.tId = this.context.opts.tId;
      const {result} = this.context.result = await loanApplicationData.getByUrl("/v1.0/loan/applications/loaned-user/list", condition);

      // this.context.result = await loanApplicationData.getListAndCountByCondition(condition);
      await Promise.all(result.map(async it => {
        Object.assign( it,await loanApplicationData.getOneByCondition({sn:it.sn,tId:condition.tId}) );
        const userData = await userAddonsData.getOneByCondition({ uId: it.uId, type: 'cxwq', archived: false });
        (userData && userData.userCatagory) && (it.userCatagory = userData.userCatagory);
        return it;
      }));
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
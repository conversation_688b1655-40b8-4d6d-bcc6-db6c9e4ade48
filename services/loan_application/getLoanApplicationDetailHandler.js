'use strict';

const HANDLER_NAME = 'getLoanApplicationDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const loanProduct = require('../dataSvc/dataUtil').loanProducts;
const WDPRODUCTS = require('../../data/products').WDPRODUCTS;
const CONSUMER_TYPE = require('../../utils/loanApplicationConst').CONSUMER_TYPE;
const enterpriseBaseData = require('../dataSvc/dataUtil').infoEnterpriseBasic;
const infoVersionData = require('../dataSvc/dataUtil').infoVersion;
const userAddonsData = require('../dataSvc/dataUtil').userAddons;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input.id;
      let isRec = self.context.input.isRec;

      let result = await loanApplicationData.getById(id);
      if (!result) {
        throw {
          errorCode: 'E_LOAN_APP_064',
          httpCode: 404,
          reason: 'not found'
        };
      }

      let product = WDPRODUCTS.get(result.pId);
      const data = await loanProduct.getById(result.pId, {cache : true , expire: 24 * 60 * 60 });
      result.productName = "";
      result.product = {};
      if (data) {
        result.productName = data.name;
        result.product = data;
      }

      if (product) {
        result.productType = product.type;
        result.type = product.type;
      }

      if (result.consumer_t == CONSUMER_TYPE.ENTERPRISE && result.addons && result.addons.info && result.addons.info.mId) {
        let version = await infoVersionData.getOneByCondition({
          mId: result.addons.info.mId,
          version: result.addons.info.version,
          archived: false
        });
        if (version) {
          let basicInfo = await enterpriseBaseData.getById(version.infoIds.basic);
          result.collectInfo = basicInfo;
        }
      }
      let userData = await userAddonsData.getOneByCondition({
        uId: result.uId,
        type:'cxwq',
        archived: false
      });
      if(userData && userData.userCatagory){
        result.userCatagory = userData.userCatagory;
      }
      if(result.isRec == '1'){
        userData = await userAddonsData.getOneByCondition({
          uId: result.uId,
          archived: false
        });
        result.IDCard = userData.IDCard;
      }
      
      self.context.result = result;

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
/**
 * @summary getAlreadListHandler
 * <AUTHOR>
 *
 * Created at     : 2020-06-23 16:45:36 
 * Last modified  : 2020-06-23 16:45:36 
 */

'use strict';

const HANDLER_NAME = 'getAlreadListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const LOAN_APP_STATUS_MAP = require('../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const debug = logFactory(logUtil())('rongxin:loan.mgr:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const loanDistributeData = require('../dataSvc/dataUtil').loanDistribute; //已办
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;  //产品区域相同的待补充资料订单
const moment = require('moment');
const loanProduct = require('../dataSvc/dataUtil').loanProducts;
const formatAreaCode = require('../../persistence/formatAreaCode');
const GROUP_ROLE = require('../permission').GROUP_ROLE;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      // let tId = self.context.input.tId;
      let _role = opts.role;
      // opts
      // 初审由业务管理认领
      if (_role === GROUP_ROLE.BUSINESSMANAGEMENT) {
        input.assignStatus = "pre_censor_destined";
      }
      // 客户经理认领
      if (_role === GROUP_ROLE.ACCOUNTMANAGER) {
        input.assignStatus = "destined";
      }

      let loanDistributeInfo = await loanDistributeData.getListAndCountByCondition(input);
      let ids = [];
      for (let loan of loanDistributeInfo.result) {
        ids.push(loan.aId);
      }
      if (ids.length > 0) {

        let result = await loanApplicationData.getByUrl('/v1.0/loan/applications/list', { _id: { $in: ids }, $sort: { createdTime: -1 } });
        let promise = [];
        for (let item of result.result) {
          item.originStatus = item.status;
          item.status = LOAN_APP_STATUS_MAP.get(item.status);
          item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm");
          item.amount = item.amount / 100;
          item.approveAmount = item.approveAmount / 100;
          
          promise.push(formatAreaCode.getFormatAreaCode(item.area).then(data => {
            item.region = data && data.region || {};
            item.location = data && data.area || "";
            item.location += item.address || "";
          }));

          promise.push(loanProduct.getById(item.pId, {cache : true , expire: 24 * 60 * 60 }).then(data => {
            item.productName = data.name;
            item.product = data;
          }))
        }
        await Promise.all(promise);

        loanDistributeInfo.result = result.result;
        self.context.result = loanDistributeInfo;
      } else {
        self.context.result = { result: [], total: 0 }
      }

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler;
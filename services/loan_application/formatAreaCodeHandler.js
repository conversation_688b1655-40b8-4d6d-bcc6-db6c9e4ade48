/**
 * @summary FormatAreaCodeHandler
 * <AUTHOR>
 *
 * Created at     : 2018-11-26 11:18:55  
 * Last modified  : 2018-11-29 10:01:25
 */

'use strict';

const HANDLER_NAME = 'FormatAreaCodeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const BIOPSYLINE = 0.7;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }
      let promises = [];

      for (let item of _result) {
        item.amount /= 100;

        promises.push(formatAreaCode.getFormatAreaCode(item.area).then(data => {
          let region = data && data.region || {};
          let townInfo = region.town || '';
          let villageInfo = region.village || '';

          item.region = region;
          item.location = data && data.area || "";
          item.village = (townInfo + villageInfo) || _result.town || '';
        }));

        if (item.area)
          item.cityAreaCode = item.area.slice(0, 6);
        if (item.approveAmount)
          item.approveAmount /= 100;

        item.biopsyStatus = 'rejected';
        if (item.biopsyConfidence >= BIOPSYLINE)
          item.biopsyStatus = 'approved';
      }

      await Promise.all(promises);


      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'listLoanApplicationOutLandGroupHanlder';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const aliOssSvc = require('../aliOssSvc');
const formatAreaCode = require('../../persistence/formatAreaCode');

const {
  loanApplicationOutLandGroup: loanApplicationOutLandGroup,
  systemOrg: systemOrg,
  groupsV2: groupsV2,
  employeeGroups:employeeGroupData,
} = require('../dataSvc/dataUtil');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { input,opts:{uId,roleId}} = this.context;
      debug(method,'groupsV2List1',input,uId);
      let result = await groupsV2.getByCondition(input);
      const employeesCondition = {employee:uId};
      roleId && (employeesCondition.group = roleId);
      const employees = await employeeGroupData.getByCondition(employeesCondition);
      debug(method,'groupsV2List2',result);
      debug(method,'groupsV2List3',roleId||'',employees);
      result = result.filter(it=>it.employees =
          employees.find(e=>new RegExp(`^${e.orgCode}.*`).test(it.code) ) );//该组织前缀必须在employeeGroup里找到，即有对应权限
      result.forEach(v=>v.employees=v.employees.groupV2);
      // result.unshift({ "name": "默认组", "_id": null });
      debug(method, '[Exit](success)',result);
      this.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}



module.exports = Handler;
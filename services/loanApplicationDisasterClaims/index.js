'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:index');
// const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const loanApplicationDisasterClaimsListHandler = require("./loanApplicationDisasterClaimsListHandler");
const loanApplicationDisasterClaimsFormatHandler = require('./loanApplicationDisasterClaimsFormatHandler');
const loanApplicationDisasterClaimsCreateOrEditHandler = require('./loanApplicationDisasterClaimsCreateOrEditHandler');

const {addHandlersForService} = require('../../utils/general')


class Service {
  constructor() {
    addHandlersForService.call(this,debug);
  }

  createOrEdit(){
    return [loanApplicationDisasterClaimsCreateOrEditHandler]
  }

  list(){
    return [loanApplicationDisasterClaimsListHandler,loanApplicationDisasterClaimsFormatHandler]
  }


}

module.exports = new Service();
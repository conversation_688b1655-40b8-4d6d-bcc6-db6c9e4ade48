'use strict';

const HANDLER_NAME = 'LoanApplicationFundReceiveListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    disasterClaims:disasterClaimsData,
    infoCollectHistory:infoCollectHistoryData,
    employeeGroups: employeeGroupData,
} = require('../dataSvc/dataUtil');
const {assert,parseEmployee} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');
const { getEmployeeLimit } = require('../../utils/getUserFromReq')
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:condition,opts,opts:{getOne,uId}} = this.context;
        // const {} = condition;
        // assert(uId,'E_APP_FUND_RECEIVE_001','uId is required');
        debug(`${HANDLER_NAME}findAreaLimitAreaPara`,opts.uId || 'null', opts.tId  || null,opts.roleId||'null');
        const areaCodeLimitOr = opts.uId && opts.tId && await getEmployeeLimit(employeeGroupData, opts.tId, opts.uId, 'requestAreaCode', opts.roleId);
        opts.uId && (condition['$or'] = areaCodeLimitOr);
        debug(`${HANDLER_NAME}findAreaLimitAreaCode`,condition['$or'] || 'null');

        const { roleName , orgId , orgCode } = await parseEmployee(opts);
        // const childrenOrgId = ( await groupV2Data.getByCondition({ tId:opts.tId,archived:false, code:{ '$regex': `^${orgCode}`, '$options': 'si' }, limit:'unlimited'}) ).map(v=>v._id) ;//拿到子孙县域
        //物权总公司特别，单独在列
        // orgId !== '60111486fda3812feb51fd00' && ( condition["orgInfo.orgId"] = { $in:childrenOrgId } );
        orgId !== '60111486fda3812feb51fd00' && ( condition["orgInfo.orgId"] = orgId );
        debug(`${HANDLER_NAME}Query`, JSON.stringify(opts), JSON.stringify(condition));

        const result = await disasterClaimsData.getListAndCountByCondition(condition),list = result.result;

        const formatOpts = {}
        this.context.opts.formatList = list;
        this.context.result = result;

        assert(!getOne || list.length ,'E_FUND_RECEIVE_DETAIL_001','record of this id not found')
        getOne && ( this.context.result = list[0] );
        // this.context.result = this.context.result || {list,uId,username};
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
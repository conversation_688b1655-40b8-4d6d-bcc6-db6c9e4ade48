'use strict';

const HANDLER_NAME = 'loanApplicationDisasterClaimsCreateOrEditHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    disasterClaims:disasterClaimsData,
    infoCollectHistory:infoCollectHistoryData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:one,opts:{uId:operator}} = this.context;
        const app = await loanApplicationData.getById(one.aId);
        assert(app,'E_ClaimsCreate_01a','订单不存在');
        assert(app.status !== 'finished_loan','E_ClaimsCreate_01b','订单已封存');
        const history = await infoCollectHistoryData.getById(app.addons.info.mId);
        const {basic={},companyBasic} = history && history.content || {};
        const addonsInfo = {
            requestAreaCode:app.area,requestUniqueId:history.uniqueId,
            requestName:basic.name,requestMobile:basic.mobile,requestType:history.type,
        };
        Object.assign(one,addonsInfo);
        Object.assign(one,{operator,lastModTime:new Date()});
        one._id || ( one.createdTime = new Date());
        one.orgInfo = app.orgInfo;// 协调灾害理赔增加县域
        one.username = app.username;

        one._id && Object.assign(one,await disasterClaimsData.putById(one._id,one));
        one._id || Object.assign(one,await disasterClaimsData.post(one));

        this.context.result = {success:'ok',one};
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
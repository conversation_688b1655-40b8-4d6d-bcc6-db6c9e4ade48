/**
 * permission svc entry
 * <AUTHOR>
 */

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:services:permission')

const PERMISSION_ROLE = {
  EVERYONE: 'everyone',
  AUTHENTICATED_USER: 'authenticated_user'
}

const GROUP_ROLE = {
  SYSTEMADMIN: 'Administrator',
  BUSINESSOPERATOR: 'BusinessOperator',
  ACCOUNTMANAGER: 'BankAccountManager',
  ACCOUNTMANAGERLEADER: 'RegionalManager',
  ACCOUNTANT: 'Accountant',
  BUSINESSMANAGEMENT: 'AccountManager',
  BANKREGIONALMANAGER: 'BankRegionalManager',
  PARTNEROPERATOR: 'PartnerOperator',
  FINALAPPROVER: "FinalApprover",
  REGIONALAPPROVER: "RegionalApprover",
  RISKAPPROVER: "RiskApprover",
  LECTURE<PERSON>: "Lecturer",
  MERCHANTOPERATOR: "MerchantOperator",
  COUNTYASSESSOR: "CountyAssessor"
};

const APPROVE_ROLE = {
  STATIONMASTER: "StationMaster", //站长
  ACCOUNTMANAGER: "AccountManager", //客户经理
  BUSINESSSUPERVISION: "BusinessSupervision", //业务督导
  HEADQUARTERSSUPERVISION: "HeadquartersSupervision", //总部监管

};


class Permission {
  constructor() {}

  async assert(user, role) {
    let self = this
    let method = 'assert'
    debug(method, '[Enter]')

    try {
      // default authenticated_user 
      let assert_role = role || PERMISSION_ROLE.AUTHENTICATED_USER

      if (assert_role === PERMISSION_ROLE.EVERYONE) {
        debug(method, '[Exit](success)')
        return
      }

      if ((!user || !user.userid) && assert_role === PERMISSION_ROLE.AUTHENTICATED_USER)
        throw {
          httpCode: 401,
          errorCode: 'EAUTH_PERMISSION',
          reason: '用户未授权'
        }

      debug(method, '[Exit](success)')
      return
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }
}

module.exports = {
  Permission: new Permission(),
  PERMISSION_ROLE: PERMISSION_ROLE,
  GROUP_ROLE: GROUP_ROLE,
  APPROVE_ROLE: APPROVE_ROLE
}
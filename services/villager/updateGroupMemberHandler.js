'use strict';

const HANDLER_NAME = 'GetGroupListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:services:villager:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const govGroupData = require('../dataSvc/dataUtil').govGroup;
const formatAreaCode = require('../../persistence/formatAreaCode');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;

      let payload = {};
      if (opts.action == "add") {
        payload = { $addToSet: { member: { $each: condition.member } } };
      } else {
        payload = { $pull: { member: { $in: condition.member } } }
      }
      let result = await govGroupData.putById(opts.id, payload);
      if (!result || !result._id) {
        throw {
          errorCode: 'E_VILLAGER_SVC_250',
          httpCode: 406,
          reason: '更新分组失败！'
        };
      }

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'GetGroupDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:services:villager:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const govVillagerData = require('../dataSvc/dataUtil').govVillager;
const govGroupData = require('../dataSvc/dataUtil').govGroup;
const formatAreaCode = require('../../persistence/formatAreaCode');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input;
      let result = await govGroupData.getById(id);
      if (!result) {
        self.context.result = {};
        debug(method, '[Exit](success)');
        return done();
      }
      result.member = result.member || [];
      result.memberInfo = [];
      for (let villagerId of result.member) {
        let villager = await govVillagerData.getById(villagerId);
        let obj = {
          username: villager.username,
          _id: villager._id
        }
        result.memberInfo.push(obj);
      }
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
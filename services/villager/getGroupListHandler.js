'use strict';

const HANDLER_NAME = 'GetGroupListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:services:villager:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const govGroupData = require('../dataSvc/dataUtil').govGroup;
const formatAreaCode = require('../../persistence/formatAreaCode');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;
      let areaList = condition.areaList;
      let result = [];
      for (let code of areaList) {
        let obj = {};
        let group = await govGroupData.getByCondition({ areaCode: `/^${code}/` });
        obj.code = code;
        obj.group = group || [];
        let areaData = await formatAreaCode.getFormatAreaCode(code);
        obj.region = areaData && areaData.region || {};
        obj.location = areaData && areaData.area || "";
        result.push(obj);
      }

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
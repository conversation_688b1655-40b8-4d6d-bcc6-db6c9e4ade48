'use strict';

const HANDLER_NAME = 'getVillagerListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:services:villager:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const govVillagerData = require('../dataSvc/dataUtil').govVillager;
const govGroupData = require('../dataSvc/dataUtil').govGroup;
const formatAreaCode = require('../../persistence/formatAreaCode');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;
      let areaList = condition.areaList;
      let result = [];
      if (areaList.length == 1 && areaList[0].length > 9) {
        let query = {
          areaCode: `/^${areaList[0]}/`,
          archived: false,
          skip: condition.skip,
          limit: condition.limit,
          $sort: condition.$sort
        }
        if (condition.username) {
          query.username = `/${condition.username}/`
        }

        result = await govVillagerData.getByCondition(query);
        for (let villager of result) {
          let group = await govGroupData.getByCondition({
            member: villager._id,
            archived: false
          });
          villager.group = group.map(item => item.name);
        }

      } else {
        let codeLength = 0;
        if (areaList.length == 1) {
          if (areaList[0].length > 6) {
            codeLength = 12;
          } else if (areaList[0].length > 4) {
            codeLength = 9;
          } else if (areaList[0].length > 2) {
            codeLength = 6;
          } else if (areaList[0].length > 0) {
            codeLength = 4;
          }
        } else if (areaList.length > 1) {
          codeLength = areaList[0].length;
        }

        let inCode = areaList.map(code => ({ areaCode: { '$regex': `^${code}` } }));
        let payload = [
          { $match: { $or: inCode } },
          {
            $project: {
              _id: 0,
              parentCode: { $substr: ["$areaCode", 0, codeLength] }
            }
          },
          {
            $group: {
              _id: "$parentCode",
              areaCode: { $first: "$parentCode" },
              total: { $sum: 1 }
            }
          }
        ]
        let data = await govVillagerData.postByUrl("/api/v1.0/govVillager/aggregate", payload);
        if (data.ok) {
          result = data.ok;
        }
        for (let item of result) {
          let areaData = await formatAreaCode.getFormatAreaCode(item.areaCode);
          item.region = areaData && areaData.region || {};
          item.location = areaData && areaData.area || "";
        }
      }

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
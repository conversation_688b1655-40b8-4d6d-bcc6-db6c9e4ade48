'use strict';

const HANDLER_NAME = 'GetVillagerDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:services:villager:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const govVillagerData = require('../dataSvc/dataUtil').govVillager;
const govGroupData = require('../dataSvc/dataUtil').govGroup;
const formatAreaCode = require('../../persistence/formatAreaCode');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input;
      let result = await govVillagerData.getById(id);
      if (!result) {
        throw {
          errorCode: 'E_INS_DETAIL_029',
          httpCode: 406,
          reason: '无效的村民id'
        }
      }
      let areaData = await formatAreaCode.getFormatAreaCode(result.areaCode);
      result.region = areaData && areaData.region || {};
      result.location = areaData && areaData.area || "";
      let group = await govGroupData.getByCondition({ member: id, archived: false, limit: "unlimited" });
      if (group && group[0]) {
        result.group = group.map(item => item.name);
      } else {
        result.group = [];
      }
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
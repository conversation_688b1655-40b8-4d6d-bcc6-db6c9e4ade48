'use strict';

const HANDLER_NAME = 'sendSMSHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const smsYunZhiXunDispatcher = require('./../../messages/dispatchers/smsYunZhiXunDispatcher');
const smsTianYanDispatcher = require('./../../messages/dispatchers/smsTianYanDispatcher');
const MsgSvcRegistry = require('nongfu.merchant.msgfw').MsgSvcRegistry.INSTANCE;
const employeeGroupsData = require('../../dataSvc/dataUtil').employeeGroups;
const employeeData = require('../../dataSvc/dataUtil').employees;
const loanDistributeData = require('../../dataSvc/dataUtil').loanDistribute;
const templateFiltrate = "547422"
const templateDispose = "547424"

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let result = self.context.result
      let opts = self.context.opts;
      let loanInfo = opts.loanInfo;
      debug(method, '[loanInfo]', loanInfo)
      if (result.success === false || !loanInfo) {
        debug(method, '[Exit](success)');
        return done();
      }

      if (loanInfo.status != "wait_filtrate" && loanInfo.status != "wait_dispose") {
        debug(method, '[Exit](success)');
        return done();
      }
      let employeeList = [];
      let templateId = templateFiltrate;
      if (loanInfo.status == "wait_filtrate") {
        let group = "5eb8fefdc6ecfe44d4ecaed4"
        templateId = templateFiltrate;
        let employeeGroupList = await employeeGroupsData.getByCondition({
          areaList: loanInfo.area,
          archived: false,
          limit: 'unlimited',
          group: group
        });

        if (employeeGroupList.length === 0) {
          if (loanInfo.area.length == 12) {
            employeeGroupList = await employeeGroupsData.getByCondition({
              areaList: loanInfo.area.substr(0, 9),
              archived: false,
              limit: 'unlimited',
              group: group
            });
          }

          if (loanInfo.area.length == 9) {
            employeeGroupList = await employeeGroupsData.getByCondition({
              areaList: loanInfo.area.substr(0, 6),
              archived: false,
              limit: 'unlimited',
              group: group
            });
          }
        }
        let employee = [];
        employeeGroupList.forEach(data => {
          employee.push(data.employee);
        })
        if (employee.length <= 0) {
          return
        }
        employeeList = await employeeData.getByCondition({
          _id: {
            $in: employee
          },
          isRevoked: false,
          archived: false
        });

      }

      if (loanInfo.status == "wait_dispose") {
        templateId = templateDispose;

        let distribute = await loanDistributeData.getOneByCondition({
          aId: loanInfo._id,
          assignStatus: "destined"
        });
        if (distribute) {
          let employee = await employeeData.getById(distribute.assigned, {cache : true , expire: 24 * 60 * 60 });

          if (employee) {
            employeeList.push(employee);
          }
        }

      }

      let SmsTianYanDispatcher = MsgSvcRegistry.getDisptcher(smsTianYanDispatcher.QNAME);


      let content = {
        templateid: "MABF0D3185",
        param: "",
        caller: "rongxin_mgr"
      };
      if (templateId === templateFiltrate) {
        content.param = "筛选";
      }
      if (templateId === templateDispose) {
        content.param = "处理";
      }

      let promises = []
      for (const item of employeeList) {
        if (item.mobile) {
          promises.push(SmsTianYanDispatcher.send_Sms(item.mobile, content, {}))
        }
      }
      await Promise.all(promises);

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
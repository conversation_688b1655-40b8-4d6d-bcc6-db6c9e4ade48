'use strict';

const HANDLER_NAME = 'rejectLoanApplicationHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanTrackingData = require('../../dataSvc/dataUtil').loanApplicationTracking;
const loanDistributeData = require('../../dataSvc/dataUtil').loanDistribute;
const requestLimit = require('../../../persistence/requestLimit');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let opts = self.context.opts;
      let input = self.context.input;

      let application = await loanApplicationData.getById(input.id);
      if (!application) {
        await requestLimit.unlock(input.id);
        throw {
          errorCode: 'E_APP_PRECENSOR_037',
          httpCode: 406,
          reason: '订单不存在'
        };
      }

      let trackingPayload = {
        src_t: "staff",
        "target_t": 1,
        "target": input.id,
        "source": opts.user.userid,
        "comments": input.comments
      };
      let putPayload = {
        destined: false,
        lastModTime: new Date()
      }

      let action = "";
      if (application.status === "pre_censor") {
        // 初审通过到待筛选
        trackingPayload.action = "rejected_censor"
        putPayload.status = "rejected_censor";
        putPayload.amount = input.body.amount;
        action = "pre_censor_destined"
      }

      if (application.status === "wait_filtrate") {
        // 待筛选通过到待处理
        trackingPayload.action = "rejected_filtrate"
        putPayload.status = "rejected_filtrate";
        putPayload.amount = input.body.amount;
        action = "destined"
      }
      if (application.status === "wait_interview") {
        // 面审拒绝
        trackingPayload.action = "rejected_interview"
        putPayload.status = "rejected_interview";
        putPayload.amount = input.body.amount;
        action = "destined"
      }
      if (application.status === "wait_dispose") {
        // 待处理  =》已放款
        trackingPayload.action = "rejected_loan"
        putPayload.status = "rejected_loan";
        action = "destined"
      }
      let loanDistribute = await loanDistributeData.getOneByCondition({
        aId: input.id,
        archived: false,
        assigned: opts.user.userid,
        assignStatus: action
      });
      if (putPayload.status != opts.status) {
        await requestLimit.unlock(input.id);
        throw {
          errorCode: 'E_LOAN_STATUS_CHANGE_037',
          httpCode: 406,
          reason: '当前订单已被处理，请处理其他订单'
        }
      }
      let result = await loanApplicationData.putById(input.id, putPayload);
      let tracking = await loanTrackingData.post(trackingPayload);
      await requestLimit.unlock(input.id);
      if (loanDistribute) {
        await loanDistributeData.putById(loanDistribute._id, {
          handleStatus: true
        })
      }
      if (!result || !tracking) {
        throw {
          errorCode: 'E_LOAN_ADDONS_107',
          httpCode: 406,
          reason: '处理出错'
        }
      }
      self.context.result = {
        success: true
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
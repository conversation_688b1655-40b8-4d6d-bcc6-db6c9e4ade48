/*
 * @Author: qays
 * @Date: 2021-08-23 17:47:11
 * @LastEditTime: 2022-11-29 14:12:17
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.mgr.app.api\services\loan_application_v2\pre_censor\userEleVerifyHandler.js
 */

'use strict';

const HANDLER_NAME = 'UserElementsVerifyHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const aliyunAuthSvc = require('../../aliyunAuth');
const moment = require('moment');
const qs = require('querystring');

const IDENTIFIED_STATUS_APPROVED = 'approved';
const IDENTIFIED_STATUS_REJECTED = 'rejected';


const LOAN_ORDER_REJECT_REASON = 'rejected_new_1';
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      
      if( input.status=='credit_access' && input.realname && input.bankMobile && input.bankCard && input.IDCard){
        
        debug(method, '[aliyunAuthSvc]input(success)', input)
        // auth identity via third part svc
        let authRes = await aliyunAuthSvc.bankcard234(input.realname, input.bankMobile, input.bankCard, input.IDCard);//{body:{resp:{code:0}},receipt:{}};//
        debug(method, '[aliyunAuthSvc]Res(success)', authRes)
        if (!authRes || !authRes.body || !authRes.receipt) {
          throw {
            errorCode: 'E_USER_IDENTIFY_SVC_052',
            httpCode: 406,
            reason: 'Failed to Auth'
          };
        }
        if (authRes && authRes.body && authRes.body.data) {
          authRes.body.resp = { res: authRes.body.data.msg, code:authRes.body.data.result, desc: authRes.body.data.desc }
        }

        if (authRes.body.resp.code !== 0) {
          throw {
            errorCode: 'E_UserEleVerify_054',
            httpCode: 406,
            reason: authRes.body.resp.desc
          }
        }

        let authStatus = authRes.body.resp.code === 0 ? IDENTIFIED_STATUS_APPROVED : IDENTIFIED_STATUS_REJECTED;

        
        self.context.result.authStatus = authStatus;

      }
      debug(method, '[Exit](success)',self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
'use strict';

const HANDLER_NAME = 'approvedLoanPreCensorHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanTrackingData = require('../../dataSvc/dataUtil').loanApplicationTracking;
const loanDistributeData = require('../../dataSvc/dataUtil').loanDistribute;
const requestLimit = require('../../../persistence/requestLimit');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let opts = self.context.opts;
      let input = self.context.input;

      let application = await loanApplicationData.getById(input.id);

      if (!application) {
        await requestLimit.unlock(input.id);
        throw {
          errorCode: 'E_APP_PRECENSOR_037',
          httpCode: 406,
          reason: '订单不存在'
        };
      }
      if (application.status === "pre_censor" && (!application.addons || !application.addons.info )) {
        await requestLimit.unlock(input.id);
        throw {
          errorCode: 'E_LOAN_ADDONS_037',
          httpCode: 406,
          reason: '不合法的订单'
        }
      }
      let oldPayload = {};
      // 初审判断个人在途订单
      if (application.status === "pre_censor" && application.addons.info.type === "personal") {
        let mId = application.addons && application.addons.info && application.addons.info.mId || "";
        if (!mId) {
          await requestLimit.unlock(input.id);
          throw {
            errorCode: 'E_LOAN_ADDONS_MID_037',
            httpCode: 406,
            reason: '不合法的订单'
          }
        }
        oldPayload = {
          "_id": {
            $ne: input.id
          },
          tId: application.tId,
          "addons.info.mId": mId,
          status: {
            $in: ["pre_censor", "wait_filtrate", "wait_dispose"]
          }
        }
      }
      // 初审判断企业在途订单
      if (application.status === "pre_censor" && application.addons.info.type === "enterprise") {
        let socialCode = application && application.socialCode;
        if (!socialCode) {
          await requestLimit.unlock(input.id);
          throw {
            errorCode: 'E_LOAN_ADDONS_MID_037',
            httpCode: 406,
            reason: '不合法的订单'
          }
        }
        oldPayload = {
          "_id": {
            $ne: input.id
          },
          tId: application.tId,
          "socialCode": socialCode,
          status: {
            $in: ["pre_censor", "wait_filtrate", "wait_dispose"]
          }
        };
      }
      if (application.status === "pre_censor") {
        let oldLoanInfo = await loanApplicationData.getByCondition(oldPayload);

        if (oldLoanInfo && oldLoanInfo.length > 0) {
          let snList = [];
          for (const item of oldLoanInfo) {
            snList.push(item.sn);
          }
          await requestLimit.unlock(input.id);
          // throw {
          //   errorCode: 'E_LOAN_ADDONS_062',
          //   httpCode: 406,
          //   reason: `已有在途订单${oldLoanInfo.length}笔,订单编号:${snList.join("-")}`
          // }
          self.context.result = {
            success: false,
            loanId: snList.join("-"),
            loanNum: oldLoanInfo.length
          }
          debug(method, '[Exit](success)', self.context.result);
          return done();
        }

      }

      let trackingPayload = {
        src_t: "staff",
        "target_t": 1,
        "target": input.id,
        "source": opts.user.userid,
        "comments": input.comments
      };
      let putPayload = {
        lastModTime: new Date()
      }
      let handleStatus = false;
      let action = "";
      if (application.status === "pre_censor") {
        // 初审通过到待筛选
        trackingPayload.action = "wait_filtrate"
        trackingPayload.parameters = {
          approveAmount: input.body.amount
        }
        putPayload.status = "wait_filtrate";
        putPayload.destined = false;
        putPayload.approveAmount = input.body.amount;
        handleStatus = true;
        action = "pre_censor_destined"
      }

      if (application.status === "wait_filtrate" && application.tId !== '5fc44d27b27399c02a0da662') {
        // 待筛选通过到待处理
        //5fc44d27b27399c02a0da662 雄安流程审批单独过授权签约
        trackingPayload.action = "wait_dispose"
        trackingPayload.parameters = {
          approveAmount: input.body.amount
        }
        putPayload.status = "wait_dispose";
        putPayload.approveAmount = input.body.amount;
        
        //待办变更为已办
        handleStatus = true;
      }
      if (application.status === "wait_filtrate" && application.tId === '5fc44d27b27399c02a0da662') {
        //5fc44d27b27399c02a0da662 雄安流程审批进入面审
        // handleStatus = true;
        // action = "destined"
        trackingPayload.action = "wait_interview"
        trackingPayload.parameters = {
          approveAmount: input.body.amount
        }
        putPayload.status = "wait_interview";
        putPayload.approveAmount = input.body.amount;
      }
      if (application.status === "wait_interview" && application.tId === '5fc44d27b27399c02a0da662') {
        //5fc44d27b27399c02a0da662 雄安流程面审通过进授权签约
        handleStatus = true;
        action = "destined"
        trackingPayload.action = "credit_access"
        trackingPayload.parameters = {
          approveAmount: input.body.amount
        }
        putPayload.status = "credit_access";
        putPayload.bankCard = input.bankCard;
        putPayload['addons.bankMobile'] = input.bankMobile;
        putPayload.approveAmount = input.body.amount;
      }
      if (application.status === "wait_dispose") {
        // 待处理  =》已放款
        trackingPayload.action = "loaned"
        trackingPayload.parameters = input.loaned;
        putPayload.status = "loaned";
        putPayload.actualLoan = input.loaned.actualLoan;
        handleStatus = true;
        action = "destined"
      }
      let loanDistribute = await loanDistributeData.getOneByCondition({
        aId: input.id,
        archived: false,
        assigned: opts.user.userid,
        assignStatus: action
      });
      if (putPayload.status !== input.status) {
        await requestLimit.unlock(input.id);
        throw {
          errorCode: 'E_LOAN_STATUS_CHANGE_037',
          httpCode: 406,
          reason: '当前订单已被处理，请处理其他订单'
        }
      }
      let result = await loanApplicationData.putById(input.id, putPayload);
      let tracking = await loanTrackingData.post(trackingPayload);
      await requestLimit.unlock(input.id);
      if (!result || !tracking) {
        throw {
          errorCode: 'E_LOAN_ADDONS_107',
          httpCode: 406,
          reason: '处理出错'
        }
      }
      if (loanDistribute && loanDistribute._id) {
        await loanDistributeData.putById(loanDistribute._id, {
          handleStatus: handleStatus
        })
      }
      self.context.opts.loanInfo = result;
      self.context.result = {
        success: true
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
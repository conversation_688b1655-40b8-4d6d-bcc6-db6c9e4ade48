/**
 * @summary loanAppWorkflows
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'loanAppWorkflows';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseWorkflow = require('../../baseWorkFlow');


const LOAN_APP_STATUS = {
  LOAN_APP_CERTIFIED: 'certified',
  LOAN_APP_BIOPSY_APPROVED: 'biopsy_approved',
  LOAN_APP_PRECENSOR: 'pre_censor',
  LOAN_APP_WAIT_INTERVIEW: "wait_interview",
  LOAN_APP_REVIEW: 'review',
  LOAN_APP_UNPAID: 'unpaid',
  LOAN_APP_WAITLOAN: 'waitLoan',
  LOAN_APP_WAITSIGN: 'waitSign',
  LOAN_APP_CONTRACT_REVIEW: 'contract_review',
  LOAN_APP_LOANED: 'loaned',
  LOAN_APP_NEW: 'new',
  LOAN_APP_REJECTED_CENSOR: 'rejected_censor',
  LOAN_APP_DECLINE_CENSOR: 'decline_censor',
  LOAN_APP_REJECTED_NEW_1: 'rejected_new_1',
  LOAN_APP_REJECTED_NEW_2: 'rejected_new_2',
  LOAN_APP_REJECTED_NEW_3: 'rejected_new_3',
  LOAN_APP_REJECTED_REVIEW: 'rejected_review',
  LOAN_APP_REJECTED_LOAN: 'rejected_loan',
  LOAN_APP_REJECTED_CONTRACT: 'rejected_contract',
  LOAN_APP_FINAL_REVIEW: 'final_review',
  LOAN_APP_REJECTED_ESIGN: 'rejected_eSign',
  LOAN_APP_REJECTED_FINAL: 'rejected_final',
  LOAN_APP_PRE_APPROVE: 'pre_approve'
};

const LOAN_APP_TRACKING_ACTIONS = {
  CERTIFIED: "certified",
  PRE_CENSOR: "pre_censor",
  WAIT_INTERVIEW: "wait_interview",
  REVIEW: "review",
  REJECTED_NEW_1: "rejected_new_1",
  REJECTED_NEW_2: "rejected_new_2",
  REJECTED_NEW_3: "rejected_new_3",
  REJECTED_NEW_4: "rejected_new_4",
  REJECTED_CERT: "rejected_cert",
  REJECTED_CENSOR: "rejected_censor",
  REJECTED_REVIEW: "rejected_review",
  REJECTED_FINAL: "rejected_final",
  DECLINE_CENSOR: "decline_censor",
  PRE_REVIEW: "pre_review",
  CB_NOTIFY: "cb_notify"
};

const LOAN_APP_WORKFLOW_PIPELINE = [
  //pre_censor => review
  {
    from: LOAN_APP_STATUS.LOAN_APP_PRECENSOR,
    to: LOAN_APP_STATUS.LOAN_APP_REVIEW,
    action: LOAN_APP_STATUS.LOAN_APP_REVIEW,
    permittedRole: []
  },
  //pre_censor => wait_interview
  {
    from: LOAN_APP_STATUS.LOAN_APP_PRECENSOR,
    to: LOAN_APP_STATUS.LOAN_APP_WAIT_INTERVIEW,
    action: LOAN_APP_STATUS.LOAN_APP_WAIT_INTERVIEW,
    permittedRole: []
  },
  // new -> rejected_censor
  {
    from: LOAN_APP_STATUS.LOAN_APP_NEW,
    to: LOAN_APP_STATUS.LOAN_APP_REJECTED_CENSOR,
    action: LOAN_APP_STATUS.LOAN_APP_REJECTED_CENSOR,
    permittedRole: []
  },
  // pre_censor -> rejected_censor
  {
    from: LOAN_APP_STATUS.LOAN_APP_PRECENSOR,
    to: LOAN_APP_STATUS.LOAN_APP_REJECTED_CENSOR,
    action: LOAN_APP_STATUS.LOAN_APP_REJECTED_CENSOR,
    permittedRole: []
  },
  // pre_censor -> decline_censor
  {
    from: LOAN_APP_STATUS.LOAN_APP_PRECENSOR,
    to: LOAN_APP_STATUS.LOAN_APP_DECLINE_CENSOR,
    action: LOAN_APP_STATUS.LOAN_APP_DECLINE_CENSOR,
    permittedRole: []
  },
  // pre_approve -> rejected_censor
  {
    from: LOAN_APP_STATUS.LOAN_APP_PRE_APPROVE,
    to: LOAN_APP_STATUS.LOAN_APP_REJECTED_CENSOR,
    action: LOAN_APP_STATUS.LOAN_APP_REJECTED_CENSOR,
    permittedRole: []
  },
  // pre_approve -> decline_censor
  {
    from: LOAN_APP_STATUS.LOAN_APP_PRE_APPROVE,
    to: LOAN_APP_STATUS.LOAN_APP_DECLINE_CENSOR,
    action: LOAN_APP_STATUS.LOAN_APP_DECLINE_CENSOR,
    permittedRole: []
  },
  // pre_approve -> pre_censor
  {
    from: LOAN_APP_STATUS.LOAN_APP_PRE_APPROVE,
    to: LOAN_APP_STATUS.LOAN_APP_PRECENSOR,
    action: LOAN_APP_STATUS.LOAN_APP_PRECENSOR,
    permittedRole: []
  },
  // contract_review => final_review
  {
    from: LOAN_APP_STATUS.LOAN_APP_CONTRACT_REVIEW,
    to: LOAN_APP_STATUS.LOAN_APP_FINAL_REVIEW,
    action: LOAN_APP_STATUS.LOAN_APP_FINAL_REVIEW,
    permittedRole: []
  },
  // contract_review => rejected_contract
  {
    from: LOAN_APP_STATUS.LOAN_APP_CONTRACT_REVIEW,
    to: LOAN_APP_STATUS.LOAN_APP_REJECTED_CONTRACT,
    action: LOAN_APP_STATUS.LOAN_APP_REJECTED_CONTRACT,
    permittedRole: []
  },
  // waitLoan => loaned
  {
    from: LOAN_APP_STATUS.LOAN_APP_WAITLOAN,
    to: LOAN_APP_STATUS.LOAN_APP_LOANED,
    action: LOAN_APP_STATUS.LOAN_APP_LOANED,
    permittedRole: []
  },
  // review => rejected_review
  {
    from: LOAN_APP_STATUS.LOAN_APP_REVIEW,
    to: LOAN_APP_STATUS.LOAN_APP_REJECTED_REVIEW,
    action: LOAN_APP_STATUS.LOAN_APP_REJECTED_REVIEW,
    permittedRole: []
  },
  // waitLoan => rejected_loan
  {
    from: LOAN_APP_STATUS.LOAN_APP_WAITLOAN,
    to: LOAN_APP_STATUS.LOAN_APP_REJECTED_LOAN,
    action: LOAN_APP_STATUS.LOAN_APP_REJECTED_LOAN,
    permittedRole: []
  }
]

const VALID_STATUS = [
  LOAN_APP_STATUS.LOAN_APP_PRECENSOR,
  LOAN_APP_STATUS.LOAN_APP_WAIT_INTERVIEW,
  LOAN_APP_STATUS.LOAN_APP_REVIEW,
  LOAN_APP_STATUS.LOAN_APP_NEW,
  LOAN_APP_STATUS.LOAN_APP_WAITLOAN,
  LOAN_APP_STATUS.LOAN_APP_LOANED,
  LOAN_APP_STATUS.LOAN_APP_REJECTED_CENSOR,
  LOAN_APP_STATUS.LOAN_APP_REJECTED_REVIEW,
  LOAN_APP_STATUS.LOAN_APP_REJECTED_LOAN,
  LOAN_APP_STATUS.LOAN_APP_DECLINE_CENSOR,
  LOAN_APP_STATUS.LOAN_APP_CERTIFIED,
  LOAN_APP_STATUS.LOAN_APP_WAITSIGN,
  LOAN_APP_STATUS.LOAN_APP_BIOPSY_APPROVED,
  LOAN_APP_STATUS.LOAN_APP_CONTRACT_REVIEW,
  LOAN_APP_STATUS.LOAN_APP_REJECTED_CONTRACT,
  LOAN_APP_STATUS.LOAN_APP_UNPAID,
  LOAN_APP_STATUS.LOAN_APP_FINAL_REVIEW,
  LOAN_APP_STATUS.LOAN_APP_REJECTED_FINAL,
  LOAN_APP_STATUS.LOAN_APP_PRE_APPROVE
];

class LoanAppWorkflow extends BaseWorkflow {
  constructor(allStates, workflowPipeline) {
    super(allStates, workflowPipeline);
  }

  /**
   * @Overwrite
   * validate whether the caller has permission to do the action
   * @param {*} caller 
   * @param {*} orderOwner 
   * @param {*} callerRole 
   * @param {*} workflowNode 
   */
  validatePermission(caller, orderOwner, callerRole, workflowNode) {
    let method = 'validatePermission';

    if (!caller) {
      debug(method, '[Exit](error)', 'invalid params');
      return false;
    }

    let result = false;

    workflowNode.permittedRole.some((role) => {
      return true;
    });

    debug(method, '[Exit](success)', result);
    return result;
  }
};

module.exports = {
  LOAN_APP_STATUS,
  LOAN_APP_TRACKING_ACTIONS,
  loanAppWorkflow: new LoanAppWorkflow(VALID_STATUS, LOAN_APP_WORKFLOW_PIPELINE)
};
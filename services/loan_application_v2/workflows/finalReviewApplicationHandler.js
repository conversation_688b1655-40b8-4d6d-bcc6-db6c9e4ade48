/**
 * @summary finalReviewApplicationHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'finalReviewApplicationHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:application_v2:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const config = require('config');
const sm4 = require('sm-crypto').sm4;
const sm4Key = config.get("rongxin_api_usercredit.sm4key");
const superagent = require('superagent');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    let id = self.context.input.aId;
    debug(method, '[Enter]')
    try {

      let opts = self.context.opts;
      let condition = self.context.input;
      let plaintext = { user: opts.user, body: condition }
      const encrypted = sm4.encrypt(JSON.stringify(plaintext), sm4Key);
      let ploayLoad = { cipherText: encrypted }
      // 调用dashboard.api  审批
      let server = `http://${config.get('rongxin_dashboardService.host')}:${config.get('rongxin_dashboardService.port')}/api/v3.0/loan/application/mgr/final/review`;
      const res = await superagent.put(server).send(ploayLoad);
      if (res && res.body) {
        self.context.result = res.body || res.text;
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      let err = error.response && error.response.Response && error.response.Response.res && error.response.Response.res.text || {
        httpCode: 406,
        errorCode: 'E_FINALREVIEW_D_051',
        reason: '服务异常，请稍后重试'
      }
      err = JSON.parse(err)
      debug.error(method, '[Exit](failed)', err);
      return done(err);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
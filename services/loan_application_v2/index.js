'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const registry = require('./register');
const PreDestineLoanAppHandler = require('./baseOperations/preDestineLoanAppHandler')
const DestineLoanAppHandler = require('./baseOperations/destineLoanAppHandler')
const LockLoanApplicationHandler = require('./baseOperations/lockLoanApplicationHandler')
const CancelDestineLoanApplicationHandler = require('./baseOperations/cancelDestineLoanApplicationHandler')
const GetLoanApplicationDetailHandler = require('./baseOperations/getLoanApplicationDetailHandler');
const PreGetLoanAppListHander = require('./baseOperations/preGetLoanAppListHander');
const GetLoanApplicationsHandler = require('./baseOperations/getLoanApplicationsHandler');
const FormatApplicatEnumHandler = require('./baseOperations/formatApplicatEnumHandler');
const UpdateLoanApplicationsHandler = require('./baseOperations/updateLoanApplicationsHandler');
const FormatAreaCodeHandler = require('./baseOperations/formatAreaCodeHandler');
const GetPresentOperatorHandler = require('./baseOperations/getPresentOperatorHandler');
const GetLoanApplicationDestinedHandler = require('./baseOperations/getLoanApplicationDestinedHandler');
const ApprovedLoanPreCensorHandler = require('./pre_censor/approvedLoanPreCensorHandler');
const UserEleVerifyHandler = require('./pre_censor/userEleVerifyHandler');
const RejectLoanApplicationHandler = require('./pre_censor/rejectLoanApplicationHandler')
const SendSMSHandler = require('./pre_censor/sendSMSHandler')
const GetPersonalBasishandler = require('./baseOperations/getPersonalBasishandler');
const GetLoanApplicationTrackingHandler = require('./baseOperations/getLoanApplicationTrackingHandler');
const FormatLoanAppTrackingOperatorHandler = require('./baseOperations/formatLoanAppTrackingOperatorHandler');
const GetPersonalInfoHandler = require('./baseOperations/getPersonalInfoHandler');
const GetEnterpriseInfoHandler = require('./baseOperations/getEnterpriseInfoHandler');
const GetICxwqSupplementHandler = require('./baseOperations/getICxwqSupplementHandler')
const FormatApplicatVerifyInfoHandler = require('./baseOperations/formatApplicatVerifyInfoHandler')
const GetLoanedApplicationListHandler = require('./baseOperations/getLoanedApplicationListHandler')
const GetLoanedApplicationDetailHandler = require('./baseOperations/getLoanedApplicationDetailHandler')
const GetMxtLoanAppListHandler = require('./baseOperations/getMxtLoanAppListHandler');
const FinalReviewApplicationHandler = require('./workflows/finalReviewApplicationHandler')
const CommentApplicationHandler = require('./baseOperations/commentApplicationHandler')
class Service {
  constructor() {
    this.createApplicationFactory = new Map();
  }



  async destineLoanApplication(input, _opts) {
    let method = 'destineLoanApplication';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new LockLoanApplicationHandler(context));
      svcHandlerMgrt.addHandler(new PreDestineLoanAppHandler(context));
      svcHandlerMgrt.addHandler(new DestineLoanAppHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async cancelDestineLoanApplication(input, _opts) {
    let method = 'cancelDestineLoanApplication';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new CancelDestineLoanApplicationHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async approvedLoanApplication(input, _opts) {
    let method = 'approvedLoanApplication';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new LockLoanApplicationHandler(context));
      svcHandlerMgrt.addHandler(new ApprovedLoanPreCensorHandler(context));
      svcHandlerMgrt.addHandler(new SendSMSHandler(context));
      svcHandlerMgrt.addHandler(new UserEleVerifyHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
  async rejectLoanApplication(input, _opts) {
    let method = 'rejectLoanApplication';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new LockLoanApplicationHandler(context));
      svcHandlerMgrt.addHandler(new RejectLoanApplicationHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLoanApplicationList(input, _opts) {
    let method = 'getLoanApplicationList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new PreGetLoanAppListHander(context));
      svcHandlerMgrt.addHandler(new GetLoanApplicationsHandler(context));
      svcHandlerMgrt.addHandler(new UpdateLoanApplicationsHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatEnumHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatVerifyInfoHandler(context));
      svcHandlerMgrt.addHandler(new GetPresentOperatorHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLoanApplicationDestinedList(input, _opts) {
    let method = 'getLoanApplicationDestinedList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetLoanApplicationDestinedHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatEnumHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLoanApplicationDetail(input, _opts) {
    let method = 'getLoanApplicationDetail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetLoanApplicationDetailHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatEnumHandler(context));
      svcHandlerMgrt.addHandler(new FormatApplicatVerifyInfoHandler(context));
      svcHandlerMgrt.addHandler(new GetPersonalBasishandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLoanApplicationTracking(input, _opts) {
    let method = 'getLoanApplicationTracking';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetLoanApplicationTrackingHandler(context));
      svcHandlerMgrt.addHandler(new FormatLoanAppTrackingOperatorHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLoanApplicationGather(input, _opts) {
    let method = 'getLoanApplicationGather';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetPersonalInfoHandler(context));
      svcHandlerMgrt.addHandler(new GetEnterpriseInfoHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getICxwqSupplement(input, _opts) {
    let method = 'getCxwqSupplement';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetICxwqSupplementHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLoanedApplicationList(input, _opts) {
    let method = 'getLoanedList';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetLoanedApplicationListHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getMxtLoanAppList(input, _opts) {
    let method = 'getMxtLoanAppList';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetMxtLoanAppListHandler(context));
      svcHandlerMgrt.addHandler(new FormatAreaCodeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getLoanedApplicationDetail(input, _opts) {
    let method = 'getLoanedDetail';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetLoanedApplicationDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async finalReviewApplication(input, _opts) {
    let method = 'finalReviewApplication';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new FinalReviewApplicationHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async commentApplication(input, _opts) {
    let method = 'commentApplication';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new CommentApplicationHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

let svc = new Service();
registry(svc);
module.exports = svc;
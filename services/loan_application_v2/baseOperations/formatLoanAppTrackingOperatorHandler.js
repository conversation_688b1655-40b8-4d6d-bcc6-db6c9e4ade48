/**
 * @summary FormatLoanAppTrackingOperatorHandler
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 11:20:03 
 * Last modified  : 2018-12-13 13:21:56
 */

'use strict';

const HANDLER_NAME = 'FormatLoanAppTrackingOperatorHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const dateFormat = require('dateformat');
const dataUtil = require('../../dataSvc/dataUtil');
const employeesData = dataUtil.employees;
const loanApplicationData = dataUtil.loanApplication;
const APPLICATION_STATUS = require('../workflows/loanAppWorkflows').LOAN_APP_STATUS;
const LOAN_TRACK_STATUS_MAP = require('../../../utils/const/applicationConst').LOAN_TRACK_STATUS_MAP;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _result = self.context.result.result || [];

      let application = await loanApplicationData.getById(self.context.input.target);
      if (!application) {
        throw {
          errorCode: 'E_FORMAT_HAO_041',
          httpCode: 406,
          reason: 'application not found'
        }
      }

      let initTracking = {
        target: self.context.input.target,
        action: APPLICATION_STATUS.LOAN_APP_NEW,
        statusChName: LOAN_TRACK_STATUS_MAP.get(APPLICATION_STATUS.LOAN_APP_NEW),
        src_t: "user",
        source: application.uId,
        createdTime: application.createdTime
      };
      _result.splice(_result.length, 0, initTracking);

      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }

      let promises = [];
      let time_offset = Math.abs(new Date().getTimezoneOffset());

      for (let item of _result) {
        if (item.src_t === "system") item.operator = "系统";

        if (item.src_t === "user") {
          item.operator = application.username;
        }

        if (item.src_t === "staff") {
          promises.push(employeesData.getById(item.source, {cache : true , expire: 24 * 60 * 60 }).then(data => {
            item.operator = data && (data.username || data.mobile) || "";
          }));
        }

        if (item.createdTime) {
          item.formatCreatedTime = dateFormat(new Date(item.createdTime) + time_offset * 60 * 1000, "yyyy-mm-dd HH:MM:ss") || item.createdTime;
        }
      }

      await Promise.all(promises);
      self.context.result.total = _result.length;
      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item._id === key) return item.name;
    }
    return '';
  }
}

module.exports = Handler
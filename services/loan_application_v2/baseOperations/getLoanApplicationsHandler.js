'use strict';

const HANDLER_NAME = 'getLoanApplicationsHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const STATUS_MAP = require('../../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanProduct = require('../../dataSvc/dataUtil').loanProducts;
const employeeData = require('../../dataSvc/dataUtil').employees;
const loanDistributeData = require('../../dataSvc/dataUtil').loanDistribute;
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let opts = self.context.opts;
      // 如果当前处理人存在查询条件 产品变更逻辑，不在处理采集资料的数据 其余当前处理人在关联表中
      if (opts.operator) {
        let employee = await employeeData.getByCondition({
          tId: condition.tId,
          username: `/${opts.operator}/`,
          limit: "unlimited"
        });
        let employeeList = [];
        // 循环拿到角色的_id
        for (const item of employee) {
          if (item._id) {
            employeeList.push(item._id);
          }
        }
        if (employeeList.length <= 0) {
          self.context.result = {
            result: []
          }
          debug(method, '[Exit](success)', self.context.result);
          return done();
        }

        let loandList = [];
        let distribute = await loanDistributeData.getByCondition({
          assigned: {
            $in: employeeList
          },
          archived: false,
          assignStatus: {
            $in: ["pre_censor_destined", "destined"]
          }
        });
        for (const item of distribute) {
          loandList.push(item.aId);
        }
        if (loandList.length <= 0) {
          self.context.result = {
            result: []
          }
          debug(method, '[Exit](success)', self.context.result);
          return done();
        }

        if (!condition.$and) {
          condition.$and = []
        }
        condition.$and.push({
          _id: {
            $in: loandList
          }
        })
        condition.destined = true
        condition.status = {
          $in: ["pre_censor", "wait_filtrate", "wait_dispose"]
        }

      }
      let result = await loanApplicationData.getListAndCountByCondition(condition);
      let promise = [];
      for (let item of result.result) {
        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm");
        item.statusChName = STATUS_MAP.get(item.status);
        promise.push(loanProduct.getById(item.pId, {cache : true , expire: 24 * 60 * 60 }).then(data => {
          item.productName = data.name;
          item.product = data;
        }))
      }
      await Promise.all(promise);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
/**
 * @summary FormatApplicatEnumHandler
 * <AUTHOR>
 *
 * Created at     : 2018-11-26 14:48:43 
 * Last modified  : 2018-12-13 16:42:58
 */

'use strict';

const HANDLER_NAME = 'formatApplicatVerifyInfoHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const dateFormat = require('dateformat');
const {
  funds: fundData,
  loanProducts: loanProduct,
  fundInvestor: fundInvestorData,
  employees: employeeData,
  groups: groupsData,
  groupsV2: groupV2Data,
  loanApplication: loanApplicationData,
  grainDepot: grainDepotData,
  employeeGroups: employeeGroupsData,
} = require('../../dataSvc/dataUtil');
const loanProductMap = require('../../../data/loan_product');
const WDPRODUCTS = require('../../../data/products').WDPRODUCTS;
const aliOss = require('../../aliOssSvc');
const aliOssSvc = require('../../aliOssSvc');
const Decimal = require('decimal.js');
const StatusListHandler = require('../baseOperations/StatusListHandler')
const stepDic = { '600fe47561f0e675643c5fa1': 'cxwq' }
const { PRODUCT } = require('../../../utils/fundConst');
const supplementsNameConfig = [
  ['collection', '县域服务中心资格审查', true],
  ['wait_fund', '登记部确认贷款银行'], // 老订单的 supplement 有之，要保留
  ['wait_investigation_verify_1', '县域服务中心初步尽职调查', true],
  ['wait_investigation_verify_2', '登记部审核补充调查', true],
  ['wait_investigation_verify_3', '监管部审核补充调查', true],
  ['wait_investigation_verify_4', '县域部审核补充调查', true],
  ['wait_investigation_verify_5', '风控部复核尽职调查', true],
  ['wait_investigation_verify_6', '监委会复核尽职调查', true],
  // ['pre_transferor','确认种植面积'],
  // ['loan_verify_4','出具鉴证报告'],
  // ['waitLoan','登记部录入放款数据'],
  // ['had_mortgage','登记部办理粮食抵押登记'],

];
const GetAllLandListWithTypeHandler = require('../../loanApplicationLandType/getAllLandListWithTypeHandler');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { opts: { getLandData, uId, uId: employee, roleId, statusAfter, statusAfterType, getAllArea } } = this.context;
      if (self.context.result.pId == PRODUCT.CG_CZD_ID) {
        debug(method, '[Exit](Nothing)')
        return done()
      }
      const _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }
      const [{ tId }] = _result;
      const query = { tId, employee, ...roleId && { group: roleId } || {} }
      const eg = tId && uId && await employeeGroupsData.getOneByCondition(query);
      const groupV2 = eg && await groupV2Data.getById(eg.groupV2);
      const { name: roleName } = roleId && await groupsData.getById(roleId) || {};
      debug(`${HANDLER_NAME}roleId:`, tId, uId, roleId, groupV2 && groupV2._id, eg && eg._id, roleName, JSON.stringify(query));
      await Promise.all(_result.map(result => this.formatOne(result, { groupV2, uId, roleName, roleId })));

      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async formatOne(result, opts) {
    const { groupV2, uId, roleName, roleId } = opts || {};
    const orgId = groupV2 && groupV2._id;
    result.appoveable = roleName !== 'AccountManager' || orgId === '60111486fda3812feb51fd00' || orgId === (result && result.orgInfo && result.orgInfo.orgId);
    debug(`${HANDLER_NAME}appoveable`, result.appoveable, result._id, uId, orgId, roleName, roleId, result.orgInfo && result.orgInfo.orgId);
    const supplementInfo = result.verifyInfo && result.verifyInfo.supplements || {};
    const handleSupplements = supplementsNameConfig
      .map(([orderStatus, title, showDescription]) => {
        const info = supplementInfo[orderStatus],
          { supplements, description } = info || {}; //确认本状态节点有没有值
        return info && {
          supplements: supplements,
          orderStatus,
          title,
          description: showDescription && description || '', //根据配置决定要不要决取备注
        }
      })
      .filter(v => v);
    result.verifyInfo = Object.assign(result.verifyInfo || {}, { handleSupplements });
    const { operator } = await loanApplicationData.getById(result._id); //未知前面何处将result.operator 改变成中文了，此处兼容情况
    const { username: operatorName } = operator && await employeeData.getById(operator, { cache: true, expire: 24 * 60 * 60 }) || {}
    result.operatorName = operatorName;
    result.operator = operatorName;

    const { data } = result.verifyInfo.assuranceInfo = Object.assign({ data: {} }, result.verifyInfo.assuranceInfo);
    result.totalArea = result.verifyInfo.assuranceInfo.totalArea =
      Object.values(data || {}).reduce((r, v) => r.add(new Decimal(v.area || 0)), new Decimal(0));

    await this.formatFundInfo(result);

    const type = stepDic[result.tId];
    const { step } = type && new StatusListHandler().execute(type, result.status) || { step: 0 };
    const statusName = new StatusListHandler().statusName(result.status, type);
    result.step = step;
    result.statusName = statusName;
    debug(`${HANDLER_NAME}Amount`, result.verifyInfo && result.verifyInfo.loanInfo && `${result.verifyInfo.loanInfo.amount}` || null);
    result.verifyInfo && result.verifyInfo.loanInfo &&
      (result.verifyInfo.loanInfo.amount = new Decimal(Number(result.verifyInfo.loanInfo.amount) || 0)
        .div(1000000).toFixed(2, Decimal.ROUND_DOWN))
    const ossFiles = [
      ...(result.verifyInfo && result.verifyInfo.loanInfo && result.verifyInfo.loanInfo.vouchers || []),
      ...handleSupplements.map(v => v.supplements).reduce((r, v) => r.concat(v), []), //无论是对象还是数组，都摊平
      // ...( result.verifyInfo && result.verifyInfo.supplements || [] ),
      ...(result.verifyInfo && result.verifyInfo.lawSignedFile &&
        Object.values(result.verifyInfo.lawSignedFile).reduce((r, v) => r.concat(v), []) || []),
    ];
    await Promise.all(ossFiles.filter(v => v).map(formatImg));

    const grainDepotId = result.verifyInfo && result.verifyInfo.superviseSupplement && result.verifyInfo.superviseSupplement.grainDepotId;
    grainDepotId && Object.assign(result.verifyInfo.superviseSupplement, { grainDepot: await grainDepotData.getById(grainDepotId) });
    // supplementsNameConfig

    const reportFiles = result.addons && result.addons.cxwqData && result.addons.cxwqData.reportFiles || [];
    await Promise.all(reportFiles.filter(v => v.path).map(async v => v.path = await aliOssSvc.getFile({ fileName: v.path })));
  }

  undoAsync(done) {
    done()
  }

  async formatFundInfo(result) {
    // 老的格式 
    if (!result.verifyInfo) return;
    const { name: text } = result.verifyInfo.fund && result.verifyInfo.fund.id && await fundInvestorData.getById(result.verifyInfo.fund.id) || {};
    result.verifyInfo.fund = Object.assign(result.verifyInfo.fund || {}, { text });
    //新格式， fund 随着 loanAndLawInfos 会有多条。但是要方便前端兼容新格式，所以
    const loanAndLawInfos = result.verifyInfo.loanAndLawInfos || [];
    await Promise.all(loanAndLawInfos.map(async ({ fund }) => {
      const { name: text } = fund && fund.id && await fundInvestorData.getById(fund.id) || {};
      Object.assign(fund || {}, { text });
    }));
    result.verifyInfo.fund.text = loanAndLawInfos.map(v => v.fund && v.fund.text || '').filter(v => v).join(',');
  }


  async formatAssuranceInfoList(app, defaultCropType, opts) {
    await this.syncCropData(app, defaultCropType, opts);

    app.verifyInfo.assuranceInfoList = app.verifyInfo.assuranceInfoList || [];
    app.addons = app.addons || {};
    app.addons.xntData = app.addons.xntData || {}
    const { verifyInfo: { assuranceInfo, assuranceInfo: { sn: assuranceSn, data: assuranceData }, assuranceInfoList } } = app;

    const ExecuteLoanAppVerifyHandler = require('../verify/ExecuteLoanAppVerifyHandler');
    const executeLoanAppVerifyHandler = new ExecuteLoanAppVerifyHandler();
    executeLoanAppVerifyHandler.formatAssuranceData(app, assuranceData);
    await executeLoanAppVerifyHandler.formatAssuranceInfo(app, assuranceInfo);
    app.assuranceInfoUnexpectedZero = Object.entries(assuranceInfo.data)
      .some(([cropType, v]) => Number(v.area) > 0 && Number(v.except) === 0);
    // // 优化后的代码x
    // const totalConfig = {totalExcept:'except',totalAvg:'avg',totalPrice:'price',totalYield:'yield',totalArea:'area'};
    // Object.entries(totalConfig).forEach(([total,key])=>
    //     assuranceInfo[total] = Object.values(assuranceData||{})
    //         .filter(v=>!isNaN(v[key]))
    //         .reduce((res,it)=>res.add( new Decimal( it[key])),new Decimal( 0 ))
    //         .toFixed(2, Decimal.ROUND_DOWN)
    // );
  }

  async syncCropData(app, defaultCropType, opts) {
    app.verifyInfo = app.verifyInfo || {};
    app.verifyInfo.assuranceInfo = app.verifyInfo.assuranceInfo || {};
    const assuranceData = app.verifyInfo.assuranceInfo.data = app.verifyInfo.assuranceInfo.data || {};

    const { statistics: { cropArea }, personStatistics } = await new GetAllLandListWithTypeHandler().handle(app._id, defaultCropType, opts) //取出汇总数据
    app.verifyInfo.assuranceInfo.personStatistics = personStatistics;
    Object.entries(cropArea).forEach(([cropType, area]) => //如果assuranceData为空，此段代码实际不会执行
      assuranceData[cropType] = Object.assign(assuranceData[cropType] || {}, { area: area.toFixed(2, Decimal.ROUND_DOWN) })); //为assuranceData里的每一项赋area字段
  }

  async formatFundInfo(result) {
    // 老的格式 
    if (!result.verifyInfo) return;
    const { name: text } = result.verifyInfo.fund && result.verifyInfo.fund.id && await fundInvestorData.getById(result.verifyInfo.fund.id) || {};
    result.verifyInfo.fund = Object.assign(result.verifyInfo.fund || {}, { text });
    //新格式， fund 随着 loanAndLawInfos 会有多条。但是要方便前端兼容新格式，所以
    const loanAndLawInfos = result.verifyInfo.loanAndLawInfos || [];
    await Promise.all(loanAndLawInfos.map(async ({ fund }) => {
      const { name: text } = fund && fund.id && await fundInvestorData.getById(fund.id) || {};
      Object.assign(fund || {}, { text });
    }));
    result.verifyInfo.fund.text = loanAndLawInfos.map(v => v.fund && v.fund.text || '').filter(v => v).join(',');
  }
}



async function formatImg(item) {
  if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
    item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
  if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
    item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
  if (item && item.url && item.url.indexOf('http') !== 0)
    item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'preDestineLoanAppHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanApplicationTrackingData = require('../../dataSvc/dataUtil').loanApplicationTracking;
const GROUP_ROLE = require('../../../services/permission').GROUP_ROLE;
const requestLimit = require('../../../persistence/requestLimit');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input.id;
      let uId = self.context.opts.uId;
      let _role = self.context.opts.role;
      let loanApp = await loanApplicationData.getById(id);

      if (loanApp.destined) {
        await requestLimit.unlock(id);
        throw {
          errorCode: 'E_PRE_DES_032',
          httpCode: 406,
          reason: '已认领'
        }
      }

      let payload = {
        src_t: 'staff',
        source: uId,
        target_t: 1,
        target: id,
      };
      // 初审由业务管理认领
      if (_role === GROUP_ROLE.BUSINESSMANAGEMENT) {
        payload.action = "pre_censor_destined";
      }
      // 客户经理认领
      if (_role === GROUP_ROLE.ACCOUNTMANAGER) {
        payload.action = "destined";
      }
      let result = loanApplicationTrackingData.post(payload);

      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
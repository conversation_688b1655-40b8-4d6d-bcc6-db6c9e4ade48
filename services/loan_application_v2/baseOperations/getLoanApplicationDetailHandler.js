'use strict';

const HANDLER_NAME = 'getLoanApplicationDetailHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanProduct = require('../../dataSvc/dataUtil').loanProducts;
const STATUS_MAP = require('../../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const loanApplicationTrackingData = require('../../dataSvc/dataUtil').loanApplicationTracking;
const moment = require('moment');
const CONTACT_RELATIONSHIP = new Map([
  ['1', "法人"],
  ['2', "股东"],
  ['3', "实际控制人"],
  ['4', "员工"],
  ['5', "其他"]
]);
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input.id;

      let result = await loanApplicationData.getById(id);
      result.statusChName = STATUS_MAP.get(result.status);

      const data = await loanProduct.getById(result.pId, {cache : true , expire: 24 * 60 * 60 });
      result.createdTime = moment(result.createdTime).format("YYYY-MM-DD HH:mm:ss");
      result.productName = data.name;
      result.product = data;
      result.contactRelationship = CONTACT_RELATIONSHIP.get(result.contactRelationship);
      // 放款信息
      let tracking = await loanApplicationTrackingData.getOneByCondition({
        target: id,
        action: "loaned"
      });
      if (tracking) {
        result.loaned = tracking.parameters ? tracking.parameters : {};
        result.loaned.comments = tracking.comments || "";
        if (result.loaned.pId) {
          const data = await loanProduct.getById(result.loaned.pId, {cache : true , expire: 24 * 60 * 60 });
          result.loaned.productName = data.name;
          result.loaned.product = data;
        }
      }

      self.context.result = result;
      if (!self.context.result) {
        throw {
          httpCode: 404,
          errorCode: 'E_LOAN_APP_D_030',
          reason: 'not found'
        };
      }

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
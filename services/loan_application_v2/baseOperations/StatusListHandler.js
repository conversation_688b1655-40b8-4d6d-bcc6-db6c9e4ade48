/**
 * @summary CheckIDcardHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'StatusListHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  APPLICATION_STATUS_LIST,
  APPLICATION_STATUS_DIC,
  APPLICATION_STATUS_SMALL_LIST,APPLICATION_STATUS_SMALL_DIC,
  APPLICATION_STATUS_STEP,
  STATUS_MENU,STATUS_MENU_LIST
} = require('./Constants')
const moment = require("moment");

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {type,currentStatus} = this.context.input;
      this.context.result = this.execute(type,currentStatus);

      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  execute(type,currentStatus){
    const menuList = STATUS_MENU_LIST[type] || [],{steps} = APPLICATION_STATUS_STEP[type] || {};
    const menu = menuList.map(([key,label])=>({key,label}));
    //step从1开始，没找到则是0
    const step = currentStatus && steps ? steps.findIndex(arr=>arr.includes(currentStatus)) + 1 : 0 ;
    return {menu,step}
  }

  statusName(status,type){
    const dic = type && APPLICATION_STATUS_SMALL_DIC[type] || APPLICATION_STATUS_DIC;
    return dic[status]
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
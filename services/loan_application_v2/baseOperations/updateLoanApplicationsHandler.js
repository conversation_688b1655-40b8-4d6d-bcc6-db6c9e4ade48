'use strict';

const HANDLER_NAME = 'updateLoanApplicationsHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const superagent = require('superagent');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanTrackingData = require('../../dataSvc/dataUtil').loanApplicationTracking;
const userVerifysData = require('../../dataSvc/dataUtil').userVerifys;
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { sm4 } = require('sm-crypto');
const config = require('config');
const creditConfig = config.get('rongxin_quanguo_api_usercredit');
const { CERTIFY_GRADE } = require('../../../data/hjq_certify_grade');
const _ = require('lodash');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      const tId = self.context.opts.tId;
      if (tId !== '638870ab3c4ae7a6977a59a1') return done(); // 只有杭锦旗走下面流程

      let applications = _.get(self, 'context.result.result');
      await Promise.all((applications || []).map(async (application) => {
        try {
          const uId = application.uId;
          let userInfo = await userVerifysData.getOneByCondition({
            uId,
            IDCardStatus: 'approved',
            archived: false
          });

          let aStatus = null;
          let aAction = null;

          const url = `${creditConfig.port == '443' || creditConfig.port == '8443' ? "https://" : "http://"}${creditConfig.host}:${creditConfig.port}/api/v1.0/credit/rating/getRatingDetailEncry/${sm4.encrypt(JSON.stringify({ cardNo: userInfo.IDCard, type: 4 }), creditConfig.sm4key)}`
          const resp = (await superagent.get(url).set("TenantId", "63884e142b6efe4416614195")).body;
          let ratingInfo = _.get(resp, 'data')
          debug(method, 'ratingInfoDATA', ratingInfo);

          if (ratingInfo && ratingInfo.ratingResult) {
            let ratingResult = ratingInfo.ratingResult;
            let grade = CERTIFY_GRADE.get(ratingResult) || 999;
            if (!grade || grade >= 18) {
              aStatus = 'rejected_operator_collect';
              aAction = 'rejected_operator_collect';
            } else {
              self.context.opts.ratingInfo = ratingInfo;
              aStatus = 'pre_censor';
              aAction = 'pre_censor';
            }
          }

          if (aStatus) {
            let putRes = await loanApplicationData.putById(application._id, {
              status: aStatus,
              lastModTime: new Date()
            });
            debug(method, '[Put] application', putRes);

            loanTrackingData.post({
              src_t: 'user',
              source: uId,
              target_t: 1,
              target: application._id,
              comments: aAction === 'pre_censor' ? "用户通过信息校验，评级通过，进入待初审" : "用户评级未通过，订单结束",
              action: aAction
            });
            application.status = aStatus
          }
        } catch (err) {
          debug.error(method, '[Exit](更新订单状态失败)', err);
        }
      }))

      if (condition.status == 'wait_operator_collect') {
        self.context.result.result = applications.filter(a => a.status == 'wait_operator_collect')
      }
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
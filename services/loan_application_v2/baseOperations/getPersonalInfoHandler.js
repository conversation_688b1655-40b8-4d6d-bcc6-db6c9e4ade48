'use strict';

const HANDLER_NAME = 'getPersonalInfoHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const infoVersionData = require('../../dataSvc/dataUtil').infoVersion;
const aliOssSvc = require('../../aliOssSvc');
const INFOPERSONAL = require('../../../utils/infoPersonalConst').INFOPERSONAL;
const infoPersonalMainData = require('../../dataSvc/dataUtil').infoPersonalMain;
const infoPersonalBasicData = require('../../dataSvc/dataUtil').infoPersonalBasic;
const infoPersonalFamilyMemberData = require('../../dataSvc/dataUtil').infoPersonalFamilyMember;
const infoPersonalProductMgrData = require('../../dataSvc/dataUtil').infoPersonalProductMgr;
const infoPersonalPropertyData = require('../../dataSvc/dataUtil').infoPersonalProperty;
const infoPersonalDebtData = require('../../dataSvc/dataUtil').infoPersonalDebt;
const infoPersonalFamilyPaymentData = require('../../dataSvc/dataUtil').infoPersonalFamilyPayment;
const infoPersonalEvaluationData = require('../../dataSvc/dataUtil').infoPersonalEvaluation;
const infoPersonalImageData = require('../../dataSvc/dataUtil').infoPersonalImage;

const moment = require('moment');

const svcData = {
  "basic": infoPersonalBasicData,
  "familyMember": infoPersonalFamilyMemberData,
  "productMgr": infoPersonalProductMgrData,
  "property": infoPersonalPropertyData,
  "debt": infoPersonalDebtData,
  "familyPayment": infoPersonalFamilyPaymentData,
  "evaluation": infoPersonalEvaluationData,
  "image": infoPersonalImageData,
}
const PERSONAL_TMP = {
  "familyMember": ['members'],
  "productMgr": ['products'],
  "property": ['buildings', 'machines', 'vehicles'],
  "familyPayment": ['incomes', 'payouts']
};

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input.id;
      let application = await loanApplicationData.getById(id);
      if (!application || !application.addons || !application.addons.info || !application.addons.info) {
        throw {
          errorCode: 'E_LOAN_ENTERPRISE_INFO_028',
          httpCode: 406,
          reason: '订单数据异常'
        }
      }
      if (application.addons.info.type !== 'personal') {
        debug(method, '[Exit](success)');
        return done();
      }
      let mId = application.addons.info.mId || "";
      let version = application.addons.info.version || "";
      if (!mId || !version) {
        throw {
          errorCode: 'E_LOAN_ENTERPRISE_064',
          httpCode: 406,
          reason: 'mId or version is missing'
        }
      }
      //取出跟订单对应的资料信息
      let versionInfo = await infoVersionData.getOneByCondition({
        mId: mId,
        version: version,
        type: 'personal'
      });
      if (!versionInfo || !versionInfo.infoIds) {
        throw {
          errorCode: 'E_LOAN_ENTERPRISE_064',
          httpCode: 406,
          reason: 'mId or version is missing'
        }
      }
      versionInfo.createdTime = moment(versionInfo.createdTime).format("YYYY-MM-DD HH:mm:ss");
      
      let main = await infoPersonalMainData.getById(mId);
      let result = {newVersion:false};
      if(version < main.version){
        result.newVersion = true;
      }
      result.versionInfo = versionInfo;
      result['main'] = await infoPersonalMainData.getById(mId);
      let infoIds = versionInfo.infoIds;
      // 获取个人信息
      for (const module in infoIds) {
        let _result = await svcData[module].getById(infoIds[module]);
        debug(method, '[continue] _result:', _result)
        if (!_result) {
          throw {
            errorCode: 'E_GET_INFO_057',
            httpCode: 406,
            reason: `Not Found ${module}`
          };
        }
        let options = INFOPERSONAL[module];
        if (options) {
          if (PERSONAL_TMP[module]) {
            Object.getOwnPropertyNames(options).forEach(value => {
              let showName = `${value}ShowName`;
              for (let tmpObj of PERSONAL_TMP[module]) {
                for (let item of _result[tmpObj]) {
                  if (module == 'productMgr' && value == 'sort') {
                    let optionKey = item[value];
                    item[showName] = options[value][optionKey.substr(0, 3)][optionKey];
                  } else {
                    item[showName] = options[value][item[value]];
                  }
                }
              }
            })
          } else {
            Object.getOwnPropertyNames(options).forEach(value => {
              let showName = `${value}ShowName`
              _result[showName] = options[value][_result[value]];
            })
          }
        }
        if (module == "image") {
          await self.getPath(_result);
        }
        result[module] = _result
      }

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
  async getPath(images) {
    let promise = [];
    for (const key in images) {
      let item = images[key];
      if (!item.image && !Array.isArray(item)) {
        continue;
      }
      if (item.image) item = [item];
      for (let i of item) {
        formatImg(i, promise);
      }
    }
    await Promise.all(promise);
  }
}

function formatImg(item, promise) {
  promise.push(aliOssSvc.getFile({
    fileName: item.thumbnail.url
  }).then(data => {
    item.thumbnail.url = data
  }));
  promise.push(aliOssSvc.getFile({
    fileName: item.image.url
  }).then(data => {
    item.image.url = data
  }));
}
module.exports = Handler
'use strict';

const HANDLER_NAME = 'GetMxtLoanAppListHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const STATUS_MAP = require('../../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanProduct = require('../../dataSvc/dataUtil').loanProducts;
const userVerifysData = require('../../dataSvc/dataUtil').userVerifys;
const { PRODUCT } = require('../../../utils/fundConst');
const _ = require('lodash');
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      // let opts = self.context.opts;

      let result = await loanApplicationData.getListAndCountByCondition(condition);
      let promise = [];
      for (let item of result.result) {
        item.amount /= 100;
        item.actualLoan = item.actualLoan || 0;
        if (item.pId == PRODUCT.TL_XNMD_ID) {
          promise.push(userVerifysData.getOneByCondition({ uId: item.uId, IDCardStatus: "approved", archived: false }).then(data => {
            item.idCard = data.IDCard || "";
            item.creditCode = data.IDCard || "";
          }))
        }
      }
      await Promise.all(promise);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'getLoanedDetailHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const userVerifysData = require('../../dataSvc/dataUtil').userVerifys;
const formatAreaCode = require('../../../persistence/formatAreaCode');
const STATUS_MAP = require('../../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const loanProduct = require('../../dataSvc/dataUtil').loanProducts;
const fundData = require('../../dataSvc/dataUtil').funds;
const loanProductMap = require('../../../data/loan_product');
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input.id;

      // 订单信息
      const loanInfo = await loanApplicationData.getById(id);
      if (!loanInfo || !loanInfo._id) {
        throw {
          httpCode: 404,
          errorCode: 'E_LOAN_APP_D_030',
          reason: 'not found'
        };
      }

      //获取到的区域信息
      const region = await formatAreaCode.getFormatAreaCode(loanInfo.area)
      loanInfo.amount /= 100;

      // 产品信息
      const product = await loanProduct.getById(loanInfo.pId, {cache : true , expire: 24 * 60 * 60 });
      if (product && product._id) {
        product.amount = loanInfo.amount;
        const REPAY_TYPE = loanProductMap.REPAY_TYPE.get(product.repay_t);
        product.repayDesc = REPAY_TYPE.name;
        product.type = product.name
      }
      loanInfo.type = product && product.name || "红本贷"
      if (loanInfo.creditEndTime) {
        loanInfo.creditRemainingTime = loanInfo.status === 'finished' || loanInfo.status === 'finished_loan' ? 0 : moment(loanInfo.creditEndTime).diff(moment(), 'days')
      } else {
        loanInfo.creditEndTime = '';
        loanInfo.creditRemainingTime = 0;
      }
      // 资方信息
      if (loanInfo.fund) {
        let fund = await fundData.getById(loanInfo.fund, {cache : true , expire: 24 * 60 * 60 });
        loanInfo.fundName = fund.name || '';
      }

      // 实名信息
      const userInfo = await userVerifysData.getOneByCondition({
        uId: loanInfo.uId,
        IDCardStatus: 'approved',
        archived: false
      });

      self.context.result = {
        loansInfo: {
          sn: loanInfo.sn,
          status: loanInfo.status,
          statusDesc: STATUS_MAP.get(loanInfo.status),
          creditEndTime: loanInfo.creditEndTime,
          creditRemainingTime: loanInfo.creditRemainingTime,
          username: loanInfo.username,
          userMobile: loanInfo.userMobile,
          actualLoan: loanInfo.actualLoan || 0,
          creditTime: loanInfo.creditTime ? moment(loanInfo.creditTime).format("YYYY-MM-DD HH:mm:ss") : '',
        },
        product: {
          name: product.name,
          amount: loanInfo.amount || 0,
          loanTerm: loanInfo.loanTerm || product.borrowPeriod || 12,
          loanUse: loanInfo.loanUse || product.loanUse || '',
          repay: product.repay,
          repayDesc: product.repayDesc,
          approveAmount: loanInfo.approveAmount || 0,
          fund: loanInfo.fund,
          fundName: loanInfo.fundName,
        },
        user: {
          idCard: userInfo.IDCard || "",
          address: region.area || "",
          bankName: loanInfo.bankName,
          bankCard: loanInfo.bankCard || "",
          contractorNo: loanInfo.addons.contractorNo,
          sn: loanInfo.sn,
          createdTime: loanInfo.createdTime ? moment(loanInfo.createdTime).format("YYYY-MM-DD") : ''
        },
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
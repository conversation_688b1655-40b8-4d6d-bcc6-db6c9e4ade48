'use strict';

const HANDLER_NAME = 'getLoanApplicationDestinedHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const STATUS_MAP = require('../../../utils/const/applicationConst').LOAN_APP_STATUS_MAP;
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanProduct = require('../../dataSvc/dataUtil').loanProducts;
const GROUP_ROLE = require('../../../services/permission').GROUP_ROLE;
const moment = require('moment');
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;
      let _role = self.context.opts.role;
      if (!_role) {
        throw {
          errorCode: 'E_LOAN_ROLE_186',
          httpCode: 406,
          reason: '没有角色信息'
        }
      }
      let assignStatus = "";
      // 初审由业务管理认领取消
      if (_role === GROUP_ROLE.BUSINESSMANAGEMENT) {
        assignStatus = "pre_censor_destined";
      }
      // 客户经理认领取消
      if (_role === GROUP_ROLE.ACCOUNTMANAGER) {
        assignStatus = "destined";
      }
      if (assignStatus) {
        condition.assignStatus = assignStatus;
      }
      let result = await loanApplicationData.getByUrl("/v1.0/loan/distribute/list", condition);
      let promise = [];
      for (let item of result.result) {
        item.createdTime = moment(item.createdTime).format("YYYY-MM-DD HH:mm");
        item.statusChName = STATUS_MAP.get(item.status);
        promise.push(loanProduct.getById(item.pId, {cache : true , expire: 24 * 60 * 60 }).then(data => {
          item.productName = data.name;
          item.product = data;
        }))
      }
      await Promise.all(promise);
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'cancelDestineLoanApplicationHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanApplicationTrackingData = require('../../dataSvc/dataUtil').loanApplicationTracking;
const loanDistributeData = require('../../dataSvc/dataUtil').loanDistribute;
const GROUP_ROLE = require('../../../services/permission').GROUP_ROLE;
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input.id;
      let uId = self.context.opts.uId;
      let _role = self.context.opts.role;
      let promises = [];

      promises.push(loanApplicationData.putById(id, {
        $unset: { destiner: "" },
        destined: false,
        lastModTime: new Date()
      }));

      let payload = {
        src_t: 'staff',
        source: uId,
        target_t: 1,
        target: id,
      };
      let assignStatus = "";
      // 初审由业务管理认领取消
      if (_role === GROUP_ROLE.BUSINESSMANAGEMENT) {
        payload.action = "pre_censor_destined_cancel";
        assignStatus = "pre_censor_destined";
      }
      // 客户经理认领取消
      if (_role === GROUP_ROLE.ACCOUNTMANAGER) {
        payload.action = "destined_cancel";
        assignStatus = "destined";
      }
      promises.push(loanApplicationTrackingData.post(payload));

      await Promise.all(promises);
      let distribute = await loanDistributeData.getOneByCondition({
        assigned: uId,
        aId: id,
        assignStatus: assignStatus,
        archived: false,
      });
      if (distribute && distribute._id) {
        await loanDistributeData.putById(distribute._id, {
          archived: true,
          lastModTime: new Date()
        })
      }
      self.context.result = {
        status: 'success'
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
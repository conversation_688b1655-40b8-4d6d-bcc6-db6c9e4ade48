'use strict';

const HANDLER_NAME = 'destineLoanAppHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const loanApplicationTrackingData = require('../../dataSvc/dataUtil').loanApplicationTracking;
const loanDistributeData = require('../../dataSvc/dataUtil').loanDistribute;
const GROUP_ROLE = require('../../../services/permission').GROUP_ROLE;
const requestLimit = require('../../../persistence/requestLimit');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let id = self.context.input.id;
      let uId = self.context.opts.uId;
      let _role = self.context.opts.role;
      let cancel_cond = {
        target: id,
        $sort: {
          createdTime: -1
        },
        limit: 1
      };
      // 初审由业务管理认领取消
      if (_role === GROUP_ROLE.BUSINESSMANAGEMENT) {
        cancel_cond.action = "pre_censor_destined_cancel";
      }
      // 客户经理认领取消
      if (_role === GROUP_ROLE.ACCOUNTMANAGER) {
        cancel_cond.action = "destined_cancel";
      }
      let cancelTracking = await loanApplicationTrackingData.getOneByCondition(cancel_cond);

      let condition = {
        target: id,
        archived: false,
        $sort: {
          createdTime: 1
        },
        limit: 1
      };
      // 初审由业务管理认领
      if (_role === GROUP_ROLE.BUSINESSMANAGEMENT) {
        condition.action = "pre_censor_destined";
      }
      // 客户经理认领
      if (_role === GROUP_ROLE.ACCOUNTMANAGER) {
        condition.action = "destined";
      }

      if (cancelTracking && cancelTracking._id) {
        condition.createdTime = {
          $gt: cancelTracking.createdTime
        };
      }

      let loanAppTracking = await loanApplicationTrackingData.getOneByCondition(condition);

      if (loanAppTracking && loanAppTracking.source !== uId) {
        await requestLimit.unlock(id);
        throw {
          errorCode: 'E_DES_040',
          httpCode: 406,
          reason: '已认领'
        }
      }
      // 修改订单中的 认领状态，第一次由业务管理认领，初审完在由客户经理认领
      let payload = {
        destined: true,
        destiner: uId,
        lastModTime: new Date()
      };

      let result = await loanApplicationData.putById(id, payload);
      let distribute = {
        assigned: uId,
        aId: id,
        assignStatus: condition.action
      }
      // 认领记录需要在loan_distribute_tracking 中保存
      result = await loanDistributeData.post(distribute);
      await requestLimit.unlock(id);

      self.context.result = {
        status: 'success'
      };
      if (!result) {
        self.context.result = {
          status: 'fail'
        };
      }
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
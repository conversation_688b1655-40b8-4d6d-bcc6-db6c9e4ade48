'use strict';

const HANDLER_NAME = 'preGetLoanAppListHander';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const GROUP_ROLE = require('../../../services/permission').GROUP_ROLE;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _opts = self.context.opts;
      let _input = self.context.input;

      if (!_opts.role) {
        debug(method, '[Exit](success)', 'do nothing');
        return done();
      }

      if (!_opts || !_opts.userInfo || !_opts.userInfo.roles) {
        debug(method, '[Exit](success)', 'userInfo not exists');
        return done();
      }

      if (!Array.isArray(_opts.userInfo.roles) || _opts.userInfo.roles.length < 1) {
        debug(method, '[Exit](success)', 'userInfo roles not exists');
        return done();
      }

      let roles = _opts.userInfo.roles;
      let _role = _opts.role;

      debug(method, "[opts](userInfo.roles):", roles);

      let flag = false;
      let realRole = null;
      roles.forEach(item => {
        if (item.name === _role) {
          flag = true;
          realRole = item;
          return;
        }
      });

      if (!flag) {
        throw {
          errorCode: 'E_LOAN_APP_ROLE_055',
          httpCode: 401,
          reason: 'invalid user'
        }
      }

      if (_opts.tId == '638870ab3c4ae7a6977a59a1') { // 杭锦旗
        const realRole = roles.find(item => item.name === _role);
        if (!_input.$or) {
          _input.$or = [];
        }
        realRole.areaList.forEach(item => {
          let area = {
            area: '/^' + item + '/'
          };
          _input.$or.push(area);
        });
      }
      // 银行区域管理  
      if (_role === GROUP_ROLE.BANKREGIONALMANAGER) {
        if (realRole.areaList && realRole.areaList.length > 0) {
          if (!_input.$or) {
            _input.$or = [];
          }
          realRole.areaList.forEach(item => {
            let area = {
              area: '/^' + item + '/'
            };
            _input.$or.push(area);
          });
        }
        if (!_input.status) {
          _input.status = {
            $in: ["wait_filtrate", "wait_dispose", "rejected_filtrate", "rejected_loan", "loaned"]
          }
        }

        self.context.input = _input;
      }

      //金秀业务管理 只需要拿到所有待初审且没被认领过得
      if (_role === GROUP_ROLE.BUSINESSMANAGEMENT) {
        if (realRole.areaList && realRole.areaList.length > 0) {
          if (!_input.$or) {
            _input.$or = [];
          }
          realRole.areaList.forEach(item => {
            let area = {
              area: '/^' + item + '/'
            };
            _input.$or.push(area);
          });
        }
        self.context.input = _input;
      }
      //金秀农信社客户经理 需要按照区域来查看订单 只能看到待筛选且没被认领过得
      if (_role === GROUP_ROLE.ACCOUNTMANAGER) {
        if (realRole.areaList && realRole.areaList.length > 0) {
          if (!_input.$or) {
            _input.$or = [];
          }
          realRole.areaList.forEach(item => {
            let area = {
              area: '/^' + item + '/'
            };
            _input.$or.push(area);
          });

          _input.status = 'wait_filtrate';
          _input.destined = false;

          self.context.input = _input;

          debug(method, '[Exit](success)', self.context.input);
          return done();
        }
      }

      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'getPersonalBasishandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const userIdentityData = require('../../dataSvc/dataUtil').userIdentity;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let result = self.context.result;
      if (!result || !result.uId) {
        debug(method, '[Exit](continue)')
        return done()
      }
      // 个人信息
      let userIdentity = await userIdentityData.getOneByCondition({
        uId: result.uId,
        IDCardStatus: "approved",
        archived: false,
      })
      if (!userIdentity) {
        debug(method, '[Exit](continue)');
        return done();
      }
      userIdentity.location = result.location || "";
      userIdentity.userMobile = result.userMobile || "";
      result.userInfo = userIdentity || {};
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
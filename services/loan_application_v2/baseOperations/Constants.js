/**
 * Constants
 * <AUTHOR>
 */

'use strict';

const COLLECT_COMPANY_TYPE_TEXT_LIST = [
  {
    id: '01',
    name: '种植大户'
  },
  {
    id: '03',
    name: '家庭农场'
  },
  {
    id: '04',
    name: '种植合作社'
  },
  {
    id: '05',
    name: '农机合作社'
  },
  {
    id: '06',
    name: '集体经济股份合作社'
  },
  {
    id: '07',
    name: '有限责任公司和股份公司'
  },
  {
    id: '08',
    name: '个人（自然人）独资企业'
  },
  {
    id: '09',
    name: '一人有限公司'
  },
];
const COLLECT_COMPANY_TYPE_TEXT_MAP = COLLECT_COMPANY_TYPE_TEXT_LIST.reduce((dic,v)=>(dic[v.id]=v.name,dic),{})

const APPLICATION_STATUS_LIST = [
    ['new', "新申请"],
    ['new_lack_credit', "新申请-无贷款额度"],
    ['wait_system_ai_collect', "待ai系统采集"],
    ['wait_manage_info_collect', "待经营信息采集"],
    ['certified', "待活体"],
    ['wait_operator_collect', "待协理员信息采集"],
    ['rejected_operator_collect', "评级拒绝"],
    ['biopsy_approved', "待完善资料"],
    ["credit_access", "待授权签约"],
    ['wait_filtrate', "待筛选"],
    ['wait_dispose', "待处理"],
    ['pre_approve', "待预审（渤海）"],
    ['rejected_approve', "资格审核拒绝"],
    ['register_bank_account', "待开户"],
    ['pre_censor', "待初审"],
    ['review', "待复审"],
    ['wait_fund','选择资金方'],
    ['wait_investigation_verify_1', '村级服务站预审'],
    ['rejected_by_wait_investigation_verify_1', '尽调预审拒绝'],
    ['wait_investigation_verify_2', '县级服务中心初审'],
    ['wait_investigation_verify_review_2', '登记部登记结果'],
    ['rejected_by_wait_investigation_verify_2', '尽调初审拒绝'],
    ['wait_investigation_verify_3', '物权登记审核'],
    ['rejected_by_wait_investigation_verify_3', '登记尽调拒绝'],
    ['wait_investigation_verify_4', '物权监管审核'],
    ['rejected_by_wait_investigation_verify_4', '监管尽调拒绝'],
    ['wait_investigation_verify_5', '物权风控审核'],
    ['rejected_by_wait_investigation_verify_5', '风控尽调拒绝'],
    ['wait_investigation_verify_6', '物权监委会审核'],
    ['rejected_by_wait_investigation_verify_6', '监委会尽调拒绝'],
    ['loan_verify_1', '待登记放款审批'],
    ['rejected_by_loan_verify_1', '登记放款审批拒绝'],
    ['loan_verify_2', '待监管放款审批'],
    ['rejected_by_loan_verify_2', '监管放款审批拒绝'],
    ['loan_verify_3', '待风控放款审批'],
    ['rejected_by_loan_verify_3', '风控放款审批拒绝'],
    ['loan_verify_4', '待生成鉴证报告'],
    ['rejected_by_loan_verify_4', '银行放款拒绝'],
    ['contract_review', "合同审核"],
    ['final_review', "待信贷系统终审"],
    ['unpaid', "支付融资服务费"],
    ['waitLoan', "待放款"],
    ['loaned', "已放款"],
    ['rejected_new_4', "地区暂未开通"],
    ['rejected_new_2', "申请人与实名不一致"],
    ['rejected_new_1', "实名失败"],
    ['rejected_cert', "信用评级不足"],
    ['rejected_new_3', "活体校验失败"],
    ['rejected_credit_access', "拒绝授信协议（渤海）"],
    ['rejected_whitelist', "银行白名单拒绝（渤海）"],
    ['decline_censor', "初审驳回"],
    ['rejected_censor', "初审拒绝"],
    ['rejected_review', "复审拒绝"],
    ['rejected_filtrate', "筛选拒绝"],
    ['rejected_eSign', "拒绝签署合同"],
    ['rejected_contract', "合同审核拒绝"],
    ['rejected_final', "终审拒绝"],
    ['rejected_loan', "贷款拒绝"],
    ['finished', "提前终止授信"],
    ['finished_loan', "账单已结清"],
    ['wait_interview', "待面审"],
    ['wait_income', "待业务进件"],
    ['rejected_interview', "面审拒绝"],
    ['rejected_biopsy', "活体拒单"],
    ['rejected_supplement', "采集资料拒单"],
    ['pre_transferor', "待补充出让方信息"],
    ['transferor', "流转信息确认"],
    ['wait_underwriting', "待担保公司审批"],
    ['rejected_credit_black', "黑名单拒绝"],
    ['collection', "待采集资料"],
    ['rejected_by_info_collection','信息采集拒绝'],
    ['wait_whitelist', "待审批"],
    ["apply_credit", "待银行授信"],
    ["wait_mortgage", "待粮食抵押登记"],
    ["overdue", "逾期"],

];

const APPLICATION_STATUS_SMALL_LIST = {
  cxwq: [
    ['new', "待补充资料"], //与别的系统不一样，新建订单要就补充资料
    ['certified', "待人像对比"], //
    ['biopsy_approved', "待完善影像资料"], //
    ["credit_access", "待授权签约"],
    ['collection', "待资格审核"],
    ['rejected_by_info_collection', '资格审核拒绝'],
    ['rejected_approve', "活体拒绝"],
    ['rejected_final', "放款审核拒绝"],
    // ['wait_fund', '待确认银行'],
    ['wait_investigation_verify_1', '待初步尽调'],
    // ['rejected_by_wait_investigation_verify_1', '初步尽调拒绝'],
    ['wait_investigation_verify_2', '待补充调查'],
    ['wait_investigation_verify_review_2', '待尽职调查'],
    ['wait_investigation_verify_5', '待风控审核'],
    ['rejected_by_wait_investigation_verify_5', '风控审核拒绝'],
    ['pre_expected_value', "待评估粮食预期产值"],
    ['wait_investigation_verify_6', '待风控评审会'],
    ['rejected_by_wait_investigation_verify_6', '风控评审会拒绝'],
    // ['pre_transferor', "待补充出让方信息"],
    ['loan_verify_4', '待生成鉴证报告'],
    ['waitLoan', "待银行放款"],
    ['waitBankSign', "待签署合同"],
    ['wait_mortgage', "待办理质押登记"],
    ['loaned', "已放款"],
    ['finished', "提前终止授信"],
    ['finished_loan', "账单已结清"],
    ['rejected_biopsy', "活体拒单"],
    ['rejected_loan','订单终止']
  ]
};
Object.values(APPLICATION_STATUS_SMALL_LIST) //key1 公共的key，pKeys 业务特有的key列表，将不存在的key1塞入数组之中
    .forEach(list=>{
        const pKeys = list.map(([k])=>k);
        const notExsitList = APPLICATION_STATUS_LIST.filter(([key1])=>!pKeys.includes(key1))
        list.push(...notExsitList)
    })

const APPLICATION_STATUS_DIC = APPLICATION_STATUS_LIST.reduce((r,[k,v])=>(r[k]=v,r),{});
const APPLICATION_STATUS_SMALL_DIC = Object.entries(APPLICATION_STATUS_SMALL_LIST).reduce((rr,[key,list])=>
    ( rr[key] = list.reduce((r,[k,v])=>(r[k]=v,r),{}) , rr ) , {} );

const APPLICATION_STATUS_STEP = { //各种业务的步骤条信息
  cxwq: {
    steps: [ //正常步骤条
      ['new', 'certified', "credit_access", 'biopsy_approved'], //新申请
      ['collection'], //资格审核
      // ['wait_fund'], //确认银行
      [ 'wait_investigation_verify_1', ], //初步尽调
      [ 'wait_investigation_verify_2','wait_investigation_verify_review_2', ], //补充调查，并行流：'wait_investigation_verify_2','wait_investigation_verify_3','wait_investigation_verify_4'
      [ 'wait_investigation_verify_5', ], //风控审核
      [ 'pre_expected_value' ],// 评估粮食预期产值
      [ 'wait_investigation_verify_6', ], //风控评审会
      // ['pre_transferor'], //流转土地签约（待确认种植面积）
      ['loan_verify_4'], //待生成鉴证报告（评估粮食预期产量）
      ['waitLoan', ], //待放款(出具鉴证报告 )
      ['wait_mortgage', 'waitBankSign', 'loaned', 'finished_loan'], //已放款
    ],
    rejects: [
      'rejected_approve', 'rejected_by_info_collection',
      // 'rejected_by_wait_investigation_verify_1', 
      'rejected_by_wait_investigation_verify_5', 'rejected_by_wait_investigation_verify_6',
      'rejected_loan','rejected_final',
    ] //拒绝的状态列表
  }
};

const STATUS_MENU = Object.entries(APPLICATION_STATUS_STEP)
  .reduce((r,[k,{steps,rejects}])=>( //合并正常步骤与拒绝状态，得到下拉列表，赋入对应键值
    r[k] = [...steps.reduce((r,v)=>r.concat(v),[]),...rejects],r
),{})

const STATUS_MENU_LIST = Object.entries(STATUS_MENU).reduce((r,[k,list])=>{
    const pList = APPLICATION_STATUS_SMALL_LIST[k] || APPLICATION_STATUS_LIST;
    r[k] = pList.filter(([status]) => list.includes(status));
    return r //过滤得到本类型的菜单全列表
},{});

module.exports = {
  COLLECT_COMPANY_TYPE_TEXT_MAP,
  COLLECT_COMPANY_TYPE_TEXT_LIST,APPLICATION_STATUS_SMALL_LIST,APPLICATION_STATUS_SMALL_DIC,
  APPLICATION_STATUS_LIST,APPLICATION_STATUS_DIC,APPLICATION_STATUS_STEP,STATUS_MENU,STATUS_MENU_LIST
};
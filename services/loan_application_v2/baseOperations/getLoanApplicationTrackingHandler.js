/**
 * @summary GetLoanApplicationTrackingHandler
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 11:08:08
 * Last modified  : 2018-12-13 13:11:46
 */

'use strict';

const HANDLER_NAME = 'GetLoanApplicationTrackingHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationTrackingData = require('../../dataSvc/dataUtil').loanApplicationTracking;
const TRACK_MAP = require('../../../utils/const/applicationConst').LOAN_TRACK_STATUS_MAP;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;

class Hand<PERSON> extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let condition = self.context.input;

      let result = await loanApplicationTrackingData.getListAndCountByCondition(condition);
      let loanInfo = await loanApplicationData.getById(condition.target);

      for (let item of result.result) {

        item.statusChName = TRACK_MAP.get(item.action);
        // 如果是金秀的订单
        if (loanInfo.tId === "5e55ec2e7f2aa8d78805f156" && (item.action === "destined" || item.action === "destined_cancel")) {
          item.statusChName = "银行" + item.statusChName
        }
        //如果是雄安的订单
        if (loanInfo.tId === "5fc44d27b27399c02a0da662" && (item.action === "wait_filtrate")) {
          item.statusChName = "待筛选"
        }
        if (loanInfo.tId === "5fc44d27b27399c02a0da662" && (item.action === "wait_dispose")) {
          item.statusChName = "待处理"
        }
        if (item.parameters && item.parameters.approveAmount) {
          item.parameters.approveAmount /= 100;
        }
      }
      self.context.result = result;

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
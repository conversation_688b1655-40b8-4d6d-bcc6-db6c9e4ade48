/**
 * @summary getPresentOperatorHandler
 * 
 */

'use strict';

const HANDLER_NAME = 'getPresentOperatorHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanDistributeData = require('../../dataSvc/dataUtil').loanDistribute;
const employeeData = require('../../dataSvc/dataUtil').employees;
const employeeGroupsData = require('../../dataSvc/dataUtil').employeeGroups;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {

      let _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }

      for (let item of _result) {
        // 认领的订单 直接查关联表数据
        if (item.destined) {
          let assignStatus = "";
          if (item.status == "wait_filtrate" || item.status == "wait_dispose") {
            assignStatus = "destined";
          }
          if (item.status == "pre_censor") {
            assignStatus = "pre_censor_destined";
          }

          let loanDistribute = await loanDistributeData.getOneByCondition({
            archived: false,
            aId: item._id,
            assignStatus: assignStatus
          });
          if (loanDistribute) {
            let employee = await employeeData.getById(loanDistribute.assigned, {cache : true , expire: 24 * 60 * 60 });
            item.operator = employee && employee.username || "";
          }

        }

      }

      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
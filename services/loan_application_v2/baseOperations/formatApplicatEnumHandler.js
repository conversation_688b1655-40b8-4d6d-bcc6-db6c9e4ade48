/**
 * @summary FormatApplicatEnumHandler
 * <AUTHOR>
 *
 * Created at     : 2018-11-26 14:48:43 
 * Last modified  : 2018-12-13 16:42:58
 */

'use strict';

const HANDLER_NAME = 'FormatApplicatEnumHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const dateFormat = require('dateformat');
// const data = require('../../../data');
const loanProduct = require('../../dataSvc/dataUtil').loanProducts;
const userVerifysData = require('../../dataSvc/dataUtil').userVerifys;
const fundData = require('../../dataSvc/dataUtil').funds;
const loanProductMap = require('../../../data/loan_product');
const aliOss = require('../../aliOssSvc');
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let _result = self.context.result.result || [self.context.result];
      if (!_result || _result.length === 0) {
        debug(method, '[Exit](continue)')
        return done()
      }

      let time_offset = Math.abs(new Date().getTimezoneOffset());

      for (let item of _result) {
        item.amount /= 100;
        if (item.createdTime) {
          item.formatCreatedTime = dateFormat(new Date(item.createdTime) + time_offset * 60 * 1000, "yyyy-mm-dd HH:MM:ss") || item.createdTime;
        }
        item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss')
        const userInfo = await userVerifysData.getOneByCondition({
          uId: item.uId,
          IDCardStatus: 'approved',
          archived: false
        });
        item.userIDCard = userInfo ? userInfo.IDCard : '';
        const product = await loanProduct.getById(item.pId, {cache : true , expire: 24 * 60 * 60 });
        if (product && product._id) {
          product.amount = item.amount;
          product.apr = product.loanRate + product.serviceRate;
          const REPAY_TYPE = loanProductMap.REPAY_TYPE.get(product.repay_t) || {};
          product.repay_t = REPAY_TYPE.name;
          item.productInfo = product;
          item.productName = product.name;
        }

        if (item.fund) {
          let fund = await fundData.getById(item.fund, {cache : true , expire: 24 * 60 * 60 });
          item.fundName = fund && fund.name || '';
        }
        if (item.addons && item.addons.riskReportFile) {
          item.addons.riskReportFile = await aliOss.getFile({
            fileName: item.addons.riskReportFile
          });
        }
        if (item.addons && item.addons.enterPriseReportFile) {
          item.addons.enterPriseReportFile = await aliOss.getFile({
            fileName: item.addons.enterPriseReportFile
          });
        }
      }

      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }

  getEnumValue(list, key) {
    for (let item of list) {
      if (item._id === key) return item.name;
    }
    return '';
  }
}

module.exports = Handler
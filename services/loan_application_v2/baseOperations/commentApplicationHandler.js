'use strict';

const HANDLER_NAME = 'commentApplicationHandler';
const { source } = require('superagent');
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:application_v2:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanApplicationTrackingData = require('../../dataSvc/dataUtil').loanApplicationTracking;
const loanApplicationData = require('../../dataSvc/dataUtil').loanApplication;
const {APPLICATION_STATUS} = require('../../../utils/loanApplicationConst');


class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {

      let opts = self.context.opts;
      let condition = self.context.input;

      const loanInfo = await loanApplicationData.getById(condition.aId);
      if (loanInfo.status !== APPLICATION_STATUS.PRE_COMMENT) {
        throw {
          errorCode: 'E_PRE_COMMENT_036',
          httpCode: 406,
          reason: '当前订单状态不允许补充材料'
        }
      }

      // 跳转到新的状态
      await loanApplicationData.putById(condition.aId, {
        status: APPLICATION_STATUS.PRE_CENSOR,
        lastModTime: new Date(),
      })

      const trackingInfo = {
        src_t: 'staff',
        target_t: 1,
        target: condition.aId,
        action: APPLICATION_STATUS.PRE_CENSOR, // 补充文字备注材料完成，到待初审 
        comments: condition.comment,
        source: opts.user.userid
      };
      await loanApplicationTrackingData.post(trackingInfo);

      self.context.result = {success: 'ok'}
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {

      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
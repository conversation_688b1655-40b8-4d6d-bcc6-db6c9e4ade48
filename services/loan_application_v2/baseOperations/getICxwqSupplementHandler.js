'use strict';

const HANDLER_NAME = 'getICxwqSupplementHandler';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:loan_application_v2:baseOperations:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const { loanSupplement: loanSupplementData, userAddons: userAddonsData, loanApplication: loanApplicationData} = require('../../dataSvc/dataUtil');
const aliOssSvc = require('../../aliOssSvc');
const {assert} = require('../../../utils/general')

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let {aId} = self.context.input;

      let loan = await loanApplicationData.getById(aId);
      if(!loan) {
        throw {
          errorCode: 'E_LOAN_GCS_300',
          httpCode: 406,
          reason: 'not find order'
        }
      }
      
      let type = loan.addons && loan.addons.info && loan.addons.info.type || loan.consumer_t
      if(type != '01' && type != '03') {
        type = '04';
      }
      debug(`${HANDLER_NAME}Type1${aId}`,type,TypeResTemplete[type] && true || false,loan.consumer_t,loan.addons && loan.addons.info || false)
      assert( TypeResTemplete[type] , 'E_SUPPLEMENT_DETAIL_TYPE_001' , `错误的类型${type}` )
      let result = await loanSupplementData.getOneByCondition({aId});
      if(!result) {
        self.context.result = [];
        return done();
      }
      // 其它信息数据格式兼容：
      result.cxwqContent && result.cxwqContent.otherInfo && !result.cxwqContent.otherInfo.otherInfo
        && ( result.cxwqContent.otherInfo = { otherInfo: result.cxwqContent.otherInfo  } );
      let res = {};

      if(result && JSON.stringify(result) !== '{}') {
        res = {
          ...result.cxwqContent,
          _id: result._id
        };
      }
      await loopFormatImg(res);

      debug(`${HANDLER_NAME}Type2${aId}`,type,TypeResTemplete[type] || null)
      let templete = JSON.parse(JSON.stringify(TypeResTemplete[type]));
      if(templete) {
        await insertTitle(res, templete);
      }
      let res2 = [];
      for (const k in templete) {
        if (Object.hasOwnProperty.call(templete, k)) {
          const v = templete[k];
          let moduleTitle = '';
          let items = [];
          for (const k2 in v) {
            if (Object.hasOwnProperty.call(v, k2)) {
              const v2 = v[k2];
              if(k2 === 'moduleTitle') {
                moduleTitle = v2;
              } else {
                items.push({
                  imageTitle: v2.imageTitle || '',
                  images: v2.images || []
                })
              }
            }
          }
          let obj = {
            moduleTitle,
            items
          }
          res2.push(obj);
        }
      }
      self.context.result = res2;
      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatUrl(url) {
  if(!url)return '';
  if(url.indexOf('http') !== 0) {
    return await aliOssSvc.getFile({fileName:url});
  }
}

async function loopFormatImg(obj) {
  for (const k in obj) {
    if (Object.hasOwnProperty.call(obj, k)) {
      const v = obj[k];
      const type = typeof v;
      if(type === 'object') {
        if(Array.isArray(v)) {
          for (const it of v) {
            for (const k2 in it) {
              if (Object.hasOwnProperty.call(it, k2)) {
                let v2 = it[k2];
                if(v2.url) {
                  it[k2].url = await formatUrl(it[k2].url);
                }
              }
            }
          }
        } else {
          loopFormatImg(v);
        }
      } else if(k === 'url') {
        obj[k] = await formatUrl(v);
      }
    }
  }
}
async function insertTitle(obj, templete) {
  for (const k in templete) {
    if (Object.hasOwnProperty.call(templete, k)) {
      const v = templete[k];
      const type = typeof v;
      if(type === 'object') {
        if(Array.isArray(v)) { // 赋值
          templete[k] = obj;
        } else if(obj[k] && obj[k].image && obj[k].thumbnail) {
          templete[k].images = [obj[k]];
        } else { // 下次循环
          insertTitle(obj[k], v);
        }
      }
    }
  }
}
const TypeResTemplete = {
  '01': {
    IDCardPapers: {
      moduleTitle: '身份证',
      backIDCardImage: {
        imageTitle: '身份证人像面',
        images: []
      },
      frontIDCardImage: {
        imageTitle: '身份证国徽面',
        images: []
      },
      handHeldIDCardImage: {
        imageTitle: '手持身份证',
        images: []
      }
    },
    spouse: {
      moduleTitle: '婚姻证明',
      marriedImage: {
        imageTitle: '婚姻证明',
        images: []
      },
      divorceImage: {
        imageTitle: '婚姻证明',
        images: []
      }
      // divorceImage: {
      //   imageTitle: '离婚证照片/离婚证明',
      //   images: []
      // }
    },
    residenceBooklet: {
      moduleTitle: '户口本',
      homePage: {
        imageTitle: '户口本首页',
        images: []
      },
      homeChief: {
        imageTitle: '户主页',
        images: []
      },
      family: {
        imageTitle: '全部家庭成员页',
        images: []
      }
    },
    creditReport: {
      moduleTitle: '人行征信报告',
      self: {
        imageTitle: '人行征信报告',
        images: []
      },
    },
    landWarrant: {
      moduleTitle: '土地确权证',
      warrantImgae: {
        imageTitle: '土地确权证',
        images: []
      },
    },
    njProcedure: {
      moduleTitle: '农机具手续及照片',
      procedureImgae: {
        imageTitle: '农机具手续及照片',
        images: []
      },
    },
    assert: {
      moduleTitle: '资产照片',
      houseImage: {
        imageTitle: '住宅',
        images: []
      },
      foodWarehouseImage: {
        imageTitle: '粮食储存库房',
        images: []
      },
      dryingTowerImgae: {
        imageTitle: '烘干塔',
        images: []
      },
      carImgae: {
        imageTitle: '车辆',
        images: []
      },
      deviceImgae: {
        imageTitle: '设备',
        images: []
      },
      procedureImgae: {
        imageTitle: '农机',
        images: []
      },
      otherImgae: {
        imageTitle: '其它',
        images: []
      },
      workPlaceImgae: {
        imageTitle: '办公场所',
        images: []
      },
      facotryRoomImgae: {
        imageTitle: '厂房',
        images: []
      },
      facotryAreaImgae: {
        imageTitle: '厂区',
        images: []
      },
      nonFoodHouseImgae: {
        imageTitle: '非粮食库房',
        images: []
      },
    },
    grainSubsidy: {
      moduleTitle: '粮食补贴',
      grainSubsidyImage: {
        imageTitle: '粮食补贴',
        images: []
      },
    },
    plantingInsurance: {
      moduleTitle: '种植保险',
      plantingInsuranceImage: {
        imageTitle: '种植保险',
        images: []
      },
      dryingTowerImgae: {
        imageTitle: '烘干塔',
        images: []
      },
      carImgae: {
        imageTitle: '车辆',
        images: []
      },
      deviceImgae: {
        imageTitle: '设备',
        images: []
      },
      procedureImgae: {
        imageTitle: '农机',
        images: []
      },
      otherImgae: {
        imageTitle: '其它',
        images: []
      },
      workPlaceImgae: {
        imageTitle: '办公场所',
        images: []
      },
      facotryRoomImgae: {
        imageTitle: '厂房',
        images: []
      },
      facotryAreaImgae: {
        imageTitle: '厂区',
        images: []
      },
      nonFoodHouseImgae: {
        imageTitle: '非粮食库房',
        images: []
      },
    },
    otherInfo: {
      moduleTitle: '其它信息',
      otherInfo:{
        imageTitle: '其它信息',
        images: []
      }
    },
  },
  '03': {
    IDCardPapers: {
      moduleTitle: '负责人身份证',
      backIDCardImage: {
        imageTitle: '身份证人像面',
        images: []
      },
      frontIDCardImage: {
        imageTitle: '身份证国徽面',
        images: []
      },
      handHeldIDCardImage: {
        imageTitle: '手持身份证',
        images: []
      }
    },
    spouse: {
      moduleTitle: '婚姻证明',
      marriedImage: {
        imageTitle: '婚姻证明',
        images: []
      },
      divorceImage: {
        imageTitle: '婚姻证明',
        images: []
      },
      // divorceImage: {
      //   imageTitle: '婚姻证明',
      //   images: []
      // }
    },
    residenceBooklet: {
      moduleTitle: '负责人户口本',
      homePage: {
        imageTitle: '户口本首页',
        images: []
      },
      homeChief: {
        imageTitle: '户主页',
        images: []
      },
      family: {
        imageTitle: '全部家庭成员页',
        images: []
      }
    },
    creditReport: {
      moduleTitle: '人行征信报告',
      self: {
        imageTitle: '人行征信报告',
        images: []
      },
    },
    landWarrant: {
      moduleTitle: '负责人土地确权证',
      warrantImgae: {
        imageTitle: '土地确权证',
        images: []
      },
    },
    njProcedure: {
      moduleTitle: '农机具手续及照片',
      procedureImgae: {
        imageTitle: '农机具手续及照片',
        images: []
      },
    },
    assert: {
      moduleTitle: '资产照片',
      houseImage: {
        imageTitle: '住宅',
        images: []
      },
      foodWarehouseImage: {
        imageTitle: '粮食储存库房',
        images: []
      },
      dryingTowerImgae: {
        imageTitle: '烘干塔',
        images: []
      },
      carImgae: {
        imageTitle: '车辆',
        images: []
      },
      deviceImgae: {
        imageTitle: '设备',
        images: []
      },
      procedureImgae: {
        imageTitle: '农机',
        images: []
      },
      otherImgae: {
        imageTitle: '其它',
        images: []
      },
      workPlaceImgae: {
        imageTitle: '办公场所',
        images: []
      },
      facotryRoomImgae: {
        imageTitle: '厂房',
        images: []
      },
      facotryAreaImgae: {
        imageTitle: '厂区',
        images: []
      },
      nonFoodHouseImgae: {
        imageTitle: '非粮食库房',
        images: []
      },
    },
    companyLicense: {
      moduleTitle: '营业执照',
      licenseImage: {
        imageTitle: '营业执照',
        images: []
      },
    },
    authorization: {
      moduleTitle: '授权书',
      bookImage: {
        imageTitle: '授权书',
        images: []
      },
    },
    grainSubsidy: {
      moduleTitle: '粮食补贴',
      grainSubsidyImage: {
        imageTitle: '粮食补贴',
        images: []
      },
    },
    plantingInsurance: {
      moduleTitle: '种植保险',
      plantingInsuranceImage: {
        imageTitle: '种植保险',
        images: []
      },
    },
    otherInfo: {
      moduleTitle: '其它信息',
      otherInfo:{
        imageTitle: '其它信息',
        images: []
      }
    },
  },
  // 非01和03都是04(企业)
  '04': {
    IDCardPapers: {
      moduleTitle: '法人身份证',
      backIDCardImage: {
        imageTitle: '身份证人像面',
        images: []
      },
      frontIDCardImage: {
        imageTitle: '身份证国徽面',
        images: []
      },
      handHeldIDCardImage: {
        imageTitle: '手持身份证',
        images: []
      }
    },
    companyLicense2: {
      moduleTitle: '企业章程',
      ruleImage: {
        imageTitle: '企业章程',
        images: []
      },
    },
    financialStatement: {
      moduleTitle: '上年财务报表',
      statement: {
        imageTitle: '上年社财务报表',
        images: []
      },
    },
    managePlace: {
      moduleTitle: '经营场地证明',
      houseCertificate: {
        imageTitle: '房产证/租赁协议',
        images: []
      },
    },
    creditReport: {
      moduleTitle: '法人及企业的征信报告',
      chairman: {
        imageTitle: '法人征信报告',
        images: []
      },
      company: {
        imageTitle: '企业征信报',
        images: []
      },
    },
    companyLicense: {
      moduleTitle: '营业执照',
      licenseImage: {
        imageTitle: '营业执照',
        images: []
      },
    },
    authorization: {
      moduleTitle: '授权书',
      bookImage: {
        imageTitle: '授权书',
        images: []
      },
    },
    grainSubsidy: {
      moduleTitle: '粮食补贴',
      grainSubsidyImage: {
        imageTitle: '粮食补贴',
        images: []
      },
    },
    plantingInsurance: {
      moduleTitle: '种植保险',
      plantingInsuranceImage: {
        imageTitle: '种植保险',
        images: []
      },
    },
    otherInfo: {
      moduleTitle: '其它信息',
      otherInfo:{
        imageTitle: '其它信息',
        images: []
      }
    },
  }
}
module.exports = Handler
'use strict';

const HANDLER_NAME = 'LoanApplicationFundReceiveExportHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  fundReceive:fundReceiveData,
} = require('../dataSvc/dataUtil');
const {generalExportExcel} = require('../../utils/general')
const moment = require('moment');

const config = [
  {w:15,f:'序号',k:'no'},
  {w:30,f:'类型',k:'actionName'},
  {w:30,f:'交易金额',k:'amount'},
  {w:30,f:'类型',k:'createdTime'},
];
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {list,uId,username} = this.context.result;
      const buffer = await generalExportExcel.call(this,list,config);
      this.context.result = {buffer,username,uId,filename:`${username}订单出用明细${moment(Date.now()).format('YYYYMMDD')}`};
      debug(method, '[Exit](success)', this.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
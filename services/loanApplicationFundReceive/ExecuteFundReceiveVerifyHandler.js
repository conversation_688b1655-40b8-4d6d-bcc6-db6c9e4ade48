/**
 * @summary loanAppWorkflows
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35 
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'ExecuteLoanAppVerifyHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const {BaseHandler} = require('nongfu.merchant.svcfw');
const LoanApplicationFundReceiveFormatHandler = require('./LoanApplicationFundReceiveFormatHandler');
const moment = require('moment');
const config = require('config');
const REPO_PACK_URL = `${config.get('repo_Service').host}/api/v1.0/file/cxwq/pay/token`;
const agent = require('superagent');
const Decimal = require('decimal.js'),unitDenominator = 100;

const {
  loanApplicationOutLand: loanApplicationOutLandData,
  loanApplicationLandType: loanApplicationLandTypeData,
  loanApplication: loanApplicationData,
  LoanSvrLand: LoanSvrLandData,
  employeeGroups:employeeGroupsData,
  employees:employeeData,
  groups:groupsData,
  infoCollectHistory:infoCollectHistoryData,
  loanApplicationVerify:loanApplicationVerifyData,
  loanSupplement: loanSupplementData,
  userAddons:userAddonsData,
  userCxwqSupplement: userCxwqSupplementData,
  contractFlowsV2: contractFlowsV2Data,
  insOrder: insOrderData,
  fundReceive: fundReceiveData,
  loanLand:loanLandData,
  cxwqCirculations:cxwqCirculationData,
  cxwqCirculationFundReceive:cxwqCirculationFundReceiveData,
  loanData,

} = require('../dataSvc/dataUtil');

const verifyConfig = [
  {current:'verify_1',next:'verify_2',refuse:'rejected_verify_1',back:null,employee:['AccountManager']},//客户经理（县级业务员）
  {current:'verify_2',next:'verify_3',refuse:'rejected_verify_2',back:null,employee:['Supervisor']},//监管员
  {current:'verify_3',next:'verify_5',refuse:'rejected_verify_3',back:null,employee:['SupervisorManager']},//监管主管
  // {current:'verify_4',next:'verify_5',refuse:'rejected_verify_4',back:null,employee:['GeneralManager']},//总经理
  {current:'verify_5',next:'verify_6',refuse:'rejected_verify_5',back:null,employee:['Supervisor','SupervisorManager']},//监管员/监管
  {current:'verify_6',next:'finished',refuse:'rejected_verify_6',back:null,employee:['Supervisor','SupervisorManager']},//监管员/监管
];

const beforeHandler = {};
const afterHandler = {
  async verify_5(preStatus,app,fundReceive,verify){
    //产品说这一步不会拒绝
    const payTokenInfo = fundReceive.extendInfo && fundReceive.extendInfo.verifyInfo
        && fundReceive.extendInfo.verifyInfo.payTokenInfo ;
    assert( payTokenInfo , 'E_FUND_RECEIVE_VERIFY_09','payTokenInfo is required' );
    debug(`${HANDLER_NAME}payTokenInfoFileAmount1`,payTokenInfo.payAmount,fundReceive.amount);
    //此处需求有过调整。因此兼容前后两种逻辑：如果前端传值则采信前端，并乘以100（前端以元为单位传值）；否则直接采用用户申请金额
    payTokenInfo.payAmount = payTokenInfo.payAmount && new Decimal(payTokenInfo.payAmount).mul(new Decimal(unitDenominator)).toString() || fundReceive.amount;// fundReceive.amount 的单位已经是分
    debug(`${HANDLER_NAME}payTokenInfoFileAmount2`,payTokenInfo.payAmount,fundReceive.amount);
    //后续需求：支付令金额必须等于申请金额
    assert( Number( Number( payTokenInfo.payAmount ) === Number( fundReceive.amount ) ), 'E_FUND_RECEIVE_VERIFY_20','批准的金额必须等于用户申请的金额' );

    payTokenInfo.payTime = moment().format('YYYY-MM-DD hh:mm:ss');
    fundReceive.hasPayToken = true;

    debug(`${HANDLER_NAME}payTokenInfoFileAmount3`,payTokenInfo.payAmount,fundReceive.amount);
    const fundReceiveCopy = await fundReceiveData.getById(fundReceive._id);
    Object.assign(fundReceiveCopy.extendInfo,{requestInfo:
          (fundReceiveCopy.extendInfo.requestInfo|| []).map(v=>({...v})).map(v=>(delete v.amountYuan,v))});
    Object.assign(fundReceiveCopy.extendInfo,{outLandRequestInfo:
          (fundReceiveCopy.extendInfo.outLandRequestInfo|| []).map(v=>({...v})).map(v=>(delete v.amount,v))});
    Object.assign(fundReceiveCopy.extendInfo.verifyInfo,{payTokenInfo});
    // outLandRequestInfo
    const [{payTokenInfoParas}] = await new LoanApplicationFundReceiveFormatHandler()
        .formatList([{...fundReceiveCopy}],{payTokenPara:1});
    debug(`${HANDLER_NAME}payTokenInfoFileAmount4`,payTokenInfo.payAmount,payTokenInfoParas.payAmount);

    const cantEmpty = ['entrustAccount','entrustBank','agreementNo','payAmount'];
    assert(payTokenInfoParas && cantEmpty.every(key=>payTokenInfoParas[key]),'E_FUND_RECEIVE_VERIFY_13','支付令信息必须填写完整')
    debug(`${HANDLER_NAME}payTokenInfoFile1`,payTokenInfo.payAmount,payTokenInfoParas.payAmount,payTokenInfoParas);
    const result = await agent.post(REPO_PACK_URL).send(payTokenInfoParas);
    payTokenInfo.file = {url:result && result.body && result.body.path};
    debug(`${HANDLER_NAME}payTokenInfoFile2`,payTokenInfo.payAmount,payTokenInfoParas.payAmount,result && result.body || null);

    debug(`${HANDLER_NAME}payTokenInfoFile3`,payTokenInfo.payAmount,payTokenInfoParas.payAmount,
        fundReceive.extendInfo.verifyInfo.payTokenInfo.payAmount,payTokenInfo);
    await fundReceiveData.putById(fundReceive._id,fundReceive);
  },
  async verify_6(preStatus,app,fundReceive,verify,landList,landFundReceiveList){
    if( verify.action !== 'agree' )return;
    const bankPayInfo = fundReceive.extendInfo && fundReceive.extendInfo.verifyInfo
        && fundReceive.extendInfo.verifyInfo.bankPayInfo ;
    assert( bankPayInfo , 'E_FUND_RECEIVE_VERIFY_10','bankPayInfo is required' )
    assert( bankPayInfo.payTime , 'E_FUND_RECEIVE_VERIFY_11','bankPayInfo.payTime is required' )
    assert( bankPayInfo.payAmount , 'E_FUND_RECEIVE_VERIFY_12','bankPayInfo.payAmount is required' )

    debug(`${HANDLER_NAME}bankPayInfoFileAmount1`,bankPayInfo.payAmount,fundReceive.amount);
    bankPayInfo.payAmount = new Decimal(bankPayInfo.payAmount).mul(new Decimal(unitDenominator)).toString();
    debug(`${HANDLER_NAME}bankPayInfoFileAmount2`,bankPayInfo.payAmount,fundReceive.amount);
    assert( Number( Number( bankPayInfo.payAmount ) <= Number( fundReceive.amount ) ), 'E_FUND_RECEIVE_VERIFY_20','批准的金额必须不能大于用户申请的金额' );

    bankPayInfo.payTime = moment(bankPayInfo.payTime).format('YYYY-MM-DD hh:mm:ss');
    fundReceive.successful = 2;
    fundReceive.payAmount = bankPayInfo.payAmount || 0;
    fundReceive.payTime = bankPayInfo.payTime;
    await fundReceiveData.putById(fundReceive._id,fundReceive);

    // await Promise.all(landList.map(v=>cxwqCirculationData.putById(v._id,{lastModTime:new Date(),fundReceiveStatus:'finished'})))
    await Promise.all(landFundReceiveList.map(v=>cxwqCirculationFundReceiveData.putById(v._id,
        {lastModTime:new Date(),fundReceiveStatus:'finished'})));
    await Promise.all(landFundReceiveList.map(v=>cxwqCirculationData.putById(v.cxwqId,
        {lastModTime:new Date(),fundReceiveStatus:'finished'})));
  },
};
const actionConfig = ['agree','refuse','back','justSave'];
const {assert} = require('../../utils/general')
const extendInfoAllowKeys = ['payType']

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {id,action='agree',extendInfo={},description,roleId:group,userId:employee,overwriteVerifyInfo,addonVerifyInfo} = this.context.input;
      process.env.DEBUG_NO_NEED_LOGIN || assert( employee , 'E_FUND_RECEIVE_VERIFY_00','没有权限');
      const fundReceive = await fundReceiveData.getById(id);
      const app = fundReceive.aId && await loanApplicationData.getById(fundReceive.aId);
      assert(app,'E_FUND_RECEIVE_VERIFY_01a','订单不存在');
      assert(app.status !== 'finished_loan','E_FUND_RECEIVE_VERIFY_01b','订单已封存');

      fundReceive.verifyRecord = fundReceive.verifyRecord || [];
      fundReceive.extendInfo = fundReceive.extendInfo || {};
      fundReceive.extendInfo.verifyInfo = fundReceive.extendInfo.verifyInfo || {};
      const extendInfoUpdate = extendInfoAllowKeys.reduce( (r,k)=>(r[k]=extendInfo[k],r) , {} );
      Object.assign( fundReceive.extendInfo , extendInfoUpdate );

      debug(`${method}QueryPara`,id,action,group,employee,overwriteVerifyInfo,addonVerifyInfo);
      const employeeGroup = employee && group && await employeeGroupsData.getOneByCondition({employee,group}) || null;
      const admin = employee && await employeeData.getById(employee, {cache : true , expire: 24 * 60 * 60 });
      assert(fundReceive,'E_FUND_RECEIVE_VERIFY_01','支用记录不存在');
      const {status} = fundReceive;
      const cf = verifyConfig.find(it=>it.current === status);
      assert(cf,'E_FUND_RECEIVE_VERIFY_02','当前状态不可审核');
      process.env.DEBUG_NO_NEED_LOGIN || assert(employeeGroup,'E_FUND_RECEIVE_VERIFY_03','用户未登录或未拥有此管理员角色');
      const role = group && await groupsData.getById(group);
      process.env.DEBUG_NO_NEED_LOGIN || assert(role,'E_FUND_RECEIVE_VERIFY_04','不存在的管理员权限');
      process.env.DEBUG_NO_NEED_LOGIN || assert(!Array.isArray(cf.employee) || cf.employee.length === 0 || cf.employee.includes(role.name),
          'E_FUND_RECEIVE_VERIFY_05','该角色当前阶段无权审核');
      assert(actionConfig.includes(action),'E_FUND_RECEIVE_VERIFY_06','错误的动作');
      assert( action !== 'back' || cf.back  ,'E_FUND_RECEIVE_VERIFY_07','此阶段不能驳回');
      const landList = [];//await cxwqCirculationData.getByCondition({fundReceiveId:fundReceiveData._id,archived: false,limit: "unlimited"});
      const landFundReceiveList = await cxwqCirculationFundReceiveData.getByCondition(
          {fundReceiveId:fundReceiveData._id,archived: false,fundReceiveStatus:'verify',limit: "unlimited"});
      //前置处理
      beforeHandler[ status ] && await beforeHandler[ status ].call( this , cf.next,app,fundReceive );

      action === 'agree' && ( fundReceive.status = cf.next );
      action === 'refuse' && ( fundReceive.status = cf.refuse , fundReceive.successful = 3 );
      action === 'back' && ( fundReceive.status = cf.back );
      Object.assign(fundReceive.extendInfo.verifyInfo,overwriteVerifyInfo || {});
      // debug('addonVerifyInfo',JSON.stringify(addonVerifyInfo),JSON.stringify(app.verifyInfo));
      Object.entries( addonVerifyInfo || {} )//追回型信息，数组追加，对象合并
          .forEach(([k,v])=>Array.isArray(v) ?
              fundReceive.extendInfo.verifyInfo[k] = [...(fundReceive.extendInfo.verifyInfo[k]||[]),...v]
              : fundReceive.extendInfo.verifyInfo[k] = Object.assign(fundReceive.extendInfo.verifyInfo[k]||{},v) )
      fundReceive.operator = employee;
      fundReceive.lastModTime = new Date();

      if( action === 'justSave' ){
        this.context.result = {success:'ok',app: fundReceive};
        await fundReceiveData.putById(fundReceive._id,fundReceive);
        debug(method, 'justSave','[Exit](success)');
        return done();
      }

      const verify = {status,action,description,operator:employee,createdTime:new Date(),lastModTime:new Date()};
      debug(`${HANDLER_NAME}VerifyStatusSave1`,status,action,fundReceive._id);
      status.match(/^rejected_/) && debug(`${HANDLER_NAME}VerifyStatusSaveError1`,status,action,fundReceive._id);
      assert( !status.match(/^rejected_/) , 'E_FUND_RECEIVE_VERIFY_08' , '操作日志的记录状态有误' );

      //后置处理
      afterHandler[ status ] && await afterHandler[ status ].call( this , status,app,fundReceive,verify,landList,landFundReceiveList );

      fundReceive.verifyRecord.unshift( verify );
      await fundReceiveData.putById(fundReceive._id,fundReceive);
      action === 'refuse' && ( //修改土地流转的状态
              // await Promise.all(landList.map(v=>cxwqCirculationData.putById(v._id,
              //     {lastModTime:new Date(),fundReceiveInfo:{},fundReceiveId:null,fundReceiveStatus:'unused'}))),
          debug(`${HANDLER_NAME}Refuse`,landFundReceiveList.map(v=>v._id).map(v=>v.cxwqId),landFundReceiveList.map(v=>v._id)),
          await Promise.all(landFundReceiveList.map(async v=>{
            const cc = await cxwqCirculationData.getById(v.cxwqId);
            await cxwqCirculationData.putById(v.cxwqId, //把未支付金额加回到签约合同之中
          {arrearageFees:new Decimal(cc.arrearageFees).add(new Decimal(v.amountYuan)).toString(),
                    lastModTime:new Date(),fundReceiveStatus:'unused',fundReceiveId:null})
          })),
          await Promise.all(landFundReceiveList.map(v=>cxwqCirculationFundReceiveData.putById(v._id,
              {lastModTime:new Date(),fundReceiveStatus:'reject'})))
      );

      await new LoanApplicationFundReceiveFormatHandler()
          .formatList([fundReceive],{formatOss:1,amount:1,appList:1,payTokenPara:0});
      this.context.result = {success:'ok',app,fundReceive,verify}
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};

module.exports = Handler;

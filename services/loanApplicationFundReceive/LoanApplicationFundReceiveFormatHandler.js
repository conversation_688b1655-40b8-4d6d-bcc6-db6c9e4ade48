'use strict';

const HANDLER_NAME = 'LoanApplicationFundReceiveFormatHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    fundReceive:fundReceiveData,
    userVerifys:userVerifyData,
    employees:employeeData,
    employeeGroups:employeeGroupsData,
    groupsV2:groupV2Data,
    groups:groupsData,
    cxwqCirculations:cxwqCirculationData,
    infoCollectHistory:infoCollectHistoryData,
    cxwqCirculationFundReceive:cxwqCirculationFundReceiveData,
    loanLand:loanLandData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const moment = require('moment');
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const Decimal = require('decimal.js'),unitDenominator = 100,unitDenominator2 = 1000000;
const {
    generalStatusList,
    rejectedStatusList,
    actionNameDic,
    statusList,
    successfulNameDic,
    actionText,
    statusText,
    statusTextVerify,
} = require('./Constants');
const {
    COLLECT_COMPANY_TYPE_TEXT_LIST,
    COLLECT_COMPANY_TYPE_TEXT_MAP,
} = require('../loan_application_v2/baseOperations/Constants');
const supplementsNameConfig = [
    ['verify_1','待县级预审',true],
    ['verify_2','待监管初审'],
    ['verify_3','待监管复审',true],
    ['verify_4','待董事长终审',true],
    ['verify_5','待生成支付令',true],
    ['verify_6','待支付',true],
    ['finished','支用完成',true],
    ['closed','已终止',true],

];
const formatApplicatVerifyInfoHandler = require('../loan_application_v2/baseOperations/formatApplicatVerifyInfoHandler')
const url = '/v1.0/loan/applications/fund/receive/amount/group/by/user';
const payTypeTextDic = ['受托支付','自主支付'];

const payTokenRequestInfoDic = {

    async generalBuy(one){
        return one.extendInfo && one.extendInfo.requestInfo || []
    },

    async land(one){
        const list = await cxwqCirculationFundReceiveData.getByCondition({fundReceiveId:one._id,archived: false,limit: "unlimited"});
        return Promise.all(list.map(async (v,i)=>{
            const fundReceiveInfo = {
                no:i+1,remark:'',//contractorNo:v.contractorNo,area:v.area,
                ...v,amount:v.amountYuan,
            };
            await this.formatPayTokenInfoLandTotal(fundReceiveInfo);
            delete fundReceiveInfo.amountYuan;
            return fundReceiveInfo;
        }));
    },

    async outland(one){

        const list = one.extendInfo && one.extendInfo.outLandRequestInfo || []
        return list.map( v=>(v.remark = v.remark || '' , v) );
    },
}

class Handler extends BaseHandler {
    constructor(context) {
        super(context)
    }

    getName() {
        return HANDLER_NAME
    }

    async doAsync(done) {
        const method = `${this.getName()}.doAsync`
        debug(method, '[Enter]')
        try {
            const { input:{tId}, opts:{uId:employee,role:roleId,formatList,formatOpts={}}} = this.context;
            const {name:roleName} = roleId && await groupsData.getById( roleId ) || {}
            const eg = await employeeGroupsData.getOneByCondition( {tId,employee,...roleId && {group:roleId} || {}} );
            const groupV2 = eg && await groupV2Data.getById( eg.groupV2 );            Object.assign( formatOpts , {roleName,groupV2,roleId} ) 
            await this.formatList(formatList || [],formatOpts);
            debug(method, '[Exit](success)');
            return done();
            } catch (error) {
            debug.error(method, '[Exit](failed)', error);
            return done(error);
        }
    }

    async formatList(list,formatOpts){
        const {groupV2,roleName,roleId} = formatOpts || {};
        const orgId = groupV2 && groupV2._id;
        debug(`${HANDLER_NAME}orgId :`,orgId,groupV2,roleName,roleId);
        await Promise.all(list.filter(v=>v).map(async (v,i)=>{
            v.no = i+1;
            v.step = generalStatusList.findIndex(([key])=>key === v.status);
            const supplementInfo = v.verifyInfo && v.verifyInfo.supplements || {};
            const handleSupplements = supplementsNameConfig
                .map(([orderStatus,title,showDescription])=>{
                    const info =  supplementInfo[orderStatus] , {supplements,description} = info || {}; //确认本状态节点有没有值
                    return info && {
                        supplements:supplements,orderStatus,title,
                        description:showDescription && description || '',//根据配置决定要不要决取备注
                    }
                })
                .filter(v=>v);
            v.extendInfo = Object.assign({verifyInfo:{payType:0}},v.extendInfo||{})
            v.verifyInfo = Object.assign(v.verifyInfo|| {},{handleSupplements});
            v.extendInfo.verifyInfo.payType && ( v.extendInfo.payTypeText = payTypeTextDic[v.extendInfo.verifyInfo.payType] ) ;

            v.actionName = actionNameDic[v.action];
            v.statusText = statusText[v.status];
            v.requestTypeName = COLLECT_COMPANY_TYPE_TEXT_MAP[v.requestType];
            v.createdTime = moment(v.createdTime).format('YYYY-MM-DD HH:mm:ss');
            v.lastModTime = moment(v.lastModTime).format('YYYY-MM-DD HH:mm:ss');
            v.payTime = v.payTime && moment(v.payTime).format('YYYY-MM-DD HH:mm:ss');
            v.amount && ( v.amount = new Decimal(v.amount)
                .div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN) );
            v.payAmount && ( v.payAmount = new Decimal(v.payAmount)
                .div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN) );
            const { payTokenInfo={},bankPayInfo={}} = v.extendInfo && v.extendInfo.verifyInfo || {},{requestInfo=[]} =  v.extendInfo || {};
            payTokenInfo.payAmount && ( payTokenInfo.payAmount = new Decimal(payTokenInfo.payAmount)
                .div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN) );
            bankPayInfo.payAmount && ( bankPayInfo.payAmount = new Decimal(bankPayInfo.payAmount)
                .div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN) );
            requestInfo.forEach(r=>{
                r.price && ( r.price = new Decimal(r.price).div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN) );
                r.amount && ( r.amount = new Decimal(r.amount).div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN) );
                r.time && ( r.time = moment(r.time).format('YYYY-MM-DD HH:mm:ss') );
                const warningSnapshotInfo = r.warningSnapshotInfo || {};
                warningSnapshotInfo.warningAmount && ( warningSnapshotInfo.warningAmount
                    = new Decimal(warningSnapshotInfo.warningAmount)
                        .div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN) );
            });

            formatOpts.formatOss && await this.formatOssFiles(v);

            const app = v.aId && await loanApplicationData.getById(v.aId);
            v.app = app;
            v.appoveable = roleName !=='AccountManager' || orgId === '60111486fda3812feb51fd00' || orgId === ( app && app.orgInfo && app.orgInfo.orgId );
            await new formatApplicatVerifyInfoHandler().formatOne(app);


            const {mId} = app && app.addons && app.addons.info || {}
            const historyInfo = mId && await infoCollectHistoryData.getById(mId);
            v.historyInfo = historyInfo;

            const employee = v.operator && await employeeData.getById(v.operator, {cache : true , expire: 24 * 60 * 60 });
            v.employeeName = employee && employee.username;

            v.areaInfo = v.requestAreaCode && await formatAreaCode.getFormatAreaCode(v.requestAreaCode);
            await Promise.all((v.verifyRecord || []).map(async item=>{
                item.actionText = `${statusTextVerify[item.status]}${actionText[item.action]}`;
                item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss');
                item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss');
                const employee = item.operator && await employeeData.getById(item.operator, {cache : true , expire: 24 * 60 * 60 });
                item.employeeName = employee && employee.username;
            }));

            formatOpts.amount && await this.formatAmount(v);
            formatOpts.appList && await this.formatNotCloseAppList(v);
            formatOpts.payTokenPara && await this.formatPayTokenInfoParas(v);
        }));
        return list;
    }

    async formatOssFiles(v){
        const {verifyInfo,requestInfo} = v && v.extendInfo || {verifyInfo:{}} ;
        const handleSupplements = verifyInfo && verifyInfo.handleSupplements;
        const ossFiles = [
            ...( verifyInfo && verifyInfo.payTokenInfo && [verifyInfo.payTokenInfo.file] || []),
            ...( verifyInfo && verifyInfo.bankPayInfo && verifyInfo.bankPayInfo.paymentVoucher || []),
            ...( requestInfo || []).map(v=>v.photos || []).reduce((r,v)=>r.concat(v),[]),
            ...( handleSupplements || []).map(v=>v.supplements).reduce((r,v)=>r.concat(v),[]),//无论是对象还是数组，都摊平
        ];

        await Promise.all( ossFiles.filter(v=>v).map(formatImg) );
        return v;
    }

    async formatAmount(v){
        const result = await fundReceiveData.getByUrl(url,{uId:v.uId,onlyClosed:0});
        result.list && result.list.length && ( v.userInfo = result.list[0] );
        const totalAmount = v.snapshotInfo && v.snapshotInfo.totalAmount && v.snapshotInfo.totalAmount || {};
        totalAmount.allLoanedAmount && ( totalAmount.allLoanedAmount = new Decimal(totalAmount.allLoanedAmount)
            .div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN) );
        totalAmount.allPayAmount && ( totalAmount.allPayAmount = new Decimal(totalAmount.allPayAmount)
            .div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN) );
        // const {snapshotInfo} = v,{totalAmount:{allRemainderAmount,allPayAmount,allLoanedAmount} = snapshotInfo || {}},
        totalAmount.allRemainderAmount && ( totalAmount.allRemainderAmount = new Decimal( totalAmount.allRemainderAmount)
            .div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN) );
        return v;
    }

    async formatNotCloseAppList(one){
        // one.notClosedAppList = await loanApplicationData.getByCondition(
        //     {uId:one.uId,tId:one.tId,archived:false,status:'loaned',limit:'unlimited'});
        one.notClosedAppList = one.aIdList && one.aIdList.length && await loanApplicationData.getByCondition(
            {uId:one.uId,tId:one.tId,archived:false,limit:'unlimited',_id:{$in:one.aIdList}}) ||[];
        one.notClosedAppList.forEach(v=>{
            v.amount = new Decimal(v.amount).div(unitDenominator).toFixed(2, Decimal.ROUND_DOWN);
            v.createdTime = moment(v.createdTime).format('YYYY-MM-DD HH:mm:ss');
            v.lastModTime = moment(v.lastModTime).format('YYYY-MM-DD HH:mm:ss');
        } ) ;
        const payTokenInfo = one.extendInfo && one.extendInfo.verifyInfo && one.extendInfo.verifyInfo.payTokenInfo;
        const [oneApp] = one.notClosedAppList;
        oneApp && oneApp.verifyInfo && payTokenInfo && ( payTokenInfo.fund = oneApp.verifyInfo.fund );
        return one;
    }

    async formatPayTokenInfoParas(one){
        const para = one.payTokenInfoParas = {};
        para.name = one.requestCompanyName || one.requestName;
        const payTokenInfo = one.extendInfo && one.extendInfo.verifyInfo
            && one.extendInfo.verifyInfo.payTokenInfo || {};
        para.entrustAccount = payTokenInfo.entrustAccount || '';
        para.entrustBank = payTokenInfo.entrustBank || '';
        para.payAmount = payTokenInfo.payAmount || '';
        para.agreementNo = payTokenInfo.agreementNo || '';
        para.actionName = one.actionName || '';
        para.action = one.action || '';
        // one.notClosedAppList = one.aIdList && one.aIdList.length && await loanApplicationData.getByCondition(
        //     {uId:one.uId,tId:one.tId,archived:false,aIdList:{$in:one.aIdList}}) ||[];
        // const appList = await loanApplicationData.getByCondition(
        //     {tId:one.tId,uId:one.uId,archived:false,status:'loaned',limit:'unlimited'});
        // const appList = one.aIdList && one.aIdList.length && await loanApplicationData.getByCondition(
        // {tId:one.tId,uId:one.uId,archived:false,aIdList:{$in:one.aIdList},limit:'unlimited'}) || [];
        const landQuery = {
            aId:one.aId,//{$in:one.aIdList},
            archived: false,limit: "unlimited",signFinish: true,
        }
        const list = await loanLandData.getByCondition(landQuery) || [];
        para.payAmount = Number(para.payAmount)*100;//专门为支付令而写的
        para.allArea = list.map(v=>v.lands)
            .reduce((r,v)=>r.concat(v),[])
            .reduce((r,v)=>r.add(new Decimal(v.area||0)),new Decimal(0))
            .toString();
        // para.allArea = list.reduce((r,v)=>r.add(new Decimal(v.area)),new Decimal('0')).toString();
        payTokenRequestInfoDic[one.action] && ( para.requestInfo = await payTokenRequestInfoDic[one.action].call(this,one));
        // one.action === 'generalBuy' && ( para.requestInfo = one.extendInfo && one.extendInfo.requestInfo || [] );
        // one.action === 'land' && ( para.requestInfo = await this.formatPayTokenInfoLandList(one) );
        return one;
    }

    // async formatPayTokenInfoLandList(one){
    //     const list = await cxwqCirculationFundReceiveData.getByCondition({fundReceiveId:one._id,archived: false,limit: "unlimited"});
    //     return Promise.all(list.map(async (v,i)=>{
    //         const fundReceiveInfo = {
    //             no:i+1,remark:'',//contractorNo:v.contractorNo,area:v.area,
    //             ...v,amount:v.amountYuan,
    //         };
    //         await this.formatPayTokenInfoLandTotal(fundReceiveInfo)
    //         return fundReceiveInfo;
    //     }));
    // }

    async formatPayTokenInfoLandTotal(fundReceiveInfo){
        const lands = await loanLandData.getByCondition( //为了兼容新老数据，所以没有使用cxwqId去关联
            {aId:fundReceiveInfo.aId,IDCard:fundReceiveInfo.idCard,archived:false,signFinish:true,limit:'unlimited'});
        fundReceiveInfo.contractorNo = lands.map(v=>v.contractorNo).join(',');
        fundReceiveInfo.area = lands.map(v=>v.area).reduce((r,v)=>r.add(new Decimal(v)),new Decimal(0)).toString();
    }

    undoAsync(done) {
        done()
    }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
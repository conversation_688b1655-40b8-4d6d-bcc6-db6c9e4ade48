'use strict';

const HANDLER_NAME = 'LoanApplicationFundReceiveListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    fundReceive:fundReceiveData,
    userVerifys:userVerifyData,
    employees:employeeData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const Decimal = require('decimal.js'),unitDenominator = 100,unitDenominator2 = 1000000;
const url = '/v1.0/loan/applications/fund/receive/amount/group/by/user';
const {COLLECT_COMPANY_TYPE_TEXT_MAP , COLLECT_COMPANY_TYPE_TEXT_LIST} = require('./Constants');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:condition,opts:{getOne}} = this.context;
        const result = await fundReceiveData.getByUrl(url,{...condition,onlyClosed:0});
        const [one] = result.list;

        await Promise.all(result.list.map(async (v,i)=>{
            const app = v.aId && await loanApplicationData.getById(v.aId);
            const areaInfo = app && await formatAreaCode.getFormatAreaCode(app.area);
            v.requestTypeName = COLLECT_COMPANY_TYPE_TEXT_MAP[v.type];
            v.areaInfo = {...areaInfo};
            v.allLoanedAmount && ( v.allLoanedAmount = new Decimal(v.allLoanedAmount)
                .div(unitDenominator2).toFixed(2, Decimal.ROUND_DOWN) );
            v.allPayAmount && ( v.allPayAmount = new Decimal(v.allPayAmount)
                .div(unitDenominator2).toFixed(2, Decimal.ROUND_DOWN) );
            v.allRemainderAmount && ( v.allRemainderAmount = new Decimal(v.allRemainderAmount)
                .div(unitDenominator2).toFixed(2, Decimal.ROUND_DOWN) );

        }));
        this.context.result = result;
        assert(!getOne || result.list.length ,'E_FUND_RECEIVE_DETAIL_001','record of this id not found')
        getOne && ( this.context.result = one );
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler

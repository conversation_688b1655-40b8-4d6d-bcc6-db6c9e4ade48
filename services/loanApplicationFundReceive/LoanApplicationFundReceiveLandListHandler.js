'use strict';

const HANDLER_NAME = 'LoanApplicationFundReceiveLandListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    fundReceive:fundReceiveData,
    userVerifys:userVerifyData,
    employees:employeeData,
    cxwqCirculations:cxwqCirculationData,
    cxwqCirculationFundReceive:cxwqCirculationFundReceiveData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');
const LoanApplicationFundReceiveFormatHandler = require('./LoanApplicationFundReceiveFormatHandler');
const moment = require('moment');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:condition,opts:{getOne,page}} = this.context;
        const {uId} = condition;
        // assert(uId,'E_APP_FUND_RECEIVE_001','uId is required');
        const result = await cxwqCirculationFundReceiveData.getListAndCountByCondition(condition);
        result.result = await Promise.all( result.result.map(async fundReceiveInfo=>
            Object.assign(await cxwqCirculationData.getById(fundReceiveInfo.cxwqId),{fundReceiveInfo,purpose:'土地流转费'})) );
        const handle = new LoanApplicationFundReceiveFormatHandler(),skip = Number( condition.skip ) || 0;
        await Promise.all( result.result.map((v,i)=>{
            v.createdTime = moment(v.createdTime).format('YYYY-MM-DD HH:mm:ss');
            v.lastModTime = moment(v.lastModTime).format('YYYY-MM-DD HH:mm:ss');
        }));
        await Promise.all( result.result.map((v,i)=>
            (v.no = skip + i + 1 , v.fundReceiveInfo)).map(handle.formatPayTokenInfoLandTotal));
        this.context.result = result
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
'use strict';

const HANDLER_NAME = 'LoanApplicationFundReceiveListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    fundReceive:fundReceiveData,
    userVerifys:userVerifyData,
    groupsV2: groupV2Data,
    employees:employeeData,
    employeeGroups:employeeGroupData,
} = require('../dataSvc/dataUtil');
const { getEmployeeLimit } = require('../../utils/getUserFromReq')

const {assert} = require('../../utils/general')
const aliOssSvc = require('../aliOssSvc');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:condition,opts,opts:{getOne,page}} = this.context;
        const {uId} = condition;
        const roleId = opts.role || opts.roleId;
        assert( opts.uId  , 'E_FUND_RECEIVE_DETAIL_0100' , 'permission denied !' )
        assert( roleId , 'E_FUND_RECEIVE_DETAIL_0101' , 'permission denied !!' )
        const areaCodeLimitOr = await getEmployeeLimit(employeeGroupData, condition.tId, opts.uId, 'requestAreaCode', roleId);
        condition['$or'] = areaCodeLimitOr;
        const eg = await employeeGroupData.getOneByCondition({ employee:opts.uId , group:roleId ,archived:false, tId:condition.tId, });
        assert( eg , 'E_FUND_RECEIVE_DETAIL_0101' , 'permission denied !!!' )
        const v2 = await groupV2Data.getById( eg.groupV2 );
        const childrenOrgId = ( await groupV2Data.getByCondition({ tId:condition.tId,archived:false, code:{ '$regex': `^${v2.code}`, '$options': 'si' }, limit:'unlimited'}) ).map(v=>v._id) ;//拿到子孙县域
        // debug('debug233',childrenOrgId,JSON.stringify({ tId:condition.tId,archived:false, code:{ '$regex': `^${v2.code}`, '$options': 'si' }, limit:'unlimited'}))
        v2._id !== '60111486fda3812feb51fd00' && ( condition["orgInfo.orgId"] = { $in:childrenOrgId } );
        condition.successful && condition.successful.$in && ( condition.successful.$in = condition.successful.$in.map(v=>Number(v)) );
        debug(`${HANDLER_NAME}AreaLimit a:`, opts.uId , eg._id , v2._id , JSON.stringify(condition));
        
        const mKey = page && 'postListAndCountByCondition' || 'postByCondition';
        const result = await fundReceiveData[mKey](condition);
        const list = page && result.result || result;
        const {realname:username} = uId && await userVerifyData.getOneByCondition({uId,"IDCardStatus": "approved","archived": false,}) || {};

        const formatOpts = this.context.opts.formatOpts = this.context.opts.formatOpts || {formatOss:1};
        this.context.opts.formatList = list;
        this.context.result = null;

        assert(!getOne || list.length ,'E_FUND_RECEIVE_DETAIL_001','record of this id not found')
        getOne && ( this.context.result = list[0],Object.assign( formatOpts , { amount:1,appList:1,payTokenPara:0}));
        page && ( this.context.result = result);
        this.context.result = this.context.result || {list,uId,username};
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
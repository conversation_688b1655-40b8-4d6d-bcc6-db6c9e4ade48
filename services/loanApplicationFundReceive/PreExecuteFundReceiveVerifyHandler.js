/**
 * @summary loanAppWorkflows
 * <AUTHOR>
 *
 * Created at     : 2018-12-13 16:51:35
 * Last modified  : 2018-12-13 17:44:55
 */

'use strict';

const HANDLER_NAME = 'PreExecuteFundReceiveVerifyHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const {BaseHandler} = require('nongfu.merchant.svcfw');

const moment = require('moment');
const Decimal = require('decimal.js');

const {assert} = require('../../utils/general')

const {
  loanApplicationOutLand: loanApplicationOutLandData,
  loanApplicationLandType: loanApplicationLandTypeData,
  loanApplication: loanApplicationData,
  LoanSvrLand: LoanSvrLandData,
  employeeGroups:employeeGroupsData,
  employees:employeeData,
  groups:groupsData,
  infoCollectHistory:infoCollectHistoryData,
  loanApplicationVerify:loanApplicationVerifyData,
  loanSupplement: loanSupplementData,
  userAddons:userAddonsData,
  userCxwqSupplement: userCxwqSupplementData,
  contractFlowsV2: contractFlowsV2Data,
  insOrder: insOrderData,
  fundReceive: fundReceiveData,
  loanData,
} = require('../dataSvc/dataUtil');

const config = {
  // wait_investigation_verify_2:{
  //   wait_investigation_verify_2:['RegistrationOperator'],
  //   wait_investigation_verify_3:['Supervisor'],
  //   wait_investigation_verify_4:['RegionalApprover'],
  // }
}

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {id,overwriteVerifyInfo,roleId:group,userId:employee,description,addonVerifyInfo} = this.context.input,{supplements} = overwriteVerifyInfo || {};
      const admin = employee && await employeeData.getById(employee, {cache : true , expire: 24 * 60 * 60 });
      const role = group && await groupsData.getById(group);
      process.env.DEBUG_NO_NEED_LOGIN || assert(role,'E_APPLICATION_VERIFY_04','不存在的管理员权限');
      process.env.DEBUG_NO_NEED_LOGIN || assert(admin,'E_APPLICATION_VERIFY_004','没有登陆！');

      if(!overwriteVerifyInfo)return done();
      const all = [].concat( ...overwriteVerifyInfo && overwriteVerifyInfo.supplements || [] )
        .concat( ...addonVerifyInfo && addonVerifyInfo.supplements || [] );
      //加上操作员信息
      all.forEach(v=> {
        v.createdTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
        v.operator = admin && admin._id;
        v.operatorName = admin && admin.username;
      });
      if(!overwriteVerifyInfo || !supplements)return done();

      const fundReceive = await fundReceiveData.getById(id);
      debug(method,'PreQueryPara1',id,fundReceive,config[fundReceive.status]);
      const cf = config[fundReceive.status] || {[fundReceive.status]:[role.name]}//如果没有配置，构建默认值
      fundReceive.verifyInfo = fundReceive.verifyInfo || {};
      debug(method,'PreQueryPara2',id,cf,role.name);
      const [key] = Object.entries(cf).find(([key,roleList])=>roleList.includes(role.name)) || [];
      assert(key,'E_APPLICATION_VERIFY_044','错误的角色');
      overwriteVerifyInfo.supplements = Object.assign( fundReceive.verifyInfo.supplements || {} , {[key]:{supplements,description}} )
      debug(method, 'result',{[key]:{supplements,description}},overwriteVerifyInfo.supplements);
      debug(method, '[Exit](success)');
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
};

module.exports = Handler;

'use strict';

const HANDLER_NAME = 'OutLandFundReceiveListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
    loanApplication:loanApplicationData,
    fundReceive:fundReceiveData,
    userVerifys:userVerifyData,
    employees:employeeData,
    loanLand:loanLandData,
    cxwqCirculations:cxwqCirculationData,
    cxwqCirculationFundReceive:cxwqCirculationFundReceiveData,
} = require('../dataSvc/dataUtil');
const {assert} = require('../../utils/general')
const moment = require('moment');
const formatAreaCode = require('../../persistence/formatAreaCode');
const aliOssSvc = require('../aliOssSvc');
const Decimal = require('decimal.js'),unitDenominator = 100;

const {
    actionNameDic,
    statusList,
    successfulNameDic,
    actionText,
    statusText,
    statusTextVerify,
} = require('./Constants');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
        const {input:{_id,skip,limit,no},opts:{getOne,uId,tId,formatOpts}} = this.context;

        assert(uId,'E_OUT_LAND_LIST_FUND_RECEIVE_001','uId is required');
        assert(tId,'E_OUT_LAND_LIST_FUND_RECEIVE_002','tId is required');
        assert(_id,'E_OUT_LAND_LIST_FUND_RECEIVE_003','_id is required');
        getOne && assert( no ,'E_OUT_LAND_LIST_FUND_RECEIVE_004','no is required');

        const fundReceive = await fundReceiveData.getById(_id);
        assert(fundReceive,'E_OUT_LAND_LIST_FUND_RECEIVE_004','_id is not exist');

        const outLandRequestInfo = fundReceive.extendInfo && fundReceive.extendInfo.outLandRequestInfo || [];
        outLandRequestInfo.forEach(v=>v.fundReceiveInfo = {...v})
        const start = parseInt(skip)||0,end = start + ( parseInt(limit)||10 );
        const list = outLandRequestInfo.slice(start,end);
        // const result = await cxwqCirculationFundReceiveData.getListAndCountByCondition(
        //     {...condition,archived:false,fundReceiveId:fundReceive._id});
        // debug('debug233',result.total,result.result);
        // const list = result.result = await Promise.all( result.result.map(async fundReceiveInfo=> //只要没有物理删除过数据，不可能为空
        //     Object.assign( await cxwqCirculationData.getById(fundReceiveInfo.cxwqId) , {fundReceiveInfo} ) ) );
        debug(`${HANDLER_NAME}Para2`,list.length ,list.map(v=>v._id));
        const result = {result:list,total:outLandRequestInfo.length},{outLandRequestVersion} = fundReceive.extendInfo || {};
        this.context.opts.formatOpts = {...formatOpts||{},formatOss:1};
        this.context.opts.formatList = list;
        this.context.result = null;

        assert(!getOne || list.length ,'E_OUT_LAND_FUND_RECEIVE_DETAIL_001','record of this id not found')
        getOne && ( this.context.result = list.find(v=>v.no===parseInt(no)));

        const all = fundReceive && await cxwqCirculationFundReceiveData.getByCondition(
            {fundReceiveId:fundReceive._id,archived: false,limit:'unlimited'}) || [];
        fundReceive && ( fundReceive.statusName = statusText[fundReceive.status] );

        this.context.result || ( this.context.result = {...result,outLandRequestVersion,fundReceive,} );
        debug(method, '[Exit](success)', this.context.result);
        return done();
    } catch (error) {
        debug.error(method, '[Exit](failed)', error);
        return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

async function formatImg(item) {

    if (item && item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0)
        item.thumbnail.url = await aliOssSvc.getFile({ fileName: item.thumbnail.url });
    if (item && item.image && item.image.url && item.image.url.indexOf('http') !== 0)
        item.image.url = await aliOssSvc.getFile({ fileName: item.image.url });
    if (item && item.url && item.url.indexOf('http') !== 0)
        item.url = await aliOssSvc.getFile({ fileName: item.url });
}

module.exports = Handler
'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:survey:index');
// const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const LoanApplicationFundReceiveListHandler = require("./LoanApplicationFundReceiveListHandler");
const LoanApplicationFundReceiveExportHandler = require("./LoanApplicationFundReceiveExportHandler");
const ExecuteFundReceiveVerifyHandler = require('./ExecuteFundReceiveVerifyHandler');
const LoanApplicationFundReceiveUserListHandler = require('./LoanApplicationFundReceiveUserListHandler');
const LoanApplicationFundReceiveFormatHandler = require('./LoanApplicationFundReceiveFormatHandler');
const LoanApplicationFundReceiveLandListHandler = require('./LoanApplicationFundReceiveLandListHandler');
const PreExecuteFundReceiveVerifyHandler = require('./PreExecuteFundReceiveVerifyHandler');
const OutLandFundReceiveListHandler = require("./OutLandFundReceiveListHandler");
const OutLandFundReceiveFormatHandler = require('./OutLandFundReceiveFormatHandler');
const LoanApplicationFundReceiveStatusListHandler = require('./LoanApplicationFundReceiveStatusListHandler');

const {addHandlersForService} = require('../../utils/general')


class Service {
  constructor() {
    addHandlersForService.call(this,debug);
  }

  fundReceiveList(){
    return [LoanApplicationFundReceiveListHandler,LoanApplicationFundReceiveFormatHandler]
  }

  fundReceiveExport(){
    return [LoanApplicationFundReceiveListHandler,LoanApplicationFundReceiveFormatHandler,LoanApplicationFundReceiveExportHandler];
  }

  executeFundReceiveVerify(){
    return [PreExecuteFundReceiveVerifyHandler,ExecuteFundReceiveVerifyHandler];
  }

  loanApplicationFundReceiveUserList(){
    return [LoanApplicationFundReceiveUserListHandler];
  }

  LoanApplicationFundReceiveLandList(){
    return [LoanApplicationFundReceiveLandListHandler];
  }

  outLandFundReceiveList(){
    return [OutLandFundReceiveListHandler,OutLandFundReceiveFormatHandler]
  }

  fundReceiveStatusListHandler(){
    return [LoanApplicationFundReceiveStatusListHandler]
  }


}

module.exports = new Service();
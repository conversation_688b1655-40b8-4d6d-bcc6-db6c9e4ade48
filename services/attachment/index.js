/**
 * attachment svc index
 * <AUTHOR>
 */

'use strict'

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('picc:eloan.mgr.app.api:services:attachment:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const UploadWXImageHandler = require('./uploadWXImageHandler');

class Server {
  constructor() {
  }

  async uploadWXImg(input, _opts) {
    let method = 'uploadWXImg';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new UploadWXImageHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}
module.exports = new Server();
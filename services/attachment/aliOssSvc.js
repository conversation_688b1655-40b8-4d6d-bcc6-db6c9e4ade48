/**
 * ali oss svc client
 * <AUTHOR>
 */

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('picc:eloan:mgr:app:api:service:attachment:aliOSSSvc');
const OSS = require('ali-oss');
const config = require('config');
const env = require('../env');
const passportSvc = require('../dataSvc/dataUtil').passport;
const agent = require('superagent');
const moment = require('moment');

class Svc {
  constructor() {
    this.client = null;
    this.name = 'aliOssSvc'
  }

  init() {
    this.client = new OSS({
      endpoint: config.get('aliOSS.endpoint'),
      accessKeyId: config.get('aliOSS.access_key'),
      accessKeySecret: config.get('aliOSS.access_key_secret'),
      region: 'oss-cn-hangzhou',
      bucket: config.get('aliOSS.bucket_pub'),
      secure: true
    });
  }

  /**
   * @param {*} fileName znrx/20190618/carrousel_1.jpg
   * @returns https://piccloan1.oss-cn-hzfinance.aliyuncs.com/znrx/20190618/carrousel_1.jpg?OSSAccessKeyId=LTAIZHfn7yYGdR4c&Expires=1560844101&Signature=LOFweNgrM5SP8c9tOWtahzXIIF0%3D
   */
  async getFile(fileName, opts = {}) {
    let method = `${this.name}.getFile`;
    debug(method, '[Enter]');
    try {
      if (!fileName) {
        debug(method, '[Exit](success)');
        return '';
      }
      opts.expires = 21600;
      let result = await this.client.signatureUrl(fileName, opts);
      // debug(method, '[Exit](success)', result);
      return result;
    } catch (err) {
      debug.error(method, '[Exit](failed)', err);
      throw err;
    }
  }

  /**
   * @param {*} path /adTmpFile/${input.userid}.png  data  str || buffer
   * @returns https://piccloan1.oss-cn-hzfinance.aliyuncs.com/znrx/20190618/carrousel_1.jpg?OSSAccessKeyId=LTAIZHfn7yYGdR4c&Expires=1560844101&Signature=LOFweNgrM5SP8c9tOWtahzXIIF0%3D
   */
  async put(path, data) {
    let method = `${this.name}.put`;
    debug(method, '[Enter]');
    try {

      // let objectName = env.getServerMode() === 'production' ? `znrx/${path}` : `znrx/${env.getServerMode()}/${path}`;
      // let objectName = `/${dir}/${fileName}`;
      let result = await this.client.put(path, data, { expires: 21600 });
      // debug(method, '[Exit](success)', result);
      return result;
    } catch (err) {
      debug.error(method, '[Exit](failed)', err);
      throw err;
    }
  }
  /**
   * @param {*} path /adTmpFile/${input.userid}.png  data  str || buffer
   * @returns https://piccloan1.oss-cn-hzfinance.aliyuncs.com/znrx/20190618/carrousel_1.jpg?OSSAccessKeyId=LTAIZHfn7yYGdR4c&Expires=1560844101&Signature=LOFweNgrM5SP8c9tOWtahzXIIF0%3D
   */
  async get(path, opts) {
    let method = `${this.name}.get`;
    debug(method, '[Enter]');
    try {
      // let objectName = env.getServerMode() === 'production' ? `znrx/${path}` : `znrx/${env.getServerMode()}/${path}`;
      let result = await this.client.get(path, opts);
      // debug(method, '[Exit](success)', result);
      return result;
    } catch (err) {
      debug.error(method, '[Exit](failed)', err);
      throw err;
    }
  }

  /**
   * @param {*} serverId wechat resource serverId
   * @param {*} fileName carrousel.jpg
   * @param {*} stream
   * @returns
   * {
   *    "name": "znrx/20190618/carrousel_1.jpg",
   *    "url": "https://piccloan1.oss-cn-hzfinance.aliyuncs.com/znrx/20190618/carrousel_1.jpg",
   *    "res": {
   *        "status": 200,
   *        "statusCode": 200,
   *        "statusMessage": "OK",
   *        "headers": {
   *            "server": "AliyunOSS",
   *            "date": "Tue, 18 Jun 2019 07:17:13 GMT",
   *            "content-length": "0",
   *            "connection": "keep-alive",
   *            "x-oss-request-id": "5D088FF8F283E2A9E286A91E",
   *            "etag": ""4EB76CD42C5C8AEA0D0997B4C97EE961"",
   *            "x-oss-hash-crc64ecma": "5941947997296721363",
   *            "content-md5": "Trds1CxciuoNCZe0yX7pYQ==",
   *            "x-oss-server-time": "96"
   *        },
   *        "size": 0,
   *        "aborted": false,
   *        "rt": 4230,
   *        "keepAliveSocket": false,
   *        "data": {
   *            "type": "Buffer",
   *            "data": [
   *            ]
   *        },
   *        "requestUrls": [
   *            "https://piccloan1.oss-cn-hzfinance.aliyuncs.com/znrx/20190618/carrousel_1.jpg"
   *        ],
   *        "timing": null,
   *        "remoteAddress": "***************",
   *        "remotePort": 443,
   *        "socketHandledRequests": 1,
   *        "socketHandledResponses": 1
   *    }
      }
   * @memberof Svc
   */
  async putStreamFromWx(serverId) {
    let method = `${this.name}.putStream`;
    debug(method, '[Enter]');
    try {
      let access_token = await passportSvc.getAccessToken('picc_eloan');
      let wx_url = `https://api.weixin.qq.com/cgi-bin/media/get?access_token=${access_token.accessToken}&media_id=${serverId}`;

      let res = await agent.get(wx_url);
      let fileBuffer = res.body;
      if (!fileBuffer || !res.header['content-disposition']) {
        throw {
          errorCode: 'E_OSS_WX_STREAM_104',
          httpCode: 500,
          reason: res.text || '微信图片获取失败'
        };
      }

      let contentLen = res.header['content-length'] || '1';
      let contentType = res.header['content-type'] || '';
      let fileName = res.header['content-disposition'] || '';
      debug(method, '[WX_FILE] ', fileBuffer)
      debug(method, '[WX_LEN] ', contentLen)
      debug(method, '[WX_TYPE] ', contentType)
      debug(method, '[WX_FILE_NAME] ', fileName)

      fileName = /".*"/.exec(fileName)[0].replace(/"/g, "");


      let dateStr = new Date().toISOString().substr(0, 10).replace(/-/g, '');
      let path = env.getServerMode() === 'production' ? `znrx/${dateStr.substr(0, 4)}/${dateStr.substr(4, 6)}` : `znrx/${env.getServerMode()}/${dateStr.substr(0, 4)}/${dateStr.substr(4, 6)}`;
      let objectName = `/${path}/${fileName}`;
      debug(method, '[ObjName] ', objectName);

      let result = await this.client.put(objectName, fileBuffer, { timeout: 5 * 60 * 1000 });
      result.size = contentLen;
      result.imgType = contentType;
      result.fileName = fileName;
      result.path = `${dateStr.substr(0, 4)}/${dateStr.substr(4, 6)}`;
      result.createdTime = moment().format('YYYY/MM/DD HH:mm:ss');
      result.fileType = fileName.substr(fileName.indexOf('.'));
      result.fullPath = path

      delete result.res;
      debug(method, '[Exit](success)', result);
      return result;
    } catch (err) {
      debug.error(method, '[Exit](failed)', err);
      if (err.code && err.code === 'ResponseTimeoutError') {
        throw {
          httpCode: 500,
          errorCode: 'E_OSS_TIMEOUT',
          reason: '提交信息超时，请再试一次'
        };
      }

      throw err;
    }
  }
}

const svc = new Svc();
svc.init();

module.exports = svc;
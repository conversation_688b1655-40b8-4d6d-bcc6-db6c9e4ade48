/**
 * uploadW<PERSON><PERSON>mageH<PERSON><PERSON>
 * <AUTHOR>
 */

'use strict';
const HANDLER_NAME = 'uploadWXImageHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('picc:eloan.mgr.app.api:services:attachment:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const ossSvc = require('./aliOssSvc');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {

      const input = self.context.input && self.context.input.imgs;

      let result = [];
      for (const item of input) {
        result.push({
          serverId: item.serverId
        });
      }

      let promises = [];
      for (let i = 0; i < input.length; i++) {
        const element = result[i];
        promises.push(
          ossSvc.putStreamFromWx(input[i].serverId).then(data => {
            element.result = data;
            return ossSvc.getFile(data && data.name || '');
          }).then(data => {
            element.url = data;
          })
        );
      }

      await Promise.all(promises);

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }
  undoAsync(done) {
    done()
  }
}

module.exports = Handler;

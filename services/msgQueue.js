/*
* @Author: wcy
* @Date:   2018-05-22 16:41:52
* @Last Modified by:   wcy
* @Last Modified time: 2018-05-22 17:57:48
*/
'use strict';

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const logger = logFactory(logUtil())('rongxin:loan.app.api:services:msgQueue');
const BaseMsgQueue = require('nongfu.merchant.msgfw').BaseMsgQueue;
const DataHelper = require('../services/dataSvc/dataUtil');
const smsQueuesData = DataHelper.msgQueue;

class MsgStore extends BaseMsgQueue {

	constructor() {
		super();
	}

	async init() {

	}

	async put(messages) {
		return smsQueuesData.post(messages);
	}

};

module.exports = new MsgStore();
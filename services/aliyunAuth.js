/**
 * z<PERSON><PERSON> auth svc
 * <AUTHOR>
 */

'use strict'

const logFactory = require('../utils/logFactory');
const logUtil = require('../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan:services:aliyunAuth');
const config = require('config');
const superagent = require('superagent');
const {
  passport: passportSvc,
  employees: employeesData,

} = require('./dataSvc/dataUtil');
const env = require('./env');
const moment = require('moment');

function getAuthorization(API_KEY, APPCODE) {
  const Authorization = `APPCODE ${APPCODE}`;
  return Authorization
}

class AliyunAuth {
  constructor() {
    this.appkey = null;
    this.apisecret = null;
    this.host = null;
    this.host2 = null;
    this.eidHost = null;
    this.ocrHost = null;
  }

  async authByIdentity(name, mobile, bankCard, cid) {
    let self = this;
    let method = 'authByIdentity';
    let path = "/lianzhuo/verifi";
    try {
      debug(method, '[Enter]');
      if (!name || !mobile || !bankCard || !cid) {
        throw {
          errorCode: 'E_AUTH_IDENTITY_029',
          reason: 'invalid input param'
        };
      }

      let condition = {
        acct_name: name,
        acct_pan: bankCard,
        cert_id: cid,
        phone_num: mobile
      };

      let Authorization = getAuthorization(self.appkey, self.appcode)

      let url = `${self.host}${path}`;
      debug(method, '[RPC-Start] url: ', url, Authorization);
      let authRes = await superagent.get(url).query(condition).set("Authorization", Authorization); //{body:{resp:{code:0}},data:{},text:'{x}'};// await superagent.get(url).set("Authorization", Authorization);
      debug(method, '[RPC-Success] text: ', authRes.text || '');

      let res = {
        body: authRes.body || {},
        receipt: authRes.text
      };
      debug(method, '[Exit](success)');
      return res;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //天眼数聚二三四要素
  async bankcard234(name, mobile, bankCard, cid) {
    let self = this;
    let method = 'bankcard234';
    let path = "bankcard234";
    try {
      debug(method, '[Enter]');
      if (!name || !cid) {
        throw {
          errorCode: 'E_AUTH_IDENTITY_029',
          reason: 'invalid input param'
        };
      }

      let condition = {
        name: name,
        idcard: cid,
      };
      if (mobile) {
        condition.mobile = mobile;
      }
      if (bankCard) {
        condition.bankcard = bankCard;
      }

      let Authorization = getAuthorization(self.appkey, self.appcode)

      let url = `${self.host2}${path}`;
      debug(method, '[RPC-Start] url: ', url, Authorization);
      let authRes = await superagent.get(url).query(condition).set("Authorization", Authorization); //{body:{resp:{code:0}},data:{},text:'{x}'};// await superagent.get(url).set("Authorization", Authorization);
      debug(method, '[RPC-Success] text: ', authRes.text || '');

      let res = {
        body: authRes.body || {},
        receipt: authRes.text
      };
      debug(method, '[Exit](success)');
      return res;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  // 实名认证
  async authIdentityByIDcard(name, cid) {
    let self = this;
    let method = 'authIdentityByIDcard';
    let path = "/eid/check";
    try {
      debug(method, '[Enter]');
      debug(method, '[name,cid]', name, cid);
      if (!name || !cid) {
        throw {
          errorCode: 'E_AUTH_IDENTITY_029',
          reason: 'invalid input param'
        };
      }

      let Authorization = getAuthorization(self.appkey, self.appcode)

      let url = `${self.eidHost}${path}`;
      debug(method, '[RPC-Start] url: ', url, Authorization);
      try {
        let authRes = await superagent.post(url).query({
          idcard: cid,
          name: name
        }).send().set("Authorization", Authorization);
        let res = {
          body: authRes.body || {},
          receipt: authRes.text
        };
        debug(method, '[RPC-Success] text: ', authRes.text || '');
        debug(method, '[Exit](success)');
        return res;
      } catch (error) {
        return error.response;
      }

    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  //身份证OCR
  async ocrPersonid(idcordImage) {
    let self = this;
    let method = 'ocrPersonid';
    let path = "ai-market/ocr/personid";
    try {
      debug(method, '[Enter]');
      if (!idcordImage) {
        throw {
          errorCode: 'E_AUTH_IDENTITY_029',
          reason: 'invalid input param'
        };
      }

      let Authorization = getAuthorization(self.appkey, self.appcode)

      let url = `${self.ocrHost}${path}`;
      debug(method, '[RPC-Start] url: ', url, Authorization);
      let authRes = await superagent.post(url)
        .set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
        .set("Authorization", Authorization)
        .type("form")
        .send({
          AI_IDCARD_IMAGE: idcordImage
        })
        .send({
          AI_IDCARD_IMAGE_TYPE: "0"
        })
        .send({
          AI_IDCARD_SIDE: "FRONT"
        })

      debug(method, '[RPC-Success] text: ', authRes.text || '');

      let res = {
        body: authRes.body || {},
        receipt: authRes.text
      };
      debug(method, '[Exit](success)');
      return res;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  /**
  * @param {*} serverId wechat resource serverId 
  * @param {*} fileName carrousel.jpg
  * @param {*} stream
  * @returns
  * {
  *    "name": "znrx/20190618/carrousel_1.jpg",
  *    "url": "https://piccloan1.oss-cn-hzfinance.aliyuncs.com/znrx/20190618/carrousel_1.jpg",
  *    "res": {
  *        "status": 200,
  *        "statusCode": 200,
  *        "statusMessage": "OK",
  *        "headers": {
  *            "server": "AliyunOSS",
  *            "date": "Tue, 18 Jun 2019 07:17:13 GMT",
  *            "content-length": "0",
  *            "connection": "keep-alive",
  *            "x-oss-request-id": "5D088FF8F283E2A9E286A91E",
  *            "etag": ""4EB76CD42C5C8AEA0D0997B4C97EE961"",
  *            "x-oss-hash-crc64ecma": "5941947997296721363",
  *            "content-md5": "Trds1CxciuoNCZe0yX7pYQ==",
  *            "x-oss-server-time": "96"
  *        },
  *        "size": 0,
  *        "aborted": false,
  *        "rt": 4230,
  *        "keepAliveSocket": false,
  *        "data": {
  *            "type": "Buffer",
  *            "data": [
  *            ]
  *        },
  *        "requestUrls": [
  *            "https://piccloan1.oss-cn-hzfinance.aliyuncs.com/znrx/20190618/carrousel_1.jpg"
  *        ],
  *        "timing": null,
  *        "remoteAddress": "***************",
  *        "remotePort": 443,
  *        "socketHandledRequests": 1,
  *        "socketHandledResponses": 1
  *    }
     }
  * @memberof Svc
  */
  async putStreamFromWx(serverId) {
    let method = `putStreamFromWx.putStream`;
    debug(method, '[Enter]');
    try {
      let urlpath = `/api/v1.0/wechat/access_token?app=rongxin`;
      let access_token = await passportSvc.getByUrl(urlpath);
      let wx_url = `https://api.weixin.qq.com/cgi-bin/media/get?access_token=${access_token.accessToken}&media_id=${serverId}`;
      debug(method, '[wxurl]', wx_url)
      let res = await superagent.get(wx_url);
      let fileBuffer = res.body;
      if (!fileBuffer || !res.header['content-disposition']) {
        throw {
          errorCode: 'E_OSS_WX_STREAM_104',
          httpCode: 500,
          reason: res.text || '微信图片获取失败'
        };
      }

      let contentLen = res.header['content-length'] || '1';
      let contentType = res.header['content-type'] || '';
      let fileName = res.header['content-disposition'] || '';
      debug(method, '[WX_FILE] ', fileBuffer)
      debug(method, '[WX_LEN] ', contentLen)
      debug(method, '[WX_TYPE] ', contentType)
      debug(method, '[WX_FILE_NAME] ', fileName)

      fileName = /".*"/.exec(fileName)[0].replace(/"/g, "");


      debug(method, '[Exit](success)', fileBuffer);
      return fileBuffer;
    } catch (err) {
      debug.error(method, '[Exit](failed)', err);
      if (err.code && err.code === 'ResponseTimeoutError') {
        throw {
          httpCode: 500,
          errorCode: 'E_OSS_TIMEOUT',
          reason: '提交信息超时，请再试一次'
        };
      }

      throw err;
    }
  }

  async limitEmployee(info){
    if(!info)return;
    const employee = await employeesData.getById(info.id, {cache : true , expire: 24 * 60 * 60 }),day = parseInt(moment(new Date()).format('YYYYMMDD'));
    let authRecordList = employee.authRecordList || [];
    const node = authRecordList.find(node => Object.entries(info).every(([key, val]) => node[key] === val));
    if (node && node.day === day && node.count >= 100) return false; //今天已经使用达到100次了
    //如果存在之前的节点，先摘出来，并自增次数
    node && ++node.count && authRecordList.splice(authRecordList.indexOf(node), 1);
    //如果有当前信息，并且还处于今日，沿用当前信息（前面已自增）；如果没有当前信息或者过了今日，重置日期并清零次数
    authRecordList.push(Object.assign(info, node || {}, !node || node.day !== day ? { day, count: 0 } : {}));
    await employeesData.putById(employee._id, { authRecordList })
    return true;
  }

  init() {
    debug('init [Enter]');
    let aliyunAuthSvcConfig = config.get('aliyunAuth');
    this.appkey = aliyunAuthSvcConfig.appkey;
    this.apisecret = aliyunAuthSvcConfig.apisecret;
    this.appcode = aliyunAuthSvcConfig.appcode;
    this.host = aliyunAuthSvcConfig.host;
    this.host2 = aliyunAuthSvcConfig.host2;
    this.eidHost = aliyunAuthSvcConfig.eidHost;
    this.ocrHost = aliyunAuthSvcConfig.ocrHost;
    debug('init [Exit](success) appkey: ', this.appkey, ' appcode: ', this.appcode, ' apisecret: ', this.apisecret, ' host2: ', this.host2, 'eidHost:', this.eidHost, 'ocrHost:', this.ocrHost);
  }
}


let aliyunAuthSvc = new AliyunAuth();
aliyunAuthSvc.init();
module.exports = aliyunAuthSvc;
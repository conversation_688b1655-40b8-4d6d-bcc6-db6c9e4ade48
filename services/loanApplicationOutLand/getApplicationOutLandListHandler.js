/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'GetApplicationOutLandListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const aliOssSvc = require('../aliOssSvc');
const formatAreaCode = require('../../persistence/formatAreaCode');

const {
  loanApplicationOutLand:loanApplicationOutLandData,
  loanApplicationLandType:loanApplicationLandTypeData,
  loanApplication:loanApplicationData,
  LoanSvrLand:LoanSvrLandData,
  insOrder:insOrderData,
  loanData,
} = require('../dataSvc/dataUtil');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {input} = this.context;

      const result = await loanApplicationOutLandData.getListAndCountByCondition(input);
      debug(method,'outLandListAndIds',result.result.map(it=>it.landId),input);
      // debug(method,'outLandListAndtTest',result.result[0].landId,await LoanSvrLandData.getById(result.result[0].landId));

      result.result = await Promise.all( result.result.map(async it=>{
        const areaInfo = await formatAreaCode.getFormatAreaCode( it.areaCode );//关联村名
        const one = await LoanSvrLandData.getById(it.landId);
        one.outlandId = it._id;
        one.areaInfo = areaInfo;
        Object.assign( it , one );
        return it;
      }) );
      // const landTypes = await loanApplicationLandTypeData.getByCondition({aId:input.aId,archived:false,sourceType:2});
      // await Promise.all( result.result.map(async it=>it.areaInfo=await formatAreaCode.getFormatAreaCode(it.areaCode) ) );//关联村名
      // result.result.forEach( it1=> it1.landType = landTypes.find(it2=>it2.uniqueCode === it1.sn) || null );
      debug(method,'outLandListAndGetSvr',result.result)

      const allImgNode = result.result.reduce( (res,it)=>res.concat( it.contractImageList ) , [] );
      await Promise.all( allImgNode.map( formatImg ).reduce( (r,v)=>r.concat(v),[] ) );
      await Promise.all( result.result.map( v=>formatImg(v.thumbnailImg) ).reduce( (r,v)=>r.concat(v),[] ) );

      debug(method, '[Exit](success)',result);
      this.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}


function formatImg(item) {
  if(!item)return []
  const promise=[];
  if (item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({fileName:item.thumbnail.url}).then(data => {
      item.thumbnail.url = data
    }));
  }
  if (item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({fileName:item.image.url}).then(data => {
      item.image.url = data
    }));
  }
  return promise;
}


module.exports = Handler;
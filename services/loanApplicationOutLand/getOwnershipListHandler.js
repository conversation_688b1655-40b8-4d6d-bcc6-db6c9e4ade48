'use strict';

const HANDLER_NAME = 'getOwnershipListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanApplication:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;

const ownershipList = [
  {
    "ownership":"10",
    "ownershipDisplay":"国有土地所有权"
  },
  {
    "ownership":"30",
    "ownershipDisplay":"集体土地所有权"
  },
  {
    "ownership":"31",
    "ownershipDisplay":"村民小组"
  },
  {
    "ownership":"32",
    "ownershipDisplay":"村级集体经济组织"
  },
  {
    "ownership":"33",
    "ownershipDisplay":"乡级集体经济组织"
  },
  {
    "ownership":"34",
    "ownershipDisplay":"其他农民集体经济组织"
  }
];

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      
      self.context.result = ownershipList;

      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
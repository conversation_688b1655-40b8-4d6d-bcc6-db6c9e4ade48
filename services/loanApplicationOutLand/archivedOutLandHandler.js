/**
 * CreateLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'CreateLoanApplicationOutLandHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplicationOutLand:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  loanApplicationOutLand:loanApplicationOutLandData,
  loanApplicationLandType:loanApplicationLandTypeData,
  loanApplication:loanApplicationData,
  LoanSvrLand:LoanSvrLandData,
  loanData,
} = require('../dataSvc/dataUtil');

// const APPLICATION_STATUS = require('../../utils/loanApplicationConst').APPLICATION_STATUS;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {id,archived} = this.context.input;

      const svrLand = await LoanSvrLandData.getById(id);
      svrLand.archived = true;
      svrLand.operator = this.context.opts.uId;
      svrLand.lastModTime = new Date();
      await LoanSvrLandData.putById(svrLand._id,svrLand);

      //功能已变，改为禁用
      const outLand = await loanApplicationOutLandData.getOneByCondition({landId:id});
      if( outLand ){
        outLand.disabled = true;
        outLand.archived = true;
        outLand.operator = this.context.opts.uId;
        outLand.lastModTime = new Date();
        await loanApplicationOutLandData.putById(outLand._id,outLand);
      }

      debug(method, '[Exit](success)');

      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplicationOutLand:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;

const ArchivedOutLandHandler = require('./archivedOutLandHandler');
const CreateOrEditOutLandHandler = require('./createOrEditOutLandHandler');
const GetOutLandListHandler = require('./getOutLandListHandler');
const GetApplicationOutLandListHandler = require('./getApplicationOutLandListHandler');
const GetOutLandDetailHandler = require('./getOutLandDetailHandler');
const GetOwnershipListHandler = require('./getOwnershipListHandler.js')
const FinishOutLandHandler = require('./finishOutLandHandler')
const SendOutLandSMSHandler = require('./sendOutLandSMSHandler');

class Service {
  constructor() {

  }

  async archivedOutLandHandler(input, _opts) {
    const method = 'archivedOutLand';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new ArchivedOutLandHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async finishOutLand(input, _opts) {
    const method = 'finishOutLand';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new FinishOutLandHandler(context));
      svcHandlerMgrt.addHandler(new SendOutLandSMSHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async createOrEditOutLand(input, _opts) {
    const method = 'createOrEditOutLand';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new CreateOrEditOutLandHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getOutLandList(input, _opts) {
    const method = 'getOutLandList';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetOutLandListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


  async getApplicationOutLandList(input, _opts) {
    const method = 'getApplicationOutLandList';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetApplicationOutLandListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getOutLandDetail(input, _opts) {
    const method = 'getOutLandDetail';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetOutLandDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getOwnershipList(input, _opts) {
    const method = 'getOwnershipList';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetOwnershipListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


}

module.exports = new Service();
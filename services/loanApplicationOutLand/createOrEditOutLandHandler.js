/**
 * CreateLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'CreateLoanApplicationOutLandHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplicationOutLand:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const aliOssSvc = require('../aliOssSvc');
const {
  loanApplicationOutLand:loanApplicationOutLandData,
  loanApplicationLandType:loanApplicationLandTypeData,
  loanApplication:loanApplicationData,
  LoanSvrLand:LoanSvrLandData,
  insOrder:insOrderData,
  loanData,
} = require('../dataSvc/dataUtil');
const formatAreaCode = require('../../persistence/formatAreaCode');

// const APPLICATION_STATUS = require('../../utils/loanApplicationConst').APPLICATION_STATUS;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {land,extendInfo} = this.context.input, {uId,tId} = this.context.opts;
      if( !land._id && ( !land.areaCode || !land.areaCode.match(/^\d+$/) ) ){
        throw {
          errorCode: 'E_OUTLAND_010',
          httpCode: 406,
          reason: '地块编码有误'
        }
      }
      // const loanApplication = land.aId && ( await loanApplicationData.getById(land.aId) ) || null;
      if( !land._id ){
        land.area = land.area || 0;
        // land.areaCode = land.areaCode || ( loanApplication && loanApplication.area ) || ''
        land.name = land.name || '';
        land.desc = land.desc || '';
        land.archived = false;
        land.creator = uId;
        land.tId = land.tId || tId;
        const {codeSn} = await insOrderData.getByUrl("/v1.0/ins/counter/inc", { id: land.areaCode })
        land.sn = codeSn;
      }
      land.lastModTime = new Date();
      const [coors] = typeof land.vertexList === 'object' && land.vertexList.type === 'Polygon'
                        && Array.isArray(land.vertexList.coordinates ) && land.vertexList.coordinates || [];
      // right land.vertexList format : {type:'Polygon',coordinates:[[[lng1,lat1],[lng2,lat2],...]]}
      if( !Array.isArray(coors) || coors.length === 0 ||
          !coors.every( v=>Array.isArray(v) && v.length === 2 && v.every(vv=>!isNaN(vv)) ) ) {
        throw {
          errorCode: 'E_OUT_LAND_005',
          httpCode: 406,
          reason: 'vertexList format is error! must be GeoJson and type is Polygon'
        }
      }

      // land.operator = this.context.opts.uId;
      // land._id && ( land.archived = false );
      land.operator = uId;
      land.contractImageList = land.contractImageList || [];
      const {_id,sn} = land;
      land._id && ( await LoanSvrLandData.putById(land._id,land) );
      land._id || ( await LoanSvrLandData.post(land) );
      // const {sn} = this.context.result;
      debug('SvrLandAdd1',_id,sn);
      const detail = this.context.result = _id ? await LoanSvrLandData.getById(_id) : await LoanSvrLandData.getOneByCondition({sn})

      // outLand.areaInfo=await formatAreaCode.getFormatAreaCode(outLand.areaCode)
      await Promise.all( land.contractImageList.map( formatImg ).reduce((r, v)=>r.concat(v),[]) );
      await Promise.all( formatImg( land.thumbnailImg ) );

      debug('SvrLandAdd2',this.context.result);
      //上线时间较紧，先写在此处，待迁到别的service
      if( extendInfo ){
        let outLand = ( await loanApplicationOutLandData.getOneByCondition({landId:this.context.result._id}) ) || {
          landId : this.context.result._id,
          disabled:false,
        };
        outLand.landId = this.context.result._id;
        outLand.tId = land.tId,
        outLand.area = land.area,
        outLand.areaCode = land.areaCode,
        outLand.lastModTime = new Date();
        outLand.operator = uId;
        outLand.aId = typeof extendInfo.aId === 'undefined' ? outLand.aId || null : extendInfo.aId || null;
        outLand.uId = typeof extendInfo.uId === 'undefined' ? outLand.uId || null : extendInfo.uId || null;
        outLand.groupId = typeof extendInfo.groupId === 'undefined' ? outLand.groupId || null : extendInfo.groupId || null;
        outLand.cropType = typeof extendInfo.cropType === 'undefined' ? outLand.cropType || null : extendInfo.cropType || null;
        outLand.otherInfo = extendInfo.otherInfo || null;

        const loanApplication = outLand.aId && await loanApplicationData.getById( outLand.aId );
        const now = new Date(),year = now.getMonth() > 9 ? now.getFullYear() + 1 : now.getFullYear(),circulationEndDate = `${year}-10-31`;
        outLand.appAreaCode = ( loanApplication && loanApplication.area ) || ( outLand.areaCode = outLand.areaCode ||  '' );
        outLand.occupyInfo = outLand.occupyInfo || { circulationEndDate , hadReleased:'0' };
        outLand.inputType = '2';
        outLand.sn = detail.sn;
        outLand.areaCode = outLand.areaCode || detail.sn.substr(0,12);

        debug('SvrLandExtendInfo',outLand);
        outLand._id && ( await loanApplicationOutLandData.putById(outLand._id,outLand) );
        outLand._id || ( outLand = await loanApplicationOutLandData.post(outLand) );
        detail.outlandId = outLand._id;
      }

      debug(method, '[Exit](success)');

      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

function formatImg(item) {
  if( !item )return [];
  const promise=[];
  if (item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({fileName:item.thumbnail.url}).then(data => {
      item.thumbnail.url = data
    }));
  }
  if (item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({fileName:item.image.url}).then(data => {
      item.image.url = data
    }));
  }
  return promise;
}




module.exports = Handler;
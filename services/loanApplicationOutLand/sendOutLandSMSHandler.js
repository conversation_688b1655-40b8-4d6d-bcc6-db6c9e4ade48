'use strict';

const HANDLER_NAME = 'sendOutLandSMSHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplicationOutLand:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const MsgSvcRegistry = require('nongfu.merchant.msgfw').MsgSvcRegistry.INSTANCE;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;
const smsALiYunDispatcher = require('../messages/dispatchers/smsALiYunDispatcher');

let templateId = "SMS_461530526";

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    const method = `${this.getName()}.doAsync`;
    debug(method, '[Enter]');

    try {
      let input = self.context.input;

      const application = await loanApplicationData.getById(input.aId);
      if (!application || !application._id) {
        throw {
          errorCode: 'E_SEND_SMS_037',
          httpCode: 406,
          reason: '订单不存在'
        }
      }

      let SmsALiYunDispatcher = MsgSvcRegistry.getDisptcher(smsALiYunDispatcher.QNAME);
      let content = {
        templateid: templateId,
        signName: "黑龙江省创新农业物权",
        caller: "rongxin_userapp"
      };
      await SmsALiYunDispatcher.send_Sms(application.userMobile, content, {})
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
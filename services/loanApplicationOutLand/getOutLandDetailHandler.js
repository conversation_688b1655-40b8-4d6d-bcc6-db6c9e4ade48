/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'GetOutLandListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const aliOssSvc = require('../aliOssSvc');
const formatAreaCode = require('../../persistence/formatAreaCode');

const {
  loanApplicationOutLand:loanApplicationOutLandData,
  loanApplicationLandType:loanApplicationLandTypeData,
  loanApplication:loanApplicationData,
  LoanSvrLand: LoanSvrLandData,
  employees:employeesData,
  user:userData,
} = require('../dataSvc/dataUtil');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let {id,outlandId} = this.context.input;

      let extendInfo = outlandId && await loanApplicationOutLandData.getById( outlandId );
      extendInfo && ( id = extendInfo.landId );
      const outLand = await LoanSvrLandData.getById( id );

      // outLand.areaInfo=await formatAreaCode.getFormatAreaCode(outLand.areaCode)
      await Promise.all( ( outLand.contractImageList || [] ).map( formatImg ).reduce((r,v)=>r.concat(v),[]) );
      await Promise.all( formatImg( outLand.thumbnailImg ) );
      outLand.areaInfo = await formatAreaCode.getFormatAreaCode( outLand.areaCode );//关联村名

      outLand.postion = outLand.vertexList.coordinates[0].map( ([lng,lat])=>({lng,lat}) );
      outLand.user = outLand.uId && await userData.getById(outLand.uId) || null;
      outLand.employee = outLand.creator && await employeesData.getById(outLand.creator, {cache : true , expire: 24 * 60 * 60 }) || null;

      extendInfo = extendInfo || await loanApplicationOutLandData.getOneByCondition({landId:outLand._id});
      outLand.extendInfo = extendInfo;
      debug(method, '[Exit](success)');
      this.context.result = outLand;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}


function formatImg(item) {
  if( !item )return [];
  const promise=[];
  if (item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({fileName:item.thumbnail.url}).then(data => {
      item.thumbnail.url = data
    }));
  }
  if (item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({fileName:item.image.url}).then(data => {
      item.image.url = data
    }));
  }
  return promise;
}


module.exports = Handler;
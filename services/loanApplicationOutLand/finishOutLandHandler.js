'use strict';

const HANDLER_NAME = 'FinishOutLandHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplicationOutLand:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const {
  loanApplication: loanApplicationData,
  loanApplicationOutLand: loanApplicationOutLandData
} = require('../dataSvc/dataUtil');
const _ = require('lodash');
const landConfirmStatus = require('../../data/land_confirm_status');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`;
    let self = this;
    debug(method, '[Enter]')
    try {
      const input = this.context.input;
      const { uId } = this.context.opts;

      const application = await loanApplicationData.getById(input.aId);
      if (!application || !application._id) {
        throw {
          errorCode: 'E_FINISH_OUT_LAND_034',
          httpCode: 406,
          reason: '订单不存在'
        }
      }

      const _outLands = await loanApplicationOutLandData.getByCondition({ aId: input.aId, archived: false });
      if ((_outLands || []).length === 0) {
        throw {
          errorCode: 'E_FINISH_OUT_LAND_044',
          httpCode: 406,
          reason: '未查到地块绘制信息'
        }
      }

      const outLand = _.get(application, 'verifyInfo.outLand', { actions: [] });
      if (outLand.status == landConfirmStatus.WAIT_CONFIRM || outLand.status == landConfirmStatus.AGREED_CONFIRM || outLand.status == landConfirmStatus.FINISHED) {
        throw {
          errorCode: 'E_FINISH_OUT_LAND_052',
          httpCode: 406,
          reason: '已结束绘制'
        }
      }

      outLand.status = landConfirmStatus.WAIT_CONFIRM;
      outLand.operator = uId;
      outLand.actions.push({
        operator: uId,
        action: landConfirmStatus.WAIT_CONFIRM,
        createdTime: new Date(),
        comment: '结束所有绘制，待用户确认'
      });
      const result = await loanApplicationData.putById(input.aId, { 'verifyInfo.outLand': outLand });
      self.context.result = result
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
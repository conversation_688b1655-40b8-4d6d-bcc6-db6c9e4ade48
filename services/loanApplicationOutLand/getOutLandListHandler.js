/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'GetOutLandListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanSupplement:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const aliOssSvc = require('../aliOssSvc');
const formatAreaCode = require('../../persistence/formatAreaCode');

const {
  loanApplicationOutLand:loanApplicationOutLandData,
  loanApplicationLandType:loanApplicationLandTypeData,
  loanApplication:loanApplicationData,
  LoanSvrLand:LoanSvrLandData,
  insOrder:insOrderData,
  employeeGroups:employeeGroupData,
  groupsV2:groupsV2Data,
} = require('../dataSvc/dataUtil');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const {input,opts} = this.context;

      // opts.roleId = '60c029b89bb57cc49605f13e';//mgr限定只有测绘员类型可以看
      const employeesCondition = {employee:opts.uId};
      opts.roleId && ( employeesCondition.group = opts.roleId );
      // opts.groupV2 && ( employeesCondition.groupV2 = opts.groupV2 );
      const employees = employeesCondition.employee && Object.keys(employeesCondition).length && await employeeGroupData.getByCondition(employeesCondition) || [];
      const groupV2 = opts.groupV2 && await groupsV2Data.getById(opts.groupV2);
      debug(method,'findEmployeesBy1',JSON.stringify(employeesCondition),employees);
      debug(method,'findEmployeesBy2',opts.groupV2,groupV2);
      // if( employees.length === 0 ){
      //   this.context.result = [];
      //   return done();
      // }
      const areaLimit = groupV2 && groupV2.areaList || employees.reduce((r,v)=>r.concat(v.areaList||[]),[]);
      if( areaLimit.length === 0 ){
        this.context.result = [];
        return done();
      }
      input['$or'] = areaLimit.map( code=> ({"areaCode":{'$regex': `^${code}`,'$options': 'si' }}) );
      debug(method,'findEmployeesBy3',JSON.stringify(input['$or']),employees);

      input['$or'] = JSON.stringify(input['$or']  );
      debug(method,'findLands',JSON.stringify(input));
      const query = {theRxApiParams:JSON.stringify(input),theRxApiParamsIsJson: 'YES'}
      const result = await LoanSvrLandData.getListAndCountByCondition(query);
      // const landTypes = await loanApplicationLandTypeData.getByCondition({aId:input.aId,archived:false,sourceType:2});
      // await Promise.all( result.result.map(async it=>it.areaInfo=await formatAreaCode.getFormatAreaCode(it.areaCode) ) );//关联村名
      // result.result.forEach( it1=> it1.landType = landTypes.find(it2=>it2.uniqueCode === it1.sn) || null );

      const allImgNode = result.result.reduce( (res,it)=>res.concat( it.contractImageList ) , [] );
      await Promise.all( allImgNode.map( formatImg ).reduce( (r,v)=>r.concat(v),[] ) );
      await Promise.all( result.result.map( v=>formatImg(v.thumbnailImg) ).reduce( (r,v)=>r.concat(v),[] ) );
      result.result.forEach(it=>{
          it.contractImageList = it.contractImageList || [];
          it.postion=it.vertexList.coordinates[0].map( ([lng,lat])=>({lng,lat}) );
      });
      result.result = result.result.filter(it=>( it.postion || [] ).length > 0 )
          .filter(it=>it.postion.every( ({lng,lat})=>[lng,lat].every( val=>val > 200 ) ) );

      debug(method, '[Exit](success)',result);
      this.context.result = result;
      opts.removeEmpty && ( this.context.result.result
          = this.context.result.result.filter(v=>v.vertexList.coordinates[0].length) )
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}


function formatImg(item) {
  if(!item)return []
  const promise=[];
  if (item.thumbnail && item.thumbnail.url && item.thumbnail.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({fileName:item.thumbnail.url}).then(data => {
      item.thumbnail.url = data
    }));
  }
  if (item.image && item.image.url && item.image.url.indexOf('http') !== 0) {
    promise.push(aliOssSvc.getFile({fileName:item.image.url}).then(data => {
      item.image.url = data
    }));
  }
  return promise;
}


module.exports = Handler;
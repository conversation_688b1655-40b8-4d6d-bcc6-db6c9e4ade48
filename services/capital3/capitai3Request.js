/*
 * @Description: 三资得请求接口封装
 * @Author: zhu xue song
 * @Date: 2021-12-23 10:22:23
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-12-23 13:11:35
 * @FilePath: \rongxin.loan.mgr.app.api\services\capital3\capitai3Request.js
 */
const HANDLER_NAME = 'capitai3Request'
const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())(
  'rongxin:loan.mgr.app.api:services:capital3:' + HANDLER_NAME
)
const superagent = require('superagent')
// const config = require('config');
// const host = 'https://txgq.signingeye.cn:9978';
// const cgbc_java_host = config.get('guangfa_ai.host');
const { SMCapital3 } = require('../../utils/smUtil')

/**
 * 三资sm4加密
 */
async function encrypt(body) {
  try {
    return SMCapital3.sm4Encrypt(body)
  } catch (err) {
    throw err
  }
}
/**
 * 三资sm4解密
 */
async function decrypt(body) {
  try {
    return SMCapital3.sm4Decrypt(body)
  } catch (err) {
    throw err
  }
}

module.exports = {
  // --------------------  四川省合作社  --------------------
  /**
   * 成员产品交易表
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - jjzzCode:     string | 合作社编码
      - searchValue:  string | 搜索条件
      - pageNum:      number | 当前页,从0开始
      - pageSize:     number | 分页大小
   * @returns JSON
   */
  getMemberProductTradeForm: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/cycpjyb`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },

  /**
   * 成员账户统计表
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - year:         string | 查询年份，格式: yyyy
      - jjzzCode:     string | 合作社编码
      - searchValue:  string | 搜索条件
      - pageNum:      number | 当前页,从0开始
      - pageSize:     number | 分页大小
   * @returns JSON
   */
  getMemberAccoutStatisticsForm: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/cyzhtjb`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 收支明细表
   *
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - issue:     string | 账期，格式: yyyyMM
      - jjzzCode:  string | 合作社编码
   * @returns JSON
   */
  getMoneyDetailForm: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/szmxb`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 查询token的用户拥有的经济组织列表
   *
   * @param {String} authorization 我方token
   * @returns JSON
   */
  getTokenUserEconomicOrganizationList: async (host, authorization) => {
    try {
      debug(HANDLER_NAME, 'params', authorization)
      const res = await superagent
        .post(`${host}/api/chungeng/app/jjzz`)
        .set('Content-type', 'application/x-www-form-urlencoded')
        .set('Authorization', 'Bearer ' + authorization)

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },

  /**
   * 资产负债表
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - issue:     string | 账期，格式: yyyyMM
      - jjzzCode:  string | 合作社编码
   * @returns JSON
   */
  getAssertsLiabilitiesForm: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/zcfzb`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },

  // --------------------  通河｜依兰 银农直连  --------------------
  /**
   * 查询token拥有的账号
   * 
   * @param {String} authorization 我方token
   * @returnS Object {
      "code": 0,
      "message": "",
      "result": {
        "": ""
      },
      "otherData": {}
    }
   */
  getAccountsListWithToken: async (host, authorization) => {
    try {
      const res = await superagent
        .post(`${host}/api/chungeng/app/ynzl/accounts`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 审批列表
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - status:     number | 审批状态，0-待审批，1-已审批，默认 待审批
      - startDate:  string | 开始时间
      - endDate:    string | 结束时间
      - start:      number | 分页-起始行，0,10,20,30
      - length:     number | 分页大小，默认10
      - account:    string | 账号 ｜ required
   * @returns JSON
   */
  getAuditList: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/ynzl/pageAuditing`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 审批详情
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - refundId:   number | 申请ID ｜ required
      - account:    string | 账号 ｜ required
   * @returns JSON
   */
  getAuditDetail: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/ynzl/auditDetail`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 查询审批的附件信息
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - streamCode: string | 申请单号 ｜ required
      - account:    string | 账号 ｜ required
   * @returns JSON
   */
  getAssertAttachments: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/ynzl/getAttachments`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 获取审批流程信息
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - streamCode: string | 申请单号 ｜ required
      - account:    string | 账号 ｜ required
   * @returns JSON
   */
  getAuditFlowing: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/ynzl/auditFlowing`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 获取文件流
   * 
   * @param {String} authorization 我方token
   * @param {JSON} query 请求参数
      - attachId: string | 申请单号 | required
   * @returns JSON
   */
  getFilestream: async (host, authorization, query, res) => {
    try {
      const result = await superagent
        .get(`${host}/api/chungeng/app/ynzl/show`)
        .set('Authorization', 'Bearer ' + authorization)
        .query(query)
        result.pipe(res)
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 拒绝审批
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - streamCode: string | 申请单号 ｜ required
      - opinion:    string | 审核意见 ｜ required
      - account:    string | 账号 ｜ required
   * @returns JSON
   */
  modifyAuditReject: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/ynzl/reject`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 通过审批
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - streamCode: string | 申请单号 ｜ required
      - opinion:    string | 审核意见 ｜ required
      - account:    string | 账号 ｜ required
   * @returns JSON
   */
  modifyAuditPass: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/ynzl/pass`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 成员列表分页查询
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - jjzzCode:     string | 合作社编码 ｜ required
      - searchValue:  string | 成员姓名   ｜ optional
      - pageNum:      Number | 页码      ｜ optional ｜ default: 0
      - pageSize:     Number | 分页大小   ｜ optional ｜ default: 20
   * @returns JSON
   */
  getMemberList: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/cypage`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
  /**
   * 成员账户列表
   * 
   * @param {String} authorization 我方token
   * @param {JSON} body 请求参数
      - jjzzCode:   string | 合作社编码       ｜ required
      - year:       string | 年度(格式: yyyy) ｜ required
      - memberCode: string | 成员编码         ｜ required
   * @returns JSON
   */
  getMemberAccountList: async (host, authorization, body) => {
    try {
      const secretData = await encrypt(body)
      const res = await superagent
        .post(`${host}/api/chungeng/app/cyzhb`)
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer ' + authorization)
        .send({ data: secretData })

      let data = null
      if (res && res.body) data = res.body.data
      const result = !data || (await decrypt(data))
      debug(HANDLER_NAME, '[Exit](success)', result)
      return result
    } catch (err) {
      throw {
        errorCode: err.code + '' || '406',
        httpCode: err.code || 406,
        reason: err.message || '',
      }
    }
  },
}

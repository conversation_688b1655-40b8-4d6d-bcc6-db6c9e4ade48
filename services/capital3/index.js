/*
 * @Description: 访问三资系统
 * @Author: zhu xue song
 * @Date: 2021-11-04 16:53:47
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-11-05 14:45:47
 * @FilePath: \rongxin.loan.mgr.app.api\services\capital3\index.js
 */
'use strict'

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())(
  'rongxin:loan.mgr.app.api:services:capital3:index'
)
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt
const getMemberProductTradeFormHandler = require('./getMemberProductTradeFormHandler')
const getMemberAccoutStatisticsFormHandler = require('./getMemberAccoutStatisticsFormHandler')
const getMoneyDetailFormHandler = require('./getMoneyDetailFormHandler')
const getTokenUserEconomicOrganizationListHandler = require('./getTokenUserEconomicOrganizationListHandler')
const getAssertsLiabilitiesFormHandler = require('./getAssertsLiabilitiesFormHandler')

const getAccountsListWithTokenHandler = require('./getAccountsListWithTokenHandler')
const getAssertAttachmentsHandler = require('./getAssertAttachmentsHandler')
const getAuditDetailHandler = require('./getAuditDetailHandler')
const getAuditFlowingHandler = require('./getAuditFlowingHandler')
const getAuditListHandler = require('./getAuditListHandler')
const getFilestreamHandler = require('./getFilestreamHandler')
const modifyAuditPassHandler = require('./modifyAuditPassHandler')
const modifyAuditRejectHandler = require('./modifyAuditRejectHandler')

const getMemberListHandler = require('./getMemberListHandler')
const getMemberAccountListHandler = require('./getMemberAccountListHandler')

class Service {
  constructor() {}
  // --------------------  四川省合作社  --------------------
  async getMemberProductTradeForm(input, _opts) {
    let method = 'getMemberProductTradeForm'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getMemberProductTradeFormHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  async getMemberAccoutStatisticsForm(input, _opts) {
    let method = 'getMemberAccoutStatisticsForm'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(
        new getMemberAccoutStatisticsFormHandler(context)
      )
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  async getMoneyDetailForm(input, _opts) {
    let method = 'getMoneyDetailForm'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getMoneyDetailFormHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  async getTokenUserEconomicOrganizationList(input, _opts) {
    let method = 'getTokenUserEconomicOrganizationList'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(
        new getTokenUserEconomicOrganizationListHandler(context)
      )
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  async getAssertsLiabilitiesForm(input, _opts) {
    let method = 'getAssertsLiabilitiesForm'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getAssertsLiabilitiesFormHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  // --------------------  通河｜依兰 银农直连  --------------------
  /** 查询token拥有的账号 */
  async getAccountsListWithToken(input, _opts) {
    let method = 'getAccountsListWithToken'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getAccountsListWithTokenHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  /** 审批列表 */
  async getAuditList(input, _opts) {
    let method = 'getAuditList'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getAuditListHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  /** 审批详情 */
  async getAuditDetail(input, _opts) {
    let method = 'getAuditDetail'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getAuditDetailHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  /** 查询审批的附件信息 */
  async getAssertAttachments(input, _opts) {
    let method = 'getAssertAttachments'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getAssertAttachmentsHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  /** 获取审批流程信息 */
  async getAuditFlowing(input, _opts) {
    let method = 'getAuditFlowing'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getAuditFlowingHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  /** 获取文件流 */
  async getFilestream(input, _opts) {
    let method = 'getFilestream'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getFilestreamHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  /** 拒绝审批 */
  async modifyAuditReject(input, _opts) {
    let method = 'modifyAuditReject'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new modifyAuditRejectHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  /** 通过审批 */
  async modifyAuditPass(input, _opts) {
    let method = 'modifyAuditPass'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new modifyAuditPassHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  async getMemberList(input, _opts) {
    let method = 'getMemberList'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getMemberListHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }

  async getMemberAccountList(input, _opts) {
    let method = 'getMemberAccountList'
    debug(method, '[Enter]')

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {},
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt()
      svcHandlerMgrt.addHandler(new getMemberAccountListHandler(context))
      await svcHandlerMgrt.processAsync(context)
      debug(method, '[Exit](success): ', context.result)
      return context.result
    } catch (error) {
      debug.error(method, '[Exit](failed)', error)
      throw error
    }
  }
}

module.exports = new Service()

/*
 * @Description: 成员账户统计表
 * @Author: zhu xue song
 * @Date: 2021-12-24 10:53:47
 * @LastEditors: zhu xue song
 * @LastEditTime: 2021-11-05 15:25:59
 * @FilePath: \rongxin.loan.mgr.app.api\services\capital3\synchronizationHandler.js
 */
/**
 * 
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'getMemberAccoutStatisticsFormHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:capital3:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const capitai3Request = require('./capitai3Request');

const clientData = require('../dataSvc/dataUtil').clients
const tenantData = require('../dataSvc/dataUtil').tenants
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { token, client, year, jjzzCode, searchValue, skip = 0, limit = 20 } = self.context.input;

      const clientInfo = await clientData.getById(client, {cache : true , expire: 24 * 60 * 60 });
      if(!clientInfo || !clientInfo.tId) {
        throw {
          errorCode: 'E_CAPTAL3_233',
          httpCode: 406,
          reason: 'client error'
        }
      }
      const tenantInfo = await tenantData.getById(clientInfo.tId, {cache : true , expire: 24 * 60 * 60 });
      if(!tenantInfo || !tenantInfo.capitalUrl) {
        throw {
          errorCode: 'E_CAPTAL3_234',
          httpCode: 406,
          reason: 'tenant error'
        }
      }
      const body = {
        year,
        jjzzCode,
        searchValue,
        pageNum: parseInt(skip / limit),
        pageSize: limit
      };
      const result = await capitai3Request.getMemberAccoutStatisticsForm(tenantInfo.capitalUrl, token, body)
      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
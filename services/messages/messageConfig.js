'use strict'

exports.DispatcherNames = {
  Rongxin_System: 'Rongxin_System'
};

exports.MessageTypes = {
  Rongxin_Internal: 'Rongxin:Internal',
  Rongxin_Push_Text: 'Rongxin:Push:Text'
};

exports.EnvironmentMark = {
  development: 'Local',
  test: 'Test',
  stage: 'Stage'
};

exports.MessageFroms = {
  Rongxin_Platform_SystemSpace_LoanApplicationModule: 'Rongxin:Platform:SystemSpace:LoanApplicationModule',
  Rongxin_Platform_SystemSpace_EmployeeModule: 'Rongxin:Platform:SystemSpace:EmployeeModule'
}
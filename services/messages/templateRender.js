/**
 * template render class
 * <AUTHOR>
 * 2017-09-17
 */

'use strict'
const CLASS_NAME = 'templateRender';
const logUtil = require('../../utils/logUtil');
const logFactory = require('../../utils/logFactory');
const debug = logFactory(logUtil())('nongfu:connect.api:services:message:' + CLASS_NAME);
const internalTemplates = require('./templates/internalTemplates').internal_templates;
const pushTemplates = require('./templates/PushTemplates').push_templates;

class TemplateRender {
  constructor() {

  }

  /**
   * render internal template
   * @param {*} templateId 
   * @param {*} replacement : {
   *                             title: ['String'], //replace params array
   *                             text: ['String']
   *                             url: ['String']        
   *                          } 
   */
  redenerIternalTemplate(templateId, replacement) {
    let method = 'redenerIternalTemplate';
    debug(method, '[Enter]');

    try {
      if (!templateId)
        throw {
          httpCode: 500,
          errorCode: 'ETEMPRENDERPARAM037',
          reason: 'invalid param: [templateId]'
        }
      //深拷贝，确保原模板的一致性
      let temps = [].concat(JSON.parse(JSON.stringify(internalTemplates)));

      let internalTemplate = temps.find((item) => {
        return templateId === item._id;
      });

      for (let attr in internalTemplate.template) {
        let params = internalTemplate.template[attr].match(/\{(.*?)\}/g);

        if (!params || !params.length)
          continue;

        if (!Array.isArray(replacement[attr]))
          replacement[attr] = [replacement[attr]];

        if (replacement[attr].length !== params.length)
          throw {
            httpCode: 500,
            errorCode: 'ETEMPRENDERPARAM052',
            reason: 'replacement [' + attr + '] param number not match'
          };

        let replaceText = internalTemplate.template[attr];
        for (let i = 0; i < params.length; i++) {
          replaceText = replaceText.replace(params[i], replacement[attr][i]);
        }
        internalTemplate.template[attr] = replaceText;
      }

      debug(method, '[Exit](success)', internalTemplate.template);
      return internalTemplate.template;
    } catch (error) {
      debug(method, '[Exit](failed)', error);
      return error;
    }
  }
  redenerPushTemplate(templateId, replacement) {
    let method = 'redenerIternalTemplate';
    debug(method, '[Enter]');
    try {
      if (!templateId)
        throw {
          httpCode: 500,
          errorCode: 'ETEMPRENDERPARAM083',
          reason: 'invalid param: [templateId]'
        }
      //深拷贝，确保原模板的一致性
      let temps = [].concat(JSON.parse(JSON.stringify(pushTemplates)));

      let pushTemplate = temps.find((item) => {
        return templateId === item._id;
      });
      for (let attr in pushTemplate.template) {
        let params = pushTemplate.template[attr].match(/\{(.*?)\}/g);

        if (!params || !params.length)
          continue;

        if (!Array.isArray(replacement[attr]))
          replacement[attr] = [replacement[attr]];

        if (replacement[attr].length !== params.length)
          throw {
            httpCode: 500,
            errorCode: 'ETEMPRENDERPARAM052',
            reason: 'replacement [' + attr + '] param number not match'
          };

        let replaceText = pushTemplate.template[attr];
        for (let i = 0; i < params.length; i++) {
          replaceText = replaceText.replace(params[i], replacement[attr][i]);
        }
        pushTemplate.template[attr] = replaceText;
      }
      debug(method, '[Exit](success)', pushTemplate.template);
      return pushTemplate.template;
    } catch (error) {
      debug(method, '[Exit](failed)', error);
      return error;
    }
  }
}

module.exports = new TemplateRender();
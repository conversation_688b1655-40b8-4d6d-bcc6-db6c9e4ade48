/*
 * @Author: fb
 * @Date:   2020-07-14 
 */
'use strict';

const agent = require('superagent');
const Q = require('q');
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const logger = logFactory(logUtil())('rongxin:loan:passport:services:signin:aLiYunMsg');

const apiId = "5f164f8266ba787de16a131c";
const config = require('config');
const APIManagement = config.get("apiManagement");

class SendSmsMessage {

  constructor() {

  }

  get NAME() {
    return 'SendSmsALiYunMessage';
  }

  sendSms(payload) {
    let self = this;
    let method = 'sendSMS';
    let defer = Q.defer();
    const path = "/api/v1.0/internal/exec";

    let url = `${APIManagement.host}${path}`;
    payload.apiId = apiId;
    logger.debug(self.NAME, method, '[Enter]', ' payload: ', payload);
    try {
      agent.post(url)
        .send(payload)
        .set("Content-Type", "application/json")
        .on('error', error => {
          logger.error(self.NAME, method, 'Failed to send SMS message.', error);
          return defer.reject(error);
        })
        .end(function (err, res) {
          if (err) {
            logger.error(self.NAME, method, 'Failed to send SMS message.', err);
            return defer.reject(err);
          }
          logger.debug(self.NAME, method, 'SMS message sent.', res.body);
          defer.resolve(res.body);
        });
    } catch (err) {
      logger.error(self.NAME, method, 'Error occur ', err);
      defer.reject(err);
    }
    return defer.promise;
  }

}


module.exports = new SendSmsMessage();
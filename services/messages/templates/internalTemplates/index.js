/**
 * internal templates
 * <AUTHOR>
 * 2017-09-17 
 */

const internal_templates = [{
    _id: '5bfcee39368c7217ff32b880',
    type: 'loanApplication',
    name: '金融贷款申请审核通过通知',
    template: {
      title: '系统通知',
      text: '您申请的农闪贷单号为{1}的订单，已审核通过，请点击详情查看具体信息。',
      url: '/apply/detail/{1}'
    },
    comments: ''
  },
  {
    _id: '5bfcee39368c7217ff32b880',
    type: 'loanApplication',
    name: '金融贷款申请审核未通过通知',
    template: {
      title: '系统通知',
      text: '您申请的农闪贷单号为xxxx的订单，未审核通过，请点击详情查看具体信息。',
      url: '/apply/detail/{1}'
    },
    comments: ''
  },
  {
    _id: '5bfcee39368c7217ff32b880',
    type: 'loanApplication',
    name: '金融贷款申请放款通知联系人',
    template: {
      title: '系统通知',
      text: '您申请的农闪贷单号为xxxx的订单，已审放款， 请点击详情查看具体信息。',
      url: '/apply/detail/{1}'
    },
    comments: ''
  },
  {
    _id: '5bfcee39368c7217ff32b880',
    type: 'loanApplication',
    name: '金融贷款申请还款通知联系人',
    template: {
      title: '系统通知',
      text: '您本期账单待还xxx元，将于xx年xx月xx日自动扣款，请保证还款账户余额充足。点击查看详情或联系客服（电话xxxx）咨询，感谢您的支持！',
    },
    comments: ''
  },
  {
    _id: '61dfe6f23584a08a624d4af0',
    type: 'loanApplication',
    name: '喜农淘购买保险提醒联系人',
    template: {
      title: '系统通知',
      text: '尊敬的粮食预期收益权质押贷款客户，您好！按照《委托监管协议》约定，您需要对质押粮食预期收益权的{1}亩土地购买种植保险。如果您已完成投保，请在贷款-种植保险上传投保凭证；如果您未完成投保工作，请尽快办理投保事宜并上传投保凭证。感谢您的配合！'
    },
    comments: ''
  }, {
    _id: '61dfe7083584a08a624d4af1',
    type: 'loanApplication',
    name: '受灾通知联系人',
    template: {
      title: '系统通知',
      text: '尊敬的粮食预期收益权质押贷款客户，您好！您有1块土地发生{1}灾害， 点击查看详情。 ',
      url: '/apply/detail/{1}'
    },
    comments: ''
  },
];

module.exports = {
  internal_templates: internal_templates
};
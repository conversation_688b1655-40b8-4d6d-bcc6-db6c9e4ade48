/*
 * @Author: wcy
 * @Date:   2018-05-22 10:22:13
 * @Last Modified by:   wcy
 * @Last Modified time: 2018-05-23 20:11:48
 */
'use strict';

const uid = require('uid-safe');
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const logger = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:messages:smsYunZhiXunDispatcher');
const Dispatcher = require('nongfu.merchant.msgfw').Dispatcher;

const MSG_T = new Set([
	'Rongxin_loan:Sms:YunZhiXun:Text'
]);

const FROM = 'Rongxin_loan:Platform:SystemSpace:system';

class Target {
	constructor(target, endpointType, address) {
		this.target = target;
		this.endpointType = endpointType;
		this.address = address;
	}
}

class SmsDispatcher extends Dispatcher {

	/**
	 * Constructor method which use to create a dispatcher instance
	 * @param opts <object>
	 * @param opts.templateStore
	 * @param opts.msgQueue
	 * @param opts.msgStore
	 * @param opts.logger [optional]
	 */
	constructor(opts) {
		super(opts);
	}

	static get QNAME() {
		return 'SmsYunZhiXunDispatcher';
	}

	getQName() {
		return SmsDispatcher.QNAME;
	}

	/**
	 * sms distribution send a message to target
	 * @param to <array>
	 * @param msgType
	 * @param callback  <promise>
	 * [{
	 *   target: <string>,
	 *   endpointType: <string>,
	 *   address: <string>
	 * }]
	 */
	async settleTargets(to, msgType, options) {
		let self = this;
		let method = 'settleTargets';
		logger.debug(self.getQName(), method, '[Enter]');
		logger.debug(self.getQName(), method, 'to: ', to, ' msgtype: ', msgType, ' options:', options);

		if (!MSG_T.has(msgType)) {
			logger.error(self.getQName(), method, '[Exit](failed)', 'msgType is not allowed');
			throw {
				errorCode: 'E_MSG_SMS_DIS_067',
				httpCode: 406,
				reason: 'unkonwn msgType'
			};
		}

		try {
			let result = null;
			if ('Rongxin_loan:Sms:YunZhiXun:Text' === msgType) {
				result = await self.getSmsTextTarget(to, options);
			}
			logger.debug(self.getQName(), method, '[Exit](success)', 'result:', result);
			return result;
		} catch (err) {
			logger.error(self.getQName(), method, '[Exit](failed)', err);
			throw err;
		}
	}

	async getSmsTextTarget(to, options) {
		let self = this;
		let method = 'getSmsTextTarget';
		logger.debug(self.getQName(), method, '[Enter]');
		try {
			if (!Array.isArray(to)) {
				to = [to];
			}
			let targets = [];
			let promises = [];
			to.forEach(item => {
				targets.push(new Target(item, 'sms', item));
			});
			await Promise.all(promises);
			logger.debug(self.getQName(), method, '[Exit](success)', 'targets: ', targets);
			return targets;
		} catch (err) {
			logger.error(self.getQName(), method, '[Exit](failed)', err);
			throw err;
		}
	}

	/**
	 * to: ['enterprise_member:_id','member:_id']
	 */
	async send_Sms(to, replacement, options) {
		let self = this;
		let method = 'send_Sms';
		logger.debug(self.getQName(), method, '[Enter]');
		try {
			let headers = {
				msgId: uid.sync(18),
				from: options.from || FROM,
				to: to,
				msg_t: 'Rongxin_loan:Sms:YunZhiXun:Text',
				QoS: 0
			};

			let dataHolder = replacement;

			let msgHolder = {
				headers: headers,
				dataHolder: dataHolder
			};
			logger.debug(self.getQName(), method, '[Exit](success)');
			return await self.dispatch(msgHolder, options);
		} catch (err) {
			logger.error(self.getQName(), method, '[Exit](failed)', err);
			throw err;
		}
	}
}

module.exports = SmsDispatcher;
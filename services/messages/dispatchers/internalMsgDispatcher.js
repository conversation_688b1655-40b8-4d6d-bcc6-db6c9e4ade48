/**
 * <AUTHOR>
 * 2018-08-06
 */

'use strict';
const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const QNAME = 'InternalMsgDispatcher';
const debug = logFactory(logUtil())('nongfu:connect.api:services:message:dispatcher' + QNAME);

const BaseDispatcher = require('nongfu.merchant.msgfw').Dispatcher;
const uid = require('uid-safe');
const msgConf = require('../messageConfig');

class Target {
  constructor(target, endpointType, address) {
    this.target = target;
    this.endpointType = endpointType;
    this.address = address;
  }
}

class Dispatcher extends BaseDispatcher {
  constructor(opts) {
    super(opts);
  }

  static get QNAME() {
    return QNAME;
  }

  getQName() {
    return QNAME;
  }

  async settleTargets(to, msg_t, opts) {
    let method = 'settleTargets';
    debug(method, '[Enter]');

    try {
      opts = opts || '';
      if (!to || !msg_t) {
        throw {
          errorCode: 'E_MSG_INTER_045',
          reason: 'invalid input params'
        };
      }

      if (msg_t !== msgConf.MessageTypes.Rongxin_Internal) {
        throw {
          errorCode: 'E_MSG_INTER_052',
          reason: 'invalid msg type'
        };
      }

      let targets = [];
      if (!Array.isArray(to)) {
        to = [to];
      }

      for (let item of to) {
        targets.push(new Target(
          item,
          'system',
          ''
        ));
      }

      debug(method, '[Exit](success)');
      return targets;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  /**
   * @param {*} from
   * @param {*} to
   * @param {*} replacement
   * @param {*} opts
   * @returns 
   *  {
   *   msgStore: created msg
   *   msgQueue: msgRefs
   *  }
   * @memberof Dispatcher
   */
  async sendMsg(from, to, replacement, opts) {
    let self = this;
    let method = 'sendMsg';
    debug(method, '[Enter]');

    try {
      opts = opts || {};
      if (!from || !to || !replacement) {
        throw {
          httpCode: 500,
          errorCode: 'E_MSG_INTER_038',
          reason: 'invalid input params'
        };
      }

      let header = {
        msgId: uid.sync(18),
        from: from,
        to: to,
        msg_t: msgConf.MessageTypes.Rongxin_Internal,
        QoS: 0
      };

      if (opts && opts.tag) {
        header.tag = opts.tag;
        delete opts.tag;
      }

      let msgHolder = {
        headers: header,
        dataHolder: {
          templateId: '',
          replacement: replacement
        }
      };

      let reval = await self.dispatch(msgHolder, opts);
      debug(method, '[Exit](success)');
      return reval;
    } catch (error) {
      debug.error(method, '[Exit](error)', error);
      throw error;
    }
  }
}

module.exports = Dispatcher;
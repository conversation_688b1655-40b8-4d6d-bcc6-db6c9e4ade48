/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-3-25.
 */


'use strict'

const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');

const QNAME = 'PushDispatcher';
const debug = logFactory(logUtil())('rongxin:loan:dashboard:api:services:email:dispatcher' + QNAME);
const BaseDispatcher = require('nongfu.merchant.msgfw').Dispatcher;
const uid = require('uid-safe');
const msgConf = require('../messageConfig');
const FROM = 'Rongxin_loan:Platform:SystemSpace:system';
class Target {
	constructor(target, endpointType, address) {
		this.target = target;
		this.endpointType = endpointType;
		this.address = address;
	}
}

class PushDispatcher extends BaseDispatcher {

	constructor(opts) {
		super(opts);
	}

	static get QNAME() {
		return QNAME;
	}

	getQName() {
		return QNAME;
	}

  /**
   * order distribution send a message to target
   * @param to
   * @param msgType
   * @param callback  <promise>
   * [{
   *   target: <string>,
   *   endpointType: <string>,
   *   address: <string>
   * }]
   */

	async settleTargets(to, msg_t, opts) {
		let self = this;
		let method = 'settleTargets';
		debug(method, '[Enter]');

		try {
			opts = opts || '';
			if (!to || !msg_t) {
				throw {
					errorCode: 'E_MSG_Push_060',
					reason: 'invalid input params'
				};
			}
			if (msg_t !== msgConf.MessageTypes.Rongxin_Push_Text) {
				throw {
					errorCode: 'E_MSG_Push_066',
					reason: 'invalid msg type'
				};
			}
			let targets = [];
			if (!Array.isArray(to)) {
				to = [to];
			}
			for (let item of to) {
				targets.push(new Target(
					item,
					'system',
					''
				));
			}
			debug(method, '[Exit](success)');
			return targets;

		} catch (err) {
			debug.error(method, '[Exit](failed)', err);
			throw err;
		}
	}

	async sendPush(to, replacement, options) {
		let self = this;
		let method = 'sendPush';
		debug(self.getQName(), method, '[Enter]');
		try {
			let header = {
				msgId: uid.sync(18),
				from: FROM + ':' + options.from,
				to: to,
				msg_t: 'Rongxin:Push:Text',
				QoS: 0,
				platform: options.platform,
				clientId: options.clientId,
				addons: options.alias,
			};


			let msgHolder = {
				headers: header,
				dataHolder: {
					templateId: '',
					replacement: replacement
				}
			};
			debug(self.getQName(), method, '[Exit](success)');
			return await self.dispatch(msgHolder, options);
		} catch (err) {
			debug(self.getQName(), method, '[Exit](failed)', err);
			throw error;
		}
	}
}

module.exports = PushDispatcher;
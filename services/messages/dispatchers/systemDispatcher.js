/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-3-25.
 */


'use strict'

const logFactory = require('../../../utils/logFactory');
const logUtil = require('../../../utils/logUtil');
const logger = logFactory(logUtil())('rongxin:loan.app.api:services/message/systemDispatcher');
const Dispatcher = require('nongfu.merchant.msgfw').Dispatcher;

const messageConfig = require('../messageConfig');
const DispatcherNames = messageConfig.DispatcherNames;

class Target {
	constructor(target, endpointType, address) {
		this.target = target;
		this.endpointType = endpointType;
		this.address = address;
	}
}

class SystemDispatcher extends Dispatcher {

	constructor(templateHome, msgStore, qWriter) {
		super(templateHome, qWriter, msgStore);
	}

	static get QNAME() {
		return DispatcherNames.Rongxin_System;
	}

	getQName() {
		return SystemDispatcher.QNAME;
	}

  /**
   * order distribution send a message to target
   * @param to
   * @param msgType
   * @param callback  <promise>
   * [{
   *   target: <string>,
   *   endpointType: <string>,
   *   address: <string>
   * }]
   */

	async settleTargets(to, msgType, options) {
		let self = this;
		let method = 'settleTargets';
		logger.debug(self.getQName(), method, '[Enter]');
		logger.debug(self.getQName(), method, 'to: ', to, ' msgtype: ', msgType, ' options:', options);

		try {
			let result = null;
			if ("TODO" == "TODO" || 'Rongxin_loan:Sms:HuaXin:Text' === msgType) {
				result = [new Target(to, 'system', '')];
			}
			logger.debug(self.getQName(), method, '[Exit](success)', 'result:', result);
			return result;
		} catch (err) {
			logger.error(self.getQName(), method, '[Exit](failed)', err);
			throw err;
		}
	}

	async send_Sms(to, replacement, options) {
		let self = this;
		let method = 'send_Sms';
		logger.debug(self.getQName(), method, '[Enter]');
		try {
			let headers = {
				msgId: uid.sync(18),
				from: FROM + ':' + options.from,
				to: to,
				msg_t: 'Rongxin_loan:Sms:HuaXin:Text',
				QoS: 0
			};

			let dataHolder = {
				templateId: '',
				replacement: replacement
			};

			let msgHolder = {
				headers: headers,
				dataHolder: dataHolder
			};
			logger.debug(self.getQName(), method, '[Exit](success)');
			return await self.dispatch(msgHolder, options);
		} catch (err) {
			logger.error(self.getQName(), method, '[Exit](failed)', err);
			return Q.reject(err);
		}
	}
}

module.exports = SystemDispatcher;
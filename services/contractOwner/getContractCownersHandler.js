/**
 * <AUTHOR> 
 */

'use strict';

const HANDLER_NAME = 'GetContractCownersHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:contract_cowner_v2:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const cownersData = require('../dataSvc/dataUtil').contractOwnersV2;
const landData = require('../dataSvc/dataUtil').landData;
const userVerifysData = require('../dataSvc/dataUtil').userVerifys;
const loanApplicationData = require('../dataSvc/dataUtil').loanApplication;

const COWNER_TYPE = {
  APPLICAT: 'applicat',
  COWNER: 'cowner'
}

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      let cowners = await cownersData.getByCondition({
        aId: input.aId,
        limit: 'unlimited',
        type: { $ne: "guarantor" },
        $sort: {
          createdTime: 1
        }
      });
      debug("Cowners", cowners);

      if (cowners.length) {
        cowners = cowners.filter(item => !item.archived);
        self.context.result = cowners;
        debug(method, '[Exit](success)', self.context.result);
        return done();
      }

      // 初始化显示
      let list = [];
      // 获取本人
      let application = await loanApplicationData.getById(input.aId);
      let userIdentity = await userVerifysData.getOneByCondition({
        uId: application.uId,
        IDCardStatus: 'approved',
        archived: false
      });

      let proposer = {
        aId: input.aId,
        applicant: true,
        realname: userIdentity.realname,
        IDCard: userIdentity.IDCard,
        type: COWNER_TYPE.APPLICAT,
        mobile: application.userMobile
      };

      // 土地信息补录则只返回申请人的信息
      // 未填入承包方编码或进行过土地补录，返回只有申请人的信息
      if ((!application.addons || !application.addons.contractorNo) || (application && application.addons && application.addons.supplementLandInfo && application.addons.supplementLandInfo.isModified)) {
        list.push(proposer);
        self.context.result = list;
        debug(method, '[Exit](success)', self.context.result);
        return done();
      }

      let landFamily = await landData.getByUrl("/api/v1.0/contractor/families", {
        contractor: application.addons.contractorNo
      }, opts);

      let land_self = landFamily.find(item => {
        return item.cardNo === proposer.IDCard;
      });
      let landRelationship;
      if (land_self) {
        proposer.contractorRelation = getRelationship(land_self.relationship);
        if (land_self.relationship === '02' || land_self.relationship === '01') {
          proposer.relationship = '1'; // 申请人也是户主
        } else if (land_self.relationship === '10' || land_self.relationship === '11' || land_self.relationship === '12') {
          landRelationship = '12'; // 申请人是户主的配偶, 那么户主既是申请人的户主也是其户主
        }

        list.push(proposer);
      }

      for (const item of landFamily) {
        // 申请人本人
        if (item.cardNo && item.cardNo === proposer.IDCard) {
          continue;
        }

        // 户主
        if (item.relationship === '02' || item.relationship === '01') {
          list.push({
            aId: input.aId,
            type: COWNER_TYPE.APPLICAT,
            realname: item.name,
            IDCard: item.cardNo,
            contractorRelation: getRelationship(item.relationship),
            relationship: landRelationship || '1'
          });
          continue;
        }

        // 申请人是户主，选取配偶
        if (proposer.relationship == '1' && (item.relationship === '10' || item.relationship === '11' || item.relationship === '12')) {
          list.push({
            aId: input.aId,
            type: COWNER_TYPE.APPLICAT,
            realname: item.name,
            IDCard: item.cardNo,
            contractorRelation: getRelationship(item.relationship),
            relationship: '2'
          });
          continue;
        }

        // 非共有人
        if (item.coOwner !== '1') {
          continue;
        }

        list.push({
          aId: input.aId,
          type: COWNER_TYPE.COWNER,
          realname: item.name,
          IDCard: item.cardNo,
          contractorRelation: getRelationship(item.relationship),
          relationship: '0'
        });
      }
      if (!list.length) {
        list.push(proposer);
      }

      cowners = list;
      self.context.result = cowners;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

function getRelationship(relationship) {
  // 10011-家庭成员 
  // 10012-亲戚 
  // 10032-其他关系 
  // 10033-配偶 
  // 10035-子女 
  // 10036-父母 
  // 10037-兄弟姐妹 
  // 10038-承包方代表 (承包方本人)
  let result;
  switch (relationship) {
  case "01":
  case "02":
    result = '10038';
    break;
  case "10":
  case "11":
  case "12":
    result = '10033';
    break;
  case "20":
  case "21":
  case "22":
  case "23":
  case "24":
  case "25":
  case "26":
  case "27":
  case "29":
  case "30":
  case "31":
  case "32":
  case "33":
  case "34":
  case "35":
  case "36":
  case "37":
  case "39":
    result = '10035';
    break;
  case "70":
  case "71":
  case "73":
  case "75":
  case "77":
  case "79":
    result = '10037';
    break;
  case "50":
  case "51":
  case "52":
    result = '10036';
    break;
  default:
    result = '';
    break;
  }

  return result;
}

module.exports = Handler
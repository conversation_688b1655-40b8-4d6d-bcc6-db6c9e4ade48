'use strict';

const HANDLER_NAME = 'GetContractOwnersListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:contractOwner:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const cownersData = require('../dataSvc/dataUtil').contractOwnersV2;
const contractSignersData = require('../dataSvc/dataUtil').contractSignersV2;
const contractData = require('../dataSvc/dataUtil').contracts;
const contractFlowsData = require('../dataSvc/dataUtil').contractFlowsV2;
const contractFlowSignTasksData = require('../dataSvc/dataUtil').contractFlowSignTasksV2;
class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let loanId = self.context.input.loanId;

      // 通过订单拿到合同
      let contrcactList = await contractData.getByCondition({
        loanId: loanId,
        archived: false,
        type: {
          $in: [1, 2, 5, 6, 8, 7]
        },
        $sort: {
          type: 1
        },
        limit: 'unlimited'
      });

      // 拿到签约人
      let signer = await contractSignersData.getByCondition({
        aId: loanId,
        archived: false,
        limit: 'unlimited',
        $sort: {
          createdTime: 1
        }
      })

      let flowId = "";

      let promises = [];
      for (const item of contrcactList) {
        promises.push(contractFlowsData.getOneByCondition({
          cId: item._id,
          archived: false,
          limit: 'unlimited'
        }).then(data => {
          if (!data) {
            item.status = -1;
          } else if (data.status) {
            if (item.type != 8 && item.type != 7) {
              flowId = data._id;
            }

            item.fId = data._id;
            item.status = data.status;
          }
        }));
      }
      await Promise.all(promises);
      promises = [];
      for (const item of signer) {
        if (flowId) {
          promises.push(contractFlowSignTasksData.getOneByCondition({
            cownerId: item.cownerId,
            archived: false,
            fId: flowId
          }).then(data => {
            if (data) {
              item.signTime = data.signTime;
              item.signStatus = data.status;
            } else {
              item.signStatus = -1;
            }
          }))
        }
        item.signStatus = -1;
      }
      await Promise.all(promises);
      promises = [];
      for (let item of contrcactList) {
        item.cowner = [];
        if (item.type == 8 || item.type == 7) {

          for (const ower of signer) {
            if (ower.cId === item._id) {
              let sign = await contractFlowSignTasksData.getOneByCondition({
                cownerId: ower.cownerId,
                archived: false,
                fId: item.fId
              })

              let cowner = await cownersData.getById(ower.cownerId);
              cowner.signStatus = -1;
              if (sign) {
                cowner.signStatus = sign.status || -1;
                cowner.signTime = sign.signTime || "";
              }

              item.cowner.push(cowner);
            }
          }


        } else {
          for (const ower of signer) {
            if (ower.cId === item._id) {
              promises.push(cownersData.getById(ower.cownerId).then(data => {
                data.signStatus = ower.signStatus;
                data.signTime = ower.signTime;
                item.cowner.push(data);
              }));
            }
          }
        }

      }
      await Promise.all(promises);
      self.context.result = contrcactList;

      debug(method, '[Exit](success)', self.context.contrcactList);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
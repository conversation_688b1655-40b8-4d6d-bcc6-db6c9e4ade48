'use strict';

const HANDLER_NAME = 'getLoanContractListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:contract:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const DataSvc = require('../dataSvc/dataUtil');
const contractData = DataSvc.contracts;
const contractFlowsData = DataSvc.contractFlowsV2;
const moment = require('moment');

const ContractStatusEnum = {
  signing: 1, // 签署中
  finished: 2 // 已完成
}

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      let input = self.context.input;
      // let uId = self.context.opts.uId;

      let condition = {
        loanId: input.loanId,
        // uId: uId,
        archived: false,
        type: {
          $in: [28]
        },
        $sort: {
          type: 1
        },
        limit: 'unlimited'
      };

      //合同列表
      let result = await contractData.getListAndCountByCondition(condition);
      const cIdArr = result.result.map(it => {return it._id});
      const contractFlows = await contractFlowsData.getByCondition({
        cId: { $in: cIdArr },
        type: { $in: [ 6, 9 ] }, // 喜农淘授权委托合同
        archived: false,
        limit: 'unlimited'
      });

      let fcIdPayload = {};
      let flowIdArr = [];
      for (const item of result.result) {
        delete item.eDocUrl;
        for (const flow of contractFlows) {
          if(flow.cId && flow.cId.indexOf(item._id) > -1) {
            fcIdPayload[item._id] = flow.eFlowId;
            flowIdArr.push(flow._id)
            item.fId = flow._id;
            item.status = flow.status;
            item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm:ss')
            item.lastModTime = moment(item.lastModTime).format('YYYY-MM-DD HH:mm:ss')
            item.statusShowName = item.status == ContractStatusEnum.finished ? '已完成' : '签署中';
          }
        }
      }

      Object.assign(self.context.opts, { fcIdPayload, flowIdArr })
      self.context.result = result;
      debug(method, '[Exit](success)', result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
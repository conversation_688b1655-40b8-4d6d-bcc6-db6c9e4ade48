'use strict';

const HANDLER_NAME = 'getLoanContractConwerListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:contract:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const DataSvc = require('../dataSvc/dataUtil');
const contractOwnersData = DataSvc.contractOwnersV2;
const contractFlowSignTasksData = DataSvc.contractFlowSignTasksV2;
const contractFlowsV2Data = DataSvc.contractFlowsV2;
const contractSignersV2Data = DataSvc.contractSignersV2;
const moment = require('moment');
const eSignSvc = require('../eSignSvc');

const SignStatusEnum = {
  NoSign: 1, // 未签字
  Signed: 2 // 已签字
}

const SignStatus2Enum = {
  Signing: 1, // 签署中
  Signed: 2 // 已签署
}

/**
 * enumValue
    - [1, '父亲'],
    - [2, '母亲'],
    - [3, '配偶'],
    - [4, '儿子'],
    - [5, '女儿'],
    - [6, '其他'],
 */
const RelationshipEnum = new Map([
  ['10036', '父母'],
  ['10033', '配偶'],
  ['10035', '子女'],
  ['10032', '其他关系'],
  ['10039', '成员'],
  ['10040', '股东'],
])
class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  /**
   * 根据合同列表，查询出每个合同的签署人信息以及其对应的签署状态
   * @param {*} done 
   */
  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {

      let _result = self.context.result.result;
      let { flowIdArr, fcIdPayload } = self.context.opts;
      let input = self.context.input;
      let signers = await contractSignersV2Data.getByCondition({
        aId: input.loanId,
        archived: false,
        limit: 'unlimited',
        $sort: {
          createdTime: 1
        }
      });
      const signerCowerIdArr = signers.map(it => {return it.cownerId});
      const flowSignTasks = await contractFlowSignTasksData.getByCondition({
        cownerId: { $in: signerCowerIdArr },
        archived: false,
        fId: { $in: flowIdArr }
      });
      for (const item of signers) {
        for (const signTask of flowSignTasks) {
          if(item.cownerId == signTask.cownerId) {
            item.signStatus = signTask.status;
            item.signStatusShowName = item.signStatus === SignStatusEnum.Signed ? '已签字' : '未签字';
          }
        }
      }

      const cowners = await contractOwnersData.getByCondition({ _id: { $in: signerCowerIdArr } });
      for (let item of _result) {
        item.signatory = [];
        for (const signer of signers) {
          if (signer.cId === item._id) {
            for (const cowner of cowners) {
              if(cowner._id == signer.cownerId) {
                item.eSignAccountId = cowner.eSignAccountId;
                cowner.signStatus = signer.signStatus;
                cowner.relationshipShowName = RelationshipEnum.get(cowner.contractorRelation);
                cowner.createdTime = moment(cowner.createdTime).format('YYYY-MM-DD HH:mm:ss')
                cowner.lastModTime = moment(cowner.lastModTime).format('YYYY-MM-DD HH:mm:ss')
                cowner.signStatusShowName = cowner.signStatus === SignStatus2Enum.Signed ? '已签署' : '签署中';
                item.signatory.push(cowner);
                if(!item.eDocUrl) {
                  const eSignObj = await eSignSvc.getFlowViewUrl({ flowId: fcIdPayload[item._id], accountId: cowner.eSignAccountId })
                  item.eDocUrl = eSignObj && eSignObj.viewUrl || '';
                }
              }
            }
          }
        }
      }

      self.context.result.result = _result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
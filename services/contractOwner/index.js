'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:contract:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;

const GetLoanContractListHandler = require('./getLoanContractListHandler');
const GetLoanContractConwerListHandler = require('./getLoanContractConwerListHandler');
const GetContractOwnersListHandler = require('./getContractOwnersListHandler');
const GetContractCownersHandler = require('./getContractCownersHandler');
class Service {
  constructor() {}

  /**
   * 获得创新物权得委托合同列表信息
   */
  async getCxwqLoanContractList(input, _opts) {
    let method = 'getLoanContractList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetLoanContractListHandler(context));
      svcHandlerMgrt.addHandler(new GetLoanContractConwerListHandler(context));
      // svcHandlerMgrt.addHandler(new ChangeLoanAppsStatusHandler(context));

      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getContractOwnersList(input, _opts) {
    let method = 'getContractOwnersList';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetContractOwnersListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getContractCowners(input, _opts) {
    let method = 'getContractCowners';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetContractCownersHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }
}

module.exports = new Service();
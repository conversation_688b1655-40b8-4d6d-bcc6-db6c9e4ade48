/**
 * rongxin data svc
 * <AUTHOR>
 * 2018-01-30
 */

'use strict'

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:mgr:app:api:services:dataSvc:rongxinDataSvc')
const BaseDataSvc = require('./baseDataSvc')
const config = require('config')
const querystring = require('qs')
const Q = require('q')
const KEY_PREFIX = 'rongxin:loan:datasource:';

class RongxinDataSvc extends BaseDataSvc {
  constructor(baseUrl, opts) {
    super('rongxin_dataService', config.get('rongxin_dataService').basicAuth, opts)
    this._baseUrl = UrlHandler(baseUrl)
    this.redisData = null;
		if (opts) {
	   this.disabledLogger = opts.disabledLogger;
		}
  }

  resError(error, errorCode) {
    let self = this;
    let method = 'DataHelper.resError';
    errorCode = errorCode || 500;
    try {
      if (typeof error == 'string') {
        return {
          errorCode: errorCode,
          message: error
        }
      } else if (typeof error == 'object') {
        if (error.response) {
          let response = error.response
          if (response.body) {
            if (typeof response.body == 'string') {
              return {
                errorCode: response.status,
                message: response.body
              }
            } else if (typeof response.body == 'object' && response.body.reason) {
              return {
                errorCode: response.status,
                message: response.body.reason
              }
            } else if (typeof response.body == 'object' && response.body.message) {
              return {
                errorCode: response.status,
                message: response.body.message
              }
            } else if (response.text) {
              return {
                errorCode: response.status,
                message: response.text
              }
            }
          }
        } else if (error.body) {
          if (typeof error.body == 'string') {
            return {
              errorCode: error.status,
              message: error.body
            }
          } else if (typeof error.body == 'object' && error.body.reason) {
            return {
              errorCode: error.status,
              message: error.body.reason
            }
          } else if (typeof error.body == 'object' && error.body.message) {
            return {
              errorCode: error.status,
              message: error.body.message
            }
          }
        } else if (error.message) {
          return {
            errorCode: errorCode,
            message: error.message
          }
        }
      }
    } catch (e) {
      if (error.text) {
        return {
          errorCode: error.status || 500,
          message: error.text
        }
      }
      return {
        errorCode: 500,
        message: error
      }
    }
    if (error.text) {
      return {
        errorCode: error.status || 500,
        message: error.text
      }
    }
    return {
      errorCode: 500,
      message: error
    }
  }

  getByCondition(condition, callback) {
    let self = this
    let method = 'getByCondition'
    if (!self.disabledLogger) {
      debug(method, '[Enter]');
    }

    let path = self._baseUrl;
    if (!condition.limit)
      condition.limit = 10
    if (!condition.skip)
      condition.skip = 0

    if (Object.getOwnPropertyNames(condition).length > 0)
      path += '?' + querystring.stringify(condition)

    return self.execute('get', path).then(function (data) {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path)
        debug(method, '[condition]:', condition)
      }
      let result = data.body

      if (!self.disabledLogger) {
        debug(method, '[Exit](success)', result)
      } else {
        debug(method, '[Exit](success)')
      }
      return result
    }).fail(function (error) {
      debug(method, '[path]:', path)
      debug(method, '[condition]:', condition)
      debug.error(method, '[Exit](error)', error)
      throw self.resError(error)
    }).nodeify(callback)
  }

  getOneByCondition(condition, callback) {
    let self = this
    let method = 'getOneByCondition'
    if (!self.disabledLogger) {
      debug(method, '[Enter]');
    }
    return self.getByCondition(condition).then(data => {
      if (!Array.isArray(data) || data.length < 1) {
        debug(method, '[Exit](success)', 'null')
        return null
      }

      if (!self.disabledLogger) {
        debug(method, '[Exit](success)', data[0])
      } else {
        debug(method, '[Exit](success)')
      }
      return data[0]
    }).nodeify(callback)
  }

  post(body, callback) {
    let self = this
    let method = 'post'
    if (!self.disabledLogger) {
      debug(method, '[Enter]');
    }
    let path = self._baseUrl

    return self.execute('post', path, body).then(function (data) {

      if (!self.disabledLogger) {
        debug(method, '[path]:', path)
        debug(method, '[body]:', body)
      }
      let result = data.body

      if (!self.disabledLogger) {
        debug(method, '[Exit](success)', result)
      } else {
        debug(method, '[Exit](success)')
      }
      return result
    }).fail(function (error) {
      debug(method, '[path]:', path)
      debug(method, '[body]:', body)
      debug.error(method, '[Exit](error)', error)
      throw self.resError(error)
    }).nodeify(callback)
  }

  putByUrl(url, body, callback) {
    let self = this
    let method = 'putByUrl'
    if (!self.disabledLogger) {
      debug(method, '[Enter]');
    }
    if (!url) {
      return Q.reject('请提供url').nodeify(callback)
    }
    let path = url

    return self.execute('put', path, body).then(function (data) {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path)
        debug(method, '[body]:', body)
      }
      let result = data.body;

      if (!self.disabledLogger) {
        debug(method, '[Exit](success)', result)
      } else {
        debug(method, '[Exit](success)')
      }
      return result;
    }).fail(function (error) {
      debug(method, '[path]:', path)
      debug(method, '[body]:', body)
      debug.error(method, '[Exit](error)', error)
      return Q.reject(self.resError(error))
    }).nodeify(callback);
  }

  putById(id, body, callback) {
    let self = this
    let method = 'putById'
    if (!self.disabledLogger) {
      debug(method, '[Enter]');
    }
    if (!id) {
      debug('putByIdError', this._baseUrl, body);
      return Q.reject('请提供记录id:').nodeify(callback)
    }
    let path = self._baseUrl
    path += '/' + id
    return self.execute('put', path, body).then(data => {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path);
        debug(method, '[body]:', body);
      }
      let result = data.body;
      if (!result || !result._id) {
        debug('putByIdNull', id, self._baseUrl);
        result = null;
      }
      if (!self.disabledLogger) {
        debug(method, '[Exit](success)', result);
      } else {
        debug(method, '[Exit](success)');
      }
      return result;
    }).fail(error => {
      debug(method, '[path]:', path);
      debug(method, '[body]:', body);
      debug.error(method, '[Exit](error)', error);
      return Q.reject(self.resError(error));
    }).nodeify(callback);
  }

  getCountByCondition(condition, callback) {
    let self = this;
    let method = 'getCountByCondition';
    let path = self._baseUrl + '/count';

    if (condition && Object.getOwnPropertyNames(condition).length > 0) {
      path += '?' + querystring.stringify(condition);
    }

    return self.execute('get', path).then(function (data) {
      if (self.disabledLogger) {
        debug(method, '[path]:', path);
        debug(method, '[condition]:', condition);
      }
      let result = data.body;
      if (!self.disabledLogger) {
        debug(method, '[Exit]:', result);
      } else {
        debug(method, '[Exit](success)');
      }
      return result;
    }).fail(function (error) {
      debug(method, '[path]:', path);
      debug(method, '[condition]:', condition);
      debug.error(method, '[error]:', error);
      return Q.reject(self.resError(error));
    }).nodeify(callback);
  };


  postByCondition(body, callback) {
    let self = this
    let method = 'postByCondition'
    if (!self.disabledLogger)
      debug(method, '[Enter]')

    let path = self._baseUrl

    return self.execute('post', `/getdata${path}`, body).then(function (data) {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path)
        debug(method, '[body]:', body)
      }

      let result = data.body

      if (!self.disabledLogger)
        debug(method, '[Exit](success)', result)

      return result
    }).fail(function (error) {
      debug(method, '[path]:', path)
      debug(method, '[body]:', body)
      debug.error(method, '[Exit](error)', error)
      throw self.resError(error)
    }).nodeify(callback)
  }

  postListAndCountByCondition(body, callback) {
    let self = this
    let method = 'postByCondition'
    if (!self.disabledLogger)
      debug(method, '[Enter]')

    let path = self._baseUrl

    return self.execute('post', `/getdata${path}`, body).then(function (data) {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path)
        debug(method, '[body]:', body)
      }

      let result = data.body;
      let count = data.headers["content-range"]
      count = count.substr(count.lastIndexOf('\/') + 1)
      if (count === '*') count = 0
      if (!self.disabledLogger) {
        debug.verbose(method, '[data]:', result);
      }

      if (!self.disabledLogger)
        debug(method, '[Exit](success)', result);

      return {
        result,
        total: parseInt(count)
      };
    }).fail(function (error) {
      debug(method, '[path]:', path)
      debug(method, '[body]:', body)
      debug.error(method, '[Exit](error)', error)
      throw self.resError(error)
    }).nodeify(callback)
  }

  getByUrl(url, condition, callback) {
    var self = this;
    var method = 'getByUrl';
    url = UrlHandler(url);
    if (!url) {
      return Q.reject('请提供完整的url').nodeify(callback);
    }
    var path = url;

    if (condition && Object.getOwnPropertyNames(condition).length > 0) {
      path += '?' + querystring.stringify(condition);
    }

    return self.execute('get', path).then(function (data) {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path);
      }
      var result = data.body;
      if (!self.disabledLogger) {
        debug(method, '[Exit]:', result);
      } else {
        debug(method, '[Exit](success)');
      }
      return result;
    }).fail(function (error) {
      debug(method, '[path]:', path);
      debug(method, '[error]:', error);
      throw self.resError(error);
    }).nodeify(callback);
  };

  /**
   * Find a record by id
   * @param id {string} record id
   * @param callback {function} optional param
   */
  getById(id, options = {}, callback) {
    if (!this.redisData) {
      this.redisData = require('../../persistence/dataStore');
    }
    var self = this;
    var method = 'getById';
    if (!id) {
      debug('getByIdError', this._baseUrl);
      return Q.reject('请提供记录id').nodeify(callback);
    }
    var path = self._baseUrl;
    path += '/' + id;
    if (options.cache === true) {
      return self.redisData.pureHgetall(`${KEY_PREFIX}${path}`).then((data) => {
        try {
          if (!data || !data.dataSourceStr) {
            return self.getByIdExecute(id).then((result) => {
              if(result)
                self.redisData.pureHset(`${KEY_PREFIX}${path}`, { dataSourceStr: JSON.stringify(result) }, { expire: options.expire || 60 })
              return result;
            }).nodeify(callback);
          } else {
            return data && data.dataSourceStr ? JSON.parse(data.dataSourceStr) : data
          }
        } catch (err) {
          debug(method, '[Exit](error)', err.message);
          self.redisData.pureDel(`${KEY_PREFIX}${path}`);
          return self.getByIdExecute(id).nodeify(callback);
        }
      })
    } else {
      return self.getByIdExecute(id).nodeify(callback);
    }
  };

  getByIdExecute(id, callback) {
    var self = this;
    var method = 'getByIdExecute';
    var path = self._baseUrl;
    path += '/' + id;

    return self.execute('get', path).then(function (data) {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path)
      }
      let result = data.body;
      if (!result || !result._id) {
          debug('getByIdNull', id, self._baseUrl);
          result = null;
      }
      if (!self.disabledLogger) {
        debug(method, '[Exit](success)', result)
      } else {
        debug(method, '[Exit](success)')
      }

      return result;
    }).fail(function (error) {
      debug(method, '[path]:', path)
      if (error.status && error.status == 404) {
        debug.error(method, '[Exit](error) not found');
        return null;
      }

      debug.error(method, '[Exit](error)', error)
      return Q.reject(self.resError(error))
    }).nodeify(callback);
  }

  /**
   * find list and count by condition
   * @param {*} condition
   * @param {*} callback
   */
  getListAndCountByCondition(condition, callback) {
    var self = this;
    var method = 'getListAndCountByCondition';
    var path = self._baseUrl;

    if (!condition.limit) {
      condition.limit = 10;
    }
    if (!condition.skip) {
      condition.skip = 0;
    }

    if (Object.getOwnPropertyNames(condition).length > 0) {
      path += '?' + querystring.stringify(condition);
    }

    return self.execute('get', path).then(function (data) {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path);
        debug(method, '[condition]:', condition);
      }
      var result = data.body;
      let count = data.headers["content-range"]
      count = count.substr(count.lastIndexOf('\/') + 1)
      if (count === '*') count = 0
      if (!self.disabledLogger) {
        debug(method, '[data]:', result);
      }
      return {
        result,
        total: parseInt(count)
      };
    }).fail(function (error) {
      debug(method, '[path]:', path);
      debug(method, '[condition]:', condition);
      debug.error(method, '[error]:', error);
      throw self.resError(error);
    }).nodeify(callback);
  };


  postByUrl(url, body, callback) {
    let self = this
    let method = 'postByUrl'
    if (!self.disabledLogger) {
      debug(method, '[Enter]');
    }
    if (!url) {
      return Q.reject('请提供url').nodeify(callback)
    }
    let path = url

    return self.execute('post', path, body).then(function (data) {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path)
        debug(method, '[body]:')
      }
      let result = data.body;
      if (!self.disabledLogger) {
        debug(method, '[Exit](success)')
      } else {
        debug(method, '[Exit](success)');
      }
      return result;
    }).fail(function (error) {
      debug(method, '[path]:', path)
      debug(method, '[body]:', body)
      debug.error(method, '[Exit](error)', error)
      return Q.reject(self.resError(error))
    }).nodeify(callback);
  }
}

function UrlHandler(url) {
  if (!url || typeof url != 'string')
    throw {
      errorCode: 'EDATAURL',
      httpCode: 500,
      reason: 'invalid input url'
    }

  if (url.substr(0, 1) != '/')
    url = '/' + url

  if (url.substr(url.length - 1, 1) == '/')
    url = url.substr(0, url.length - 2)

  return url;
}

module.exports = RongxinDataSvc
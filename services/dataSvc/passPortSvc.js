/**
 * nonghe data svc
 * <AUTHOR>
 * 2018-01-30
 */

'use strict'

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:dashboard:services:dataSvc:rongxinDataSvc')
const BaseDataSvc = require('./baseDataSvc')
const Q = require('q')

class PassPortDataSvc extends BaseDataSvc {
  constructor(baseUrl, opts) {
    super('passport_Service');
    this._baseUrl = UrlHandler(baseUrl);
    if (opts) { 
      this.disabledLogger = opts.disabledLogger;
    }
  }
  resError(error, errorCode) {
    let self = this;
    let method = 'DataHelper.resError';
    errorCode = errorCode || 500;
    try {
      if (typeof error == 'string') {
        return {
          errorCode: errorCode,
          message: error
        }
      } else if (typeof error == 'object') {
        if (error.response) {
          let response = error.response
          if (response.body) {
            if (typeof response.body == 'string') {
              return {
                errorCode: response.status,
                message: response.body
              }
            } else if (typeof response.body == 'object' && response.body.reason) {
              return {
                errorCode: response.status,
                message: response.body.reason
              }
            } else if (typeof response.body == 'object' && response.body.message) {
              return {
                errorCode: response.status,
                message: response.body.message
              }
            } else if (response.text) {
              return {
                errorCode: response.status,
                message: response.text
              }
            }
          }
        } else if (error.body) {
          if (typeof error.body == 'string') {
            return {
              errorCode: error.status,
              message: error.body
            }
          } else if (typeof error.body == 'object' && error.body.reason) {
            return {
              errorCode: error.status,
              message: error.body.reason
            }
          } else if (typeof error.body == 'object' && error.body.message) {
            return {
              errorCode: error.status,
              message: error.body.message
            }
          }
        } else if (error.message) {
          return {
            errorCode: errorCode,
            message: error.message
          }
        }
      }
    } catch (e) {
      if (error.text) {
        return {
          errorCode: error.status || 500,
          message: error.text
        }
      }
      return {
        errorCode: 500,
        message: error
      }
    }
    if (error.text) {
      return {
        errorCode: error.status || 500,
        message: error.text
      }
    }
    return {
      errorCode: 500,
      message: error
    }
  }

  putByUrl(url, body, callback) {
    let self = this
    let method = 'putByUrl'
    debug(method, '[Enter]')
    if (!url) {
      return Q.reject('请提供url').nodeify(callback)
    }

    let path = self._baseUrl
    path += '/' + url
    return self.execute('put', path, body).then(function (data) {
      debug(method, '[path]:', path)
      debug(method, '[body]:', body)
      let result = data.body;
      debug(method, '[Exit](success)', result)
      return result;
    }).fail(function (error) {
      debug(method, '[path]:', path)
      debug(method, '[body]:', body)
      debug.error(method, '[Exit](error)', error)
      return Q.reject(self.resError(error))
    }).nodeify(callback);
  }
}

function UrlHandler(url) {
  if (!url || typeof url != 'string')
    throw {
      errorCode: 'EDATAURL',
      httpCode: 500,
      reason: 'invalid input url'
    }

  if (url.substr(0, 1) != '/')
    url = '/' + url

  if (url.substr(url.length - 1, 1) == '/')
    url = url.substr(0, url.length - 2)

  return url;
}
module.exports = PassPortDataSvc
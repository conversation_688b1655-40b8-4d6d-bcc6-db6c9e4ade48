/**
 * rongxin data svc
 * <AUTHOR>
 */

const RongxinDataSvc = require('./rongxinDataSvc')
const AssetDataSvc = require('./assetDataSvc');
const LandDataSvc = require('./landDataSvc');
const PassPortDataSvc = require('./passPortSvc');

module.exports = {

  user: new RongxinDataSvc('/v1.0/users'),
  employees: new RongxinDataSvc('/v1.0/employees'),
  employeeGroups: new RongxinDataSvc('v1.0/employee/groups'),
  dictAreas: new RongxinDataSvc('/v1.0/dict/areas'),
  surveys: new RongxinDataSvc('/v1.0/surveys'),
  clientVersion: new RongxinDataSvc('/v1.0/client/versions'),
  msgQueue: new RongxinDataSvc('/v1.0/message/queues'),
  msgStore: new RongxinDataSvc('/v1.0/message/stores'),
  devices: new RongxinDataSvc('/v1.0/devices'),
  loanApplication: new RongxinDataSvc('v1.0/loan/applications'),
  loanFeedbacks: new RongxinDataSvc('/v1.0/loan/feedbacks'),
  clients: new RongxinDataSvc('/v1.0/clients'),
  surveyTrackings: new RongxinDataSvc('/v1.0/survey/tracking'),
  surveyLoans: new RongxinDataSvc('/v1.0/survey/loans'),
  loanProducts: new RongxinDataSvc('/v1.0/loan/products'),
  funds: new RongxinDataSvc('/v1.0/funds'),
  userIdentity: new RongxinDataSvc('v1.0/user/verifys'),
  loanApplicationTracking: new RongxinDataSvc('v1.0/loan/application/trackings'),
  loanDistribute: new RongxinDataSvc('/v1.0/loan/distribute/trackings'),
  managerDistribute: new RongxinDataSvc('/v1.0/managerDistribute/list'),
  groupsV2: new RongxinDataSvc('v2.0/groups'),
  adviceFeedbacks: new RongxinDataSvc('/v1.0/advice/feedbacks'),
  news: new RongxinDataSvc('/v1.0/news'),
  newsTrackings: new RongxinDataSvc('/v1.0/newsTracking'),
  newsCatalogs: new RongxinDataSvc('/v1.0/newsCatalog'),
  failedReports: new RongxinDataSvc('/v1.0/system/failed/report'),
  helpCenters: new RongxinDataSvc('/v1.0/help/centers'),
  groups: new RongxinDataSvc('/v1.0/roles'),
  tenants: new RongxinDataSvc('/v1.0/tenants'),
  infoPersonalMain: new RongxinDataSvc('/v1.0/info/personal/main'),
  transferWhitelist: new RongxinDataSvc('/v1.0/loan/transfer/whitelist'),
  infoPersonalBasic: new RongxinDataSvc('/v1.0/info/personal/basic'),
  infoPersonalFamilyMember: new RongxinDataSvc('/v1.0/info/personal/family/member'),
  infoPersonalProductMgr: new RongxinDataSvc('/v1.0/info/personal/product/mgr'),
  infoPersonalProperty: new RongxinDataSvc('/v1.0/info/personal/property'),
  infoPersonalDebt: new RongxinDataSvc('/v1.0/info/personal/debt'),
  infoPersonalFamilyPayment: new RongxinDataSvc('/v1.0/info/personal/family/payment'),
  infoPersonalEvaluation: new RongxinDataSvc('/v1.0/info/personal/evaluation'),
  infoPersonalImage: new RongxinDataSvc('/v1.0/info/personal/image'),
  infoEnterpriseMain: new RongxinDataSvc('/v1.0/info/enterprise/main'),
  infoEnterpriseBasic: new RongxinDataSvc('/v1.0/info/enterprise/basic'),
  infoEnterpriseLoanWill: new RongxinDataSvc('/v1.0/info/enterprise/loan/will'),
  infoEnterpriseOperation: new RongxinDataSvc('/v1.0/info/enterprise/operation'),
  infoEnterpriseDebt: new RongxinDataSvc('/v1.0/info/enterprise/debt'),
  infoEnterpriseFinance: new RongxinDataSvc('/v1.0/info/enterprise/finance'),
  infoEnterpriseOther: new RongxinDataSvc('/v1.0/info/enterprise/other'),
  infoEnterpriseImage: new RongxinDataSvc('/v1.0/info/enterprise/image'),
  infoVersion: new RongxinDataSvc('/v1.0/info/version'),
  contractFlowsV2: new RongxinDataSvc('/v2.0/contract/flows'),
  contractOwnersV2: new RongxinDataSvc('/v2.0/contract/cowners'),
  activity: new RongxinDataSvc('/v1.0/activity'),
  activityEvent: new RongxinDataSvc('/v1.0/activity/event'),
  activityUsers: new RongxinDataSvc('/v1.0/activity/users'),
  clientBundleVersions: new RongxinDataSvc('/v1.0/client/bundle/versions'),
  productFunds: new RongxinDataSvc('/v1.0/product/funds'),
  carrousels: new RongxinDataSvc('/v1.0/carrousels'),
  loanApplicationOutLand: new RongxinDataSvc('v1.0/loan/application/out/land'),
  loanApplicationLandType: new RongxinDataSvc('v1.0/loan/application/land/type'),
  userAddons: new RongxinDataSvc('/v1.0/user/addon'),
  userCreditRating: new RongxinDataSvc('/v1.0/user/credit/rating'),
  userFamilyCredit: new RongxinDataSvc('/v1.0/user/family/credit'),
  creditTrackings: new RongxinDataSvc('/v1.0/credit/trackings'),
  loanApplicationOutLandGroup: new RongxinDataSvc('v1.0/loan/application/outland/group'),
  insOrder: new RongxinDataSvc('/v1.0/ins/order'),

  // loanApplicationOutLandGroup: new RongxinDataSvc('v1.0/loan/application/outland/group'),
  systemOrg: new RongxinDataSvc('v1.0/system/org'),

  assetSvrLand: new AssetDataSvc('/api/v1.0/loanSvrLand'),
  drawSvrLand: new AssetDataSvc('/api/v1.0/drawSvrLand'),
  assetSvrOutContractor: new AssetDataSvc('/api/v1.0/svrOutTradeLand'),
  assetLoanCBF: new AssetDataSvc('/api/v1.0/loancbf'),
  LoanSvrLand: new AssetDataSvc('/api/v1.0/loanSvrLand'),
  govVillager: new AssetDataSvc('/api/v1.0/govVillager'),
  govGroup: new AssetDataSvc('/api/v1.0/govGroup'),
  landSupervisoryRecord: new RongxinDataSvc('/v1.0/land/supervisory/record'),
  userVerifys: new RongxinDataSvc('v1.0/user/verifys'),
  landData: new LandDataSvc('api/v1.0', { key: 'land_dataService' }),
  creditUserData: new LandDataSvc('api/v1.0', { key: 'credit_user_service' }),
  loanLand: new RongxinDataSvc('/v1.0/loan/land/origin'),
  estimatedOutput: new RongxinDataSvc('/v1.0/estimated/output'),
  cxwqCirculations: new RongxinDataSvc('/v1.0/cxwq/circulation'),
  infoCollectDraft: new RongxinDataSvc('/v1.0/info/collect/draft'),
  infoCollectHistory: new RongxinDataSvc('/v1.0/info/collect/history'),

  loanApplicationVerify: new RongxinDataSvc('/v1.0/loan/application/verify'),

  fundInvestor: new RongxinDataSvc('/v1.0/fund/investor'),
  passport: new PassPortDataSvc('api/v1.0'),

  contracts: new RongxinDataSvc('/v1.0/contracts'),
  contractTemplates: new RongxinDataSvc('/v1.0/contract/templates'),
  contractSignersV2: new RongxinDataSvc('/v2.0/contract/signers'),
  contractFlowSignTasksV2: new RongxinDataSvc('/v2.0/contract/flow/signTasks'),
  meeting: new RongxinDataSvc('/v1.0/loan/application/meeting'),

  iguopinRecord: new RongxinDataSvc("/v1.0/i/guopin/record"),
  iguopinEnroll: new RongxinDataSvc("/v1.0/i/guopin/enroll"),
  iguopinOffline: new RongxinDataSvc("/v1.0/i/guopin/offline"),

  countryCollect: new RongxinDataSvc("/v1.0/country/collect"),
  guopinEmployees: new RongxinDataSvc('/v1.0/i/guopin/employee'),
  loanSupplement: new RongxinDataSvc('v1.0/loan/application/supplements'),

  assistanceRequest: new RongxinDataSvc('/v1.0/assistance/request'),
  assistanceAgent: new RongxinDataSvc('/v1.0/assistance/agent'),
  assistanceFeedback: new RongxinDataSvc('/v1.0/assistance/feedback'),
  assistanceFaq: new RongxinDataSvc('/v1.0/assistance/faq'),
  assistanceSubscribe: new RongxinDataSvc('/v1.0/assistance/subscribe'),
  assistanceFaqCategory: new RongxinDataSvc('/v1.0/assistance/faq-category'),
  assistanceFaqTracking: new RongxinDataSvc('/v1.0/assistance/faq-tracking'),

  infoFarmer: new RongxinDataSvc('/v1.0/info/farmer'),
  fundReceive: new RongxinDataSvc('/v1.0/loan/application/fund/Receive'),
  cxwqCirculationFundReceive: new RongxinDataSvc('/v1.0/cxwq/circulation/fund/receive'),
  cxwqWarnRule: new RongxinDataSvc('/v1.0/cxwq/warnRule'),
  cxwqInsurance: new RongxinDataSvc('/v1.0/cxwq/insurance'),
  disasterClaims: new RongxinDataSvc('/v1.0/application/disaster/claims'),
  grainManage: new RongxinDataSvc('/v1.0/loan/application/grain/manage'),
  loanApplicationGrainSale: new RongxinDataSvc('/v1.0/loan/application/grain/sale'),
  grainDepot: new RongxinDataSvc('/v1.0/grain/depot'),
  applicationSubcontract: new RongxinDataSvc('/v1.0/loan/application/subcontract'),
  applicationSubcontractLand: new RongxinDataSvc('/v1.0/loan/application/subcontractLand'),
  loanApplicationConfirm: new RongxinDataSvc('/v1.0/loan/application/confirm'),
  loanApplicationConfirmLand: new RongxinDataSvc('/v1.0/loan/application/confirmLand'),
  loanApplicationCustomerVisit: new RongxinDataSvc('/v1.0/application/customer/visit'),
  loanApplicationInsureProduct: new RongxinDataSvc('/v1.0/loan-application-insure-products'),
  loanApplicationInsureOrder: new RongxinDataSvc('/v1.0/loan-application-insure-orders'),
  loanVillageInfo: new RongxinDataSvc('/v1.0/loan/infos/village'),
  village: new RongxinDataSvc('/v1.0/loan/village'),
  shortUrls: new RongxinDataSvc('/v1.0/shortUrls'),
  loanApplicationStatis: new RongxinDataSvc('/v1.0/loan/application/statis'),
  certificate: new RongxinDataSvc('/v1.0/certificate'),
  serviceSite: new RongxinDataSvc('/v1.0/service/sites'),
  serviceSiteTracking: new RongxinDataSvc('/v1.0/service/site/trackings'),
  assistanters: new RongxinDataSvc('/v1.0/assistanters'),
  assistantTracking: new RongxinDataSvc('/v1.0/assistant/tracking'),
  configSeals: new RongxinDataSvc('/v1.0/config/seal'),
  assistantProcess: new RongxinDataSvc('/v1.0/assistant/process'),
  notices: new RongxinDataSvc('/v1.0/notices'),
  commissionEmployee: new RongxinDataSvc('/v1.0/commission/employee'),
  commissionTracking: new RongxinDataSvc('/v1.0/commission/tracking'),
  commissionWithdraw: new RongxinDataSvc('/v1.0/commission/withdraw'),
  commissionConfig: new RongxinDataSvc('/v1.0/commission/config'),
  crops: new RongxinDataSvc('/v1.0/crops'),
  breeds: new RongxinDataSvc('/v1.0/breeds'),
}
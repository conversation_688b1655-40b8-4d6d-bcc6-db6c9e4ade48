/**
 * base data svc
 * <AUTHOR>
 * 2018-01-30
 */

'use strict'

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:mgr:services:dataSvc:baseDataSvc')
const agent = require('superagent')
const Q = require('q')
const config = require('config')
const os = require('os')

const METHOD_GET = 'get'
const METHOD_POST = 'post'
const METHOD_PUT = 'put'
const METHOD_DELETE = 'delete'
const METHOD_PATCH = 'patch'

const rongxin_platform_name = 'rongxin-platform-name'
const rongxin_rpc_iid = 'rongxin_rpc_iid'
const serverHost = os.hostname()
const pid = process.pid
const Counter = require('../../utils/counter')
const REQ_DEFAULT_TIMEOUT = 2 * 60 * 1000
const Rongxin_DataSvc = 'rongxin_dataService'

let counter = new Counter(0, 200)
counter.on('target', self => {
  self.value = 1
})

class BaseDataSvc {
  constructor(dataSource, authentication) {
    this._dataSource = dataSource
    if (authentication)
      this._authentication = authentication
  }

  execute(verb, path, payload, authentication, req, options) {
    let method = 'execute'
    let self = this;
    if (!self.disabledLogger) {
      debug.verbose(method, '[Enter]');
    }

    let defer = Q.defer();

    try {
      let host = config.get(self._dataSource).host;
      let port = config.get(self._dataSource).port;
      const protocol = config.get(self._dataSource).protocol || 'http';
      let unixsock = config.get(self._dataSource).unixsock;
      if (unixsock) {
        unixsock = pathUtil.join(os.tmpdir(), unixsock);
      }

      let source = port ? `${protocol}://${host}:${port}` : `${protocol}://${host}`
      if (unixsock && fs.existsSync(unixsock)) {
        source = 'http+unix://' + encodeURIComponent(unixsock);
      }

      let url = `${source}${path}`

      if (!this.disabledLogger)
        debug(method, `Url: ${url}`)

      let request_method = verb || METHOD_GET
      request_method = verb.trim().toLowerCase()

      let request

      switch (request_method) {
        case METHOD_GET:
          request = agent.get(url);
          break;
        case METHOD_POST:
          request = agent.post(url);
          break;
        case METHOD_PUT:
          request = agent.put(url);
          break;
        case METHOD_DELETE:
          request = agent.del(url);
          break;
        case METHOD_PATCH:
          request = agent.patch(url);
          break;
        default:
          request = agent.get(url);
          break;
      }

      if (typeof payload === 'string' && !authentication)
        authentication = payload

      request.set('Content-Type', 'application/json')
      if (authentication || self._authentication)
        request.set('Authorization', authentication || self._authentication)

      if (options && options.landHeader) {
        request.set(options.landHeader.key, options.landHeader.value);
      }
      if (req) {
        let platformName = req.get(rongxin_platform_name)
        if (platformName)
          request.set(rongxin_platform_name, platformName)
      }

      counter.value += 1
      let invocationId = `${serverHost}_${pid}_${new Date().getTime()}_${counter.value}`
      request.set(rongxin_rpc_iid, invocationId)

      if (request_method === METHOD_PATCH || request_method === METHOD_PUT || request_method === METHOD_POST)
        request.send(JSON.stringify(payload))

      // set default timeout
      request.timeout({
        deadline: REQ_DEFAULT_TIMEOUT
      })

      if (self._dataSource !== Rongxin_DataSvc && !self.disabledLogger)
        debug(`[RPC-Entry] source: ${source} path: ${path} reqHeaders: ${JSON.stringify(request.header)} body: ${typeof payload === 'string' ? '' : JSON.stringify(payload)}`)

      if (!self.disabledLogger)
        debug(`[RPC-Entry] source: ${source} path: ${path} iid ${invocationId}`);

      request.then(res => {
        if (self._dataSource !== Rongxin_DataSvc && !self.disabledLogger)
          debug(`[RPC-Exit] source: ${source} path: ${path} reqHeaders: ${JSON.stringify(request.header)} resHeaders: ${JSON.stringify(res.headers)} resStatus: ${res.status}`)

        if (!self.disabledLogger) {
          debug.verbose(method, '[Exit](success)', res.text || res)
        }
        defer.resolve(res)
      }).catch(error => {
        if (error.errno && error.errno === 'ETIME')
          error = {
            errorCode: 'ERPCTIME',
            httpCode: 504,
            reason: `${path} error : ${error.message}` || `rpc timeout for path ${path}`
          }

        debug.error(method, '[Exit](error)', ' Failed to execute the data service', error)
        defer.reject(error)
      })
    } catch (error) {
      debug.error(method, '[Exit](error)', error)
      defer.reject(error)
    }

    return defer.promise
  }
}

module.exports = BaseDataSvc;
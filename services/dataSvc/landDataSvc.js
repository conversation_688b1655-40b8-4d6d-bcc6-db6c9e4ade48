/**
 * land data svc
 * <AUTHOR>
 */

'use strict'

const logFactory = require('../../utils/logFactory')
const logUtil = require('../../utils/logUtil')
const debug = logFactory(logUtil())('rongxin:loan:dashboard:services:dataSvc:landDataSvc')
const BaseDataSvc = require('./baseDataSvc')
const querystring = require('qs')
const Q = require('q')

class NongheDataSvc extends BaseDataSvc {
  constructor(baseUrl, opts) {
    super( opts && opts.key || 'land_dataService' );
    this._baseUrl = UrlHandler(baseUrl);
    if (opts) {
      this.disabledLogger = opts.disabledLogger;
    }
  }

  resError(error, errorCode) {
    let self = this;
    let method = 'DataHelper.resError';
    errorCode = errorCode || 500;
    try {
      if (typeof error == 'string') {
        return {
          errorCode: errorCode,
          message: error
        }
      } else if (typeof error == 'object') {
        if (error.response) {
          let response = error.response
          if (response.body) {
            if (typeof response.body == 'string') {
              return {
                errorCode: response.status,
                message: response.body
              }
            } else if (typeof response.body == 'object' && response.body.reason) {
              return {
                errorCode: response.status,
                message: response.body.reason
              }
            } else if (typeof response.body == 'object' && response.body.message) {
              return {
                errorCode: response.status,
                message: response.body.message
              }
            } else if (response.text) {
              return {
                errorCode: response.status,
                message: response.text
              }
            }
          }
        } else if (error.body) {
          if (typeof error.body == 'string') {
            return {
              errorCode: error.status,
              message: error.body
            }
          } else if (typeof error.body == 'object' && error.body.reason) {
            return {
              errorCode: error.status,
              message: error.body.reason
            }
          } else if (typeof error.body == 'object' && error.body.message) {
            return {
              errorCode: error.status,
              message: error.body.message
            }
          }
        } else if (error.message) {
          return {
            errorCode: errorCode,
            message: error.message
          }
        }
      }
    } catch (e) {
      if (error.text) {
        return {
          errorCode: error.status || 500,
          message: error.text
        }
      }
      return {
        errorCode: 500,
        message: error
      }
    }
    if (error.text) {
      return {
        errorCode: error.status || 500,
        message: error.text
      }
    }
    return {
      errorCode: 500,
      message: error
    }
  }

  /**
   * Find a record by id
   * @param id {string} record id
   * @param callback {function} optional param
   */
  getById(id, callback) {
    var self = this;
    var method = 'getById';
    if (!id) {
      return Q.reject('请提供记录id').nodeify(callback);
    }
    var path = self._baseUrl;
    path += '/' + id;

    return self.execute('get', path).then(function (data) {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path)
      }
      let result = data.body;
      if (!result || !result._id) {
        result = null;
      }
      if (!self.disabledLogger) {
        debug(method, '[Exit](success)', result)
      } else {
        debug(method, '[Exit](success)')
      }

      return result;
    }).fail(function (error) {
      debug(method, '[path]:', path)
      debug.error(method, '[Exit](error)', error)
      return Q.reject(self.resError(error))
    }).nodeify(callback);
  }


  getByUrl(url, condition, options, callback) {
    var self = this;
    var method = 'getByUrl';
    url = UrlHandler(url);
    if (!url) {
      return Q.reject('请提供完整的url').nodeify(callback);
    }
    var path = url;

    if (condition && Object.getOwnPropertyNames(condition).length > 0) {
      path += '?' + querystring.stringify(condition);
    }

    if (!options) options = {};
    if (!options.landHeader) {
      options.landHeader = { key: "rongxin-land-area", value: "22" };
    }

    return self.execute('get', path, null, null, null, options).then(function (data) {
      if (!self.disabledLogger) {
        debug(method, '[path]:', path);
      }
      var result = data.body;
      if (!self.disabledLogger) {
        debug(method, '[Exit]:', result);
      } else {
        debug(method, '[Exit](success)');
      }
      if (!result) {
        throw {
          errorCode: 'E_LAND_SVC_049',
          httpCode: 406,
          reason: `failed to retrieve land data`
        }
      }

      return result.data;
    }).fail(function (error) {
      debug(method, '[path]:', path);
      debug(method, '[error]:', error);
      throw error.response && error.response.body ? {
        errorCode: error.response.body.error,
        httpCode: error.response.body.status,
        reason: error.response.body.message
      } : error;
    }).nodeify(callback);
  };

}

function UrlHandler(url) {
  if (!url || typeof url != 'string')
    throw {
      errorCode: 'EDATAURL',
      httpCode: 500,
      reason: 'invalid input url'
    }

  if (url.substr(0, 1) != '/')
    url = '/' + url

  if (url.substr(url.length - 1, 1) == '/')
    url = url.substr(0, url.length - 2)

  return url;
}

module.exports = NongheDataSvc
/**
 * loanProduct Server index
 * <AUTHOR>
 */


'use strict'

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.dashboard.api:services:loanProduct:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetLoanProductListHandler = require('./getLoanProductListHandler');

class Server {
  constructor() {

  }

  async loanProductList(input, _opts) {
    let method = 'loanProductList';
    debug.verbose(method, '[Enter]');
    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    };
    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetLoanProductListHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

}
module.exports = new Server();
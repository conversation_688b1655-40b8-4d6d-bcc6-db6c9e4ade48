'use strict';

const HANDLER_NAME = 'getLoanProductListHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanProduct:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const loanProduct = require('../dataSvc/dataUtil').loanProducts;
const productFundData = require('../dataSvc/dataUtil').productFunds;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      if (opts.areaCode) {
        let prov, city, country, town, village;
        if (opts.areaCode.length > 9) {
          prov = opts.areaCode.substr(0, 2);
          city = opts.areaCode.substr(0, 4);
          country = opts.areaCode.substr(0, 6);
          town = opts.areaCode.substr(0, 9);
          village = opts.areaCode;
          input.areaList = {
            $in: [prov, city, country, town, village]
          }
        } else if (opts.areaCode.length > 6) {
          prov = opts.areaCode.substr(0, 2);
          city = opts.areaCode.substr(0, 4);
          country = opts.areaCode.substr(0, 6);
          town = opts.areaCode;
          input.areaList = {
            $in: [prov, city, country, town]
          }
        } else if (opts.areaCode.length > 4) {
          prov = opts.areaCode.substr(0, 2);
          city = opts.areaCode.substr(0, 4);
          country = opts.areaCode;
          input.areaList = {
            $in: [prov, city, country]
          }
        } else if (opts.areaCode.length > 2) {
          prov = opts.areaCode.substr(0, 2);
          city = opts.areaCode;
          input.areaList = {
            $in: [prov, city]
          }
        } else {
          prov = opts.areaCode;
          input.areaList = prov;
        }
      }
      let products = await loanProduct.getListAndCountByCondition(input);

      for (let item of products.result) {
        item.fund = await productFundData.getOneByCondition({
          pId: item._id
        });
      }
      self.context.result = products;
      debug(method, '[Exit](success)')
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
/**
 * GetLoanSupplementHandler
 * <AUTHOR>
 */

'use strict';

const HANDLER_NAME = 'GetAllLandListWithTypeHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanLandType:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const formatAreaCode = require('../../persistence/formatAreaCode');
const Decimal = require('decimal.js');
const {assert,getAllMyLands} = require('../../utils/general');


const {
  loanApplicationOutLand: loanApplicationOutLandData,
  loanApplicationLandType: loanApplicationLandTypeData,
  loanApplication: loanApplicationData,
  userVerifys: userVerifyData,
  landData,
  loanLand,
  cxwqCirculations: cxwqCirculationsData,
  LoanSvrLand:LoanSvrLandData,
  applicationSubcontract: loanApplicationSubcontractData,
  applicationSubcontractLand: loanApplicationSubcontractLandData,
  loanApplicationConfirm: loanApplicationConfirmData,
  loanApplicationConfirmLand: loanApplicationConfirmLandData,
} = require('../dataSvc/dataUtil');

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    const method = `${this.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      const { aId,defaultCropType } = this.context.input;

      debug(method, '[Exit](success)');
      this.context.result = Object.assign(this.context.result || {},await this.handle(aId,defaultCropType))
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  async handle(aId,defaultCropType,opts){
    const method = `${this.getName()}.doAsync`
    defaultCropType = defaultCropType || 'corn';
    const { assuranceSn , assuranceNotEmpty , outlandAssuranceIncludedIn , noCxwqWithoutLandOffline } = opts || {}
    const loanApplication = await loanApplicationData.getById(aId),
        { uId,username,userMobile } = loanApplication;

    const {lands:allMyLandsOrigin=[],contractList=[]} = loanApplication.verifyInfo && loanApplication.verifyInfo.occupyInfo && loanApplication.verifyInfo.occupyInfo || {};
    const allMyLands = allMyLandsOrigin.filter( v=>!assuranceSn || v.assuranceSn === assuranceSn );
    let assuranceSnQuery = assuranceSn && { assuranceSn } || {};
    assuranceNotEmpty &&  ( assuranceSnQuery = { $or:[ { assuranceSn:{$exists:false} } , { assuranceSn:''} ] } )
    const assuranceIncludedInQuery = outlandAssuranceIncludedIn && { assuranceIncludedIn:true } || {};
    
    // const { allMyLands , myLands , userVerify , allLandCodes } = await getAllMyLands( loanApplication , debug , method )
    // const userVerify = await userVerifyData.getOneByCondition({ uId, IDCardStatus: 'approved', archived: false });
    // assert(userVerify,'E_GET_ALL_LAND_LIST_WITH_TYPE_001','该订单的用户未通过实名认证！')
    //自用地
    // const options = { landHeader: { key: "rongxin-land-area", value: loanApplication.area.substr(0, 2) } };
    // // const myLands = [];
    // const { content: myLandsOrigin = [] } = process.env.NODE_ENV !=='local-dev' && (
    //     await landData.getByUrl("/api/v1.0/contract/list", { cardNo: userVerify.IDCard }, options)
    // ) || { content: [] };
    // const myLands = ( await Promise.all( myLandsOrigin.map( async contract=>{
    //   const families = await landData.getByUrl("/api/v1.0/contractor/families", { contractor: contract.code }, options);
    //   const isCoOwner = families.some( _faimly=>_faimly.cardNo === userVerify.IDCard && _faimly.coOwner === '1' )
    //   return isCoOwner ? contract : null;
    // } ) ) ).filter(v=>v);
    // debug(method,'contractList',myLands);
    // allMyLands.map(vv=>{
    //     vv.request = { name:loanApplication.username,uniqueId: userVerify.IDCard } ;
    //     vv.contractorName=vv.contract.name;
    //     vv.area = vv.contractArea;
    //   });
    // debug(method,'contractLandList',allMyLands);
    // for (const contract of landData.content) {
    //   let lands = await landsData.getByUrl("/api/v1.0/contract/land/list", { contract: contract.grantNo }, options);
    //
    //   for (let land of lands) {
    //     let obj = {};
    //     obj.name = land.name;
    //     obj.contractCode = contract.code
    //     obj.landCode = land.land;
    //     obj.area = parseFloat((land.contractArea * 0.0015).toFixed(2));
    //     obj.code = contract.grantNo;
    //     obj.type = 0;
    //     debug(method,'landQueryExtendInfo1',JSON.stringify({sourceType:'legal',sn:land.land}));
    //     obj.extendInfo = await loanApplicationOutLandData.getOneByCondition({sourceType:'legal',sn:land.land});
    //     result.lands.push(obj);
    //   }
    // }
    //流转地
    const newAndOldOtherLands = await loanLand.postByCondition({ aId, archived: false,...assuranceSnQuery,limit: "unlimited" });
    newAndOldOtherLands.forEach(vv=>{
      vv.area = Number( vv.lands.reduce((r,v)=>r.add(new Decimal(v.area)),new Decimal(0)) );
      vv.lands.forEach(v=>{
        v.request = { name:vv.username , uniqueId:vv.IDCard };//默认值
        v.contract = {grantNo:v.contractCode};//默认值
        v.areaMu = v.area
      });
    });
    const cxwqGeneral = await cxwqCirculationsData.postByCondition({ aId: loanApplication._id,circulationType:1, archived: false,isRevoked:false,limit: "unlimited" });
    const cxwqWithoutLandOnline = !noCxwqWithoutLandOffline && await cxwqCirculationsData.postByCondition({ aId: loanApplication._id,circulationType:2,...assuranceSnQuery,isRevoked:false, archived: false,limit: "unlimited" }) || [];
    const cxwqWithoutLandOffline = await cxwqCirculationsData.postByCondition({ aId: loanApplication._id,circulationType:3,...assuranceSnQuery,isRevoked:false, archived: false,limit: "unlimited" });
    const cxwqWithoutLandOnlineHadSignList = cxwqWithoutLandOnline.filter(v=>v.status === 4);
    const cxwqWithoutLandOnlineUnSignList = cxwqWithoutLandOnline.filter(v=>v.status !== 4);
    const cxwqWithoutEffective = [...cxwqWithoutLandOnlineHadSignList,...cxwqWithoutLandOffline];
    const newLands = newAndOldOtherLands.map(v=>{
      const c = v.cxwqId && cxwqGeneral.find(vv=>v.cxwqId===vv._id) ;
      if( !c )return ;
      const request = { name:c.name,uniqueId:c.idCard };
      v.cropType = c.cropType;
      v.lands.forEach(vv=>( vv.cropType=v.cropType , vv.request = request ));
      return v;
    }).filter(v=>v);
    const otherLands = newAndOldOtherLands.filter(v=>v.signFinish);
    const oldOtherLandsCount = newAndOldOtherLands.filter(v=>!v.cxwqId).length;
    //从签约里取出地块本身
    const allOtherLands = otherLands.map(v=>v.lands||[]).reduce((res,it)=>res.concat(it),[]).map(v=>({...v}));
    //册外地
    const outLands = await loanApplicationOutLandData.postByCondition({ aId, ...assuranceSnQuery , ...assuranceIncludedInQuery , archived: false, disabled: false,limit: "unlimited" });
    const subcontracts = await loanApplicationSubcontractData.getByCondition({aId,archived: false,limit:'unlimited'});

    allMyLands.forEach(v=>{
      Object.assign(v,{ landType:0,landCode:v.land});
      v.areaMu = new Decimal(v.area||v.contractArea||0).mul(new Decimal(0.0015)).toFixed(2, Decimal.ROUND_DOWN);
      // v.contract = {grantNo: v.contract}
    });
    // 由平方米转亩，乘以0.0015
    const myLandArea = allMyLands.reduce((res, it) => res.add(new Decimal(it.areaMu)), new Decimal(0)).toFixed(2, Decimal.ROUND_DOWN);
    debug(method,'allMyLands',allMyLands);
    allOtherLands.forEach(v=>(v.landType=1));
    outLands.forEach(v=>(v.landType=2));
    await Promise.all(outLands.map(async it=>{
      const land = await LoanSvrLandData.getById(it.landId);
      it.position = land && land.vertexList.coordinates[0].map( ([lng,lat])=>({lng,lat}) ) || [{lng:0,lat:0}];
      it.name = land && land.name || '';
      it.landCode=land && land.sn || '';
    }));
    // const landTypes = await loanApplicationLandTypeData.getByCondition({aId,archived:false});

    // myLands.forEach( it1=> it1.landType = landTypes.find(it2=>it2.sourceType===0 && it2.uniqueCode === it1.grantNo)
    //     || {uId,aId,area:it1.area,sourceType:0,uniqueCode:it1.grantNo,landType:0} );
    // otherLands.forEach( it1=> it1.landType = landTypes.find(it2=>it2.sourceType===1 && it2.uniqueCode === it1.contractorNo)
    //     || {uId,aId,area:it1.area,sourceType:1,uniqueCode:it1.contractorNo,landType:0} );
    // outLands.forEach( it1=> it1.landType = landTypes.find(it2=>it2.sourceType===2 && it2.uniqueCode === it1.sn)
    //     || {uId,aId,area:it1.area,sourceType:2,uniqueCode:it1.sn,landType:0} );

    // const all = [...myLands, ...otherLands, ...outLands];

    const confirmLands = await loanApplicationConfirmLandData.postByCondition({limit:'unlimited',aId,archived:false,...assuranceSnQuery,isRevoked:{$ne:true}});
    //先分组再求长度
    const oldOtherLandHadSignCount = otherLands.filter( v=>!v.cxwqId ).length;
    const newOtherLandHadSignCount = Array.from( new Set(otherLands.map( v=>v.cxwqId ).filter(v=>v) ) ).length;
    const otherLandHadSignCount = oldOtherLandHadSignCount + newOtherLandHadSignCount;
    // const otherLandHadSignCount = Array.from( new Set(otherLands.map( v=>v.cxwqId ).filter(v=>v) ) ).length;
    // 已签约/未签约
    const hadSign = Math.max( cxwqGeneral.filter(v => v.status === 4).length , otherLandHadSignCount ),
        unSign = Math.max( 0 , cxwqGeneral.length + oldOtherLandsCount - hadSign );
    debug(method,'signCountFromAllType',hadSign,unSign,',[cxwq status is 4 length]:',
        cxwqGeneral.filter(v => v.status === 4).length,',[',oldOtherLandHadSignCount,'+',
        newOtherLandHadSignCount,']=',otherLandHadSignCount,',cxwqGeneral.length:',cxwqGeneral.length,',oldOtherLandsCount:' , oldOtherLandsCount);
    const withoutLandCount = cxwqWithoutLandOnline.length + cxwqWithoutLandOffline.length;
    const withoutLandOnlineHadSignCount = cxwqWithoutLandOnlineHadSignList.length;
    const withoutLandOnlineUnSignCount = cxwqWithoutLandOnlineUnSignList.length;
    const withoutLandOfflineCount = cxwqWithoutLandOffline.length;
    const withoutLandArea = cxwqWithoutEffective.reduce( (r,v)=>r.add( new Decimal(v.area||0) ) , new Decimal(0) ).toFixed(2, Decimal.ROUND_DOWN);
    const withoutLandOnlineHadSignArea = cxwqWithoutLandOnlineHadSignList.reduce( (r,v)=>r.add( new Decimal(v.area||0) ) , new Decimal(0) ).toFixed(2, Decimal.ROUND_DOWN);
    const withoutLandOnlineUnSignArea = cxwqWithoutLandOnlineUnSignList.reduce( (r,v)=>r.add( new Decimal(v.area||0) ) , new Decimal(0) ).toFixed(2, Decimal.ROUND_DOWN);
    const withoutLandOfflineArea = cxwqWithoutLandOffline.reduce( (r,v)=>r.add( new Decimal(v.area||0) ) , new Decimal(0) ).toFixed(2, Decimal.ROUND_DOWN);
    debug( method , 'withoutLandInfo' , withoutLandCount , withoutLandOnlineHadSignCount , withoutLandOnlineUnSignCount , withoutLandOfflineCount , withoutLandArea , withoutLandOnlineHadSignArea , withoutLandOnlineUnSignArea , withoutLandOfflineArea );
    // 已签约流转面积
    const signArea = allOtherLands.reduce((res, it) => res.add(new Decimal(it.area)), new Decimal(0)).toFixed(2, Decimal.ROUND_DOWN);
    // 自有地面积
    // 流转地面积
    const otherLandArea = otherLands.reduce((res, it) => res.add(new Decimal(it.area)), new Decimal(0)).toFixed(2, Decimal.ROUND_DOWN);
    // 册外地面积
    const outLandArea = outLands.reduce((res, it) => res.add(new Decimal(it.area || 0)), new Decimal(0)).toFixed(2, Decimal.ROUND_DOWN);
    const outLandCount = outLands.length;
    const confirmArea = confirmLands.reduce((res, it) => res.add(new Decimal(it.areaMu || 0)), new Decimal(0)).toFixed(2, Decimal.ROUND_DOWN);
    const confirmPersons = Array.from( new Set( confirmLands.map(v=>v.request.uniqueId) ) ).map( uniqueId=>({
      uniqueId,
      area: confirmLands.filter(v=>v.request.uniqueId === uniqueId)
          .reduce((res, it) => res.add(new Decimal(it.areaMu || 0)), new Decimal(0)).toFixed(2, Decimal.ROUND_DOWN)
    }) )
    const hadSignSubcontractCount = subcontracts.filter(v=>v.type ==='personal' && v.signFinish).length;
    const unHadSignSubcontractCount = subcontracts.filter(v=>v.type ==='personal' && !v.signFinish).length;
    const hadSignSubcontractArea = subcontracts.filter(v=>v.type ==='personal' && v.signFinish)
                                    .reduce((res, it) => res.add(new Decimal(it.areaMu)), new Decimal(0))
                                    .toFixed(2, Decimal.ROUND_DOWN);
    const villageSubcontractCount = subcontracts.filter( v=>v.type ==='village' ).length;
    const villageSubcontractArea = subcontracts.filter( v=>v.type ==='village' )
        .reduce((res, it) => res.add(new Decimal(it.areaMu)), new Decimal(0))
        .toFixed(2, Decimal.ROUND_DOWN);

    // 所有面积
    const allArea = new Decimal(myLandArea)
        .add(new Decimal(otherLandArea))
        .add(new Decimal(outLandArea))
        .add(new Decimal(hadSignSubcontractArea))
        .add(new Decimal(villageSubcontractArea))
        .add(new Decimal(confirmArea))
        .add(new Decimal(withoutLandArea))
        .toFixed(2, Decimal.ROUND_DOWN);
    const allAreaWithoutMine = new Decimal(otherLandArea)
        .add(new Decimal(outLandArea))
        .toFixed(2, Decimal.ROUND_DOWN);
    // all.reduce((res, it) => res.add(new Decimal(it.area)), new Decimal(0)).toFixed(2, Decimal.ROUND_DOWN);

    //因为条目较少，以下未考虑效率换取简洁性
    // 水田户
    // const paddyCount = all.filter(it=>it.landType.landType === 1 ).length;
    // 水田面积
    // const paddyArea = all.filter(it=>it.landType.landType === 1 ).reduce((res,it)=>res+it.area,0);
    // 旱田户
    // const dryCount = all.filter(it=>it.landType.landType === 0 ).length;
    // 旱田面积
    // const dryArea = all.filter(it=>it.landType.landType === 0 ).reduce((res,it)=>res+it.area,0);

    const subcontractLands = await loanApplicationSubcontractLandData.postByCondition({limit:'unlimited',aId,archived:false,signFinish:true,...assuranceSnQuery,isRevoked:{$ne:true}});
    subcontractLands.forEach(v=>v.request = v.request || {name:v.contract.name,uniqueId:v.contract.cardNo});
    [ ...confirmLands,...subcontractLands].forEach(v=>(v.contractorName = v.contract.name,v.name=v.originData.name));
    const subcontractOnlyContractList = subcontractLands
        .filter((v,i)=>!subcontractLands.some((vv,ii)=>v.contract.grantNo === vv.contract.grantNo && ii < i))
        .map( v=>v.contract );
    const subcontractContracts = subcontractOnlyContractList.map(v=>{
      const lands = subcontractLands.filter(vv=>vv.contractorNo === v.grantNo).map(v=>({...v}));
      v.areaMu = lands.reduce((r,vv)=>r.add( new Decimal(vv.areaMu) ),new Decimal(0)).toFixed(2, Decimal.ROUND_DOWN);
      return v;
    });
    [ ...allMyLands || [] , ... subcontractLands || [],...confirmLands || [] ].forEach(v=>{
      v.area = v.areaMu || v.area;
      v.landType = 0;
      v.landCode = v.landCode || v.land;
      v.cropType =  v.cropType || defaultCropType;
    });
    const lands = { myLands:allMyLands, otherLands:allOtherLands, outLands };
    // const allLandList = Object.values(lands).reduce((res,arr)=>res.concat(arr),[]);
    const cxwqWithoutLandOnlineLandCount = cxwqWithoutLandOnlineHadSignList.reduce(( r,v )=>r +(  v.simpleLandInfo || [] ).length , 0 );
    const cxwqWithoutLandOfflineLandCount = cxwqWithoutLandOffline.reduce(( r,v )=>r +(  v.simpleLandInfo || [] ).length , 0 );
    const landCount = Object.values(lands).reduce((res,arr)=>res+arr.length,0)
        + cxwqWithoutLandOnlineLandCount;
    + cxwqWithoutLandOfflineLandCount;
    [...allOtherLands,...outLands].reduce((res,v)=>
        res.concat(v),[]).forEach(v=>(v.area=v.area||0,v.username = loanApplication.username,v.userMobile=loanApplication.userMobile));
    // cropTypeDic = { '玉米':'corn','水稻':'rice','大豆':'soybean','其它':'other','其他':'other', }
    const cropArea = ['corn','rice','soybean','other'].reduce((r,k)=>(r[k]=new Decimal(0),r),{});
    const addCropAreaFunc = (cropType,area)=>cropArea[ cropType ] = cropArea[ cropType ].add( new Decimal(area||0));
    Object.values(lands).reduce((r,v)=>r.concat(v),[])
        .map(v=>(v.cropType = v.cropType || defaultCropType,v) )
        .forEach( ({cropType,area})=>addCropAreaFunc( cropType , area ) );
        // .reduce( (r,v)=>( r[v.cropType] = r[v.cropType].add( new Decimal(v.area||0)) ,r) ,cropArea);
        // multipleCropType
    cxwqWithoutEffective.forEach(({area,multipleCropType,cropType=defaultCropType})=>{
      multipleCropType = multipleCropType || { [ cropType ]:area };
      Object.entries( multipleCropType ).forEach( ([ cropType , area ])=>addCropAreaFunc( cropType , area ) )
    } );
    // cropArea[defaultCropType] = cropArea[defaultCropType].add(new Decimal(myLandArea));//自有耕地按默认面积计
    cropArea[defaultCropType] = cropArea[defaultCropType]
        .add(new Decimal(hadSignSubcontractArea))
        .add(new Decimal(villageSubcontractArea))
        .add( new Decimal( confirmArea ) );

    const allLands = Object.values( lands ).reduce((r,v)=>r.concat(v),[])
        .concat( subcontractLands )
        .concat( confirmLands );
    await Promise.all(allLands.map(async it => it.areaInfo = await formatAreaCode.getFormatAreaCode(( it.landCode || it.areaCode || '' ).substr(0,12)))); //关联村名

    const personStatisticsLands = [...allMyLands,...allOtherLands,...subcontractLands,...confirmLands];
    const personStatisticsUniqueId = Array.from( new Set( personStatisticsLands.map( v=>v.request.uniqueId ) ) );
    // 有法定地块编码的数据，从各方数据中拿到地，再组织数据
    const personStatisticsWithLand = personStatisticsUniqueId.map( uniqueId=>{
      const list = personStatisticsLands.filter(v=>uniqueId === v.request.uniqueId) , [{request:{name}}] = list;
      const areaMu = list.reduce( (r,v)=>r.add( new Decimal( v.areaMu ) ) , new Decimal(0) ).toFixed(2, Decimal.ROUND_DOWN)
      const grantNo = [...new Set( list.map(v=>v.contract.grantNo) )].join(',');
      return {name,uniqueId,areaMu,grantNo}
    } );
    // 无法定地块编码的数据，以合同为单位，从合同中提取面积
    const personStatisticsWithoutLand = cxwqWithoutEffective.map(v=>{
      const {name,idCard,principalUniqueId,area,contractorNoList} = v;
      return {
        name , areaMu:area.toFixed(2) , uniqueId: principalUniqueId || idCard ,
        grantNo: ( contractorNoList || [] ).join(',')
      }
    });
    const personStatistics = [...personStatisticsWithLand , ...personStatisticsWithoutLand];

    const statistics = {
      hadSign, unSign, withoutLandCount , withoutLandOnlineHadSignCount , withoutLandOnlineUnSignCount , withoutLandOfflineCount ,
      signArea,
      myLandArea,
      otherLandArea,
      outLandArea,outLandCount,
      withoutLandArea , withoutLandOnlineHadSignArea , withoutLandOnlineUnSignArea , withoutLandOfflineArea,
      allArea,
      allAreaWithoutMine,
      confirmArea,
      confirmPersonCount:confirmPersons.length,
      hadSignSubcontractCount,unHadSignSubcontractCount,hadSignSubcontractArea,
      villageSubcontractCount,villageSubcontractArea,
      landCount,
      cropArea,
      // paddyCount,paddyArea,dryCount,dryArea
    };

    [...Object.values(lands).reduce((r,v)=>r.concat(v),[]),...subcontractLands,...confirmLands].forEach(v=>Object.assign(v,{username,userMobile}));
    return { lands, statistics, personStatistics ,  contractList, subcontractLands, subcontractContracts,confirmLands,confirmPersons};
  }

  undoAsync(done) {
    done();
  }
}


module.exports = Handler;
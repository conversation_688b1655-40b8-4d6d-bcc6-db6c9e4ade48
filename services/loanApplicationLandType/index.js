/**
 * LoanSupplement Svc index
 * <AUTHOR>
 */

'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:app.api:services:loanApplicationLandType:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;

const GetAllLandListWithTypeHandler = require('./getAllLandListWithTypeHandler');

class Service {
  constructor() {

  }

  async getAllLandListWithTypeHandler(input, _opts) {
    const method = 'createUserSupplement';
    debug(method, '[Enter]');

    const context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      const svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetAllLandListWithTypeHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }


}

module.exports = new Service();
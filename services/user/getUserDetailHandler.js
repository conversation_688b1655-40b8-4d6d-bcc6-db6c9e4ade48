'use strict';

const HANDLER_NAME = 'GetUserDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:services:user:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const employeeData = require('../dataSvc/dataUtil').employees;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      
      let employee = await employeeData.getById(input.uId);
      if (!employee) {
        throw {
          httpCode: 406,
          errorCode: 'E_USER_INTO_037',
          reason: 'not found'
        };
      }

      let result = {
        _id: employee._id,
        mobile: employee.mobile,
        realname: employee.username,
      };

      debug(method, '[Exit](success)');
      self.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
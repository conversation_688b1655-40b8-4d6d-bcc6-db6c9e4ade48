/*
 * @Author: qays
 * @Date: 2023-04-12 15:27:08
 * @LastEditTime: 2023-04-12 15:50:07
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.mgr.app.api\services\user\getSm4InfoHandler.js
 */
'use strict';

const HANDLER_NAME = 'getSm4InfoHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:services:user:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const config = require('config');
const { sm4 } = require('sm-crypto')
const key = config.get('rongxin_api_usercredit.sm4key')
const employeeData = require('../dataSvc/dataUtil').employees;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      let opts = self.context.opts;
      
      let employee = await employeeData.getById(opts.uId);
      if (!employee) {
        throw {
          httpCode: 404,
          errorCode: 'E_USER_INTO_037',
          reason: 'not found'
        };
      }

      const result = {
        username: sm4.encrypt(employee.username, key, {mode: 'cbc', iv: 'fedcba98765432100123456789abcdef'}),
        mobile: sm4.encrypt(employee.mobile, key, {mode: 'cbc', iv: 'fedcba98765432100123456789abcdef'}),
        timestamp: new Date().getTime()
      };

      debug(method, '[Exit](success)');
      self.context.result = result;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
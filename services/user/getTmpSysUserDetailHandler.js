'use strict';

const HANDLER_NAME = 'GetTmpSysUserDetailHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.api:services:user:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const userData = require('../dataSvc/dataUtil').user;
const userVerifysData = require('../dataSvc/dataUtil').userVerifys;

class Handler extends BaseHandler {
  constructor(context) {
    super(context)
  }

  getName() {
    return HANDLER_NAME
  }

  async doAsync(done) {
    let self = this;
    let method = `${self.getName()}.doAsync`
    debug(method, '[Enter]')
    try {
      let input = self.context.input;
      
      let user = await userData.getById(input.uId);
      if (!user) {
        throw {
          httpCode: 406,
          errorCode: 'E_USER_INTO_037',
          reason: 'not found'
        };
      }

      const data = {
        realname: '',
        mobile: user.mobile,
      };
      const verify = await userVerifysData.getOneByCondition({ uId: user._id, IDCardStatus: 'approved', archived: false });
      if(verify) {
        data.realname = verify.realname;
      }

      debug(method, '[Exit](success)');
      self.context.result = data;
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done()
  }
}

module.exports = Handler
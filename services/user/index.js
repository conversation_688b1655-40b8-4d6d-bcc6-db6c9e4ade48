/*
 * @Author: qays
 * @Date: 2023-02-01 11:39:09
 * @LastEditTime: 2023-04-12 15:29:44
 * @Description: Do not edit
 * @FilePath: \rongxin.loan.mgr.app.api\services\user\index.js
 */
'use strict';

const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.cms.api:services:villager:index');
const SvcHandlerMgrt = require('nongfu.merchant.svcfw').SvcHandlerMgrt;
const GetUserDetailHandler = require("./getUserDetailHandler");
const GetTmpSysUserDetailHandler = require("./getTmpSysUserDetailHandler");
const GetSm4InfoHandler = require("./getSm4InfoHandler");

class Service {
  constructor() {

  }

  async getUserDetail(input, _opts) {
    let method = 'getUserDetail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetUserDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getTmpSysUserDetail(input, _opts) {
    let method = 'getTmpSysUserDetail';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();
      svcHandlerMgrt.addHandler(new GetTmpSysUserDetailHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

  async getSm4Info(input, _opts) {
    let method = 'getSm4Info';
    debug(method, '[Enter]');

    let context = {
      input: input || {},
      opts: _opts || {},
      result: {},
      error: {}
    }

    try {
      let svcHandlerMgrt = new SvcHandlerMgrt();

      svcHandlerMgrt.addHandler(new GetSm4InfoHandler(context));
      await svcHandlerMgrt.processAsync(context);
      debug(method, '[Exit](success): ', context.result);
      return context.result;
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      throw error;
    }
  }

}

module.exports = new Service();
const superagent = require('superagent');
const util = require('util');
const config = require('config')
const { join } = require('path');
const _ = require('lodash');
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:assistanceAgent:emchat');

const apiUrl = 'https://a1.easemob.com';
const orgName = config.get('emchatAuth.orgName');
const appName = config.get('emchatAuth.appName');
const clientId = config.get('emchatAuth.clientId');
const clientSecret = config.get('emchatAuth.clientSecret');

let accessToken = '';

// #region utils
exports.get = async (path) => {
  const url = apiUrl + join('/', orgName, appName, path);
  const token = await this.getToken();

  const { body } = await superagent.get(url).set({
    'content-type': 'application/json',
    Authorization: 'Bearer ' + token
  });
  return body;
}

exports.post = async (path, data) => {
  const url = apiUrl + join('/', orgName, appName, path);
  const token = await this.getToken();

  const { body } = await superagent.post(url).set({
    'content-type': 'application/json',
    Authorization: 'Bearer ' + token
  }).send(data);
  return body;
}

exports.put = async (path, data) => {
  const url = apiUrl + join('/', orgName, appName, path);
  const token = await this.getToken();

  const { body } = await superagent.put(url).set({
    'content-type': 'application/json',
    Authorization: 'Bearer ' + token
  }).send(data);
  return body;
}

exports.del = async (path) => {
  const url = apiUrl + join('/', orgName, appName, path);
  const token = await this.getToken();

  const { body } = await superagent.del(url).set({
    'content-type': 'application/json',
    Authorization: 'Bearer ' + token
  });
  return body;
}

/**
 * 获取 token
 */
exports.getToken = async () => {
  if (accessToken !== '') {
    return accessToken;
  }

  const url = apiUrl + join('/', orgName, appName, '/token');
  const { body } = await superagent.post(url).set({
    'content-type': 'application/json'
  }).send({
    grant_type: 'client_credentials',
    client_id: clientId,
    client_secret: clientSecret
  });

  accessToken = body.access_token;
  return accessToken;
};
// #endregion

// #region 用户

/**
 * 注册用户(先查询用户是否存在)
 * @param  {string} 用户名
 * @param  {string} 密码
 * @param  {string} 昵称
 */
exports.register = async (username, password, nickname) => {
  try {
    const _user = await this.get('/users/' + username).catch((err) => {
      if (err.status !== 404) {
        debug.error(err)
      }
    });

    if (_user) {
      return _.get(_user, 'entities[0]');
    }

    const data = { username, password, nickname };
    const result = await this.post('/users', data);
    return _.get(result, 'entities[0]');
  } catch (error) {
    return;
  }
};

/**
 * 注册用户
 * @param  {string} 用户名
 * @param  {string} 密码
 */
exports.createUser = async (username, password, nickname) => {
  const data = { username, password, nickname };
  const result = await this.post('/users', data);
  return _.get(result, 'entities[0]');
};

/**
 * 批量注册用户
 * @param  {array}
 */
exports.createUsers = async (users) => {
  return await this.post('/users', users);
};

/**
 * 重置密码
 * @param  {string} 用户名
 * @param  {string} 旧密码
 * @param  {string} 新密码
 */
exports.resetPassword = async (username, newPassword) => {
  const data = {
    newpassword: newPassword
  };
  return await this.put('/users/' + username + '/password', data);
};

/**
 * 获取用户
 * @param  {string} 用户名
 */
exports.getUser = async (username) => {
  try {
    const result = await this.get('/users/' + username);
    return _.get(result, 'entities[0]');
  } catch (error) {
    return;
  }
};

/**
 * 获取用户列表
 * @param  {int} 数量
 */
exports.getUsers = async (limit) => {
  return await this.get('/users?limit=' + limit);
};

/**
 * 删除用户
 * @param  {string} 用户名
 */
exports.deleteUser = async (username) => {
  return await this.del('/users/' + username);
};

/**
 * 批量删除用户
 * @param  {limit} 数量
 */
exports.deleteUsers = async (limit) => {
  return await this.del('/users?limit=' + limit);
};

/**
 * 修改用户昵称
 * @param  {string} 用户名
 * @param  {string} 昵称
 */
exports.updateNickname = async (username, nickname) => {
  const data = {
    nickname: nickname
  };
  return await this.put('/users/' + username, data);
};

/**
 * 添加好友
 * @param {string} 用户名
 * @param {string} 好友用户名
 */
exports.addFriend = async (username, friendName) => {
  return await this.post('/users/' + username + '/contacts/users/' + friendName);
};

/**
 * 删除好友
 * @param  {string} 用户名
 * @param  {string} 好友用户名
 */
exports.deleteFriend = function (username, friendName) {
  return this.getToken(function () {
    this.del('/users/' + username + '/contacts/users/' + friendName);
  });
};

/**
 * 得到用户好友列表
 * @param  {string} 用户名
 */
exports.getFriends = function (username, callback) {
  return this.getToken(function () {
    this.get('/users/' + username + '/contacts/users', callback);
  });
};

/**
 * 获取用户黑名单列表
 * @param  {string} 用户名
 */
exports.getBlacklist = function (username, callback) {
  return this.getToken(function () {
    this.get('/users/' + username + '/blocks/users', callback);
  });
};

/**
 * 批量添加黑名单
 * @param {string} 用户名
 * @param {array} 黑名单用户名数组
 */
exports.addUserForBlacklist = function (username, users, callback) {
  return this.getToken(function () {
    const data = {
      usernames: users
    };
    this.post('/users/' + username + '/blocks/users', data, callback);
  });
};

/**
 * 移除黑名单
 * @param  {string} 用户名
 * @param  {string} 黑名单用户名
 */
exports.deleteUserFromBlacklist = function (username, blackUser, callback) {
  return this.getToken(function () {
    this.del('/users/' + username + '/blocks/users/' + blackUser, callback);
  });
};

/**
 * 查看用户在线状态
 * @param  {string} 用户名
 */
exports.isOnline = function (username, callback) {
  return this.getToken(function () {
    this.get('/users/' + username + '/status', callback);
  });
};

/**
 * 查询用户离线消息数
 * @param  {string} 用户名
 */
exports.getOfflineMessages = function (username, callback) {
  return this.getToken(function () {
    this.post('/users/' + username + '/offline_msg_count', callback);
  });
};

/**
 * 获取离线消息状态
 * @todo 状态 delivered 表示此用户的该条离线消息已经收到过了，undelivered 表示此用户的该条离线消息未还未收到
 * @param  {string} 用户名
 * @param  {string} 消息ID
 */
exports.getOfflineMessageStatus = function (username, msgid, callback) {
  return this.getToken(function () {
    this.get('/users/' + username + '/offline_msg_status/' + msgid, callback);
  });
};

/**
 * 禁用用户账号
 * @param  {string} 用户名
 */
exports.deactivateUser = function (username, callback) {
  return this.getToken(function () {
    this.post('/users/' + username + '/deactivate', callback);
  });
};

/**
 * 解禁用户账号
 * @param  {string} 用户名
 */
exports.activeUser = function (username, callback) {
  return this.getToken(function () {
    this.post('/users/' + username + '/activate', callback);
  });
};

/**
 * 强制用户下线
 * @param  {string} 用户名
 */
exports.disconnectUser = function (username, callback) {
  return this.getToken(function () {
    this.post('/users/' + username + '/disconnect', callback);
  });
};

// #endregion

// #region 消息

/** 发送文本消息
 * @param  {string} 消息类型: users 发送给用户  chatgroups 发送给群聊  chatrooms 发送给聊天室
 * @param  {string} 发送人用户名
 * @param  {string|array} 接收人用户名|群聊 ID|聊天室 ID
 * @param  {string} 消息正文
 * @param  {object} 扩展信息
 */
exports.sendText = function (type, from, target, content, ext, callback) {
  return this.getToken(function () {

    if (!util.isArray(target)) {
      target = [target];
    }

    const data = {
      target_type: type,
      target: target,
      msg: {
        type: 'txt',
        msg: content
      },
      from: from,
      // ext: ext
    };

    this.post('/messages', data, callback);
  });
};

/**
 * 发送图片消息
 * @param  {string} 消息类型(参照文本消息)
 * @param  {string} 发送人用户名
 * @param  {string|array} 接收人(参照文本消息)
 * @param  {string} 文件名称
 * @param  {string} 文件 url
 * @param  {string} 安全校验
 * @param  {object} 扩展信息
 */
exports.sendImage = function (type, from, target, filename, fileUrl, secret, ext, callback) {
  return this.getToken(function () {

    if (!util.isArray(target)) {
      target = [target];
    }

    const data = {
      target_type: type,
      target: target,
      msg: {
        type: 'img',
        url: fileUrl,
        filename: filename,
        secret: secret,
        size: {
          width: 480,
          height: 720
        }
      },
      from: from,
      ext: ext,
    };

    this.post('/messages', data, callback);
  });
};

/**
 * 发送语音消息
 * @param  {string} 消息类型(参照文本消息)
 * @param  {string} 发送人用户名
 * @param  {string|array} 接收人(参照文本消息)
 * @param  {string} 文件名
 * @param  {string} 文件 url
 * @param  {string} 安全校验
 * @param  {int} 文件大小
 * @param  {object} 扩展信息
 */
exports.sendAudio = function (type, from, target, filename, fileUrl, secret, length, ext, callback) {

  return this.getToken(function () {

    if (!util.isArray(target)) {
      target = [target];
    }

    const data = {
      target_type: type,
      target: target,
      msg: {
        type: 'audio',
        url: fileUrl,
        filename: filename,
        length: length,
        secret: secret
      },
      from: from,
      ext: ext,
    };

    this.post('/messages', data, callback);
  });
};

/**
 * @param  {string} 消息类型(参照文本消息)
 * @param  {string} 发送人用户名
 * @param  {string|array} 接收人(参照文本消息)
 * @param  {string} 文件名
 * @param  {string} 文件 url
 * @param  {int} 文件大小
 * @param  {string} 视频缩略图
 * @param  {int} 视频缩略图大小
 * @param  {string} 缩略图安全校验码
 * @param  {string} 视频安全校验码
 * @param  {object} 扩展信息
 */
exports.sendVideo = function (type, from, target, filename, fileUrl, fileLength, thumb, thumbFileLength, thumbSecret, secret, ext, callback) {
  return this.getToken(function () {
    if (!util.isArray(target)) {
      target = [target];
    }

    const data = {
      target_type: type,
      target: target,
      msg: {
        type: 'video',
        url: fileUrl,
        filename: filename,
        thumb: thumb,
        length: fileLength,
        file_length: thumbFileLength,
        thumb_secret: thumbSecret,
        secret: secret
      },
      from: from,
      ext: ext,
    };

    this.post('/messages', data, callback);
  });
};

/**
 * 发送透传消息
 * @param  {string} 消息类型(参照文本消息)
 * @param  {string} 发送人用户名
 * @param  {string|array} 接收人(参照文本消息)
 * @param  {string} 消息命令
 * @param  {object} 扩展信息
 */
exports.sendCmd = function (type, from, target, action, ext, callback) {
  return this.getToken(function () {
    if (!util.isArray(target)) {
      target = [target];
    }

    const data = {
      target_type: type,
      target: target,
      msg: {
        type: 'cmd',
        action: action
      },
      from: from,
      ext: ext,
    };

    this.post('/messages', data, callback);
  });
};

// #endregion

// #region 群组

/**
 * 获取所有群组
 * @param  {int} 数量
 */
exports.getGroups = function (limit, callback) {
  return this.getToken(function () {
    this.get('/chatgroups?limit=' + limit, callback);
  });
};

/**
 * 获取一个或多个群组的详情
 * @param  {array} 群组 ID
 */
exports.getGroupDetail = function (groupIds, callback) {
  return this.getToken(function () {
    this.get('/chatgroups/' + groupIds, callback);
  });
};

/**
 * 创建一个群组
 * @param  {string} 群聊名称
 * @param  {string} 群公告
 * @param  {bool} 是否是公开群
 * @param  {int} 群内最大成员数
 * @param  {bool} 加入是否需要批准
 * @param  {string} 创建人用户名
 * @param  {array} 群成员
 */
exports.createGroup = function (groupName, desc, isPublic, maxUsers, approval, owner, members, callback) {
  return this.getToken(function () {
    const data = {
      groupname: groupName,
      desc: desc,
      public: isPublic,
      maxusers: maxUsers,
      approval: approval,
      owner: owner,
      members: members
    };

    this.post('/chatgroups', data, callback);
  });
};

/**
 * 修改群组信息
 * @param  {string} 群 ID
 * @param  {string} 群名称
 * @param  {string} 群公告
 * @param  {int} 群内最大成员数
 */
exports.updateGroup = function (groupId, groupName, description, maxUsers, callback) {

  return this.getToken(function () {

    const data = {
      groupname: groupId,
      description: description,
      maxusers: maxUsers
    };

    this.put('/chatgroups/' + groupId, data, callback);
  });

};

/**
 * 删除群组
 * @param  {string} 群 ID
 */
exports.deleteGroup = function (groupId, callback) {
  return this.getToken(function () {
    this.del('/chatgroups/' + groupId, callback);
  });
};

/**
 * 获取群组中成员
 * @param  {string} 群 ID
 */
exports.getGroupUsers = function (groupId, callback) {
  return this.getToken(function () {
    this.get('/chatgroups/' + groupId + '/users', callback);
  });
};

/**
 * 加入群
 * @param {string} 群 ID
 * @param {string} 被加入的成员用户名
 */
exports.addGroupMember = function (groupId, username, callback) {
  return this.getToken(function () {
    const data = {}
    this.post('/chatgroups/' + groupId + '/users/' + username, data, callback);
  });
};

/**
 * 往群内批量加入成员
 * @param {string} 群 ID
 * @param {array} 要加入的成员
 */
exports.addGroupMembers = function (groupId, users, callback) {
  return this.getToken(function () {

    const data = {
      usernames: users
    };

    this.post('/chatgroups/' + groupId + '/users', data, callback);
  });
};

/**
 * 往群内批量删除成员
 * @param {string} 群 ID
 * @param {string} 要删除的成员
 */
exports.delGroupMembers = function (groupId, users, callback) {
  return this.getToken(function () {
    this.del('/chatgroups/' + groupId + '/users/' + users, callback);
  });
};

/**
 * 从群组中删除指定的成员
 * @param  {string} 群 ID
 * @param  {string} 被删除的成员用户名
 */
exports.deleteGroupMember = function (groupId, username, callback) {
  return this.getToken(function () {
    this.del('/chatgroups/' + groupId + '/users/' + username, callback);
  });
};

/**
 * 获取用户参与的群组列表
 * @param  {string} 用户名
 */
exports.getGroupsForUser = function (username, callback) {
  return this.getToken(function () {
    this.get('/users/' + username + '/joined_chatgroups', callback);
  });
};

/**
 * 转让群组
 * @param  {string} 群 ID
 * @param  {string} 接收人用户名
 */
exports.changeGroupOwner = function (groupId, newOwner, callback) {

  return this.getToken(function () {
    const data = {
      newowner: newOwner,
    };

    this.put('/chatgroups/' + groupId, data, callback);
  });
};

/**
 * 获取群组黑名单用户名列表
 * @param  {string} 群 ID
 */
exports.getGroupBlackList = function (groupId, callback) {
  return this.getToken(function () {
    this.get('/chatgroups/' + groupId + '/blocks/users', callback);
  });
};

/**
 * 将成员添加到群组黑名单
 * @param {string} 群 ID
 * @param {string} 被加入黑名单成员用户名
 */
exports.addGroupBlackMember = function (groupId, username, callback) {
  return this.getToken(function () {
    this.post('/chatgroups/' + groupId + '/blocks/users/' + username, callback);
  });
};

/**
 * 将成员从群组黑名单中移除
 * @param  {string}
 * @param  {string}
 */
this.deleteGroupBlackMember = function (groupId, username, callback) {
  return this.getToken(function () {
    this.del('/chatgroups/' + groupId + '/blocks/users/' + username, callback);
  });
};

// #endregion

// #region 下载文件

/**
 * 下载文件
 * @param  {string}
 */
exports.downloadFile = function (uuid, callback) {
  return this.getToken(function () {
    this.get('/chatfiles/' + uuid, callback);
  });
};

// #endregion

// #region 聊天记录

/**
 * 获取聊天记录
 * @param  {string} 查询语句
 * @param  {int} 数量
 * @param  {int} 游标位置
 */
exports.getChatRecord = function (time, callback) {
  return this.getToken(function () {
    this.get('/chatmessages/' + time, callback);
  });
};

// #endregion

// #region 聊天室

/**
 * 创建聊天室
 * @param  {string} 聊天室名称
 * @param  {string} 聊天室公告
 * @param  {int} 最大成员数量
 * @param  {string} 创建人用户名
 * @param  {array} 成员列表
 */
exports.createChatRoom = function (name, description, maxUsers, owner, members, callback) {
  return this.getToken(function () {

    const data = {
      name: name,
      description: description,
      maxusers: maxUsers,
      owner: owner,
      members: members
    };

    this.post('/chatrooms', data, callback);
  });
};

/**
 * 修改聊天室信息
 * @param  {string} 聊天室 ID
 * @param  {string} 聊天室名称
 * @param  {string} 聊天室公告
 * @param  {int} 聊天室最大成员数量
 */
exports.updateChatRoom = function (chatRoomId, name, description, maxUsers, callback) {

  return this.getToken(function () {

    const data = {
      name: name,
      description: description,
      maxusers: maxUsers,
    };

    this.put('/chatrooms/' + chatRoomId, data, callback);
  });

};

/**
 * 删除聊天室
 * @param  {string} 聊天室 id
 */
exports.deleteChatRoom = async (chatRoomId) => {
  return await this.del('/chatrooms/' + chatRoomId);
};

/**
 * 获取聊天室列表
 */
exports.getChatRooms = async () => {
  return await this.get('/chatrooms');
};

/**
 * 获取聊天室详情
 * @param  {string} 聊天室 ID
 */
exports.getChatRoomDetail = async (chatRoomId) => {
  return await this.get('/chatrooms/' + chatRoomId);
};

/**
 * 获取用户加入的聊天室列表
 * @param  {string} 聊天室 id
 */
exports.getChatRoomJoined = async (username) => {
  return await this.get('/users/' + username + '/joined_chatrooms');
};

/**
 * 加入聊天室
 * @param {string} 聊天室 ID
 * @param {string} 被加入的成员用户名
 */
exports.addChatRoomMember = async (chatRoomId, username) => {
  return await this.post('/chatrooms/' + chatRoomId + '/users/' + username);
};

/**
 * 将成员移出聊天室
 * @param  {string} 聊天室 ID
 * @param  {string} 被移出的成员用户名
 */
exports.deleteChatRoomMember = async (chatRoomId, username) => {
  return await this.del('/chatrooms/' + chatRoomId + '/users/' + username);
};

// #endregion

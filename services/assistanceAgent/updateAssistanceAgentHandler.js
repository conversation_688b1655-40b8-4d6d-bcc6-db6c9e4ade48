'use strict';

const HANDLER_NAME = 'CreateAssistanceAgentHandler';
const logFactory = require('../../utils/logFactory');
const logUtil = require('../../utils/logUtil');
const debug = logFactory(logUtil())('rongxin:loan.mgr.app.api:services:assistanceAgent:' + HANDLER_NAME);
const BaseHandler = require('nongfu.merchant.svcfw').BaseHandler;
const assistanceAgentData = require('../dataSvc/dataUtil').assistanceAgent;
const employeeData = require('../dataSvc/dataUtil').employees;

class Handler extends BaseHandler {
  constructor(context) {
    super(context);
  }

  getName() {
    return HANDLER_NAME;
  }

  async doAsync(done) {
    const self = this;
    const method = `${self.getName()}.doAsync`;
    debug(method, '[Enter]');
    try {
      const input = self.context.input;
      const opts = self.context.opts;
      let result;
      const _agent = await assistanceAgentData.getOneByCondition({
        tId: opts.tId,
        agentId: opts.userid,
        archived: false
      });

      if (_agent) {
        result = await assistanceAgentData.putById(_agent._id, input)
      } else {
        const employee = await employeeData.getById(opts.userid);
        if (employee) {
          result = await assistanceAgentData.post({
            tId: opts.tId,
            agentId: opts.userid,
            queueId: employee.mobile || "",
            ...input
          });
        }

      }

      self.context.result = result;
      debug(method, '[Exit](success)', self.context.result);
      return done();
    } catch (error) {
      debug.error(method, '[Exit](failed)', error);
      return done(error);
    }
  }

  undoAsync(done) {
    done();
  }
}

module.exports = Handler;
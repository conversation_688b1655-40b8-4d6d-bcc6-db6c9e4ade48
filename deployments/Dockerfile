FROM harbor.cacfintech.com/rongxin/nodejs:build AS build

ARG GIT_BRANCH_FOR_MAKE=prod
ENV NODE_ENV $GIT_BRANCH_FOR_MAKE

ARG PACKAGE_JSON_MD5

# 创建项目目录
RUN mkdir -pv /var/app
WORKDIR /var/app
COPY package.json /var/app/package.json
COPY deployments/.ssh /root/.ssh
RUN npm run install:${GIT_BRANCH_FOR_MAKE} && npm cache clean --force
RUN rm -rf /root/.ssh


FROM harbor.cacfintech.com/rongxin/nodejs:runtime

ENV NODE_ENV=prod

# 从 build 阶段复制需要的文件到当前阶段
WORKDIR /var/app
COPY --from=build /var/app .
COPY . /var/app

CMD npm run start:${NODE_ENV} 2>&1 | tee -a trace.log

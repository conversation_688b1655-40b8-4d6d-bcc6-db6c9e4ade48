#!/usr/bin/env bash

ENV="test"
IMAGE_NAME="rongxin.loan.mgr.app.api"

docker pull harbor.cacfintech.com/rongxin/${IMAGE_NAME}:${ENV}

docker build -f ./deployments/Dockerfile . \
  -t harbor.cacfintech.com/rongxin/${IMAGE_NAME}:${ENV} \
  --cache-from harbor.cacfintech.com/rongxin/${IMAGE_NAME}:${ENV} \
  --build-arg GIT_BRANCH_FOR_MAKE=${ENV} \
  --build-arg PACKAGE_JSON_MD5="$(md5sum package.json | cut -d ' ' -f1)" \
  --build-arg COMMIT_SHA="$(git rev-parse HEAD)"

docker push harbor.cacfintech.com/rongxin/${IMAGE_NAME}:${ENV}

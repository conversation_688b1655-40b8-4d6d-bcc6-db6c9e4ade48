const FARMLANDTYPE = [{
  'key': 1,
  'value': {
    'type': '自有土地',
    'amount': [1, 2, 3, 4, 5]
  }
}, {
  'key': '2',
  'value': {
    'type': '流转土地',
    'amount': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
  }
}];

const LOANTERM = [{
  'key': 3,
  'value': '3个月'
}, {
  'key': 6,
  'value': '6个月'
}, {
  'key': 12,
  'value': '12个月'
}, {
  'key': 15,
  'value': '15个月'
}];

const LOANUSE = [{
  'key': 0,
  'value': '其他'
}, {
  'key': 1,
  'value': '支付地租'
}, {
  'key': 2,
  'value': '购买农资农机'
}, {
  'key': 3,
  'value': '扩大生产'
}, {
  'key': 4,
  'value': '生产设备维护'
}, {
  'key': 5,
  'value': '机械化运作'
}, {
  'key': 6,
  'value': '劳务雇佣'
}];

const REPAYMENT = [{
    'key': '1',
    'value': '按月付息，到期还本'
  },
  {
    'key': '03',
    'value': '等额本息'
  }
];

const MARITA = [{
  'key': 1,
  'value': '已婚'
}, {
  'key': 2,
  'value': '未婚'
}, {
  'key': 3,
  'value': '离异'
}];

const CONTACT_RELATIONSHIP = [{
  'key': "1",
  'value': '法人'
}, {
  'key': "2",
  'value': '股东'
}, {
  'key': "3",
  'value': '实际控制人'
}, {
  'key': "4",
  'value': '员工'
}, {
  'key': "5",
  'value': '其他'
}];

const BH_WORK_TYPE = [
  { value: "中国共产党机关负责人", key: "10100" },
  { value: "国家机关负责人", key: "10200" },
  { value: "民主党派和工商联负责人", key: "10300" },
  { value: "人民团体和群众团体、社会组织及其他成员组织负责人", key: "10400" },
  { value: "基层群众自治组织负责人", key: "10500" },
  { value: "企事业单位负责人", key: "10600" },
  { value: "科学研究人员", key: "20100" },
  { value: "工程技术人员", key: "20200" },
  { value: "农业技术人员", key: "20300" },
  { value: "飞机和船舶技术人员", key: "20400" },
  { value: "卫生专业技术人员", key: "20500" },
  { value: "经济和金融专业人员", key: "20600" },
  { value: "法律、社会和宗教专业人员", key: "20700" },
  { value: "教学人员", key: "20800" },
  { value: "文学艺术、体育专业人员", key: "20900" },
  { value: "新闻出版、文化专业人员", key: "21000" },
  { value: "其他专业技术人员", key: "29900" },
  { value: "办事人员:30100" },
  { value: "安全和消防人员", key: "30200" },
  { value: "其他办事人员和有关人员", key: "39900" },
  { value: "批发与零售服务人员 ", key: "40100" },
  { value: "交通运输、仓储和邮政业服务人员 ", key: "40200" },
  { value: "住宿和餐饮服务人员", key: "40300" },
  { value: "信息传输、软件和信息技术服务人员", key: "40400" },
  { value: "金融服务人员", key: "40500" },
  { value: "房地产服务人员", key: "40600" },
  { value: "租赁和商务服务人员", key: "40700" },
  { value: "技术辅助服务人员", key: "40800" },
  { value: "水利、环境和公共设施管理服务人员", key: "40900" },
  { value: "居民服务人员", key: "41000" },
  { value: "电力、燃气及水供应服务人员", key: "41100" },
  { value: "修理及制作服务人员", key: "41200" },
  { value: "文化、体育和娱乐服务人员", key: "41300" },
  { value: "健康服务人员", key: "41400" },
  { value: "其他社会生产和生活服务人员", key: "49900" },
  { value: "农业生产人员", key: "50100" },
  { value: "林业生产人员", key: "50200" },
  { value: "畜牧业生产人员", key: "50300" },
  { value: "渔业生产人员", key: "50400" },
  { value: "农、林、牧、渔业生产辅助人员", key: "50500" },
  { value: "其他农、林、牧、渔业生产及辅助人员", key: "59900" },
  { value: "农副产品加工人员", key: "60100" },
  { value: "食品、饮料生产加工人员", key: "60200" },
  { value: "烟草及其制品加工人员", key: "60300" },
  { value: "纺织、针织、印染人员", key: "60400" },
  { value: "纺织品、服装和皮革、毛皮制品加工制作人员", key: "60500" },
  { value: "木材加工、家具与木制品制作人员", key: "60600" },
  { value: "纸及纸制品生产加工人员", key: "60700" },
  { value: "印刷和记录媒介复制人员", key: "60800" },
  { value: "文教、工美、体育和娱乐用品制作人员", key: "60900" },
  { value: "石油加工和炼焦、煤化工生产人员", key: "61000" },
  { value: "化学原料和化学制品制造人员", key: "61100" },
  { value: "医药制造人员", key: "61200" },
  { value: "化学纤维制造人员", key: "61300" },
  { value: "橡胶和塑料制品制造人员", key: "61400" },
  { value: "非金属矿物制品制造人员", key: "61500" },
  { value: "采矿人员", key: "61600" },
  { value: "金属冶炼和压延加工人员", key: "61700" },
  { value: "机械制造基础加工人员", key: "61800" },
  { value: "金属制品制造人员", key: "61900" },
  { value: "通用设备制造人员", key: "62000" },
  { value: "专用设备制造人员", key: "62100" },
  { value: "汽车制造人员", key: "62200" },
  { value: "铁路、船舶、航空设备制造人员", key: "62300" },
  { value: "电气机械和器材制造人员", key: "62400" },
  { value: "计算机、通信和其他电子设备制造人员", key: "62500" },
  { value: "仪器仪表制造人员", key: "62600" },
  { value: "废弃资源综合利用人员", key: "62700" },
  { value: "电力、热力、气体、水生产和输配人员", key: "62800" },
  { value: "建筑施工人员", key: "62900" },
  { value: "运输设备和通用工程机械操作人员及有关人员", key: "63000" },
  { value: "生产辅助人员", key: "63100" },
  { value: "其他生产制造及有关人员", key: "69900" },
  { value: "军人", key: "70100" },
  { value: "不便分类的其他从业人员", key: "80100" },
  { value: "学生", key: "90100" },
  { value: "离退休人员", key: "90200" },
  { value: "其他无业人员", key: "90300" }
];

const LOAN_APP_ENUM_MAP = new Map([
  ['1', FARMLANDTYPE],
  ['2', LOANTERM],
  ['3', LOANUSE],
  ['4', REPAYMENT],
  ['5', MARITA],
  ['6', CONTACT_RELATIONSHIP],
  ['7', BH_WORK_TYPE]
]);

module.exports = {
  LOAN_APP_ENUM_MAP
}